@echo off
echo Step-by-step CSP fix
echo.

:menu
echo Choose an option:
echo 1. Kill port 8080 processes
echo 2. Start dev server
echo 3. Launch Chrome no security
echo 4. Do all steps automatically
echo 5. Exit
echo.
set /p choice="Enter choice (1-5): "

if "%choice%"=="1" goto kill_port
if "%choice%"=="2" goto start_server
if "%choice%"=="3" goto start_chrome
if "%choice%"=="4" goto do_all
if "%choice%"=="5" goto exit
goto menu

:kill_port
echo.
echo Killing processes on port 8080...
for /f "tokens=5" %%i in ('netstat -ano ^| findstr :8080') do (
    taskkill /f /pid %%i >nul 2>&1
)
taskkill /f /im node.exe >nul 2>&1
taskkill /f /im chrome.exe >nul 2>&1
echo Done!
echo.
goto menu

:start_server
echo.
echo Starting dev server...
start "Dev Server" cmd /k "npm run dev"
echo Server started in new window
echo.
goto menu

:start_chrome
echo.
set TEMP_DIR=%TEMP%\chrome-no-csp-%RANDOM%
mkdir "%TEMP_DIR%" >nul 2>&1
echo Starting Chrome no security...
start "Chrome No Security" chrome.exe --disable-web-security --no-sandbox --user-data-dir="%TEMP_DIR%" "http://localhost:8080"
echo Chrome started!
echo.
goto menu

:do_all
echo.
echo Doing all steps...
call :kill_port
timeout /t 2 /nobreak >nul
call :start_server
timeout /t 3 /nobreak >nul
call :start_chrome
echo.
echo All done! Check Chrome window
echo.
goto menu

:exit
exit