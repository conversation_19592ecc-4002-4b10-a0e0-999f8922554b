<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>管理后台连通性测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }
        .container {
            background: white;
            border-radius: 12px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        h1 {
            color: #2c3e50;
            text-align: center;
            margin-bottom: 30px;
        }
        .test-item {
            padding: 15px;
            margin: 10px 0;
            border-radius: 8px;
            border-left: 4px solid #ddd;
        }
        .test-item.pending { border-left-color: #f39c12; background: #fef9e7; }
        .test-item.success { border-left-color: #27ae60; background: #d5f4e6; }
        .test-item.error { border-left-color: #e74c3c; background: #fdf2f2; }
        
        .test-name { font-weight: bold; margin-bottom: 5px; }
        .test-url { font-size: 12px; color: #666; margin-bottom: 10px; }
        .test-result { font-size: 14px; }
        
        button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
            transition: transform 0.2s;
        }
        button:hover { transform: translateY(-2px); }
        button:disabled { opacity: 0.6; cursor: not-allowed; transform: none; }
        
        .summary {
            margin-top: 30px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 8px;
            text-align: center;
        }
        
        .log {
            background: #2c3e50;
            color: #ecf0f1;
            padding: 15px;
            border-radius: 6px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔗 管理后台与小程序连通性测试</h1>
        
        <div class="controls">
            <button onclick="startTests()" id="startBtn">开始测试</button>
            <button onclick="diagnoseCloudFunction()" id="diagnoseBtn">诊断云函数</button>
            <button onclick="clearResults()" id="clearBtn">清空结果</button>
        </div>
        
        <div id="testResults"></div>
        
        <div class="summary" id="summary" style="display: none;">
            <h3>测试总结</h3>
            <div id="summaryContent"></div>
        </div>
        
        <div class="log" id="logContainer">
            等待开始测试...
        </div>
    </div>

    <script>
        // API配置 - 根据诊断结果优先使用可用地址
        const API_URLS = [
            // 诊断显示这个地址可用
            'https://cloud1-4g85f8xlb8166ff1.tcb.qcloud.la/admin',

            // 备用地址
            'https://cloud1-4g85f8xlb8166ff1.tcb.qcloud.la/adminAPI',
            'https://cloud1-4g85f8xlb8166ff1.ap-beijing.app.tcloudbase.com/admin',
            'https://cloud1-4g85f8xlb8166ff1.ap-beijing.app.tcloudbase.com/adminAPI',

            // 本地测试地址
            'http://localhost:3001/admin',
            'http://127.0.0.1:3001/admin'
        ]
        
        let currentApiUrl = API_URLS[0]
        let testResults = []
        
        // 日志函数
        function log(message, type = 'info') {
            const logContainer = document.getElementById('logContainer')
            const timestamp = new Date().toLocaleTimeString()
            const logEntry = `[${timestamp}] ${message}\n`
            logContainer.textContent += logEntry
            logContainer.scrollTop = logContainer.scrollHeight
        }
        
        // 通用API调用函数
        async function callApi(action, data = {}) {
            const requestId = `browser_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
            
            for (let i = 0; i < API_URLS.length; i++) {
                const url = API_URLS[i]
                
                try {
                    log(`🚀 测试 ${action} - 尝试地址: ${url}`)
                    
                    const response = await fetch(url, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            action,
                            ...data,
                            timestamp: Date.now(),
                            requestId
                        })
                    })

                    if (!response.ok) {
                        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
                    }

                    const result = await response.json()
                    log(`✅ ${action} 成功 - ${url}`)
                    
                    currentApiUrl = url
                    return { success: true, data: result, url }
                } catch (error) {
                    log(`⚠️ ${action} 失败 - ${url}: ${error.message}`)
                    
                    if (i === API_URLS.length - 1) {
                        return { success: false, error: error.message, url }
                    }
                }
            }
        }
        
        // 测试配置
        const tests = [
            {
                name: '健康检查',
                action: 'healthCheck',
                data: { source: 'browser-connectivity-test' }
            },
            {
                name: '获取仪表板统计',
                action: 'data.getDashboardStats',
                data: {}
            },
            {
                name: '获取最近活动',
                action: 'data.getRecentActivities',
                data: { limit: 5 }
            },
            {
                name: '获取实时统计',
                action: 'realtime.getStats',
                data: {}
            },
            {
                name: '获取学生列表',
                action: 'data.getStudents',
                data: { params: { page: 1, limit: 5 } }
            }
        ]
        
        // 开始测试
        async function startTests() {
            const startBtn = document.getElementById('startBtn')
            const clearBtn = document.getElementById('clearBtn')
            const resultsContainer = document.getElementById('testResults')
            const summaryContainer = document.getElementById('summary')
            
            startBtn.disabled = true
            clearBtn.disabled = true
            resultsContainer.innerHTML = ''
            summaryContainer.style.display = 'none'
            testResults = []
            
            log('🔗 开始管理后台与小程序连通性测试...')
            
            for (const test of tests) {
                // 创建测试项UI
                const testItem = document.createElement('div')
                testItem.className = 'test-item pending'
                testItem.innerHTML = `
                    <div class="test-name">${test.name}</div>
                    <div class="test-url">接口: ${test.action}</div>
                    <div class="test-result">测试中...</div>
                `
                resultsContainer.appendChild(testItem)
                
                // 执行测试
                const result = await callApi(test.action, test.data)
                
                // 更新UI
                if (result.success) {
                    testItem.className = 'test-item success'
                    testItem.querySelector('.test-result').textContent = `✅ 成功 - 使用地址: ${result.url}`
                } else {
                    testItem.className = 'test-item error'
                    testItem.querySelector('.test-result').textContent = `❌ 失败: ${result.error}`
                }
                
                testResults.push({
                    name: test.name,
                    action: test.action,
                    success: result.success,
                    error: result.error,
                    url: result.url
                })
                
                // 添加延迟
                await new Promise(resolve => setTimeout(resolve, 1000))
            }
            
            // 显示总结
            showSummary()
            
            startBtn.disabled = false
            clearBtn.disabled = false
            
            log('🎉 测试完成！')
        }
        
        // 显示测试总结
        function showSummary() {
            const summaryContainer = document.getElementById('summary')
            const summaryContent = document.getElementById('summaryContent')
            
            const successCount = testResults.filter(r => r.success).length
            const totalCount = testResults.length
            
            let summaryHtml = `
                <p><strong>成功: ${successCount}/${totalCount}</strong></p>
                <p><strong>失败: ${totalCount - successCount}/${totalCount}</strong></p>
            `
            
            if (successCount > 0) {
                summaryHtml += `<p style="color: #27ae60;">✅ 可用的API地址: ${currentApiUrl}</p>`
            }
            
            if (successCount === totalCount) {
                summaryHtml += `<p style="color: #27ae60; font-weight: bold;">🎉 所有测试通过！管理后台与小程序连通正常。</p>`
            } else {
                summaryHtml += `<p style="color: #e74c3c; font-weight: bold;">⚠️ 部分测试失败，请检查配置和网络连接。</p>`
            }
            
            summaryContent.innerHTML = summaryHtml
            summaryContainer.style.display = 'block'
        }
        
        // 诊断云函数部署状态
        async function diagnoseCloudFunction() {
            log('🔍 开始诊断云函数部署状态...')

            const diagnoseBtn = document.getElementById('diagnoseBtn')
            diagnoseBtn.disabled = true

            // 1. 检查基础连通性
            log('📡 检查基础网络连通性...')
            for (const url of API_URLS) {
                try {
                    const response = await fetch(url, {
                        method: 'GET',
                        mode: 'no-cors' // 避免CORS问题
                    })
                    log(`✅ 网络可达: ${url}`)
                } catch (error) {
                    log(`❌ 网络不可达: ${url} - ${error.message}`)
                }
            }

            // 2. 检查OPTIONS请求（CORS预检）
            log('🔒 检查CORS配置...')
            for (const url of API_URLS.slice(0, 4)) { // 只检查主要地址
                try {
                    const response = await fetch(url, {
                        method: 'OPTIONS'
                    })
                    log(`✅ CORS支持: ${url} - Status: ${response.status}`)
                } catch (error) {
                    log(`❌ CORS问题: ${url} - ${error.message}`)
                }
            }

            // 3. 尝试简单的GET请求
            log('📥 尝试GET请求...')
            for (const url of API_URLS.slice(0, 4)) {
                try {
                    const response = await fetch(url, {
                        method: 'GET'
                    })
                    const text = await response.text()
                    log(`✅ GET响应: ${url} - Status: ${response.status}`)
                    if (text.length < 200) {
                        log(`   响应内容: ${text}`)
                    }
                } catch (error) {
                    log(`❌ GET失败: ${url} - ${error.message}`)
                }
            }

            // 4. 检查云函数特定的调用方式
            log('☁️ 检查云函数调用方式...')
            const cloudFunctionUrl = 'https://cloud1-4g85f8xlb8166ff1.ap-beijing.app.tcloudbase.com/admin'
            try {
                const response = await fetch(cloudFunctionUrl, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-WX-SOURCE': 'admin-dashboard'
                    },
                    body: JSON.stringify({
                        action: 'ping',
                        timestamp: Date.now()
                    })
                })
                const result = await response.json()
                log(`✅ 云函数调用: ${response.status} - ${JSON.stringify(result)}`)
            } catch (error) {
                log(`❌ 云函数调用失败: ${error.message}`)
            }

            log('🎯 诊断完成！请查看上述日志确定问题所在。')
            diagnoseBtn.disabled = false
        }

        // 清空结果
        function clearResults() {
            document.getElementById('testResults').innerHTML = ''
            document.getElementById('summary').style.display = 'none'
            document.getElementById('logContainer').textContent = '等待开始测试...'
            testResults = []
        }
    </script>
</body>
</html>
