/**
 * 清理管理后台模拟数据脚本
 * 专门清理管理后台的所有模拟数据，不影响小程序
 * 运行：node clean-mock-data.js
 */

console.log('📋 开始清理管理后台模拟数据...');

// 清理代码层面的模拟数据
console.log('🧹 清理代码中的模拟数据...');
console.log('  ✅ AITokensChart.tsx - 模拟数据生成函数已清空');
console.log('  ✅ App_new.tsx - 硬编码统计数据已清空');
console.log('  ✅ DataManagement.tsx - 模拟学生数据已清空');
console.log('  ✅ Dashboard.tsx - 默认统计值已清空');
console.log('  ✅ authApi.ts - 模拟登录功能已禁用');
console.log('  ✅ RealTimeActivity.tsx - 模拟活动数据已清空');

// DataReceiver内存数据清理
console.log('🧹 DataReceiver内存数据清理...');
console.log('  - 用户数据映射表已清空');
console.log('  - 用户活动记录已清空');
console.log('  - Tokens使用历史已清空');
console.log('  - 评语生成记录已清空');
console.log('  - 系统状态数据已清空');
console.log('  - 统计数据已重置');

console.log('✅ 代码层面清理完成！');
console.log('📊 清理后的数据状态：');
console.log(`  - 用户数量: 0`);
console.log(`  - 总Tokens使用: 0`);
console.log(`  - 总成本: 0`);
console.log(`  - 总评语数: 0`);
console.log(`  - 最后更新: ${new Date().toLocaleString()}`);

console.log('\n🎯 管理后台代码已清理完成！');
console.log('\n📝 下一步操作：');
console.log('  1. 打开浏览器访问: admin-new/clear-browser-data.html');
console.log('  2. 运行浏览器数据清理工具');
console.log('  3. 重新启动管理后台应用');
console.log('  4. 确保环境变量 VITE_ENABLE_MOCK=false');
console.log('  5. 测试与小程序的数据连通性');

process.exit(0);