/* 班级详情页面样式 */
.class-detail-page {
  min-height: 100vh;
  background: #f8f9fa;
  padding-bottom: 120rpx;
}

/* 班级信息卡片 */
.class-info-card {
  background: white;
  margin: 24rpx;
  border-radius: 20rpx;
  padding: 32rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.06);
}

.class-header {
  display: flex;
  align-items: flex-start;
  margin-bottom: 24rpx;
}

.class-icon {
  width: 64rpx;
  height: 64rpx;
  background: rgba(64, 128, 255, 0.1);
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16rpx;
}

.class-basic-info {
  flex: 1;
}

.class-name {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 8rpx;
}

.class-meta {
  display: flex;
  gap: 16rpx;
}

.grade-text, .subject-text {
  font-size: 24rpx;
  color: #666;
  background: #f0f0f0;
  padding: 4rpx 12rpx;
  border-radius: 8rpx;
}

.class-description {
  background: #f8f9fa;
  padding: 20rpx;
  border-radius: 12rpx;
  margin-bottom: 24rpx;
  font-size: 26rpx;
  color: #666;
  line-height: 1.6;
}

/* 统计网格 */
.class-stats-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 16rpx;
}

.stat-card {
  text-align: center;
  padding: 20rpx 12rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
}

.stat-number {
  font-size: 32rpx;
  font-weight: 600;
  color: #4080FF;
  margin-bottom: 8rpx;
}

.stat-label {
  font-size: 22rpx;
  color: #999;
}

/* 功能菜单 */
.function-menu {
  background: white;
  margin: 24rpx;
  border-radius: 20rpx;
  padding: 32rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.06);
}

.menu-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 24rpx;
}

.menu-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20rpx;
}

.menu-item {
  padding: 24rpx;
  background: #f8f9fa;
  border-radius: 16rpx;
  text-align: center;
  transition: all 0.3s ease;
}

.menu-item:active {
  transform: scale(0.98);
  background: #f0f0f0;
}

.menu-icon {
  width: 56rpx;
  height: 56rpx;
  border-radius: 14rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 12rpx;
}

.menu-icon.student { background: #4080FF; }
.menu-icon.record { background: #52C873; }
.menu-icon.comment { background: #FFAA33; }
.menu-icon.analytics { background: #8B5CF6; }

.menu-text {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 6rpx;
}

.menu-desc {
  font-size: 22rpx;
  color: #999;
}

/* 最近活动 */
.recent-activities {
  background: white;
  margin: 24rpx;
  border-radius: 20rpx;
  padding: 32rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.06);
}

.section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 24rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.section-more {
  display: flex;
  align-items: center;
  gap: 6rpx;
  font-size: 24rpx;
  color: #999;
}

.activity-list {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.activity-item {
  display: flex;
  align-items: center;
  padding: 20rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
}

.activity-icon {
  width: 40rpx;
  height: 40rpx;
  border-radius: 10rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16rpx;
  flex-shrink: 0;
}

.activity-icon.positive { background: #52C873; }
.activity-icon.academic { background: #4080FF; }
.activity-icon.social { background: #FFAA33; }
.activity-icon.negative { background: #FF5247; }

.activity-content {
  flex: 1;
}

.activity-title {
  font-size: 26rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 4rpx;
}

.activity-desc {
  font-size: 22rpx;
  color: #666;
}

.activity-time {
  font-size: 20rpx;
  color: #999;
  flex-shrink: 0;
}

.empty-activities {
  text-align: center;
  padding: 60rpx 20rpx;
}

.empty-text {
  font-size: 26rpx;
  color: #999;
  margin-top: 16rpx;
}

/* 底部操作 */
.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  padding: 24rpx;
  border-top: 1rpx solid #f0f0f0;
  display: flex;
  gap: 16rpx;
}

.edit-btn {
  flex: 1;
  background: #f8f9fa !important;
  color: #666 !important;
  border: none !important;
}

.quick-record-btn {
  flex: 2;
}