<!--
  班级管理页面
-->
<view class="class-list-page">
  <!-- 搜索区域 -->
  <view class="search-section">
    <van-search
      value="{{searchKeyword}}"
      placeholder="搜索班级名称"
      bind:change="onSearchChange"
      bind:search="onSearch"
    />
  </view>

  <!-- 班级列表 -->
  <view class="class-list-container">
    <view wx:if="{{classList.length > 0}}" class="class-list">
      <view
        wx:for="{{classList}}"
        wx:key="id"
        class="class-item"
        data-class="{{item}}"
        bindtap="goToClassDetail"
      >
        <view class="class-header">
          <view class="class-icon">
            <van-icon name="friends-o" size="20px" color="#4080FF" />
          </view>
          <view class="class-info">
            <view class="class-name">{{item.name}}</view>
            <view class="class-desc">{{item.description}}</view>
          </view>
          <view class="class-actions">
            <van-icon
              name="ellipsis"
              size="20px"
              color="#999"
              data-class="{{item}}"
              catchtap="showClassActions"
            />
          </view>
        </view>

        <view class="class-stats">
          <view class="stat-item">
            <view class="stat-number">{{item.studentCount}}</view>
            <view class="stat-label">学生数</view>
          </view>
          <view class="stat-item">
            <view class="stat-number">{{item.todayRecords}}</view>
            <view class="stat-label">今日记录</view>
          </view>
          <view class="stat-item">
            <view class="stat-number">{{item.weeklyAvg}}</view>
            <view class="stat-label">周均分</view>
          </view>
        </view>

        <view class="class-footer">
          <view class="class-time">创建于 {{item.createTimeText}}</view>
          <van-tag
            type="{{item.status === 'active' ? 'primary' : 'default'}}"
            size="small"
          >
            {{item.statusText}}
          </van-tag>
        </view>
      </view>
    </view>

    <!-- 空状态 -->
    <view wx:else class="empty-state">
      <view class="empty-text">暂无班级</view>
      <view class="empty-desc">点击下方按钮创建第一个班级</view>
    </view>
  </view>

  <!-- 创建按钮 -->
  <view class="create-button">
    <van-button
      type="primary"
      size="large"
      round
      bindtap="goToCreateClass"
    >
      创建班级
    </van-button>
  </view>

  <!-- 操作菜单 -->
  <van-action-sheet
    show="{{showActionSheet}}"
    actions="{{actionSheetActions}}"
    bind:close="hideClassActions"
    bind:select="onActionSelect"
  />
</view>