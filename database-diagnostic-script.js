// 🔍 数据库诊断脚本
// 在浏览器控制台中运行此脚本来诊断数据库问题

async function diagnoseDatabaseStructure() {
  console.log('🔍 开始数据库诊断...')
  
  try {
    // 获取 cloudbaseService 实例
    const { cloudbaseService } = await import('./admin-new/src/services/cloudWebSDK')
    
    // 调用诊断功能
    const result = await cloudbaseService.callFunction('dataQuery', {
      action: 'diagnoseDatabaseStructure'
    })
    
    console.log('🔍 数据库诊断原始结果:', result)
    
    if (result.code === 200 && result.data) {
      const data = result.data
      
      console.log('\n' + '='.repeat(60))
      console.log('📊 数据库诊断报告')
      console.log('='.repeat(60))
      console.log(`🌍 环境: ${data.environment}`)
      console.log(`⏰ 时间: ${data.timestamp}`)
      console.log(`📁 总集合数: ${data.summary.totalCollections}`)
      console.log(`✅ 存在的集合: ${data.summary.existingCollections}`)
      console.log(`📈 有数据的集合: ${data.summary.collectionsWithData}`)
      console.log('')
      
      // 详细集合信息
      for (const [collectionName, info] of Object.entries(data.collections)) {
        if (info.exists) {
          const status = info.hasData ? '✅ 有数据' : '⚠️  无数据'
          console.log(`📦 ${collectionName}: ${status} (${info.count} 条记录)`)
          if (info.sampleFields && info.sampleFields.length > 0) {
            console.log(`   字段: ${info.sampleFields.join(', ')}`)
          }
        } else {
          console.log(`❌ ${collectionName}: 不存在 - ${info.error}`)
        }
      }
      
      console.log('\n' + '='.repeat(60))
      
      // 给出建议
      console.log('🔧 建议和解决方案:')
      console.log('')
      
      const { collections } = data
      
      if (!collections.users?.hasData) {
        console.log('⚠️  users 集合无数据 - 可能是小程序还没有用户注册或登录')
      }
      
      if (!collections.students?.hasData) {
        console.log('⚠️  students 集合无数据 - 这就是为什么学生总数显示0的原因！')
        console.log('   👉 需要在小程序端录入学生信息')
      }
      
      if (!collections.comments?.hasData) {
        console.log('⚠️  comments 集合无数据 - 这就是为什么评语数显示0的原因！')
        console.log('   👉 需要在小程序端生成一些评语')
      }
      
      console.log('')
      console.log('💡 解决方案：')
      console.log('1. 打开你的小程序')
      console.log('2. 登录小程序（创建users数据）')
      console.log('3. 录入一些学生信息（创建students数据）') 
      console.log('4. 生成一些评语（创建comments数据）')
      console.log('5. 刷新管理后台页面')
      
    } else {
      console.error('❌ 诊断失败:', result.message)
    }
    
  } catch (error) {
    console.error('❌ 诊断脚本执行失败:', error)
    
    // 提供手动检查方案
    console.log('')
    console.log('🔧 手动检查步骤：')
    console.log('1. 确认你的小程序确实有数据')
    console.log('2. 检查云环境ID是否正确：cloud1-4g85f8xlb8166ff1')
    console.log('3. 确认数据库权限设置正确')
    console.log('4. 检查控制台是否有其他错误信息')
  }
}

// 简化版诊断 - 直接检查现有数据
async function quickDiagnose() {
  console.log('🔍 快速诊断现有数据结构...')
  
  // 检查 realDataService 的调用结果
  try {
    const { realDataService } = await import('./admin-new/src/services/realDataService')
    const result = await realDataService.getDashboardStats()
    
    console.log('📊 realDataService 返回结果:', result)
    
    if (result.totalUsers === 0 && result.todayComments === 0 && result.aiCalls === 0) {
      console.log('❌ 所有数据都是0，说明数据库中没有数据或查询有问题')
      console.log('')
      console.log('💡 可能的原因：')
      console.log('1. 小程序数据库中确实没有数据')
      console.log('2. 集合名称不匹配（比如应该是student而不是students）')
      console.log('3. 字段名称不匹配')
      console.log('4. 数据库权限问题')
    }
    
  } catch (error) {
    console.error('❌ 快速诊断失败:', error)
  }
}

// 导出函数供控制台使用
console.log('🔍 数据库诊断工具已加载')
console.log('使用方法：')
console.log('1. diagnoseDatabaseStructure() - 完整诊断')
console.log('2. quickDiagnose() - 快速诊断')

// 自动导出到 window 对象
window.diagnoseDatabaseStructure = diagnoseDatabaseStructure
window.quickDiagnose = quickDiagnose