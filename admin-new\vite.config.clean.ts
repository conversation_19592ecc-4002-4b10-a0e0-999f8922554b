import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'
import path from 'path'

// 极简配置，避免所有可能的问题
export default defineConfig({
  plugins: [
    react({
      // 使用最基础的配置
      include: "**/*.{jsx,tsx}",
      babel: {
        plugins: [],
        presets: []
      }
    })
  ],
  
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src')
    }
  },

  server: {
    port: 3000,
    host: '127.0.0.1',
    strictPort: true,
    open: false,
    cors: true,
    // 完全禁用HMR
    hmr: false,
    // 最宽松的头部设置
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': '*',
      'Access-Control-Allow-Headers': '*'
    }
  },

  build: {
    target: 'es2015',
    outDir: 'dist',
    assetsDir: 'assets',
    sourcemap: false,
    minify: false,
    rollupOptions: {
      output: {
        manualChunks: undefined
      }
    }
  },

  // 设置基础路径为相对路径，适用于静态文件部署
  base: './',

  esbuild: {
    target: 'es2015',
    logOverride: { 'this-is-undefined-in-esm': 'silent' }
  },

  optimizeDeps: {
    force: true,
    include: ['react', 'react-dom']
  }
})
