<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>快速连通测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 { color: #2c3e50; text-align: center; }
        button {
            background: #3498db;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #2980b9; }
        .result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 5px;
            font-family: monospace;
        }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .info { background: #d1ecf1; color: #0c5460; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 快速连通测试</h1>
        
        <div>
            <button onclick="testSimpleAPI()">测试简单API</button>
            <button onclick="checkAdminAPIStatus()">检查AdminAPI状态</button>
            <button onclick="testHealthCheck()">测试健康检查</button>
            <button onclick="testDashboardStats()">测试仪表板数据</button>
            <button onclick="testRealtimeStats()">测试实时数据</button>
            <button onclick="clearResults()">清空结果</button>
        </div>
        
        <div id="results"></div>
    </div>

    <script>
        const API_URLS = {
            admin: 'https://cloud1-4g85f8xlb8166ff1.tcb.qcloud.la/admin-v2',
            test: 'https://cloud1-4g85f8xlb8166ff1.tcb.qcloud.la/test',
            adminOld: 'https://cloud1-4g85f8xlb8166ff1.tcb.qcloud.la/admin'
        }
        
        function addResult(message, type = 'info') {
            const results = document.getElementById('results')
            const div = document.createElement('div')
            div.className = `result ${type}`
            div.textContent = `[${new Date().toLocaleTimeString()}] ${message}`
            results.appendChild(div)
            results.scrollTop = results.scrollHeight
        }
        
        async function callAPI(action, data = {}, useTestAPI = false) {
            try {
                const apiUrl = useTestAPI ? API_URLS.test : API_URLS.admin
                addResult(`🚀 调用 ${action} (${useTestAPI ? 'testAPI' : 'adminAPI'})...`, 'info')

                const response = await fetch(apiUrl, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        action,
                        ...data,
                        timestamp: Date.now(),
                        requestId: `test_${Date.now()}`
                    })
                })

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`)
                }

                const result = await response.json()

                if (result.code === 200) {
                    addResult(`✅ ${action} 成功: ${JSON.stringify(result.data).substring(0, 100)}...`, 'success')
                    return result.data
                } else {
                    addResult(`❌ ${action} 失败: ${result.message}`, 'error')
                    return null
                }

            } catch (error) {
                addResult(`❌ ${action} 错误: ${error.message}`, 'error')
                return null
            }
        }

        async function testSimpleAPI() {
            const result = await callAPI('test', { message: 'hello from admin' }, true)
            if (result) {
                addResult(`🎉 简单API测试成功！状态: ${result.status}`, 'success')
            }
        }

        async function checkAdminAPIStatus() {
            try {
                addResult('🔍 检查AdminAPI状态...', 'info')

                const response = await fetch(API_URLS.admin, {
                    method: 'OPTIONS'
                })

                if (response.ok) {
                    addResult('✅ AdminAPI CORS预检成功，函数状态正常', 'success')
                    addResult('💡 可以尝试测试健康检查了', 'info')
                } else {
                    addResult(`⚠️ AdminAPI状态异常: ${response.status}`, 'error')
                }

            } catch (error) {
                if (error.message.includes('Failed to fetch')) {
                    addResult('❌ AdminAPI仍在更新中或CORS未配置', 'error')
                } else {
                    addResult(`❌ AdminAPI检查失败: ${error.message}`, 'error')
                }
            }
        }
        
        async function testHealthCheck() {
            const result = await callAPI('healthCheck', { source: 'quick-test' })
            if (result) {
                addResult(`🎉 健康检查成功！版本: ${result.version}, 状态: ${result.status}`, 'success')
            }
        }
        
        async function testDashboardStats() {
            const result = await callAPI('data.getDashboardStats')
            if (result) {
                addResult(`📊 仪表板数据: 用户${result.totalUsers}个, 评语${result.todayComments}条`, 'success')
            }
        }
        
        async function testRealtimeStats() {
            const result = await callAPI('realtime.getStats')
            if (result) {
                addResult(`⚡ 实时数据: 在线${result.current?.onlineUsers || 0}人, 今日操作${result.current?.recentOperations || 0}次`, 'success')
            }
        }
        
        function clearResults() {
            document.getElementById('results').innerHTML = ''
        }
        
        // 页面加载时自动测试
        window.addEventListener('load', () => {
            addResult('🔗 页面加载完成，准备测试连通性...', 'info')
            setTimeout(() => {
                addResult('📝 建议操作顺序：', 'info')
                addResult('1. 先测试简单API（验证基础连通性）', 'info')
                addResult('2. 再测试健康检查（验证adminAPI）', 'info')
                addResult('3. 最后测试数据接口', 'info')
            }, 500)
        })
    </script>
</body>
</html>
