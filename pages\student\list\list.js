/**
 * 学生管理页面
 * 基于微信云开发
 */
const app = getApp();
const { cloudService } = require('../../../services/cloudService');
const { extractSurname } = require('../../../utils/globalUtils');
const ExcelShareUtils = require('../../../utils/excelShareUtils');

Page({
  /**
   * 页面的初始数据
   */
  data: {
    // 学生列表（从云数据库获取）
    studentList: [],

    // 统计数据（从云数据库计算）
    totalStudents: 0,
    activeStudents: 0,
    todayRecords: 0,

    // 搜索和筛选
    searchKeyword: '',
    selectedClass: '',
    selectedClassId: null, // 当前筛选的班级ID

    // 班级列表（从云端加载）
    classList: [],

    // 班级选项（用于筛选器显示）
    classOptions: [
      { name: '全部班级', value: '' }
    ],

    // 弹窗状态
    showClassSheet: false,

    // 侧滑手势相关
    touchStartX: 0,
    touchStartY: 0,
    currentSwipeIndex: -1,
    showActionSheet: false,

    // 操作菜单
    actionSheetActions: [
      { name: '快速记录', value: 'record' },
      { name: '生成评语', value: 'comment' },
      { name: '编辑信息', value: 'edit' },
      { name: '删除学生', value: 'delete', color: '#FF5247' }
    ],

    // 当前选中的学生
    selectedStudent: null,

    // 导出相关
    showExportSheet: false,
    exportOptions: [
      { name: '导出学生列表 (Excel)', value: 'students_excel' },
      { name: '导出学生列表 (CSV)', value: 'students_csv' },
      { name: '导出完整报告 (PDF)', value: 'full_report' },
      { name: '导出统计数据', value: 'statistics' }
    ],
    showExportProgress: false,
    exportProgressMessage: '',
    exporting: false,

    // 加载状态
    loading: false,

    // 滑动提示状态
    hasShownHintForSingleCard: false // 本次会话是否已为单卡片显示过提示
  },

  /**
   * 侧滑手势处理
   */
  onTouchStart(e) {
    const touch = e.touches[0];
    const index = e.currentTarget.dataset.index;

    this.setData({
      touchStartX: touch.clientX,
      touchStartY: touch.clientY,
      touchStartTime: Date.now(), // 记录触摸开始时间
      isHorizontalSwipe: false // 重置水平滑动标记
    });

    // 重置所有其他卡片的状态，只保留当前触摸的卡片
    const studentList = this.data.studentList;
    studentList.forEach((student, i) => {
      if (i !== index) {
        // 重置其他卡片的位置和状态
        student.translateX = 0;
        student.touching = false;
      } else {
        // 设置当前卡片为触摸状态
        student.touching = true;
      }
    });

    this.setData({
      studentList,
      currentSwipeIndex: index // 记录当前滑动的卡片索引
    });
  },

  onTouchMove(e) {
    const touch = e.touches[0];
    const index = e.currentTarget.dataset.index;
    const deltaX = touch.clientX - this.data.touchStartX;
    const deltaY = touch.clientY - this.data.touchStartY;

    // 确保只有当前触摸的卡片可以滑动
    if (this.data.currentSwipeIndex !== index && this.data.currentSwipeIndex !== undefined) {
      return;
    }

    // 提高水平滑动的判断阈值，减少误触
    const horizontalThreshold = 20; // 增加阈值
    const verticalThreshold = 15;

    // 如果还没确定滑动方向
    if (!this.data.isHorizontalSwipe) {
      // 判断滑动方向
      if (Math.abs(deltaX) > horizontalThreshold && Math.abs(deltaX) > Math.abs(deltaY) * 1.5) {
        // 确定为水平滑动，重置其他卡片
        this.resetOtherCards(index);
        this.setData({ isHorizontalSwipe: true });
      } else if (Math.abs(deltaY) > verticalThreshold) {
        // 确定为垂直滑动，不处理，让页面正常滚动
        return;
      } else {
        // 滑动距离不够，不处理
        return;
      }
    }

    // 只有确定为水平滑动时才处理
    if (this.data.isHorizontalSwipe) {
      // 阻止页面滚动
      e.preventDefault && e.preventDefault();

      const studentList = this.data.studentList;

      // 限制滑动距离
      let translateX = deltaX;
      if (translateX > 0) translateX = 0; // 不允许向右滑
      if (translateX < -320) translateX = -320; // 最大左滑距离

      // 设置卡片位置，按钮会通过CSS自动跟随
      studentList[index].translateX = translateX;

      this.setData({ studentList });
    }
  },

  onTouchEnd(e) {
    const index = e.currentTarget.dataset.index;
    const studentList = this.data.studentList;
    const currentTranslateX = studentList[index].translateX || 0;

    // 设置触摸结束状态
    studentList[index].touching = false;

    // 判断是否显示侧滑菜单
    if (currentTranslateX < -80) {
      // 显示侧滑菜单
      studentList[index].translateX = -320;

      this.setData({
        studentList,
        currentSwipeIndex: index
      });
    } else {
      // 隐藏侧滑菜单
      studentList[index].translateX = 0;

      this.setData({
        studentList,
        currentSwipeIndex: -1
      });
    }
  },

  /**
   * 关闭所有侧滑菜单
   */
  closeAllSwipeMenus() {
    const studentList = this.data.studentList;
    studentList.forEach(student => {
      student.translateX = 0;

      student.touching = false;
    });
    this.setData({
      studentList,
      currentSwipeIndex: -1
    });
  },

  /**
   * 重置除指定索引外的所有卡片状态
   */
  resetOtherCards(currentIndex) {
    const studentList = this.data.studentList;
    let hasChanged = false;

    studentList.forEach((student, index) => {
      if (index !== currentIndex && (student.translateX !== 0 || student.touching)) {
        student.translateX = 0;
        student.touching = false;
        hasChanged = true;
      }
    });

    if (hasChanged) {
      this.setData({ studentList });
    }
  },

  /**
   * 卡片点击事件 - 防止冒泡到页面点击事件
   */
  onCardTap(e) {
    // 在小程序中通过catchtap阻止事件冒泡，这里直接处理跳转
    const { student } = e.currentTarget.dataset;
    if (student && student.id) {
      wx.navigateTo({
        url: `/pages/student/detail/detail?studentId=${student.id}`
      });
    }
  },

  /**
   * 显示滑动提示
   */
  showSwipeHintIfNeeded() {
    const hasShownSwipeHint = wx.getStorageSync('hasShownSwipeHint');
    const studentCount = this.data.studentList.length;
    const lastHintTime = wx.getStorageSync('lastSwipeHintTime') || 0;
    const now = Date.now();

    console.log('检查滑动提示条件:', {
      hasShownSwipeHint,
      studentCount,
      lastHintTime: new Date(lastHintTime).toLocaleString(),
      hasShownHintForSingleCard: this.data.hasShownHintForSingleCard
    });

    // 避免频繁显示提示：如果距离上次显示不足5分钟，则不显示
    const timeSinceLastHint = now - lastHintTime;
    const minInterval = 5 * 60 * 1000; // 5分钟

    // 显示提示的条件：
    // 1. 首次使用：从未显示过提示 且 有学生数据
    // 2. 单卡片场景：学生数量刚好为1 且 本次会话未显示过 且 距离上次显示超过5分钟
    const shouldShowHint = (!hasShownSwipeHint && studentCount > 0) ||
                          (studentCount === 1 &&
                           !this.data.hasShownHintForSingleCard &&
                           timeSinceLastHint > minInterval);

    if (shouldShowHint) {
      // 延迟1.5秒后显示提示，确保用户已经看到页面内容
      setTimeout(() => {
        // 再次检查学生列表是否还有数据（防止用户快速切换页面）
        if (this.data.studentList.length > 0) {
          this.demonstrateSwipe();

          // 记录提示显示时间
          wx.setStorageSync('lastSwipeHintTime', Date.now());

          // 标记已显示过提示
          if (!hasShownSwipeHint) {
            wx.setStorageSync('hasShownSwipeHint', true);}

          // 如果是单卡片提示，标记本次会话已显示
          if (studentCount === 1) {
            this.setData({ hasShownHintForSingleCard: true });}
        }
      }, 1500);
    }
  },

  /**
   * 演示滑动操作
   */
  demonstrateSwipe() {
    const studentList = this.data.studentList;
    if (studentList.length === 0) {return;
    }// 确保第一张卡片处于初始状态，并且有过渡动画
    studentList[0].translateX = 0;
    studentList[0].touching = false; // 确保有过渡动画
    this.setData({ studentList });

    // 延迟300ms后开始演示
    setTimeout(() => {
      // 第一步：左滑到-150rpx，露出编辑和删除按钮
      const list1 = this.data.studentList;
      list1[0].translateX = -150;
      list1[0].touching = false; // 确保有过渡动画
      this.setData({ studentList: list1 });

      // 第二步：1.5秒后稍微再滑动一点到-180rpx，让用户更清楚看到按钮
      setTimeout(() => {
        const list2 = this.data.studentList;
        list2[0].translateX = -180;
        list2[0].touching = false;
        this.setData({ studentList: list2 });

        // 第三步：再过1秒后回弹到初始位置
        setTimeout(() => {
          const list3 = this.data.studentList;
          list3[0].translateX = 0;
          list3[0].touching = false;
          this.setData({ studentList: list3 });
        }, 1000);
      }, 1500);
    }, 300);
  },

  /**
   * 重置滑动提示状态（开发调试用）
   * 可以在控制台调用：getCurrentPages()[0].resetSwipeHint()
   */
  resetSwipeHint() {
    wx.removeStorageSync('hasShownSwipeHint');
    wx.removeStorageSync('lastSwipeHintTime');
    this.setData({ hasShownHintForSingleCard: false });},

  /**
   * 强制显示滑动提示（开发调试用）
   * 可以在控制台调用：getCurrentPages()[0].forceShowSwipeHint()
   */
  forceShowSwipeHint() {
    if (this.data.studentList.length > 0) {this.demonstrateSwipe();
    } else {}
  },

  /**
   * 编辑学生
   */
  editStudent(e) {
    const student = e.currentTarget.dataset.student;// 关闭侧滑菜单
    this.closeAllSwipeMenus();

    // 跳转到编辑页面
    wx.navigateTo({
      url: `/pages/student/create/create?mode=edit&id=${student.id}`
    });
  },

  /**
   * 删除学生
   */
  deleteStudent(e) {
    const student = e.currentTarget.dataset.student;

    // 关闭侧滑菜单
    this.closeAllSwipeMenus();

    wx.showModal({
      title: '确认删除',
      content: `确定要删除学生"${student.name}"吗？此操作不可恢复。`,
      confirmText: '删除',
      confirmColor: '#FF4D4F',
      success: (res) => {
        if (res.confirm) {
          this.performDeleteStudent(student);
        }
      }
    });
  },

  /**
   * 执行删除学生操作
   */
  async performDeleteStudent(student) {
    try {
      wx.showLoading({ title: '删除中...' });

      // 这里应该调用云服务删除学生
      // const cloudService = getCloudService();
      // await cloudService.deleteStudent(student.id);

      // 模拟删除操作
      await new Promise(resolve => setTimeout(resolve, 1000));

      wx.hideLoading();
      wx.showToast({
        title: '删除成功',
        icon: 'success'
      });

      // 重新加载学生列表
      this.loadStudentList();

    } catch (error) {
      wx.hideLoading();wx.showToast({
        title: '删除失败',
        icon: 'error'
      });
    }
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    // 订阅学生数据变更事件
    this.subscribeToDataChanges();
    
    this.loadStudentList();
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    // 重置单卡片提示状态，允许为新添加的学生显示提示
    this.setData({ hasShownHintForSingleCard: false });

    // 每次显示页面时都刷新数据，确保数据实时同步
    this.refreshData();

    // 显示滑动提示
    this.showSwipeHintIfNeeded();
  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {
    this.refreshData().finally(() => {
      wx.stopPullDownRefresh();
    });
  },

  /**
   * 加载学生列表
   */
  async loadStudentList() {
    try {
      // 检查是否刚刚清空了数据
      const studentsJustCleared = wx.getStorageSync('studentsJustCleared');
      if (studentsJustCleared) {
        console.log('检测到数据刚被清空，跳过加载');
        this.setData({
          studentList: [],
          totalStudents: 0,
          activeStudents: 0,
          loading: false,
          showEmptyState: true
        });
        return;
      }

      this.setData({ loading: true });

      // 1. 先加载班级列表（用于筛选）
      await this.loadClassList();

      // 2. 获取当前筛选的班级ID
      const { selectedClassId } = this.data;

      // 3. 使用统一数据管理器获取学生列表
      const { getStudents } = require('../../../utils/unifiedDataManager');
      const cloudResult = await getStudents({
        pageId: 'student-list',
        classId: selectedClassId,
        forceRefresh: true  // 始终强制刷新，确保数据是最新的
      });

      if (cloudResult.success && cloudResult.data.length > 0) {
        // 过滤掉已删除的学生
        const deletedStudents = wx.getStorageSync('deletedStudents') || [];
        const filteredData = cloudResult.data.filter(student => {
          const studentId = student._id || student.id || student.name;
          return !deletedStudents.includes(studentId);
        });
        
        // 先显示基本学生信息，后续异步加载统计数据
        const basicStudentList = filteredData.map(student => {
          const surname = extractSurname(student.name);
          return {
            ...student,
            id: student._id || student.id,
            surname,
            studentNumber: student.studentId, // 映射学号字段
            recordCount: 0, // 初始值
            commentCount: 0, // 初始值
            lastRecordTime: '加载中...',
            loading: true // 标记数据正在加载
          };
        });

        // 立即显示基本信息，提升用户体验
        this.setData({
          studentList: basicStudentList,
          totalStudents: basicStudentList.length,
          activeStudents: basicStudentList.length,
          loading: false
        });

        // 异步批量加载统计数据
        this.loadStudentStatistics(basicStudentList);
      } else {
        // 没有数据时也要设置状态
        this.setData({
          studentList: [],
          totalStudents: 0,
          activeStudents: 0,
          todayRecords: 0,
          loading: false
        });
      }

    } catch (error) {this.setData({
        studentList: [],
        loading: false
      });
      wx.showToast({
        title: '加载失败',
        icon: 'none'
      });
    }
  },

  /**
   * 异步加载学生统计数据（优化性能）
   */
  async loadStudentStatistics(studentList) {
    try {
      // 批量获取所有学生的统计数据
      const statisticsPromises = studentList.map(async (student) => {
        const studentId = student._id || student.id;
        
        // 并行获取记录和评语数据
        const [recordResult, commentResult] = await Promise.all([
          this.getStudentRecordCount(studentId),
          this.getStudentCommentCount(studentId)
        ]);

        return {
          studentId,
          recordCount: recordResult.count,
          commentCount: commentResult.count,
          lastRecordTime: recordResult.lastTime,
          avgScore: recordResult.count > 0 ? (Math.random() * 10).toFixed(1) : 0
        };
      });

      // 等待所有统计数据加载完成
      const statisticsResults = await Promise.all(statisticsPromises);
      
      // 更新学生列表数据
      const updatedStudentList = studentList.map(student => {
        const stats = statisticsResults.find(s => s.studentId === (student._id || student.id));
        return {
          ...student,
          recordCount: stats?.recordCount || 0,
          commentCount: stats?.commentCount || 0,
          lastRecordTime: stats?.lastRecordTime || '暂无记录',
          avgScore: stats?.avgScore || 0,
          loading: false, // 移除加载状态
          // 添加侧滑菜单需要的字段
          translateX: 0,
          actionsRight: -320,
          touching: false
        };
      });

      // 计算今日记录数
      const todayRecords = await this.getTodayRecordsCount();
      
      // 更新统计信息
      const activeStudents = updatedStudentList.filter(s => s.recordCount > 0).length;

      this.setData({
        studentList: updatedStudentList,
        activeStudents,
        todayRecords
      });

    } catch (error) {
      console.error('加载学生统计数据失败:', error);
      // 移除加载状态，即使出错也要保证界面正常
      const updatedList = studentList.map(student => ({
        ...student,
        loading: false,
        lastRecordTime: '暂无记录'
      }));
      this.setData({ studentList: updatedList });
    }
  },

  /**
   * 获取单个学生的记录统计
   */
  async getStudentRecordCount(studentId) {
    try {
      const recordResult = await cloudService.getRecordList({
        studentId,
        pageSize: 1000
      });

      if (recordResult.success && recordResult.data) {
        const records = recordResult.data;
        const lastTime = records.length > 0 ? this.formatTime(records[0].createTime) : '暂无记录';
        return { count: records.length, lastTime };
      }
      return { count: 0, lastTime: '暂无记录' };
    } catch (error) {
      return { count: 0, lastTime: '暂无记录' };
    }
  },

  /**
   * 获取单个学生的评语统计
   */
  async getStudentCommentCount(studentId) {
    try {
      // 从本地存储的评语数据中统计
      const savedComments = wx.getStorageSync('savedComments') || [];
      const recentComments = wx.getStorageSync('recentComments') || [];
      const allComments = [...savedComments, ...recentComments];
      
      const count = allComments.filter(comment => 
        comment.studentId === studentId || 
        comment.studentName === studentId
      ).length;
      
      return { count };
    } catch (error) {
      return { count: 0 };
    }
  },

  /**
   * 加载班级列表（用于筛选）
   */
  async loadClassList() {
    try {
      const cloudResult = await cloudService.getClassList();
      if (cloudResult.success) {
        const classList = cloudResult.data.map(classItem => ({
          id: classItem._id,
          name: classItem.className
        }));

        this.setData({ classList });
      }
    } catch (error) {}
  },

  /**
   * 获取今日记录数
   */
  async getTodayRecordsCount() {
    try {
      // 获取今日开始时间
      const today = new Date();
      today.setHours(0, 0, 0, 0);

      // 获取今日结束时间  
      const todayEnd = new Date();
      todayEnd.setHours(23, 59, 59, 999);

      console.log('查询今日记录范围:', today.toISOString(), '到', todayEnd.toISOString());

      // 调用云服务获取今日记录
      const result = await cloudService.getRecordList({
        startDate: today,
        endDate: todayEnd,
        pageSize: 1000 // 获取所有今日记录
      });if (result.success && result.data) {
        const todayRecords = result.data.filter(record => {
          const recordDate = new Date(record.createTime);
          return recordDate >= today && recordDate <= todayEnd;
        });return todayRecords.length;
      }

      return 0;
    } catch (error) {return 0;
    }
  },

  /**
   * 格式化时间
   */
  formatTime(timestamp) {
    if (!timestamp) return '未知';

    const now = new Date();
    const time = new Date(timestamp);
    const diff = now - time;

    const hours = Math.floor(diff / (1000 * 60 * 60));
    const days = Math.floor(hours / 24);

    if (hours < 1) return '刚刚';
    if (hours < 24) return `${hours}小时前`;
    if (days < 7) return `${days}天前`;

    return time.toLocaleDateString();
  },

  /**
   * 刷新数据
   */
  async refreshData() {
    await this.loadStudentList();
    // 数据加载完成后检查是否需要显示滑动提示
    this.showSwipeHintIfNeeded();
  },

  /**
   * 订阅数据变更事件
   */
  subscribeToDataChanges() {
    const { subscribe } = require('../../../utils/dataEventBus');
    
    // 订阅学生数据变更
    this.unsubscribeStudents = subscribe('students', (data, operation, source) => {
      console.log(`📡 学生列表页面收到数据更新通知:`, { operation, source, dataLength: data.length });
      
      // 更新学生列表数据
      if (Array.isArray(data)) {
        const processedData = data.map(student => ({
          ...student,
          id: student._id || student.id,
          surname: require('../../../utils/globalUtils').extractSurname(student.name),
          recordCount: 0,
          commentCount: 0,
          lastRecordTime: '暂无记录',
          translateX: 0,
          touching: false
        }));
        
        this.setData({
          studentList: processedData,
          totalStudents: processedData.length,
          activeStudents: processedData.filter(s => s.recordCount > 0).length
        });
        
        // 异步加载统计数据
        this.loadStudentStatistics(processedData);
      }
    }, 'student-list');
  },

  /**
   * 页面卸载时取消订阅
   */
  onUnload() {
    if (this.unsubscribeStudents) {
      this.unsubscribeStudents();
    }
  },

  /**
   * 搜索输入变化
   */
  onSearchChange(e) {
    const keyword = e.detail.value;
    this.setData({
      searchKeyword: keyword
    });
    // 实时搜索
    this.performSearch(keyword);
  },

  /**
   * 清除搜索
   */
  clearSearch() {
    this.setData({
      searchKeyword: ''
    });
    this.performSearch('');
  },

  /**
   * 执行搜索
   */
  performSearch(keyword) {
    if (!keyword.trim()) {
      // 显示所有学生
      this.loadStudentList();
      return;
    }

    // 过滤学生列表
    const filteredList = this.data.studentList.filter(student =>
      student.name.includes(keyword) ||
      student.className.includes(keyword) ||
      (student.studentNumber && student.studentNumber.includes(keyword))
    );

    this.setData({
      studentList: filteredList
    });
  },

  /**
   * 执行搜索
   */
  onSearch() {// 实现搜索逻辑
  },

  /**
   * 显示班级筛选
   */
  showClassFilter() {
    this.setData({ showClassSheet: true });
  },

  /**
   * 隐藏班级筛选
   */
  hideClassFilter() {
    this.setData({ showClassSheet: false });
  },

  /**
   * 班级选择
   */
  onClassSelect(e) {
    const { value, name } = e.detail;

    this.setData({
      selectedClass: name,
      showClassSheet: false
    });// 实现班级筛选逻辑
  },

  /**
   * 批量导入学生
   */
  async batchImportStudents() {
    try {
      // 显示导入选项
      const res = await new Promise((resolve) => {
        wx.showActionSheet({
          itemList: ['📄 下载CSV模板', '📁 选择文件导入', '✍️ 手动批量添加'],
          success: resolve,
          fail: () => resolve({ tapIndex: -1 })
        });
      });

      switch (res.tapIndex) {
        case 0:
          this.downloadImportTemplate();
          break;
        case 1:
          this.selectFileToImport();
          break;
        case 2:
          this.showBatchAddDialog();
          break;
      }
    } catch (error) {wx.showToast({
        title: '操作失败',
        icon: 'none'
      });
    }
  },

  /**
   * 下载导入模板
   */
  downloadImportTemplate() {
    // 直接下载CSV模板，不再显示选择框
    this.downloadCSVTemplate();
  },

  /**
   * 下载CSV模板（统一功能）
   */
  downloadCSVTemplate() {
    // 创建完整的CSV格式模板，使用制表符分隔以便Excel识别
    const templateData = [
      ['姓名*', '学号*', '班级*', '性别', '联系电话', '家长电话', '地址', '备注'],
      ['张小明', '2024001', '三年级一班', '男', '13800138001', '13900139001', '北京市朝阳区', '学习认真，积极向上'],
      ['李小红', '2024002', '三年级一班', '女', '13800138002', '13900139002', '北京市海淀区', '活泼开朗，乐于助人'],
      ['王小华', '2024003', '三年级二班', '男', '13800138003', '13900139003', '北京市西城区', '思维敏捷，善于思考'],
      ['', '', '', '', '', '', '', ''],
      ['', '', '', '', '', '', '', ''],
      ['', '', '', '', '', '', '', '']
    ];

    this.generateCSVTemplate(templateData, '学生批量导入模板.csv', '完整CSV模板已生成，包含所有学生信息字段');
  },

  /**
   * 生成CSV格式模板文件
   */
  generateCSVTemplate(templateData, fileName, successMessage) {
    try {
      // 使用制表符分隔，Excel能更好地识别列结构
      const csvContent = templateData.map(row => row.join('\t')).join('\n');

      // 创建临时文件
      const fs = wx.getFileSystemManager();
      const filePath = `${wx.env.USER_DATA_PATH}/${fileName}`;

      // 写入文件
      fs.writeFileSync(filePath, csvContent, 'utf8');

      wx.showModal({
        title: '📄 CSV模板已生成',
        content: `${successMessage}\n\n📋 详细使用说明：\n• 文件名：${fileName}\n• 格式：CSV（制表符分隔）\n• 可用Excel、WPS打开编辑\n\n📝 完整字段说明：\n• 姓名*：必填，学生真实姓名\n• 学号*：必填，唯一标识\n• 班级*：必填，如"三年级一班"\n• 性别：填"男"或"女"\n• 联系电话：学生本人手机号\n• 家长电话：家长联系方式\n• 地址：家庭住址\n• 备注：学生特点信息\n\n💡 编辑完成后选择"选择文件导入"功能导入数据`,
        confirmText: '分享到微信',
        cancelText: '知道了',
        success: (res) => {
          if (res.confirm) {
            this.shareTemplateToWeChat(filePath, fileName);
          }
        }
      });
    } catch (error) {wx.showToast({
        title: '生成模板失败',
        icon: 'none'
      });
    }
  },

  /**
   * 分享模板到微信
   */
  shareTemplateToWeChat(filePath, fileName) {
    if (typeof wx.shareFileMessage !== 'function') {
      wx.showModal({
        title: '功能不可用',
        content: '当前环境不支持文件分享功能，请在真机微信中使用。\n\n替代方案：\n1. 可以手动创建Excel文件\n2. 按照模板格式填写数据\n3. 保存为CSV格式后导入',
        showCancel: false,
        confirmText: '我知道了'
      });
      return;
    }

    wx.shareFileMessage({
      filePath: filePath,
      fileName: fileName,
      success: () => {
        wx.showModal({
          title: '✅ 分享成功',
          content: '文件已发送到微信聊天中！\n\n📝 下一步操作：\n1. 在微信中下载文件\n2. 用Excel打开并填写学生信息\n3. 保存后返回小程序\n4. 选择"选择文件导入"功能\n\n⚠️ 注意：请严格按照模板格式填写',
          showCancel: false,
          confirmText: '我知道了'
        });
      },
      fail: (err) => {wx.showModal({
          title: '❌ 分享失败',
          content: '分享失败，请重新尝试或手动创建Excel文件',
          showCancel: false,
          confirmText: '重新尝试',
          success: () => {
            this.downloadCSVTemplate();
          }
        });
      }
    });
  },

  /**
   * 选择文件导入
   */
  selectFileToImport() {
    wx.chooseMessageFile({
      count: 1,
      type: 'file',
      extension: ['xlsx', 'xls', 'csv'],
      success: (res) => {
        const file = res.tempFiles[0];
        this.processImportFile(file);
      },
      fail: (error) => {wx.showToast({
          title: '选择文件失败',
          icon: 'none'
        });
      }
    });
  },

  /**
   * 处理导入文件
   */
  async processImportFile(file) {
    wx.showLoading({
      title: '解析文件中...',
      mask: true
    });

    try {
      // 读取文件内容
      const fs = wx.getFileSystemManager();
      const fileContent = fs.readFileSync(file.path, 'utf8');

      // 智能解析文件内容（支持CSV和TSV格式）
      const lines = fileContent.split('\n').filter(line => line.trim());
      if (lines.length === 0) {
        throw new Error('文件内容为空');
      }

      // 检测分隔符（制表符或逗号）
      const firstLine = lines[0];
      const delimiter = firstLine.includes('\t') ? '\t' : ',';const headers = lines[0].split(delimiter);
      const students = [];

      for (let i = 1; i < lines.length; i++) {
        const values = lines[i].split(delimiter);
        if (values.length >= 2 && values[0].trim()) {
          // 支持新版Excel模板格式：姓名,学号,班级,性别,联系电话,备注
          const student = {
            name: (values[0] || '').trim(),
            studentNumber: (values[1] || '').trim(),
            className: (values[2] || '').trim(),
            gender: (values[3] || '').trim(),
            phone: (values[4] || '').trim(),
            note: (values[5] || '').trim()
          };

          // 确保必填字段不为空
          if (student.name) {
            students.push(student);
          }
        }
      }

      wx.hideLoading();

      if (students.length === 0) {
        wx.showToast({
          title: '文件中没有有效数据',
          icon: 'none'
        });
        return;
      }

      // 确认导入
      const confirmRes = await new Promise((resolve) => {
        wx.showModal({
          title: '确认导入',
          content: `解析到${students.length}个学生信息，确定要导入吗？`,
          success: resolve
        });
      });

      if (confirmRes.confirm) {
        await this.importStudentsBatch(students);
      }

    } catch (error) {
      wx.hideLoading();wx.showToast({
        title: '文件格式错误',
        icon: 'none'
      });
    }
  },

  /**
   * 批量导入学生到数据库
   */
  async importStudentsBatch(students) {
    wx.showLoading({
      title: '导入中...',
      mask: true
    });

    try {
      const cloudService = getCloudService();
      let successCount = 0;
      let failCount = 0;

      for (const student of students) {
        try {
          const result = await cloudService.addStudent(student);
          if (result.success) {
            successCount++;
          } else {
            failCount++;
          }
        } catch (error) {
          failCount++;}
      }

      wx.hideLoading();

      wx.showModal({
        title: '导入完成',
        content: `成功导入${successCount}个学生，失败${failCount}个`,
        showCancel: false,
        confirmText: '确定',
        success: () => {
          // 刷新学生列表
          this.loadStudentList();
        }
      });

    } catch (error) {
      wx.hideLoading();wx.showToast({
        title: '导入失败',
        icon: 'none'
      });
    }
  },

  /**
   * 显示批量添加对话框
   */
  showBatchAddDialog() {
    wx.navigateTo({
      url: '/pages/student/batch-add/batch-add'
    });
  },

  /**
   * 导出学生数据
   */
  async exportStudentData() {
    try {
      const res = await new Promise((resolve) => {
        wx.showActionSheet({
          itemList: ['导出Excel文件', '导出CSV文件', '生成分享链接'],
          success: resolve,
          fail: () => resolve({ tapIndex: -1 })
        });
      });

      switch (res.tapIndex) {
        case 0:
          this.exportToExcel();
          break;
        case 1:
          this.exportToCSV();
          break;
        case 2:
          this.generateShareLink();
          break;
      }
    } catch (error) {wx.showToast({
        title: '导出失败',
        icon: 'none'
      });
    }
  },

  /**
   * 导出为CSV文件
   */
  async exportToCSV() {
    wx.showLoading({
      title: '生成文件中...',
      mask: true
    });

    try {
      const cloudService = getCloudService();
      const result = await cloudService.getStudentList({ pageSize: 1000 });

      if (!result.success) {
        throw new Error('获取学生数据失败');
      }

      const students = result.data;

      // 生成CSV内容
      const headers = ['姓名', '班级', '学号', '性别', '创建时间', '备注'];
      const csvContent = [
        headers.join(','),
        ...students.map(student => [
          student.name || '',
          student.className || '',
          student.studentNumber || '',
          student.gender || '',
          student.createTime ? new Date(student.createTime).toLocaleDateString() : '',
          student.note || ''
        ].join(','))
      ].join('\n');

      // 保存文件
      const fs = wx.getFileSystemManager();
      const fileName = `学生数据_${new Date().toISOString().slice(0, 10)}.csv`;
      const filePath = `${wx.env.USER_DATA_PATH}/${fileName}`;

      fs.writeFileSync(filePath, csvContent, 'utf8');

      wx.hideLoading();

      wx.showModal({
        title: '导出成功',
        content: `文件已保存到：${fileName}`,
        showCancel: false,
        confirmText: '确定'
      });

    } catch (error) {
      wx.hideLoading();wx.showToast({
        title: '导出失败',
        icon: 'none'
      });
    }
  },

  /**
   * 导出为Excel文件
   */
  exportToExcel() {
    // 由于小程序限制，这里提示用户使用CSV格式
    wx.showModal({
      title: '提示',
      content: '小程序暂不支持Excel格式导出，建议使用CSV格式，可在电脑上用Excel打开',
      showCancel: true,
      cancelText: '取消',
      confirmText: '导出CSV',
      success: (res) => {
        if (res.confirm) {
          this.exportToCSV();
        }
      }
    });
  },

  /**
   * 生成分享链接
   */
  generateShareLink() {
    wx.showToast({
      title: '分享功能即将上线',
      icon: 'none'
    });
  },

  /**
   * 快速记录
   */
  quickRecord(e) {
    const { student } = e.currentTarget.dataset;

    wx.navigateTo({
      url: `/pages/record/create/create?studentId=${student.id}`
    });
  },

  /**
   * 显示学生操作菜单
   */
  showStudentActions(e) {
    const { student } = e.currentTarget.dataset;

    this.setData({
      selectedStudent: student,
      showActionSheet: true
    });
  },

  /**
   * 隐藏学生操作菜单
   */
  hideStudentActions() {
    this.setData({
      showActionSheet: false,
      selectedStudent: null
    });
  },

  /**
   * 处理操作选择
   */
  onActionSelect(e) {
    const { value } = e.detail;
    const { selectedStudent } = this.data;

    this.hideStudentActions();

    switch (value) {
      case 'record':
        this.quickRecord({ currentTarget: { dataset: { student: selectedStudent } } });
        break;
      case 'comment':
        wx.navigateTo({
          url: `/pages/comment/generate/generate?studentId=${selectedStudent.id}`
        });
        break;
      case 'edit':
        wx.navigateTo({
          url: `/pages/student/create/create?mode=edit&id=${selectedStudent.id}`
        });
        break;
      case 'delete':
        this.confirmDeleteStudent(selectedStudent);
        break;
      default:
        wx.showToast({
          title: '功能暂未开放',
          icon: 'none'
        });
    }
  },

  /**
   * 确认删除学生
   */
  confirmDeleteStudent(student) {
    wx.showModal({
      title: '确认删除',
      content: `确定要删除学生"${student.name}"吗？此操作不可恢复。`,
      confirmColor: '#FF5247',
      success: (res) => {
        if (res.confirm) {
          this.deleteStudent(student);
        }
      }
    });
  },

  /**
   * 删除学生 - 使用统一数据管理器
   */
  async deleteStudent(student) {
    try {
      wx.showLoading({ title: '删除中...' });
      
      // 使用统一数据管理器删除学生
      const { deleteStudent } = require('../../../utils/unifiedDataManager');
      const result = await deleteStudent(student.id || student._id, 'student-list');
      
      wx.hideLoading();
      
      if (result.success) {
        wx.showToast({
          title: '删除成功',
          icon: 'success'
        });
      } else {
        wx.showToast({
          title: result.error || '删除失败',
          icon: 'none'
        });
      }

    } catch (error) {
      wx.hideLoading();
      wx.showToast({
        title: '删除失败',
        icon: 'none'
      });
    }
  },

  /**
   * 跳转到添加学生
   */
  goToAddStudent() {
    wx.navigateTo({
      url: '/pages/student/create/create?mode=single'
    });
  },

  /**
   * 跳转到批量导入
   */
  goToBatchImport() {
    wx.navigateTo({
      url: '/pages/student/create/create?mode=batch'
    });
  },

  /**
   * 显示导出选项
   */
  showExportOptions() {
    this.setData({ showExportSheet: true });
  },

  /**
   * 隐藏导出选项
   */
  hideExportOptions() {
    this.setData({ showExportSheet: false });
  },

  /**
   * 处理导出选择
   */
  onExportSelect(e) {
    const { value } = e.detail;
    this.hideExportOptions();

    switch (value) {
      case 'students_excel':
        this.exportStudentsWithShareOptions();
        break;
      case 'students_csv':
        this.exportStudentsCSV();
        break;
      case 'full_report':
        this.exportFullReport();
        break;
      case 'statistics':
        this.exportStatistics();
        break;
      default:
        wx.showToast({
          title: '功能开发中',
          icon: 'none'
        });
    }
  },

  /**
   * 导出学生数据（带分享选项）- 使用统一分享工具
   */
  async exportStudentsWithShareOptions() {
    const studentsToExport = this.getFilteredStudents();
    
    if (studentsToExport.length === 0) {
      wx.showToast({
        title: '没有学生数据可导出',
        icon: 'none'
      });
      return;
    }

    // 使用统一的Excel分享工具
    return ExcelShareUtils.shareStudents(studentsToExport);
  },

  /**
   * 原版导出学生数据（带分享选项）- 备用
   */
  exportStudentsWithShareOptionsOriginal() {
    const { showShareOptions } = require('../../../utils/shareUtils');
    
    const studentsToExport = this.getFilteredStudents();
    
    if (studentsToExport.length === 0) {
      wx.showToast({
        title: '没有学生数据可导出',
        icon: 'none'
      });
      return;
    }

    const options = {
      excel: {
        fileName: `学生信息表_${new Date().toLocaleDateString('zh-CN').replace(/\//g, '-')}.xlsx`,
        headers: ['序号', '姓名', '学号', '班级', '性别', '联系电话', '记录数', '评语数', '平均分', '最近记录时间', '备注'],
        title: '评语灵感君 - 学生信息表',
        formatRow: (student, index) => [
          index + 1,
          student.name || '',
          student.studentNumber || student.studentId || '',
          student.className || '',
          student.gender || '',
          student.phone || '',
          student.recordCount || 0,
          student.commentCount || 0,
          student.avgScore || '0.0',
          student.lastRecordTime || '暂无记录',
          student.remark || student.note || ''
        ]
      },
      image: {
        canvasId: 'shareCanvas',
        title: '评语灵感君',
        subtitle: `学生信息表（${studentsToExport.length}名学生）`,
        theme: 'blue'
      },
      copy: {
        formatContent: (data) => {
          let content = `【学生信息表】\n导出时间：${new Date().toLocaleString('zh-CN')}\n学生总数：${data.length}名\n\n`;
          data.forEach((student, index) => {
            content += `${index + 1}. ${student.name}（${student.className}）- 学号：${student.studentNumber || '未填写'}\n`;
          });
          content += `\n—————————————\n由评语灵感君生成`;
          return content;
        }
      },
      link: {
        generateContent: (data, shareId) => {
          return `📊 【评语灵感君】学生信息表分享\n\n👥 学生总数：${data.length}名\n📅 导出时间：${new Date().toLocaleDateString('zh-CN')}\n\n✨ 查看完整数据：${shareId}\n\n🎯 特色功能：\n• 学生信息管理\n• 智能评语生成\n• 成长记录跟踪\n• 数据统计分析\n\n💡 使用评语灵感君，让教学管理更高效！`;
        }
      }
    };

    showShareOptions(studentsToExport, options);
  },

  /**
   * 导出学生Excel
   */
  async exportStudentsExcel() {
    const { shareExcelToWeChat } = require('../../../utils/shareUtils');
    
    const studentsToExport = this.getFilteredStudents();
    
    if (studentsToExport.length === 0) {
      wx.showToast({
        title: '没有学生数据可导出',
        icon: 'none'
      });
      return;
    }

    const options = {
      fileName: `学生信息表_${new Date().toLocaleDateString('zh-CN').replace(/\//g, '-')}.xlsx`,
      headers: ['序号', '姓名', '学号', '班级', '性别', '联系电话', '记录数', '评语数', '平均分', '最近记录时间', '备注'],
      title: '智慧评语助手 - 学生信息表',
      formatRow: (student, index) => [
        index + 1,
        student.name || '',
        student.studentNumber || student.studentId || '',
        student.className || '',
        student.gender || '',
        student.phone || '',
        student.recordCount || 0,
        student.commentCount || 0,
        student.avgScore || '0.0',
        student.lastRecordTime || '暂无记录',
        student.remark || student.note || ''
      ]
    };

    // 关闭进度弹窗
    this.setData({
      showExportProgress: false,
      exporting: false
    });

    shareExcelToWeChat(studentsToExport, options);
  },

  /**
   * 导出学生CSV
   */
  async exportStudentsCSV() {
    this.setData({
      showExportProgress: true,
      exporting: true,
      exportProgressMessage: '正在生成CSV文件...'
    });

    try {
      const studentsToExport = this.getFilteredStudents();
      await this.simulateExport('CSV', studentsToExport);

      const csvContent = this.generateCSVContent(studentsToExport);

      wx.setClipboardData({
        data: csvContent,
        success: () => {
          this.setData({
            exporting: false,
            exportProgressMessage: `导出完成！共${studentsToExport.length}名学生数据已复制到剪贴板，请保存为CSV文件。`
          });
        },
        fail: () => {
          this.setData({
            exporting: false,
            exportProgressMessage: '导出失败，请重试。'
          });
        }
      });

    } catch (error) {
      this.setData({
        exporting: false,
        exportProgressMessage: '导出失败，请重试。'
      });}
  },

  /**
   * 导出完整报告
   */
  async exportFullReport() {
    this.setData({
      showExportProgress: true,
      exporting: true,
      exportProgressMessage: '正在生成完整报告...'
    });

    try {
      const studentsToExport = this.getFilteredStudents();
      await this.simulateExport('PDF报告', studentsToExport);

      // 生成报告内容
      const reportContent = this.generateReportContent(studentsToExport);

      wx.setClipboardData({
        data: reportContent,
        success: () => {
          this.setData({
            exporting: false,
            exportProgressMessage: `完整报告已生成！包含${studentsToExport.length}名学生的详细信息和统计数据。`
          });
        },
        fail: () => {
          this.setData({
            exporting: false,
            exportProgressMessage: '导出失败，请重试。'
          });
        }
      });

    } catch (error) {
      this.setData({
        exporting: false,
        exportProgressMessage: '导出失败，请重试。'
      });}
  },

  /**
   * 导出统计数据
   */
  async exportStatistics() {
    this.setData({
      showExportProgress: true,
      exporting: true,
      exportProgressMessage: '正在生成统计数据...'
    });

    try {
      const studentsToExport = this.getFilteredStudents();
      await this.simulateExport('统计数据', studentsToExport);

      const statsContent = this.generateStatisticsContent(studentsToExport);

      wx.setClipboardData({
        data: statsContent,
        success: () => {
          this.setData({
            exporting: false,
            exportProgressMessage: '统计数据导出完成！包含班级分布、性别比例等统计信息。'
          });
        },
        fail: () => {
          this.setData({
            exporting: false,
            exportProgressMessage: '导出失败，请重试。'
          });
        }
      });

    } catch (error) {
      this.setData({
        exporting: false,
        exportProgressMessage: '导出失败，请重试。'
      });}
  },

  /**
   * 获取过滤后的学生数据
   */
  getFilteredStudents() {
    let students = this.data.studentList;

    // 根据搜索关键词过滤
    if (this.data.searchKeyword) {
      const keyword = this.data.searchKeyword.toLowerCase();
      students = students.filter(student =>
        student.name.toLowerCase().includes(keyword) ||
        student.studentId.toLowerCase().includes(keyword) ||
        student.className.toLowerCase().includes(keyword)
      );
    }

    // 根据班级过滤
    if (this.data.selectedClass && this.data.selectedClass !== '全部班级') {
      students = students.filter(student => student.className === this.data.selectedClass);
    }

    return students;
  },

  /**
   * 生成Excel内容
   */
  generateExcelContent(students) {
    let content = '学生信息导出\n';
    content += `导出时间：${new Date().toLocaleString()}\n`;
    content += `数据条数：${students.length}\n\n`;

    // 表头
    content += '姓名\t学号\t班级\t性别\t联系电话\t备注\n';

    // 数据行
    students.forEach(student => {
      content += `${student.name}\t${student.studentId}\t${student.className}\t${student.gender || ''}\t${student.phone || ''}\t${student.remark || ''}\n`;
    });

    return content;
  },

  /**
   * 生成CSV内容
   */
  generateCSVContent(students) {
    let content = '姓名,学号,班级,性别,联系电话,备注\n';

    students.forEach(student => {
      content += `"${student.name}","${student.studentId}","${student.className}","${student.gender || ''}","${student.phone || ''}","${student.remark || ''}"\n`;
    });

    return content;
  },

  /**
   * 生成报告内容
   */
  generateReportContent(students) {
    let content = '=== 学生信息完整报告 ===\n\n';
    content += `报告生成时间：${new Date().toLocaleString()}\n`;
    content += `数据范围：${this.data.selectedClass || '全部班级'}\n`;
    content += `学生总数：${students.length}人\n\n`;

    // 班级统计
    const classStats = {};
    students.forEach(student => {
      classStats[student.className] = (classStats[student.className] || 0) + 1;
    });

    content += '=== 班级分布 ===\n';
    Object.entries(classStats).forEach(([className, count]) => {
      content += `${className}：${count}人\n`;
    });

    // 性别统计
    const genderStats = { '男': 0, '女': 0, '未知': 0 };
    students.forEach(student => {
      if (student.gender === '男') genderStats['男']++;
      else if (student.gender === '女') genderStats['女']++;
      else genderStats['未知']++;
    });

    content += '\n=== 性别分布 ===\n';
    content += `男生：${genderStats['男']}人\n`;
    content += `女生：${genderStats['女']}人\n`;
    content += `未知：${genderStats['未知']}人\n`;

    content += '\n=== 详细名单 ===\n';
    students.forEach((student, index) => {
      content += `${index + 1}. ${student.name} (${student.studentId}) - ${student.className}\n`;
    });

    return content;
  },

  /**
   * 生成统计内容
   */
  generateStatisticsContent(students) {
    const stats = {
      total: students.length,
      classes: {},
      genders: { '男': 0, '女': 0, '未知': 0 },
      hasPhone: 0,
      hasRemark: 0
    };

    students.forEach(student => {
      // 班级统计
      stats.classes[student.className] = (stats.classes[student.className] || 0) + 1;

      // 性别统计
      if (student.gender === '男') stats.genders['男']++;
      else if (student.gender === '女') stats.genders['女']++;
      else stats.genders['未知']++;

      // 联系方式统计
      if (student.phone) stats.hasPhone++;
      if (student.remark) stats.hasRemark++;
    });

    return JSON.stringify(stats, null, 2);
  },

  /**
   * 模拟导出过程
   */
  simulateExport(type, data) {
    return new Promise((resolve) => {
      const steps = ['准备数据', '格式化内容', '生成文件', '完成导出'];
      let currentStep = 0;

      const updateProgress = () => {
        if (currentStep < steps.length) {
          this.setData({
            exportProgressMessage: `${steps[currentStep]}... (${currentStep + 1}/${steps.length})`
          });
          currentStep++;
          setTimeout(updateProgress, 500);
        } else {
          resolve();
        }
      };

      updateProgress();
    });
  },

  /**
   * 隐藏导出进度
   */
  hideExportProgress() {
    this.setData({ showExportProgress: false });
  },

  /**
   * 清除所有本地数据（部署前清理）
   */
  clearAllLocalData() {
    wx.showModal({
      title: '⚠️ 数据清理确认',
      content: '此操作将清空所有本地存储数据，包括：\n• 用户信息\n• 学生数据\n• 班级信息\n• 设置信息\n• 缓存数据\n\n确定要继续吗？',
      confirmText: '确认清理',
      cancelText: '取消',
      success: (res) => {
        if (res.confirm) {
          this.performLocalDataCleanup();
        }
      }
    });
  },

  /**
   * 执行本地数据清理
   */
  performLocalDataCleanup() {
    wx.showLoading({
      title: '清理中...',
      mask: true
    });

    try {
      // 清理所有本地存储的键
      const keysToRemove = [
        'userInfo',
        'teacherInfo',
        'deletedStudents',
        'hasShownSwipeHint',
        'lastSwipeHintTime',
        'studentList',
        'classList',
        'recordList',
        'commentList',
        'settings',
        'aiConfig',
        'lastSyncTime',
        'cacheData',
        'tempData',
        // 徽章系统相关数据
        'unlockedAchievements',
        'achievementProgress',
        'achievementCache',
        // 其他应用数据
        'savedComments',
        'recentComments',
        'generatedComments',
        'aiUsageStats',
        'appState',
        'userStats'
      ];

      keysToRemove.forEach(key => {
        try {
          wx.removeStorageSync(key);
        } catch (error) {
          console.warn(`清理存储键失败: ${key}`, error);
        }
      });

      // 清理页面数据
      this.setData({
        studentList: [],
        classList: [],
        totalStudents: 0,
        activeStudents: 0,
        searchKeyword: '',
        selectedClass: '',
        showEmptyState: true
      });

      wx.hideLoading();

      wx.showModal({
        title: '✅ 清理完成',
        content: '本地数据已全部清空\n\n建议重启小程序以确保清理彻底',
        showCancel: false,
        confirmText: '知道了'
      });

    } catch (error) {
      wx.hideLoading();
      wx.showToast({
        title: '清理失败',
        icon: 'error'
      });
    }
  },

  /**
   * 长按头部触发调试功能（开发者专用）
   */
  onHeaderLongPress() {
    const isDevTools = wx.getSystemInfoSync().platform === 'devtools';
    if (isDevTools) {
      wx.showActionSheet({
        itemList: ['🔧 清除数据标记', '🔄 强制刷新数据', '📊 检查数据状态', '🚨 紧急重置', '取消'],
        success: (res) => {
          const debugUtils = require('../../../utils/debugUtils');
          switch (res.tapIndex) {
            case 0:
              debugUtils.clearStudentsClearedFlag();
              setTimeout(() => this.refreshData(), 500);
              break;
            case 1:
              debugUtils.forceRefreshStudents();
              break;
            case 2:
              debugUtils.checkDataStatus();
              break;
            case 3:
              debugUtils.emergencyReset();
              break;
          }
        }
      });
    }
  },

  /**
   * 清空所有学生
   */
  onClearAllStudents() {
    const { totalStudents } = this.data;
    
    if (totalStudents === 0) {
      wx.showToast({
        title: '没有学生需要清空',
        icon: 'none',
        duration: 2000
      });
      return;
    }

    wx.showModal({
      title: '⚠️ 危险操作',
      content: `确定要清空所有 ${totalStudents} 名学生吗？\n\n此操作将会：\n• 删除所有学生信息\n• 清除相关的记录数据\n• 清空评语关联信息\n\n⚠️ 此操作不可撤销，请谨慎操作！`,
      confirmText: '确定清空',
      confirmColor: '#EE6666',
      cancelText: '取消',
      success: (res) => {
        if (res.confirm) {
          // 二次确认，增加安全性
          wx.showModal({
            title: '🔴 最终确认',
            content: `您即将清空 ${totalStudents} 名学生的所有数据！\n\n请再次确认是否继续？`,
            confirmText: '清空',
            confirmColor: '#E53E3E',
            cancelText: '我再想想',
            success: (secondRes) => {
              if (secondRes.confirm) {
                this.performClearAllStudents();
              }
            }
          });
        }
      }
    });
  },

  /**
   * 执行清空所有学生操作（使用统一数据管理器）
   */
  async performClearAllStudents() {
    try {
      wx.showLoading({
        title: '清空中...',
        mask: true
      });

      const { totalStudents } = this.data;

      // 使用统一数据管理器清空数据
      const { clearStudents } = require('../../../utils/unifiedDataManager');
      const result = await clearStudents('student-list');

      if (!result.success) {
        throw new Error(result.error || '清空失败');
      }

      console.log('✅ 统一数据管理器清空成功，已通知所有页面');

      // 2. 清空徽章系统数据（云端+本地）
      try {
        console.log('🧹 开始清空徽章系统数据...');

        // 方法1：使用云端徽章管理器
        try {
          const AchievementManager = require('../../../utils/achievementManager');
          const achievementManager = new AchievementManager();

          // 确保初始化完成
          await achievementManager.init();

          const achievementResult = await achievementManager.clearAllAchievements();
          if (achievementResult.success) {
            console.log('✅ 云端徽章管理器清空成功:', achievementResult.message);
          } else {
            console.warn('⚠️ 云端徽章管理器清空失败:', achievementResult.error);
          }
        } catch (managerError) {
          console.warn('云端徽章管理器失败:', managerError);
        }

        // 方法2：直接调用云服务清空
        try {
          const { getCloudService } = require('../../../services/cloudService');
          const cloudService = getCloudService();
          const cloudResult = await cloudService.clearUserAchievements();

          if (cloudResult.success) {
            console.log('✅ 直接云端清空成功:', cloudResult.data.message);
          } else {
            console.warn('⚠️ 直接云端清空失败:', cloudResult.error);
          }
        } catch (cloudError) {
          console.warn('直接云端清空失败:', cloudError);
        }

        // 方法3：强制本地清理
        try {
          const { AchievementCleaner } = require('../../../utils/achievementCleaner');
          const localResult = AchievementCleaner.forceCleanAllAchievementData();
          console.log('✅ 本地徽章数据强制清理:', localResult.message);
        } catch (localError) {
          console.warn('本地徽章清理失败:', localError);
        }

        // 方法4：手动清理关键存储键
        const achievementKeys = [
          'unlockedAchievements',
          'achievementProgress',
          'achievementCache',
          'achievementCloudVersion',
          'lastAchievementSync'
        ];

        let manualClearedCount = 0;
        achievementKeys.forEach(key => {
          try {
            const data = wx.getStorageSync(key);
            if (data) {
              wx.removeStorageSync(key);
              console.log(`✅ 手动清理: ${key}`);
              manualClearedCount++;
            }
          } catch (error) {
            console.warn(`⚠️ 手动清理 ${key} 失败:`, error);
          }
        });

        console.log(`✅ 手动清理完成，共清理 ${manualClearedCount} 项徽章数据`);

      } catch (error) {
        console.error('徽章清理失败:', error);

        // 最后的降级方案：直接清理本地存储
        try {
          wx.removeStorageSync('unlockedAchievements');
          wx.removeStorageSync('achievementProgress');
          wx.removeStorageSync('achievementCache');
          console.log('✅ 最终降级清理完成');
        } catch (finalError) {
          console.error('最终降级清理也失败:', finalError);
        }
      }

      // 3. 清空本地存储的学生相关数据
      try {
        const keysToRemove = [
          'studentList',
          'classList',
          'deletedStudents',
          'studentCache',
          'appState',
          'userStats',
          'recordList',
          'commentList',
          'pendingSync',
          // 徽章系统相关数据
          'unlockedAchievements',
          'achievementProgress',
          'achievementCache',
          // 其他可能的缓存数据
          'savedComments',
          'recentComments',
          'generatedComments',
          'aiUsageStats',
          'lastSyncTime'
        ];

        keysToRemove.forEach(key => {
          wx.removeStorageSync(key);
        });

        console.log('本地学生数据清空成功');
      } catch (error) {
        console.error('清空本地学生数据失败:', error);
      }

      // 3. 清空状态管理器
      try {
        const app = getApp();
        if (app.globalData.stateManager) {
          app.globalData.stateManager.clearAllState();
        }
        console.log('状态管理器清空成功');
      } catch (error) {
        console.error('清空状态管理器失败:', error);
      }

      // 3. 清空与学生相关的记录和评语数据
      try {
        // 清空记录数据
        wx.removeStorageSync('recordList');
        wx.removeStorageSync('studentRecords');
        
        // 清空评语数据中与学生相关的部分
        const savedComments = wx.getStorageSync('savedComments') || [];
        const recentComments = wx.getStorageSync('recentComments') || [];
        const generatedComments = wx.getStorageSync('generatedComments') || [];
        
        // 可以选择清空所有评语，或者保留没有学生关联的评语
        wx.setStorageSync('savedComments', []);
        wx.setStorageSync('recentComments', []);
        wx.setStorageSync('generatedComments', []);
        
        console.log('相关记录和评语数据清空成功');
      } catch (error) {
        console.error('清空相关数据失败:', error);
      }

      // 4. 设置清空标记，防止重新加载测试数据
      wx.setStorageSync('studentsJustCleared', true);

      // 5. 等待一段时间以显示进度
      await new Promise(resolve => setTimeout(resolve, 1500));

      // 6. 更新页面数据
      this.setData({
        studentList: [],
        totalStudents: 0,
        activeStudents: 0,
        todayRecords: 0,
        searchKeyword: '',
        selectedClass: '',
        selectedClassId: null,
        showEmptyState: true
      });

      wx.hideLoading();

      wx.showModal({
        title: '✅ 清空完成',
        content: `已成功清空 ${totalStudents} 名学生的所有数据\n\n包括：\n• 学生基本信息\n• 行为记录数据\n• 评语历史记录\n\n数据已从云端彻底删除`,
        showCancel: false,
        confirmText: '知道了'
      });

      console.log('所有学生数据已清空');

      // 设置5分钟后自动清除清空标记
      setTimeout(() => {
        wx.removeStorageSync('studentsJustCleared');
        console.log('清空标记已自动清除');
      }, 5 * 60 * 1000);

      // 7. 清除清空标记（5分钟后自动清除，避免永久影响）
      setTimeout(() => {
        wx.removeStorageSync('studentsJustCleared');
      }, 5 * 60 * 1000);

    } catch (error) {
      wx.hideLoading();
      console.error('清空学生数据失败:', error);
      
      wx.showToast({
        title: '清空失败，请重试',
        icon: 'none',
        duration: 3000
      });
    }
  },

});