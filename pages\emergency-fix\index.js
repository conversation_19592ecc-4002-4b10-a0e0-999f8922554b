/**
 * 紧急修复页面
 * 一键解决云开发环境问题
 */

const { emergencyFix } = require('../../utils/emergencyFix')

Page({
  data: {
    fixing: false,
    fixResult: null,
    fixAttempts: 0,
    logs: [],
    showLogs: false
  },

  onLoad() {
    console.log('紧急修复页面加载')
    this.addLog('页面加载完成，准备开始修复')
  },

  /**
   * 开始紧急修复
   */
  async startEmergencyFix() {
    if (this.data.fixing) {
      return
    }

    this.setData({ 
      fixing: true, 
      fixResult: null,
      logs: ['开始紧急修复...']
    })

    this.addLog('🚨 启动紧急修复程序')

    try {
      // 显示修复进度
      wx.showLoading({
        title: '正在修复环境...',
        mask: true
      })

      // 执行紧急修复
      const result = await emergencyFix.emergencyFix()

      wx.hideLoading()

      this.setData({
        fixing: false,
        fixResult: result,
        fixAttempts: emergencyFix.fixAttempts
      })

      if (result.success) {
        this.addLog('✅ 紧急修复成功！')
        
        wx.showModal({
          title: '修复成功',
          content: '云开发环境已修复，现在可以正常使用小程序功能了！',
          confirmText: '返回首页',
          showCancel: false,
          success: () => {
            wx.switchTab({
              url: '/pages/index/index'
            })
          }
        })
      } else {
        this.addLog('❌ 修复失败: ' + result.message)
        
        if (result.suggestions) {
          this.addLog('💡 修复建议:')
          result.suggestions.forEach((suggestion, index) => {
            this.addLog(`${index + 1}. ${suggestion}`)
          })
        }

        wx.showModal({
          title: '修复失败',
          content: result.message + '\n\n请查看详细日志获取更多信息。',
          showCancel: false
        })
      }

    } catch (error) {
      wx.hideLoading()
      
      this.setData({
        fixing: false,
        fixResult: { success: false, error: error.message }
      })

      this.addLog('❌ 修复过程异常: ' + error.message)

      wx.showModal({
        title: '修复异常',
        content: '修复过程中发生异常，请重试或联系技术支持。',
        showCancel: false
      })
    }
  },

  /**
   * 快速诊断
   */
  async quickDiagnose() {
    wx.showLoading({
      title: '正在诊断...',
      mask: true
    })

    this.addLog('🔍 开始快速诊断')

    try {
      // 检查基本环境
      const diagnosis = {
        wxExists: typeof wx !== 'undefined',
        cloudExists: typeof wx !== 'undefined' && !!wx.cloud,
        timestamp: new Date().toISOString()
      }

      // 检查网络
      if (wx.getNetworkType) {
        const networkInfo = await new Promise((resolve, reject) => {
          wx.getNetworkType({
            success: resolve,
            fail: reject
          })
        })
        diagnosis.network = networkInfo.networkType
      }

      // 检查云开发连接
      if (diagnosis.cloudExists) {
        try {
          wx.cloud.init({ env: 'cloud1-4g85f8xlb8166ff1' })
          const db = wx.cloud.database()
          await db.collection('users').limit(1).get()
          diagnosis.cloudConnection = 'success'
          this.addLog('✅ 云开发连接正常')
        } catch (error) {
          diagnosis.cloudConnection = 'failed'
          diagnosis.cloudError = error.message
          this.addLog('❌ 云开发连接失败: ' + error.message)
        }
      }

      wx.hideLoading()

      // 显示诊断结果
      const resultText = `诊断结果：
微信API: ${diagnosis.wxExists ? '✅' : '❌'}
云开发API: ${diagnosis.cloudExists ? '✅' : '❌'}
网络状态: ${diagnosis.network || '未知'}
云开发连接: ${diagnosis.cloudConnection === 'success' ? '✅' : '❌'}`

      wx.showModal({
        title: '诊断完成',
        content: resultText,
        showCancel: false
      })

    } catch (error) {
      wx.hideLoading()
      this.addLog('❌ 诊断失败: ' + error.message)
      
      wx.showModal({
        title: '诊断失败',
        content: error.message,
        showCancel: false
      })
    }
  },

  /**
   * 手动重置
   */
  async manualReset() {
    wx.showModal({
      title: '手动重置',
      content: '这将清除所有云开发缓存并重新初始化，是否继续？',
      success: async (res) => {
        if (res.confirm) {
          wx.showLoading({
            title: '正在重置...',
            mask: true
          })

          try {
            // 清除应用状态
            const app = getApp()
            if (app && app.globalData) {
              app.globalData.db = null
              app.globalData.degradedMode = false
            }

            // 重新初始化云开发
            wx.cloud.init({
              env: 'cloud1-4g85f8xlb8166ff1',
              traceUser: true,
              timeout: 60000
            })

            // 等待初始化完成
            await new Promise(resolve => setTimeout(resolve, 3000))

            // 测试连接
            const db = wx.cloud.database()
            await db.collection('users').limit(1).get()

            wx.hideLoading()
            
            this.addLog('✅ 手动重置成功')
            
            wx.showToast({
              title: '重置成功',
              icon: 'success'
            })

          } catch (error) {
            wx.hideLoading()
            
            this.addLog('❌ 手动重置失败: ' + error.message)
            
            wx.showToast({
              title: '重置失败',
              icon: 'error'
            })
          }
        }
      }
    })
  },

  /**
   * 添加日志
   */
  addLog(message) {
    const timestamp = new Date().toLocaleTimeString()
    const logEntry = `[${timestamp}] ${message}`
    
    this.setData({
      logs: [...this.data.logs, logEntry]
    })
    
    console.log(logEntry)
  },

  /**
   * 切换日志显示
   */
  toggleLogs() {
    this.setData({
      showLogs: !this.data.showLogs
    })
  },

  /**
   * 复制日志
   */
  copyLogs() {
    const logsText = this.data.logs.join('\n')
    
    wx.setClipboardData({
      data: logsText,
      success: () => {
        wx.showToast({
          title: '日志已复制',
          icon: 'success'
        })
      }
    })
  },

  /**
   * 返回首页
   */
  goHome() {
    wx.switchTab({
      url: '/pages/index/index'
    })
  }
})
