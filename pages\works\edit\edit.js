/**
 * 评语编辑页面 - 重写版本
 */
Page({
  data: {
    workId: '',
    studentName: '',
    content: '',
    originalContent: '',
    wordCount: 0,
    saving: false
  },

  /**
   * 页面加载
   */
  onLoad(options) {
    console.log('页面参数:', options);
    
    const { id, studentName, content } = options;
    
    if (!studentName) {
      wx.showToast({
        title: '参数错误',
        icon: 'error'
      });
      setTimeout(() => wx.navigateBack(), 1500);
      return;
    }

    // 解码内容
    const decodedContent = content ? decodeURIComponent(content) : '';
    
    this.setData({
      workId: id || '',
      studentName: studentName,
      content: decodedContent,
      originalContent: decodedContent,
      wordCount: decodedContent.length
    });

    // 设置标题
    wx.setNavigationBarTitle({
      title: `编辑${studentName}的评语`
    });
  },

  /**
   * 处理输入
   */
  handleInput(e) {
    const content = e.detail.value || '';
    console.log('输入内容:', content);
    
    this.setData({
      content: content,
      wordCount: content.length
    });
  },

  /**
   * 取消编辑
   */
  handleCancel(e) {
    console.log('取消按钮被点击', e);

    if (this.data.content !== this.data.originalContent) {
      wx.showModal({
        title: '确认取消',
        content: '您有未保存的更改，确定要取消吗？',
        success: (res) => {
          if (res.confirm) {
            wx.navigateBack();
          }
        }
      });
    } else {
      wx.navigateBack();
    }
  },

  /**
   * 保存评语
   */
  handleSave(e) {
    console.log('保存按钮被点击', e);
    console.log('当前数据:', this.data);

    const content = this.data.content.trim();

    if (!content) {
      wx.showToast({
        title: '请输入评语内容',
        icon: 'none'
      });
      return;
    }

    if (this.data.saving) {
      console.log('正在保存中，忽略点击');
      return;
    }

    console.log('开始保存，内容:', content);
    this.setData({ saving: true });

    wx.showLoading({
      title: '保存中...'
    });

    // 模拟保存过程
    setTimeout(() => {
      wx.hideLoading();
      this.setData({ saving: false });

      wx.showToast({
        title: '保存成功',
        icon: 'success'
      });

      // 返回上一页并刷新
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
    }, 1500);
  }
});
