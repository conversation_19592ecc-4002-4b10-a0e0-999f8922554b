import express from 'express'
import { createProxyMiddleware } from 'http-proxy-middleware'
import path from 'path'
import { fileURLToPath } from 'url'

const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)

const app = express()
const PORT = 8082

// 开发环境宽松的CSP配置
const cspHeaders = {
  'Content-Security-Policy': [
    "default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob: *",
    "script-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob: localhost:* 127.0.0.1:* ws: wss: *",
    "style-src 'self' 'unsafe-inline' data: blob: *",
    "img-src 'self' data: blob: https: http: *",
    "font-src 'self' data: blob: *",
    "connect-src 'self' ws: wss: data: blob: localhost:* 127.0.0.1:* https: http: *",
    "worker-src 'self' blob: *",
    "frame-src 'self' *",
    "object-src 'self' data: blob:",
    "base-uri 'self'",
    "form-action 'self'"
  ].join('; ')
}

// 设置CSP头部中间件
app.use((req, res, next) => {
  // 为所有响应添加CSP头部
  Object.keys(cspHeaders).forEach(header => {
    res.setHeader(header, cspHeaders[header])
  })
  next()
})

// 代理到Vite开发服务器
const viteProxy = createProxyMiddleware({
  target: 'http://localhost:8081', // Vite服务器地址
  changeOrigin: true,
  ws: true, // 支持WebSocket（HMR需要）
  onProxyReq: (proxyReq, req, res) => {
    // 确保代理请求也有正确的头部
    console.log(`🔄 代理请求: ${req.method} ${req.url}`)
  },
  onError: (err, req, res) => {
    console.error('❌ 代理错误:', err.message)
    res.status(500).send('代理服务器错误')
  }
})

// 使用代理中间件
app.use('/', viteProxy)

// 启动服务器
app.listen(PORT, () => {
  console.log(`🚀 CSP兼容开发服务器启动成功！`)
  console.log(`📍 本地访问: http://localhost:${PORT}`)
  console.log(`🔒 CSP策略: 开发环境兼容模式`)
  console.log(`🔄 代理目标: http://localhost:8081`)
  console.log(`\n💡 使用说明:`)
  console.log(`1. 确保Vite服务器在8081端口运行`)
  console.log(`2. 通过此服务器访问应用以避免CSP错误`)
  console.log(`3. 支持热更新和WebSocket连接`)
})

// 优雅关闭
process.on('SIGTERM', () => {
  console.log('\n🛑 正在关闭CSP开发服务器...')
  process.exit(0)
})

process.on('SIGINT', () => {
  console.log('\n🛑 正在关闭CSP开发服务器...')
  process.exit(0)
})
