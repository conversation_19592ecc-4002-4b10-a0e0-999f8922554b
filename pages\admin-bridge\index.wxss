/* 管理后台数据桥接页面样式 */

.bridge-container {
  padding: 40rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
  color: white;
}

.bridge-header {
  text-align: center;
  margin-bottom: 60rpx;
}

.bridge-title {
  font-size: 48rpx;
  font-weight: bold;
  display: block;
  margin-bottom: 20rpx;
}

.bridge-status {
  background: rgba(255, 255, 255, 0.2);
  padding: 20rpx 40rpx;
  border-radius: 50rpx;
  display: inline-block;
}

.status-text {
  font-size: 28rpx;
}

.status-indicator {
  font-size: 28rpx;
  font-weight: bold;
}

.status-indicator.ready {
  color: #4ade80;
}

.status-indicator.loading {
  color: #fbbf24;
}

.status-indicator.error {
  color: #ef4444;
}

.bridge-info {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 20rpx;
  padding: 40rpx;
  margin-bottom: 40rpx;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.info-item:last-child {
  margin-bottom: 0;
}

.info-label {
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.8);
}

.info-value {
  font-size: 28rpx;
  font-weight: bold;
}

.bridge-actions {
  text-align: center;
  margin-bottom: 60rpx;
}

.sync-btn {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  border: 2rpx solid rgba(255, 255, 255, 0.3);
  border-radius: 50rpx;
  padding: 20rpx 60rpx;
  font-size: 32rpx;
  font-weight: bold;
}

.sync-btn:disabled {
  opacity: 0.5;
}

.bridge-description {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 20rpx;
  padding: 40rpx;
}

.desc-title {
  font-size: 32rpx;
  font-weight: bold;
  display: block;
  margin-bottom: 20rpx;
}

.desc-text {
  font-size: 26rpx;
  line-height: 1.6;
  display: block;
  margin-bottom: 10rpx;
  color: rgba(255, 255, 255, 0.9);
}