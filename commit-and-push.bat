@echo off
echo 正在设置GitHub仓库地址...
git remote set-url origin https://github.com/chanwarmsun/wechat3.0.git

echo 正在添加所有变更到暂存区...
git add .

echo 正在创建提交...
git commit -m "feat: 完成项目架构全面优化和清理

- 重构管理后台数据连通架构，统一数据服务接口
- 优化云函数结构，删除冗余组件，保留核心6个函数
- 清理admin-v2旧版本，统一使用admin-new现代化架构
- 完善实时数据连接和WebSocket稳定性
- 优化用户权限认证和错误处理机制
- 添加详细技术文档和部署指南
- 修复管理后台各组件数据显示问题

🤖 Generated with [Claude Code](https://claude.ai/code)

Co-Authored-By: <PERSON> <<EMAIL>>"

echo 正在推送到GitHub...
git push origin master

echo 提交和推送完成！
pause