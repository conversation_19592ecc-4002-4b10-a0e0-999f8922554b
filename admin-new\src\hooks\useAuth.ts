/**
 * 认证相关Hook
 * 提供安全的认证状态和操作管理
 */

import { useState, useEffect, useCallback } from 'react'
import { authService, LoginRequest, AuthState } from '../services/authService'
import { SecureStorageService } from '../services/secureStorageService'

/**
 * 主认证Hook
 */
export const useAuth = () => {
  const [authState, setAuthState] = useState<AuthState>({
    isAuthenticated: false,
    token: null,
    refreshToken: null,
    user: null,
    loading: true,
    error: null
  })

  /**
   * 初始化认证状态
   */
  useEffect(() => {
    checkAuth()
  }, [])

  /**
   * 检查认证状态
   */
  const checkAuth = useCallback(async () => {
    try {
      setAuthState(prev => ({ ...prev, loading: true, error: null }))
      const state = await authService.checkAuthStatus()
      setAuthState(state)
    } catch (error) {
      setAuthState(prev => ({
        ...prev,
        loading: false,
        error: error instanceof Error ? error.message : '认证检查失败'
      }))
    } finally {
      setAuthState(prev => ({ ...prev, loading: false }))
    }
  }, [])

  /**
   * 用户登录
   */
  const login = useCallback(async (credentials: LoginRequest) => {
    try {
      setAuthState(prev => ({ ...prev, loading: true, error: null }))
      
      const response = await authService.login(credentials)
      
      setAuthState({
        isAuthenticated: true,
        token: response.token,
        refreshToken: response.refreshToken,
        user: authService.getCurrentUser(),
        loading: false,
        error: null
      })
      
      return { success: true }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '登录失败'
      setAuthState(prev => ({
        ...prev,
        loading: false,
        error: errorMessage
      }))
      return { success: false, error: errorMessage }
    }
  }, [])

  /**
   * 用户登出
   */
  const logout = useCallback(async () => {
    try {
      setAuthState(prev => ({ ...prev, loading: true }))
      await authService.logout()
      
      // 清理所有认证相关的缓存
      SecureStorageService.cache.clear()
      
      setAuthState({
        isAuthenticated: false,
        token: null,
        refreshToken: null,
        user: null,
        loading: false,
        error: null
      })
      
      return { success: true }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '登出失败'
      setAuthState(prev => ({
        ...prev,
        loading: false,
        error: errorMessage
      }))
      return { success: false, error: errorMessage }
    }
  }, [])

  return {
    ...authState,
    login,
    logout,
    checkAuth,
  }
}

/**
 * 受保护的路由Hook
 */
export const useProtectedRoute = () => {
  const { isAuthenticated, loading, error } = useAuth()

  const isRouteAccessible = useCallback(() => {
    return isAuthenticated && !loading && !error
  }, [isAuthenticated, loading, error])

  const redirectIfNotAuthenticated = useCallback(async (redirectTo = '/login') => {
    if (!isAuthenticated && !loading) {
      window.location.href = redirectTo
    }
  }, [isAuthenticated, loading])

  return {
    isAccessible: isRouteAccessible(),
    redirectIfNotAuthenticated,
    loading,
    error
  }
}

/**
 * 权限检查Hook
 */
export const usePermission = () => {
  const { user } = useAuth()

  const hasPermission = useCallback((permission: string): boolean => {
    if (!user) return false
    return user.permissions.includes('*') || user.permissions.includes(permission)
  }, [user])

  const hasRole = useCallback((role: string): boolean => {
    if (!user) return false
    return user.role === role
  }, [user])

  return {
    hasPermission,
    hasRole,
    user
  }
}

/**
 * API密钥管理Hook
 */
export const useAPIKeys = () => {
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  /**
   * 保存API配置
   */
  const saveAPIConfig = useCallback(async (provider: keyof import('../services/secureStorageService').AIProviderConfig, config: import('../services/secureStorageService').APIConfig) => {
    try {
      setLoading(true)
      setError(null)
      
      // 验证API密钥
      const validation = await SecureStorageService.apiKeys.validateAPIKey(config.apiKey)
      if (!validation.isValid) {
        throw new Error(validation.error || 'API密钥无效')
      }

      await SecureStorageService.apiKeys.saveAPIConfig(provider, config)
      return { success: true }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '保存API配置失败'
      setError(errorMessage)
      return { success: false, error: errorMessage }
    } finally {
      setLoading(false)
    }
  }, [])

  /**
   * 获取API配置
   */
  const getAPIConfig = useCallback(async (provider: keyof import('../services/secureStorageService').AIProviderConfig) => {
    try {
      setLoading(true)
      setError(null)
      
      const config = await SecureStorageService.apiKeys.getAPIConfig(provider)
      return { success: true, config }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '获取API配置失败'
      setError(errorMessage)
      return { success: false, error: errorMessage, config: null }
    } finally {
      setLoading(false)
    }
  }, [])

  /**
   * 获取所有API配置
   */
  const getAllAPIConfigs = useCallback(async () => {
    try {
      setLoading(true)
      setError(null)
      
      const configs = await SecureStorageService.apiKeys.getAllAPIConfigs()
      return { success: true, configs }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '获取API配置失败'
      setError(errorMessage)
      return { success: false, error: errorMessage, configs: null }
    } finally {
      setLoading(false)
    }
  }, [])

  return {
    loading,
    error,
    saveAPIConfig,
    getAPIConfig,
    getAllAPIConfigs,
    validateAPIKey: SecureStorageService.apiKeys.validateAPIKey.bind(SecureStorageService.apiKeys)
  }
}

/**
 * 会话管理Hook
 */
export const useSession = () => {
  const [sessionRenewing, setSessionRenewing] = useState(false)

  /**
   * 检查会话剩余时间
   */
  const getSessionRemainingTime = useCallback(async (): Promise<number> => {
    try {
      const { token } = useAuth()
      if (!token || authService.getToken() !== token) return 0

      const user = authService.getCurrentUser()
      if (!user) return 0

      const now = Math.floor(Date.now() / 1000)
      return Math.max(0, user.exp - now)
    } catch {
      return 0
    }
  }, [])

  /**
   * 刷新会话
   */
  const refreshSession = useCallback(async (): Promise<boolean> => {
    try {
      setSessionRenewing(true)
      
      const remainingTime = await getSessionRemainingTime()
      
      // 如果会话即将过期（5分钟内），自动刷新
      if (remainingTime > 0 && remainingTime < 300) {
        const newToken = await authService.refreshAccessToken()
        return !!newToken
      }
      
      return remainingTime > 0
    } catch (error) {
      console.error('Session refresh failed:', error)
      return false
    } finally {
      setSessionRenewing(false)
    }
  }, [getSessionRemainingTime])

  return {
    sessionRenewing,
    getSessionRemainingTime,
    refreshSession
  }
}