/**
 * 环境修复页面
 * 帮助用户诊断和修复云开发环境问题
 */

const { envChecker } = require('../../utils/envChecker')

Page({
  data: {
    checking: false,
    result: null,
    suggestions: [],
    currentEnvId: '',
    errorInfo: ''
  },

  onLoad() {
    console.log('环境修复页面加载')
    this.checkEnvironment()
  },

  /**
   * 检测环境
   */
  async checkEnvironment() {
    this.setData({ checking: true })

    try {
      console.log('🔍 开始环境检测...')
      const result = await envChecker.checkEnvironment()
      
      const diagnosticInfo = envChecker.getDiagnosticInfo()
      
      this.setData({
        checking: false,
        result: result,
        currentEnvId: diagnosticInfo.currentEnvId || '未检测到',
        errorInfo: diagnosticInfo.errorInfo || '',
        suggestions: diagnosticInfo.suggestions || []
      })

      if (result.success) {
        wx.showToast({
          title: '环境检测成功',
          icon: 'success'
        })
      } else {
        wx.showToast({
          title: '环境检测失败',
          icon: 'error'
        })
      }

    } catch (error) {
      console.error('环境检测异常:', error)
      this.setData({
        checking: false,
        result: { success: false, error: error.message },
        errorInfo: error.message
      })
    }
  },

  /**
   * 修复环境
   */
  async fixEnvironment() {
    wx.showLoading({ title: '正在修复...' })

    try {
      const result = await envChecker.fixEnvironment()
      
      wx.hideLoading()
      
      if (result.success) {
        wx.showModal({
          title: '修复成功',
          content: `环境配置已修复\n环境ID: ${result.envId}`,
          showCancel: false,
          success: () => {
            // 返回首页
            wx.switchTab({
              url: '/pages/index/index'
            })
          }
        })
      } else {
        wx.showModal({
          title: '修复失败',
          content: result.message,
          showCancel: false
        })
      }

    } catch (error) {
      wx.hideLoading()
      wx.showModal({
        title: '修复异常',
        content: error.message,
        showCancel: false
      })
    }
  },

  /**
   * 手动设置环境ID
   */
  manualSetEnv() {
    wx.showModal({
      title: '手动设置环境ID',
      content: '请在微信开发者工具中查看正确的环境ID',
      editable: true,
      placeholderText: '请输入环境ID',
      success: async (res) => {
        if (res.confirm && res.content) {
          const envId = res.content.trim()
          
          wx.showLoading({ title: '验证中...' })
          
          try {
            const isValid = await envChecker.validateEnvId(envId)
            
            wx.hideLoading()
            
            if (isValid) {
              // 更新配置
              const app = getApp()
              app.globalData.cloudEnv = envId
              app.globalData.cloudConfig.env = envId
              
              wx.showToast({
                title: '设置成功',
                icon: 'success'
              })
              
              // 重新检测
              this.checkEnvironment()
            } else {
              wx.showToast({
                title: '环境ID无效',
                icon: 'error'
              })
            }
          } catch (error) {
            wx.hideLoading()
            wx.showToast({
              title: '验证失败',
              icon: 'error'
            })
          }
        }
      }
    })
  },

  /**
   * 查看详细信息
   */
  showDetails() {
    const diagnosticInfo = envChecker.getDiagnosticInfo()
    
    wx.showModal({
      title: '诊断详情',
      content: JSON.stringify(diagnosticInfo, null, 2),
      showCancel: false
    })
  },

  /**
   * 返回首页
   */
  goHome() {
    wx.switchTab({
      url: '/pages/index/index'
    })
  }
})
