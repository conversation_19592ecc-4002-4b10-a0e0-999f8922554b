@echo off
echo Testing different browsers for CSP compatibility
echo.

echo 1. Starting dev server...
start "Dev Server" cmd /k "npm run dev"
timeout /t 3 /nobreak >nul

echo.
echo 2. Testing different browsers:
echo.

echo Testing Firefox...
start "Firefox Test" firefox "http://localhost:8080" >nul 2>&1
if errorlevel 1 echo Firefox not found

echo.
echo Testing Edge...
start "Edge Test" msedge "http://localhost:8080" >nul 2>&1
if errorlevel 1 echo Edge not found

echo.
echo Testing Chrome Canary...
start "Chrome Canary Test" chrome-canary "http://localhost:8080" >nul 2>&1
if errorlevel 1 echo Chrome Canary not found

echo.
echo Testing Opera...
start "Opera Test" opera "http://localhost:8080" >nul 2>&1
if errorlevel 1 echo Opera not found

echo.
echo All browsers launched! 
echo Check which one works without CSP errors
echo.
pause