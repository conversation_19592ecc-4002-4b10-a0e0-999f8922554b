/**
 * 云函数：清理数据库中的测试数据
 * 部署到微信云开发后调用
 */
const cloud = require('wx-server-sdk');

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
});

const db = cloud.database();

// 测试数据特征配置
const TEST_DATA_PATTERNS = {
  // 测试ID模式
  testIds: /^(test_|mock_|demo_|sample_)/,
  
  // 测试姓名
  testNames: [
    '张小明', '李小红', '王小刚', '陈小美', '刘小强', 
    '赵小丽', '孙小军', '周小花', '吴小东', '郑小西'
  ],
  
  // 测试班级
  testClasses: /^(一|二|三|四|五|六)年级[1-9]班$/,
  
  // 测试学号
  testStudentNumbers: /^00[0-9]$/
};

/**
 * 检查是否为测试数据
 */
function isTestData(item) {
  // 检查ID
  if (item._id && TEST_DATA_PATTERNS.testIds.test(item._id.toString())) {
    return true;
  }
  
  if (item.id && TEST_DATA_PATTERNS.testIds.test(item.id.toString())) {
    return true;
  }
  
  // 检查姓名
  if (item.name && TEST_DATA_PATTERNS.testNames.includes(item.name)) {
    return true;
  }
  
  if (item.studentName && TEST_DATA_PATTERNS.testNames.includes(item.studentName)) {
    return true;
  }
  
  // 检查班级
  if (item.className && TEST_DATA_PATTERNS.testClasses.test(item.className)) {
    return true;
  }
  
  // 检查学号
  if (item.studentNumber && TEST_DATA_PATTERNS.testStudentNumbers.test(item.studentNumber)) {
    return true;
  }
  
  // 检查评语内容
  if (item.content || item.comment) {
    const content = item.content || item.comment || '';
    const testContentPatterns = [
      '同学在本学期表现优秀',
      '学习态度认真，课堂积极发言',
      '希望继续保持这种良好的学习习惯'
    ];
    
    if (testContentPatterns.some(pattern => content.includes(pattern))) {
      return true;
    }
  }
  
  return false;
}

/**
 * 清理指定集合中的测试数据
 */
async function cleanCollection(collectionName) {
  try {
    console.log(`开始清理集合: ${collectionName}`);
    
    // 获取所有数据
    const queryResult = await db.collection(collectionName).get();
    const allData = queryResult.data;
    
    if (allData.length === 0) {
      return {
        collection: collectionName,
        success: true,
        totalCount: 0,
        testCount: 0,
        deletedCount: 0,
        message: '集合为空'
      };
    }
    
    // 识别测试数据
    const testData = allData.filter(item => isTestData(item));
    
    if (testData.length === 0) {
      return {
        collection: collectionName,
        success: true,
        totalCount: allData.length,
        testCount: 0,
        deletedCount: 0,
        message: '未发现测试数据'
      };
    }
    
    // 删除测试数据
    let deletedCount = 0;
    const deletePromises = testData.map(async (item) => {
      try {
        await db.collection(collectionName).doc(item._id).remove();
        deletedCount++;
        console.log(`删除测试数据: ${collectionName}/${item._id} - ${item.name || item.studentName || 'unknown'}`);
      } catch (error) {
        console.error(`删除失败: ${collectionName}/${item._id}`, error);
      }
    });
    
    await Promise.all(deletePromises);
    
    return {
      collection: collectionName,
      success: true,
      totalCount: allData.length,
      testCount: testData.length,
      deletedCount,
      message: `成功删除 ${deletedCount} 条测试数据`
    };
    
  } catch (error) {
    console.error(`清理集合 ${collectionName} 失败:`, error);
    return {
      collection: collectionName,
      success: false,
      error: error.message,
      message: `清理失败: ${error.message}`
    };
  }
}

/**
 * 云函数入口
 */
exports.main = async (event, context) => {
  console.log('开始清理数据库中的测试数据...');
  
  // 需要清理的集合
  const collections = [
    'students',    // 学生信息
    'comments',    // 评语记录
    'classes',     // 班级信息
    'records',     // 行为记录
    'works'        // 作品记录
  ];
  
  const results = [];
  let totalDeleted = 0;
  
  try {
    // 逐个清理集合
    for (const collectionName of collections) {
      const result = await cleanCollection(collectionName);
      results.push(result);
      
      if (result.success) {
        totalDeleted += result.deletedCount;
      }
    }
    
    // 生成清理报告
    const summary = {
      success: true,
      timestamp: new Date().toISOString(),
      totalCollections: collections.length,
      totalDeleted,
      results,
      message: `清理完成，共删除 ${totalDeleted} 条测试数据`
    };
    
    console.log('数据库测试数据清理完成:', summary);
    return summary;
    
  } catch (error) {
    console.error('数据库清理过程中发生错误:', error);
    return {
      success: false,
      error: error.message,
      results,
      message: `清理过程中发生错误: ${error.message}`
    };
  }
};
