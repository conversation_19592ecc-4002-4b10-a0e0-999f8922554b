# 项目清理记录

## 已删除的废弃云函数
- adminAPI_v2 (测试版本)
- testAPI (测试用)
- simpleAPI (测试用) 
- adminDataQuery (未使用的备用函数)
- getUserOpenId (测试用)
- testDoubaoAI (测试用)

## 保留的生产云函数
- adminAPI (生产主API)
- dataQuery (数据查询专用)
- generateComment (评语生成核心)
- doubaoAI (AI模型调用)
- login (用户登录)
- getStudents (学生管理)
- getStatistics (数据统计)
- 其他业务相关云函数

## 架构优化
- 统一数据入口为dataService
- 简化dataBridgeService为代理模式
- 修复HTTP调用格式错误
- 清理重复的服务实现

## 注意事项
在腾讯云开发控制台中，请手动删除以下废弃云函数：
- adminAPI_v2
- testAPI  
- simpleAPI
- adminDataQuery
- getUserOpenId
- testDoubaoAI

删除后记得在小程序开发工具中重新部署，确保只保留需要的云函数。