/**
 * API连接状态指示器组件
 * 显示与小程序后端的连接状态
 */

import React from 'react'

interface ApiConnectionStatusProps {
  isConnected: boolean
  connectionError?: string | null
  className?: string
}

const ApiConnectionStatus: React.FC<ApiConnectionStatusProps> = ({ 
  isConnected, 
  connectionError, 
  className = '' 
}) => {
  const getStatusColor = () => {
    if (connectionError) return '#ff6b6b'
    return isConnected ? '#51cf66' : '#ffd43b'
  }

  const getStatusText = () => {
    if (connectionError) return '连接异常'
    return isConnected ? '已连接' : '连接中...'
  }

  const getStatusIcon = () => {
    if (connectionError) return '❌'
    return isConnected ? '🟢' : '🟡'
  }

  return (
    <div 
      className={className}
      style={{
        display: 'flex',
        alignItems: 'center',
        gap: '8px',
        padding: '6px 12px',
        background: 'rgba(255,255,255,0.9)',
        borderRadius: '20px',
        border: `1px solid ${getStatusColor()}20`,
        fontSize: '12px',
        fontWeight: '600'
      }}
      title={connectionError || '小程序数据连接状态'}
    >
      <span style={{ fontSize: '10px' }}>{getStatusIcon()}</span>
      <span style={{ color: getStatusColor() }}>{getStatusText()}</span>
    </div>
  )
}

export default ApiConnectionStatus