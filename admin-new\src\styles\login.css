/* 登录页面专用样式 */

/* 输入框文字颜色修复 */
.login-form .ant-input {
  color: #1f2937 !important;
  font-size: 16px !important;
  font-weight: 500;
}

.login-form .ant-input::placeholder {
  color: #9ca3af !important;
  font-weight: 400;
}

/* 密码输入框 */
.login-form .ant-input-password {
  color: #1f2937 !important;
  font-size: 16px !important;
}

.login-form .ant-input-password input {
  color: #1f2937 !important;
  font-size: 16px !important;
  font-weight: 500;
}

.login-form .ant-input-password input::placeholder {
  color: #9ca3af !important;
  font-weight: 400;
}

/* 输入框包装器 */
.login-form .ant-input-affix-wrapper {
  color: #1f2937 !important;
  background-color: #ffffff !important;
}

.login-form .ant-input-affix-wrapper input {
  color: #1f2937 !important;
  font-size: 16px !important;
  font-weight: 500;
  background-color: transparent !important;
}

.login-form .ant-input-affix-wrapper input::placeholder {
  color: #9ca3af !important;
  font-weight: 400;
}

/* 前缀图标颜色 */
.login-form .ant-input-prefix {
  color: #6b7280 !important;
}

/* 聚焦状态 */
.login-form .ant-input:focus,
.login-form .ant-input-focused {
  border-color: #3b82f6 !important;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1) !important;
}

.login-form .ant-input-affix-wrapper:focus,
.login-form .ant-input-affix-wrapper-focused {
  border-color: #3b82f6 !important;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1) !important;
}

/* 悬停状态 */
.login-form .ant-input:hover,
.login-form .ant-input-affix-wrapper:hover {
  border-color: #60a5fa !important;
}

/* 禁用状态 */
.login-form .ant-input:disabled,
.login-form .ant-input-affix-wrapper-disabled {
  background-color: #f9fafb !important;
  color: #9ca3af !important;
}

/* 错误状态 */
.login-form .ant-input-status-error,
.login-form .ant-input-affix-wrapper-status-error {
  border-color: #ef4444 !important;
}

.login-form .ant-input-status-error:focus,
.login-form .ant-input-affix-wrapper-status-error:focus {
  border-color: #ef4444 !important;
  box-shadow: 0 0 0 2px rgba(239, 68, 68, 0.1) !important;
}

/* 表单项标签 */
.login-form .ant-form-item-label > label {
  color: #374151 !important;
  font-weight: 500;
}

/* 错误信息 */
.login-form .ant-form-item-explain-error {
  color: #ef4444 !important;
  font-size: 14px;
}

/* 登录按钮样式增强 */
.login-form .ant-btn-primary {
  background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%) !important;
  border: none !important;
  box-shadow: 0 4px 14px 0 rgba(59, 130, 246, 0.3) !important;
  transition: all 0.3s ease !important;
}

.login-form .ant-btn-primary:hover {
  background: linear-gradient(135deg, #2563eb 0%, #7c3aed 100%) !important;
  box-shadow: 0 6px 20px 0 rgba(59, 130, 246, 0.4) !important;
  transform: translateY(-1px) !important;
}

.login-form .ant-btn-primary:active {
  transform: translateY(0) !important;
  box-shadow: 0 2px 8px 0 rgba(59, 130, 246, 0.3) !important;
}

/* 响应式设计 */
@media (max-width: 640px) {
  .login-form .ant-input,
  .login-form .ant-input-password input,
  .login-form .ant-input-affix-wrapper input {
    font-size: 16px !important; /* 防止iOS缩放 */
  }
}

/* 自动填充样式修复 */
.login-form .ant-input:-webkit-autofill,
.login-form .ant-input-password input:-webkit-autofill {
  -webkit-box-shadow: 0 0 0 1000px #ffffff inset !important;
  -webkit-text-fill-color: #1f2937 !important;
  transition: background-color 5000s ease-in-out 0s !important;
}

/* 密码可见性切换按钮 */
.login-form .ant-input-password-icon {
  color: #6b7280 !important;
}

.login-form .ant-input-password-icon:hover {
  color: #374151 !important;
}
