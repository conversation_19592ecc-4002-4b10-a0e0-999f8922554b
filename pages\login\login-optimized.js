/**
 * 优化后的登录页面
 * 支持渐进式登录策略
 */
const app = getApp();

Page({
  /**
   * 页面的初始数据
   */
  data: {
    // 弹窗状态
    showAgreement: false,
    showPrivacy: false,
    
    // 登录状态
    isLogging: false,
    
    // 来源页面信息
    fromPage: '',
    fromAction: '',
    
    // 登录激励文案
    loginIncentives: [
      '解锁无限AI评语生成',
      '专属数据云端同步',
      '个性化学情分析报告'
    ]
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    console.log('登录页面加载', options);
    
    // 记录来源信息，用于登录后跳转
    if (options.from) {
      this.setData({
        fromPage: options.from,
        fromAction: options.action || ''
      });
    }
    
    // 检查是否已经登录
    this.checkLoginStatus();
    
    // 记录页面访问（用于增长分析）
    this.trackPageView(options);
  },

  /**
   * 检查登录状态
   */
  checkLoginStatus() {
    const userInfo = wx.getStorageSync('userInfo');
    const token = wx.getStorageSync('token');
    
    if (userInfo && token) {
      // 已登录，根据来源页面跳转
      this.redirectAfterLogin();
    }
  },

  /**
   * 微信登录
   */
  async wxLogin() {
    if (this.data.isLogging) {
      return;
    }

    this.setData({ isLogging: true });

    try {
      // 显示加载提示
      wx.showLoading({
        title: '登录中...',
        mask: true
      });

      // 获取微信登录凭证
      const loginRes = await this.getWxLoginCode();
      console.log('微信登录凭证获取成功:', loginRes);

      // 获取用户信息
      const userProfile = await this.getUserProfile();
      console.log('用户信息获取成功:', userProfile);

      // 调用后端登录接口
      const loginResult = await this.callLoginAPI(loginRes.code, userProfile);
      console.log('后端登录成功:', loginResult);

      // 保存登录信息
      this.saveLoginInfo(loginResult, userProfile);

      // 登录成功提示
      wx.showToast({
        title: '登录成功',
        icon: 'success',
        duration: 1500
      });

      // 延迟跳转，让用户看到成功提示
      setTimeout(() => {
        this.redirectAfterLogin();
      }, 1500);

      // 记录登录成功事件
      this.trackLoginSuccess('wechat');

    } catch (error) {
      console.error('登录失败:', error);
      
      // 显示错误信息
      wx.showToast({
        title: error.message || '登录失败，请重试',
        icon: 'none',
        duration: 2000
      });

      // 记录登录失败事件
      this.trackLoginFailure('wechat', error.message);
    } finally {
      wx.hideLoading();
      this.setData({ isLogging: false });
    }
  },

  /**
   * 游客模式进入
   */
  enterGuestMode() {
    console.log('用户选择游客模式');
    
    // 设置游客模式标识
    wx.setStorageSync('isGuestMode', true);
    
    // 记录游客模式选择
    this.trackGuestModeEntry();
    
    // 跳转到首页或来源页面
    this.redirectAfterLogin(true);
  },

  /**
   * 获取微信登录凭证
   */
  getWxLoginCode() {
    return new Promise((resolve, reject) => {
      wx.login({
        success: resolve,
        fail: reject
      });
    });
  },

  /**
   * 获取用户信息
   */
  getUserProfile() {
    return new Promise((resolve, reject) => {
      wx.getUserProfile({
        desc: '用于完善用户资料',
        success: resolve,
        fail: reject
      });
    });
  },

  /**
   * 调用后端登录接口
   */
  async callLoginAPI(code, userProfile) {
    // 这里调用实际的登录API
    // 暂时返回模拟数据
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve({
          token: 'mock_token_' + Date.now(),
          userId: 'user_' + Date.now(),
          userInfo: userProfile.userInfo
        });
      }, 1000);
    });
  },

  /**
   * 保存登录信息
   */
  saveLoginInfo(loginResult, userProfile) {
    // 保存到本地存储
    wx.setStorageSync('token', loginResult.token);
    wx.setStorageSync('userId', loginResult.userId);
    wx.setStorageSync('userInfo', userProfile.userInfo);
    wx.setStorageSync('isGuestMode', false);
    
    // 保存到全局数据
    app.globalData.token = loginResult.token;
    app.globalData.userInfo = userProfile.userInfo;
    app.globalData.isLogin = true;
  },

  /**
   * 登录后跳转
   */
  redirectAfterLogin(isGuestMode = false) {
    const { fromPage, fromAction } = this.data;
    
    // 根据来源页面决定跳转目标
    let targetUrl = '/pages/index/index';
    
    if (fromPage) {
      targetUrl = fromPage;
      if (fromAction) {
        targetUrl += `?action=${fromAction}`;
      }
    }
    
    // 使用 reLaunch 确保清除登录页面
    wx.reLaunch({
      url: targetUrl,
      success: () => {
        console.log(`${isGuestMode ? '游客模式' : '登录'}后跳转成功:`, targetUrl);
      },
      fail: (error) => {
        console.error('跳转失败:', error);
        // 降级到首页
        wx.reLaunch({
          url: '/pages/index/index'
        });
      }
    });
  },

  /**
   * 返回上一页
   */
  goBack() {
    const pages = getCurrentPages();
    if (pages.length > 1) {
      wx.navigateBack();
    } else {
      // 如果是第一个页面，跳转到首页
      wx.reLaunch({
        url: '/pages/index/index'
      });
    }
  },

  /**
   * 显示用户协议
   */
  showUserAgreement() {
    this.setData({ showAgreement: true });
    this.trackAgreementView('user_agreement');
  },

  /**
   * 隐藏用户协议
   */
  hideAgreement() {
    this.setData({ showAgreement: false });
  },

  /**
   * 显示隐私政策
   */
  showPrivacyPolicy() {
    this.setData({ showPrivacy: true });
    this.trackAgreementView('privacy_policy');
  },

  /**
   * 隐藏隐私政策
   */
  hidePrivacy() {
    this.setData({ showPrivacy: false });
  },

  /**
   * 记录页面访问
   */
  trackPageView(options) {
    try {
      // 这里可以接入实际的数据统计服务
      console.log('登录页面访问统计:', {
        timestamp: Date.now(),
        from: options.from || 'direct',
        action: options.action || 'none'
      });
    } catch (error) {
      console.warn('页面访问统计失败:', error);
    }
  },

  /**
   * 记录登录成功事件
   */
  trackLoginSuccess(method) {
    try {
      console.log('登录成功统计:', {
        method,
        timestamp: Date.now(),
        fromPage: this.data.fromPage
      });
    } catch (error) {
      console.warn('登录成功统计失败:', error);
    }
  },

  /**
   * 记录登录失败事件
   */
  trackLoginFailure(method, reason) {
    try {
      console.log('登录失败统计:', {
        method,
        reason,
        timestamp: Date.now()
      });
    } catch (error) {
      console.warn('登录失败统计失败:', error);
    }
  },

  /**
   * 记录游客模式进入
   */
  trackGuestModeEntry() {
    try {
      console.log('游客模式进入统计:', {
        timestamp: Date.now(),
        fromPage: this.data.fromPage
      });
    } catch (error) {
      console.warn('游客模式统计失败:', error);
    }
  },

  /**
   * 记录协议查看
   */
  trackAgreementView(type) {
    try {
      console.log('协议查看统计:', {
        type,
        timestamp: Date.now()
      });
    } catch (error) {
      console.warn('协议查看统计失败:', error);
    }
  }
});
