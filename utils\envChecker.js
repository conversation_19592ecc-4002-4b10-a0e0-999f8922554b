/**
 * 环境检测和修复工具
 * 自动检测和修复云开发环境配置问题
 */

class EnvChecker {
  constructor() {
    this.envId = null
    this.isValid = false
    this.errorInfo = null
  }

  /**
   * 检测当前云开发环境
   */
  async checkEnvironment() {
    console.log('🔍 开始检测云开发环境...')
    
    try {
      // 方法1：从微信开发者工具获取
      if (typeof wx !== 'undefined' && wx.cloud) {
        const envInfo = await this.getEnvFromWx()
        if (envInfo.success) {
          this.envId = envInfo.envId
          this.isValid = true
          console.log('✅ 从微信开发者工具获取环境ID:', this.envId)
          return { success: true, envId: this.envId }
        }
      }

      // 方法2：从配置文件获取
      const configEnvId = this.getEnvFromConfig()
      if (configEnvId) {
        console.log('📋 从配置文件获取环境ID:', configEnvId)
        
        // 验证配置文件中的环境ID
        const isValidConfig = await this.validateEnvId(configEnvId)
        if (isValidConfig) {
          this.envId = configEnvId
          this.isValid = true
          return { success: true, envId: this.envId }
        }
      }

      // 方法3：尝试自动检测
      const detectedEnvId = await this.autoDetectEnvId()
      if (detectedEnvId) {
        this.envId = detectedEnvId
        this.isValid = true
        console.log('🔍 自动检测到环境ID:', this.envId)
        return { success: true, envId: this.envId }
      }

      throw new Error('无法检测到有效的云开发环境ID')

    } catch (error) {
      console.error('❌ 环境检测失败:', error)
      this.errorInfo = error.message
      return { success: false, error: error.message }
    }
  }

  /**
   * 从微信开发者工具获取环境信息
   */
  async getEnvFromWx() {
    try {
      // 尝试调用云函数获取环境信息
      const result = await new Promise((resolve, reject) => {
        wx.cloud.callFunction({
          name: 'getEnvInfo', // 需要创建这个云函数
          success: resolve,
          fail: reject
        })
      })
      
      if (result.result && result.result.envId) {
        return { success: true, envId: result.result.envId }
      }
      
      throw new Error('云函数返回的环境信息无效')
    } catch (error) {
      console.log('⚠️ 无法从云函数获取环境信息:', error.message)
      return { success: false, error: error.message }
    }
  }

  /**
   * 从配置文件获取环境ID
   */
  getEnvFromConfig() {
    try {
      // 尝试从多个配置源获取
      const sources = [
        () => {
          const { getCurrentCloudConfig } = require('../config/cloudConfig')
          return getCurrentCloudConfig().env
        },
        () => {
          const { envLoader } = require('../config/envLoader')
          return envLoader.get('CLOUD_ENV_ID')
        },
        () => 'cloud1-4g85f8xlb8166ff1' // 默认值
      ]

      for (const getEnv of sources) {
        try {
          const envId = getEnv()
          if (envId && typeof envId === 'string' && envId.length > 0) {
            return envId
          }
        } catch (error) {
          console.log('⚠️ 配置源获取失败:', error.message)
        }
      }

      return null
    } catch (error) {
      console.error('❌ 从配置文件获取环境ID失败:', error)
      return null
    }
  }

  /**
   * 验证环境ID是否有效
   */
  async validateEnvId(envId) {
    try {
      console.log('🔍 验证环境ID:', envId)
      
      // 尝试初始化云开发
      wx.cloud.init({ env: envId })
      
      // 尝试简单的数据库操作
      const db = wx.cloud.database()
      await db.collection('users').limit(1).get()
      
      console.log('✅ 环境ID验证成功:', envId)
      return true
    } catch (error) {
      console.log('❌ 环境ID验证失败:', envId, error.message)
      return false
    }
  }

  /**
   * 自动检测环境ID
   */
  async autoDetectEnvId() {
    const possibleEnvIds = [
      'cloud1-4g85f8xlb8166ff1',
      'cloud1-4g85f8xlb8166ff1-dev',
      'cloud1-4g85f8xlb8166ff1-test',
      // 可以添加更多可能的环境ID
    ]

    for (const envId of possibleEnvIds) {
      console.log('🔍 尝试环境ID:', envId)
      const isValid = await this.validateEnvId(envId)
      if (isValid) {
        return envId
      }
    }

    return null
  }

  /**
   * 修复环境配置
   */
  async fixEnvironment() {
    console.log('🔧 开始修复环境配置...')
    
    const checkResult = await this.checkEnvironment()
    if (!checkResult.success) {
      return {
        success: false,
        message: '无法检测到有效的环境配置',
        suggestions: [
          '1. 检查微信开发者工具中的云开发环境是否正确',
          '2. 确认云开发环境ID是否有效',
          '3. 检查网络连接是否正常',
          '4. 尝试重新初始化云开发环境'
        ]
      }
    }

    // 更新全局配置
    try {
      const app = getApp()
      if (app && app.globalData) {
        app.globalData.cloudEnv = this.envId
        app.globalData.cloudConfig = {
          env: this.envId,
          traceUser: true,
          timeout: 60000
        }
        console.log('✅ 全局配置已更新')
      }

      // 重新初始化云开发
      wx.cloud.init({
        env: this.envId,
        traceUser: true,
        timeout: 60000
      })

      console.log('✅ 云开发重新初始化成功')

      return {
        success: true,
        envId: this.envId,
        message: '环境配置修复成功'
      }
    } catch (error) {
      console.error('❌ 环境配置修复失败:', error)
      return {
        success: false,
        message: '环境配置修复失败: ' + error.message
      }
    }
  }

  /**
   * 获取环境诊断信息
   */
  getDiagnosticInfo() {
    return {
      currentEnvId: this.envId,
      isValid: this.isValid,
      errorInfo: this.errorInfo,
      timestamp: new Date().toISOString(),
      platform: typeof wx !== 'undefined' ? 'miniprogram' : 'unknown',
      suggestions: this.getSuggestions()
    }
  }

  /**
   * 获取修复建议
   */
  getSuggestions() {
    const suggestions = []

    if (!this.isValid) {
      suggestions.push('环境ID配置无效，需要检查云开发环境设置')
    }

    if (this.errorInfo && this.errorInfo.includes('env check invalid')) {
      suggestions.push('环境ID验证失败，请检查是否使用了正确的环境ID')
      suggestions.push('建议在微信开发者工具中重新创建或选择云开发环境')
    }

    if (this.errorInfo && this.errorInfo.includes('network')) {
      suggestions.push('网络连接问题，请检查网络设置')
    }

    suggestions.push('可以尝试重启微信开发者工具')
    suggestions.push('确认云开发服务是否正常运行')

    return suggestions
  }
}

// 创建全局实例
const envChecker = new EnvChecker()

// 导出
module.exports = {
  envChecker,
  EnvChecker
}
