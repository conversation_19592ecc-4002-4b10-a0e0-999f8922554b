/**
 * 清理用户所有数据的云函数
 * 用于用户注销账号时彻底清理云端数据
 */

const cloud = require('wx-server-sdk');

// 初始化云开发环境
cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
});

const db = cloud.database();
const _ = db.command;

exports.main = async (event, context) => {
  const { userId } = event;
  
  console.log('[clearAllData] 开始清理用户数据, userId:', userId);
  
  if (!userId) {
    return {
      success: false,
      error: '缺少用户ID参数'
    };
  }

  try {
    const results = {
      userProfiles: 0,
      students: 0,
      classes: 0,
      comments: 0,
      records: 0,
      achievements: 0,
      settings: 0,
      files: 0
    };

    // 1. 清理用户档案
    try {
      const userProfileResult = await db.collection('user_profiles')
        .where({
          userId: userId
        })
        .remove();
      results.userProfiles = userProfileResult.stats.removed;
      console.log(`[clearAllData] 清理用户档案: ${results.userProfiles} 条`);
    } catch (error) {
      console.warn('[clearAllData] 清理用户档案失败:', error);
    }

    // 2. 清理学生数据
    try {
      const studentsResult = await db.collection('students')
        .where({
          userId: userId
        })
        .remove();
      results.students = studentsResult.stats.removed;
      console.log(`[clearAllData] 清理学生数据: ${results.students} 条`);
    } catch (error) {
      console.warn('[clearAllData] 清理学生数据失败:', error);
    }

    // 3. 清理班级数据
    try {
      const classesResult = await db.collection('classes')
        .where({
          userId: userId
        })
        .remove();
      results.classes = classesResult.stats.removed;
      console.log(`[clearAllData] 清理班级数据: ${results.classes} 条`);
    } catch (error) {
      console.warn('[clearAllData] 清理班级数据失败:', error);
    }

    // 4. 清理评语记录
    try {
      const commentsResult = await db.collection('comments')
        .where({
          userId: userId
        })
        .remove();
      results.comments = commentsResult.stats.removed;
      console.log(`[clearAllData] 清理评语记录: ${results.comments} 条`);
    } catch (error) {
      console.warn('[clearAllData] 清理评语记录失败:', error);
    }

    // 5. 清理行为记录
    try {
      const recordsResult = await db.collection('records')
        .where({
          userId: userId
        })
        .remove();
      results.records = recordsResult.stats.removed;
      console.log(`[clearAllData] 清理行为记录: ${results.records} 条`);
    } catch (error) {
      console.warn('[clearAllData] 清理行为记录失败:', error);
    }

    // 6. 清理成就数据
    try {
      const achievementsResult = await db.collection('achievements')
        .where({
          userId: userId
        })
        .remove();
      results.achievements = achievementsResult.stats.removed;
      console.log(`[clearAllData] 清理成就数据: ${results.achievements} 条`);
    } catch (error) {
      console.warn('[clearAllData] 清理成就数据失败:', error);
    }

    // 7. 清理用户设置
    try {
      const settingsResult = await db.collection('user_settings')
        .where({
          userId: userId
        })
        .remove();
      results.settings = settingsResult.stats.removed;
      console.log(`[clearAllData] 清理用户设置: ${results.settings} 条`);
    } catch (error) {
      console.warn('[clearAllData] 清理用户设置失败:', error);
    }

    // 8. 清理云存储文件
    try {
      // 获取用户相关的文件列表
      const fileList = await cloud.getTempFileURL({
        fileList: [`user-files/${userId}/`]
      });
      
      if (fileList && fileList.fileList && fileList.fileList.length > 0) {
        // 删除用户文件夹下的所有文件
        const deleteResult = await cloud.deleteFile({
          fileList: fileList.fileList.map(file => file.fileID)
        });
        results.files = deleteResult.fileList.length;
        console.log(`[clearAllData] 清理云存储文件: ${results.files} 个`);
      }
    } catch (error) {
      console.warn('[clearAllData] 清理云存储文件失败:', error);
    }

    // 9. 记录清理日志
    try {
      await db.collection('data_cleanup_logs').add({
        data: {
          userId: userId,
          cleanupTime: new Date(),
          results: results,
          totalCleaned: Object.values(results).reduce((sum, count) => sum + count, 0)
        }
      });
    } catch (error) {
      console.warn('[clearAllData] 记录清理日志失败:', error);
    }

    const totalCleaned = Object.values(results).reduce((sum, count) => sum + count, 0);
    
    console.log(`[clearAllData] 用户数据清理完成, 总计清理: ${totalCleaned} 条记录`);
    console.log('[clearAllData] 清理详情:', results);

    return {
      success: true,
      message: '用户数据清理完成',
      results: results,
      totalCleaned: totalCleaned
    };

  } catch (error) {
    console.error('[clearAllData] 清理用户数据失败:', error);
    return {
      success: false,
      error: error.message || '清理数据时发生未知错误',
      details: error
    };
  }
};
