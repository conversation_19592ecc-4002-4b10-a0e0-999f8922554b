/**
 * 班级管理页面
 * 基于微信云开发
 */
const app = getApp();
const { cloudService } = require('../../../services/cloudService');

Page({
  /**
   * 页面的初始数据
   */
  data: {
    // 班级列表（从云数据库获取）
    classList: [],

    // 搜索关键词
    searchKeyword: '',

    // 加载状态
    loading: false,

    // 操作弹窗
    showActionSheet: false,
    actionSheetActions: [
      { name: '编辑班级', value: 'edit' },
      { name: '班级统计', value: 'stats' },
      { name: '导出数据', value: 'export' },
      { name: '删除班级', value: 'delete', color: '#FF5247' }
    ],

    // 删除确认弹窗
    showDeleteDialog: false,

    // 当前选中的班级
    selectedClass: null
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    console.log('班级管理页面加载');
    this.loadClassList();
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    // 从创建/编辑页面返回时刷新列表
    this.loadClassList();
  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {
    setTimeout(() => {
      wx.stopPullDownRefresh();
    }, 1000);
  },

  /**
   * 搜索输入变化
   */
  onSearchChange(e) {
    this.setData({
      searchKeyword: e.detail.value
    });
  },

  /**
   * 执行搜索
   */
  async onSearch() {
    const keyword = this.data.searchKeyword.trim();
    if (!keyword) {
      this.loadClassList();
      return;
    }

    try {
      this.setData({ loading: true });

      const result = await cloudService.getClassList();
      if (result.success) {
        const filteredClasses = result.data
          .filter(cls =>
            cls.className.includes(keyword) ||
            (cls.description && cls.description.includes(keyword)) ||
            cls.subject.includes(keyword)
          )
          .map(classItem => ({
            id: classItem._id,
            name: classItem.className,
            description: classItem.description || `${classItem.grade}${classItem.subject}班级`,
            studentCount: classItem.studentCount || 0,
            status: 'active',
            statusText: '正常',
            createTimeText: this.formatTime(classItem.createTime),
            grade: classItem.grade,
            subject: classItem.subject
          }));

        this.setData({
          classList: filteredClasses,
          loading: false
        });
      } else {
        throw new Error(result.error || '搜索失败');
      }
    } catch (error) {
      console.error('搜索班级失败:', error);
      this.setData({
        classList: [],
        loading: false
      });
      wx.showToast({
        title: '搜索失败',
        icon: 'none'
      });
    }
  },

  /**
   * 加载班级列表
   */
  async loadClassList() {
    this.setData({ loading: true });

    try {
      const result = await cloudService.getClassList();

      if (result.success) {
        const classList = result.data.map(classItem => ({
          id: classItem._id,
          name: classItem.className,
          description: classItem.description || `${classItem.grade}${classItem.subject}班级`,
          studentCount: classItem.studentCount || 0,
          status: 'active',
          statusText: '正常',
          createTimeText: this.formatTime(classItem.createTime),
          grade: classItem.grade,
          subject: classItem.subject
        }));

        this.setData({
          classList: classList,
          loading: false
        });
      } else {
        throw new Error(result.error || '获取班级列表失败');
      }
    } catch (error) {
      console.error('加载班级列表失败:', error);
      this.setData({
        classList: [],
        loading: false
      });
      wx.showToast({
        title: '加载失败',
        icon: 'none'
      });
    }
  },

  /**
   * 格式化时间
   */
  formatTime(timestamp) {
    if (!timestamp) return '未知';

    const now = new Date();
    const time = new Date(timestamp);
    const diff = now - time;

    const days = Math.floor(diff / (1000 * 60 * 60 * 24));
    const months = Math.floor(days / 30);

    if (months > 0) {
      return `${months}个月前`;
    } else if (days > 0) {
      return `${days}天前`;
    } else {
      return '今天';
    }
  },

  /**
   * 跳转到班级详情
   */
  goToClassDetail(e) {
    const { class: classInfo } = e.currentTarget.dataset;
    wx.navigateTo({
      url: `/pages/class/detail/detail?id=${classInfo.id}`
    });
  },

  /**
   * 跳转到学生列表
   */
  goToStudentList(e) {
    e.stopPropagation();
    wx.showToast({
      title: '功能开发中',
      icon: 'none'
    });
  },

  /**
   * 显示班级操作菜单
   */
  showClassActions(e) {
    const { class: classInfo } = e.currentTarget.dataset;

    console.log('点击操作菜单:', classInfo);

    this.setData({
      selectedClass: classInfo,
      showActionSheet: true
    });

    console.log('设置showActionSheet为true');
  },

  /**
   * 隐藏班级操作菜单
   */
  hideClassActions() {
    this.setData({
      showActionSheet: false,
      selectedClass: null
    });
  },

  /**
   * 处理操作选择
   */
  onActionSelect(e) {
    const { value } = e.detail;
    const selectedClass = this.data.selectedClass;

    this.hideClassActions();

    switch (value) {
      case 'edit':
        this.editClass(selectedClass);
        break;
      case 'stats':
        this.viewClassStats(selectedClass);
        break;
      case 'export':
        this.exportClassData(selectedClass);
        break;
      case 'delete':
        this.showDeleteConfirm(selectedClass);
        break;
      default:
        wx.showToast({
          title: '功能开发中',
          icon: 'none'
        });
    }
  },

  /**
   * 编辑班级
   */
  editClass(classInfo) {
    wx.navigateTo({
      url: `/pages/class/create/create?mode=edit&id=${classInfo.id}`
    });
  },

  /**
   * 查看班级统计
   */
  viewClassStats(classInfo) {
    wx.navigateTo({
      url: `/pages/analytics/class/class?id=${classInfo.id}`
    });
  },

  /**
   * 导出班级数据
   */
  exportClassData(classInfo) {
    wx.showLoading({
      title: '导出中...',
      mask: true
    });

    // 模拟导出过程
    setTimeout(() => {
      wx.hideLoading();
      wx.showToast({
        title: '导出成功',
        icon: 'success'
      });
    }, 2000);
  },

  /**
   * 显示删除确认
   */
  showDeleteConfirm(classInfo) {
    wx.showModal({
      title: '确认删除',
      content: `确定要删除班级"${classInfo.name}"吗？删除后无法恢复。`,
      confirmText: '删除',
      confirmColor: '#FF5247',
      success: (res) => {
        if (res.confirm) {
          this.deleteClass(classInfo);
        }
      }
    });
  },

  /**
   * 删除班级
   */
  async deleteClass(classInfo) {
    try {
      wx.showLoading({
        title: '删除中...',
        mask: true
      });

      // 模拟删除API调用
      await new Promise(resolve => setTimeout(resolve, 1000));

      // 从列表中移除
      const updatedList = this.data.classList.filter(item => item.id !== classInfo.id);
      this.setData({ classList: updatedList });

      wx.hideLoading();
      wx.showToast({
        title: '删除成功',
        icon: 'success'
      });

    } catch (error) {
      wx.hideLoading();
      wx.showToast({
        title: '删除失败',
        icon: 'none'
      });
    }
  },

  /**
   * 隐藏删除确认
   */
  hideDeleteDialog() {
    this.setData({
      showDeleteDialog: false,
      selectedClass: null
    });
  },

  /**
   * 确认删除班级
   */
  confirmDelete() {
    wx.showToast({
      title: '删除成功',
      icon: 'success'
    });
    this.hideDeleteDialog();
  },

  /**
   * 跳转到创建班级
   */
  goToCreateClass() {
    wx.navigateTo({
      url: '/pages/class/create/create'
    });
  }
});