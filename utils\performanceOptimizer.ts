/**
 * 性能优化工具
 * 2025年企业级性能优化系统
 */

import { monitoring } from './monitoring'
import { ModernUtils } from './modernUtils'

// 性能指标类型
interface PerformanceMetrics {
  fps: number
  memory: number
  loadTime: number
  renderTime: number
  apiResponseTime: number
  cacheHitRate: number
}

// 优化配置类型
interface OptimizationConfig {
  enableLazyLoading: boolean
  enableVirtualList: boolean
  enableImageOptimization: boolean
  enableRequestBatching: boolean
  enableMemoryOptimization: boolean
  maxConcurrentRequests: number
  cacheStrategy: 'aggressive' | 'moderate' | 'conservative'
}

class PerformanceOptimizer {
  private config: OptimizationConfig
  private metrics: PerformanceMetrics
  private observers: Map<string, any> = new Map()
  private requestQueue: Array<() => Promise<any>> = []
  private isProcessingQueue = false

  constructor(config: Partial<OptimizationConfig> = {}) {
    this.config = {
      enableLazyLoading: true,
      enableVirtualList: true,
      enableImageOptimization: true,
      enableRequestBatching: true,
      enableMemoryOptimization: true,
      maxConcurrentRequests: 5,
      cacheStrategy: 'moderate',
      ...config
    }

    this.metrics = {
      fps: 60,
      memory: 0,
      loadTime: 0,
      renderTime: 0,
      apiResponseTime: 0,
      cacheHitRate: 0
    }

    this.init()
  }

  /**
   * 初始化性能优化器
   */
  private init(): void {
    // 启动性能监控
    this.startPerformanceMonitoring()
    
    // 设置图片懒加载
    if (this.config.enableLazyLoading) {
      this.setupLazyLoading()
    }
    
    // 设置内存优化
    if (this.config.enableMemoryOptimization) {
      this.setupMemoryOptimization()
    }
    
    // 设置请求批处理
    if (this.config.enableRequestBatching) {
      this.setupRequestBatching()
    }

    console.log('⚡ Performance Optimizer initialized')
  }

  /**
   * 启动性能监控
   */
  private startPerformanceMonitoring(): void {
    // 监控FPS
    this.monitorFPS()
    
    // 监控内存使用
    this.monitorMemoryUsage()
    
    // 监控页面加载时间
    this.monitorPageLoadTime()
    
    // 定期收集性能指标
    setInterval(() => {
      this.collectPerformanceMetrics()
    }, 30000) // 30秒
  }

  /**
   * 监控FPS
   */
  private monitorFPS(): void {
    let lastTime = Date.now()
    let frameCount = 0
    
    const measureFPS = () => {
      frameCount++
      const currentTime = Date.now()
      
      if (currentTime - lastTime >= 1000) {
        this.metrics.fps = frameCount
        frameCount = 0
        lastTime = currentTime
        
        // FPS过低时触发优化
        if (this.metrics.fps < 30) {
          this.handleLowFPS()
        }
      }
      
      requestAnimationFrame(measureFPS)
    }
    
    requestAnimationFrame(measureFPS)
  }

  /**
   * 监控内存使用
   */
  private monitorMemoryUsage(): void {
    setInterval(() => {
      try {
        // 小程序环境下获取内存信息
        wx.getSystemInfo({
          success: (res) => {
            // 估算内存使用情况
            const memoryInfo = res as any
            if (memoryInfo.memorySize) {
              this.metrics.memory = memoryInfo.memorySize
            }
          }
        })
      } catch (error) {
        console.warn('获取内存信息失败:', error)
      }
    }, 10000) // 10秒
  }

  /**
   * 监控页面加载时间
   */
  private monitorPageLoadTime(): void {
    const startTime = Date.now()
    
    // 页面加载完成后计算加载时间
    wx.onAppShow?.(() => {
      const loadTime = Date.now() - startTime
      this.metrics.loadTime = loadTime
      
      monitoring.trackPerformance({
        type: 'page_load',
        name: 'app_show',
        duration: loadTime,
        success: true
      })
    })
  }

  /**
   * 设置图片懒加载
   */
  private setupLazyLoading(): void {
    // 创建Intersection Observer
    const observer = wx.createIntersectionObserver?.()
    
    if (!observer) return
    
    observer.observe('.lazy-image', (res) => {
      if (res.intersectionRatio > 0) {
        const target = res.target as any
        const dataset = target.dataset
        
        if (dataset.src && !target.src) {
          // 加载图片
          this.loadImageOptimized(dataset.src).then(optimizedSrc => {
            target.src = optimizedSrc
            target.classList?.remove('lazy-image')
          })
        }
        
        observer.unobserve(target)
      }
    })
    
    this.observers.set('lazyLoading', observer)
  }

  /**
   * 优化图片加载
   */
  private async loadImageOptimized(src: string): Promise<string> {
    if (!this.config.enableImageOptimization) {
      return src
    }

    try {
      // 检查是否为本地图片
      if (src.startsWith('/') || src.startsWith('./')) {
        return src
      }

      // 对于网络图片，添加压缩参数
      const url = new URL(src)
      
      // 根据设备像素比调整图片质量
      const pixelRatio = wx.getSystemInfoSync().pixelRatio || 1
      const quality = pixelRatio > 2 ? 80 : 90
      
      url.searchParams.set('quality', quality.toString())
      url.searchParams.set('format', 'webp')
      
      return url.toString()
    } catch (error) {
      console.warn('图片优化失败:', error)
      return src
    }
  }

  /**
   * 设置内存优化
   */
  private setupMemoryOptimization(): void {
    // 定期清理缓存
    setInterval(() => {
      this.cleanupMemory()
    }, 60000) // 1分钟
    
    // 监听内存警告
    wx.onMemoryWarning?.(() => {
      console.warn('⚠️ 内存警告，执行紧急清理')
      this.emergencyMemoryCleanup()
    })
  }

  /**
   * 清理内存
   */
  private cleanupMemory(): void {
    try {
      // 清理过期缓存
      const cacheKeys = wx.getStorageInfoSync().keys
      const expiredKeys = cacheKeys.filter(key => {
        try {
          const item = wx.getStorageSync(key)
          if (item && item.expiry && Date.now() > item.expiry) {
            return true
          }
        } catch (error) {
          return true // 损坏的缓存项也清理
        }
        return false
      })
      
      expiredKeys.forEach(key => {
        try {
          wx.removeStorageSync(key)
        } catch (error) {
          console.warn(`清理缓存失败: ${key}`, error)
        }
      })
      
      if (expiredKeys.length > 0) {
        console.log(`🧹 清理了 ${expiredKeys.length} 个过期缓存`)
      }
      
      // 清理监控数据
      monitoring.clearLocalData()
      
    } catch (error) {
      console.warn('内存清理失败:', error)
    }
  }

  /**
   * 紧急内存清理
   */
  private emergencyMemoryCleanup(): void {
    try {
      // 清理所有非必要缓存
      const cacheKeys = wx.getStorageInfoSync().keys
      const nonEssentialKeys = cacheKeys.filter(key => 
        !key.startsWith('user_') && 
        !key.startsWith('auth_') &&
        !key.startsWith('store_')
      )
      
      nonEssentialKeys.forEach(key => {
        try {
          wx.removeStorageSync(key)
        } catch (error) {
          // 忽略清理错误
        }
      })
      
      // 强制垃圾回收（如果支持）
      if (typeof gc === 'function') {
        gc()
      }
      
      monitoring.trackEvent('emergency_memory_cleanup', {
        clearedKeys: nonEssentialKeys.length
      })
      
    } catch (error) {
      console.error('紧急内存清理失败:', error)
    }
  }

  /**
   * 设置请求批处理
   */
  private setupRequestBatching(): void {
    // 定期处理请求队列
    setInterval(() => {
      if (!this.isProcessingQueue && this.requestQueue.length > 0) {
        this.processRequestQueue()
      }
    }, 100) // 100ms
  }

  /**
   * 添加请求到队列
   */
  addRequestToQueue<T>(request: () => Promise<T>): Promise<T> {
    return new Promise((resolve, reject) => {
      this.requestQueue.push(async () => {
        try {
          const result = await request()
          resolve(result)
        } catch (error) {
          reject(error)
        }
      })
    })
  }

  /**
   * 处理请求队列
   */
  private async processRequestQueue(): Promise<void> {
    if (this.isProcessingQueue || this.requestQueue.length === 0) {
      return
    }

    this.isProcessingQueue = true
    
    try {
      // 批量处理请求
      const batchSize = Math.min(this.config.maxConcurrentRequests, this.requestQueue.length)
      const batch = this.requestQueue.splice(0, batchSize)
      
      await Promise.all(batch.map(request => request()))
      
    } catch (error) {
      console.warn('请求批处理失败:', error)
    } finally {
      this.isProcessingQueue = false
    }
  }

  /**
   * 创建虚拟列表
   */
  createVirtualList(options: {
    itemHeight: number
    containerHeight: number
    data: any[]
    renderItem: (item: any, index: number) => any
  }) {
    if (!this.config.enableVirtualList) {
      return options.data.map(options.renderItem)
    }

    const { itemHeight, containerHeight, data, renderItem } = options
    const visibleCount = Math.ceil(containerHeight / itemHeight) + 2 // 缓冲区
    
    return {
      getVisibleItems(scrollTop: number) {
        const startIndex = Math.floor(scrollTop / itemHeight)
        const endIndex = Math.min(startIndex + visibleCount, data.length)
        
        return data.slice(startIndex, endIndex).map((item, index) => 
          renderItem(item, startIndex + index)
        )
      },
      
      getTotalHeight() {
        return data.length * itemHeight
      },
      
      getItemOffset(index: number) {
        return index * itemHeight
      }
    }
  }

  /**
   * 防抖优化的事件处理器
   */
  createDebouncedHandler<T extends (...args: any[]) => any>(
    handler: T,
    delay = 300
  ): T {
    return ModernUtils.debounce(handler, delay) as T
  }

  /**
   * 节流优化的事件处理器
   */
  createThrottledHandler<T extends (...args: any[]) => any>(
    handler: T,
    limit = 100
  ): T {
    return ModernUtils.throttle(handler, limit) as T
  }

  /**
   * 处理低FPS情况
   */
  private handleLowFPS(): void {
    console.warn('⚠️ 检测到低FPS，启动性能优化')
    
    // 降低动画质量
    this.reduceAnimationQuality()
    
    // 清理内存
    this.cleanupMemory()
    
    // 减少并发请求
    this.config.maxConcurrentRequests = Math.max(2, this.config.maxConcurrentRequests - 1)
    
    monitoring.trackEvent('low_fps_optimization', {
      fps: this.metrics.fps,
      actions: ['reduce_animation', 'cleanup_memory', 'reduce_concurrency']
    })
  }

  /**
   * 降低动画质量
   */
  private reduceAnimationQuality(): void {
    // 禁用复杂动画
    const style = document.createElement?.('style')
    if (style) {
      style.textContent = `
        * {
          animation-duration: 0.1s !important;
          transition-duration: 0.1s !important;
        }
      `
      document.head?.appendChild(style)
    }
  }

  /**
   * 收集性能指标
   */
  private collectPerformanceMetrics(): void {
    const metrics = {
      ...this.metrics,
      timestamp: Date.now(),
      config: this.config
    }
    
    monitoring.trackEvent('performance_metrics', metrics)
    
    // 性能异常检测
    if (this.metrics.fps < 30 || this.metrics.memory > 100) {
      monitoring.captureError({
        message: '性能异常检测',
        category: 'performance',
        level: 'warning',
        context: metrics
      })
    }
  }

  /**
   * 获取性能报告
   */
  getPerformanceReport(): {
    metrics: PerformanceMetrics
    config: OptimizationConfig
    recommendations: string[]
  } {
    const recommendations: string[] = []
    
    if (this.metrics.fps < 30) {
      recommendations.push('FPS过低，建议减少动画效果')
    }
    
    if (this.metrics.memory > 100) {
      recommendations.push('内存使用过高，建议清理缓存')
    }
    
    if (this.metrics.loadTime > 3000) {
      recommendations.push('页面加载时间过长，建议优化资源')
    }
    
    if (this.metrics.apiResponseTime > 2000) {
      recommendations.push('API响应时间过长，建议优化接口')
    }
    
    return {
      metrics: this.metrics,
      config: this.config,
      recommendations
    }
  }

  /**
   * 更新配置
   */
  updateConfig(newConfig: Partial<OptimizationConfig>): void {
    this.config = { ...this.config, ...newConfig }
    
    monitoring.trackEvent('performance_config_updated', {
      config: this.config
    })
  }

  /**
   * 销毁优化器
   */
  destroy(): void {
    // 清理观察器
    this.observers.forEach(observer => {
      if (observer && typeof observer.disconnect === 'function') {
        observer.disconnect()
      }
    })
    this.observers.clear()
    
    // 清空请求队列
    this.requestQueue.length = 0
    
    console.log('⚡ Performance Optimizer destroyed')
  }
}

// 创建全局性能优化器实例
const performanceOptimizer = new PerformanceOptimizer()

export { PerformanceOptimizer, performanceOptimizer }
export type { PerformanceMetrics, OptimizationConfig }
