import { useState, useEffect, useCallback, useMemo } from 'react'
import useSWR from 'swr'

// 优化的数据获取Hook
export const useOptimizedData = <T>(
  key: string,
  fetcher: () => Promise<T>,
  options?: {
    refreshInterval?: number
    revalidateOnFocus?: boolean
    dedupingInterval?: number
  }
) => {
  const {
    data,
    error,
    isLoading,
    mutate
  } = useSWR(key, fetcher, {
    refreshInterval: options?.refreshInterval || 30000, // 30秒刷新
    revalidateOnFocus: options?.revalidateOnFocus ?? false,
    dedupingInterval: options?.dedupingInterval || 2000, // 2秒内去重
    errorRetryCount: 3,
    errorRetryInterval: 1000
  })

  return {
    data,
    error,
    loading: isLoading,
    refresh: mutate
  }
}

// 虚拟化列表Hook
export const useVirtualList = <T>(
  items: T[],
  itemHeight: number = 80,
  containerHeight: number = 600
) => {
  const [scrollTop, setScrollTop] = useState(0)
  
  const visibleItems = useMemo(() => {
    const startIndex = Math.floor(scrollTop / itemHeight)
    const endIndex = Math.min(
      startIndex + Math.ceil(containerHeight / itemHeight) + 1,
      items.length
    )
    
    return {
      items: items.slice(startIndex, endIndex),
      startIndex,
      endIndex,
      totalHeight: items.length * itemHeight,
      offsetY: startIndex * itemHeight
    }
  }, [items, itemHeight, containerHeight, scrollTop])

  return {
    visibleItems,
    setScrollTop,
    containerHeight,
    itemHeight
  }
}

// 防抖Hook
export const useDebounce = <T>(value: T, delay: number): T => {
  const [debouncedValue, setDebouncedValue] = useState<T>(value)

  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedValue(value)
    }, delay)

    return () => {
      clearTimeout(handler)
    }
  }, [value, delay])

  return debouncedValue
}

// 性能监控Hook
export const usePerformanceMonitor = (pageName: string) => {
  useEffect(() => {
    // 记录页面加载时间
    const startTime = performance.now()
    
    const observer = new PerformanceObserver((list) => {
      for (const entry of list.getEntries()) {
        if (entry.entryType === 'measure') {
          console.log(`[Performance] ${pageName} - ${entry.name}: ${entry.duration}ms`)
        }
      }
    })
    
    observer.observe({ entryTypes: ['measure'] })
    
    return () => {
      const endTime = performance.now()
      performance.mark(`${pageName}-end`)
      performance.measure(`${pageName}-total`, `${pageName}-start`, `${pageName}-end`)
      console.log(`[Performance] ${pageName} total time: ${endTime - startTime}ms`)
      observer.disconnect()
    }
  }, [pageName])
  
  const measureAction = useCallback((actionName: string, action: () => void) => {
    const start = performance.now()
    performance.mark(`${actionName}-start`)
    
    action()
    
    const end = performance.now()
    performance.mark(`${actionName}-end`)
    performance.measure(actionName, `${actionName}-start`, `${actionName}-end`)
    
    console.log(`[Performance] ${actionName}: ${end - start}ms`)
  }, [])
  
  return { measureAction }
}