# 学生批量导入功能说明

## 📋 功能概述

学生批量导入功能允许教师通过Excel或CSV文件一次性导入多名学生信息，大大提高了数据录入效率。

## 🚀 使用步骤

### 1. 下载模板
- 在"添加学生"页面切换到"批量导入"模式
- 选择下载Excel模板或CSV模板
- 推荐使用Excel模板，支持更多字段和更好的格式控制

### 2. 填写学生信息
按照模板格式填写学生信息：

| 字段名称 | 是否必填 | 格式要求 | 示例 |
|---------|---------|---------|------|
| 姓名 | ✅ 必填 | 1-20个字符 | 张小明 |
| 学号 | ✅ 必填 | 1-20个字符，不能重复 | 2023001 |
| 班级 | ✅ 必填 | 必须与系统中已有班级完全一致 | 三年级一班 |
| 性别 | 可选 | 只能填写"男"或"女" | 男 |
| 联系电话 | 可选 | 11位手机号码 | 13800138001 |
| 备注 | 可选 | 最多200个字符 | 学习认真，积极向上 |

### 3. 上传文件
- 点击上传区域选择填写好的文件
- 支持.xlsx和.csv格式
- 文件大小不超过5MB

### 4. 预览数据
- 系统会自动解析文件内容
- 显示数据预览和错误检查结果
- 红色标记的行表示有错误，需要修正

### 5. 确认导入
- 检查预览数据无误后点击"导入学生"
- 系统会显示导入进度和结果

## ⚠️ 注意事项

### 数据要求
1. **学号唯一性**：同一批次导入的学号不能重复，也不能与系统中已有学号重复
2. **班级匹配**：班级名称必须与系统中已创建的班级完全一致
3. **数据完整性**：姓名、学号、班级为必填项，不能为空
4. **格式规范**：严格按照模板格式填写，避免多余的空格或特殊字符

### 限制说明
- 单次最多导入100名学生
- 支持的文件格式：.xlsx、.csv
- 文件大小限制：5MB以内
- 导入过程中请勿关闭页面

### 常见错误及解决方案

| 错误类型 | 原因 | 解决方案 |
|---------|------|---------|
| 姓名不能为空 | 姓名字段为空或只有空格 | 填写完整的学生姓名 |
| 学号重复 | 同一文件中有重复学号 | 检查并修改重复的学号 |
| 班级不存在 | 班级名称与系统不匹配 | 确认班级名称拼写正确 |
| 电话格式错误 | 手机号码格式不正确 | 填写11位有效手机号 |

## 📝 模板示例

### Excel模板内容
```
姓名*    学号*     班级*      性别  联系电话     备注
张小明   2023001   三年级一班  男   13800138001  学习认真
李小红   2023002   三年级一班  女   13800138002  活泼开朗
王小华   2023003   三年级二班  男   13800138003  积极向上
```

### CSV模板内容
```csv
姓名*,学号*,班级*,性别,联系电话,备注
张小明,2023001,三年级一班,男,13800138001,学习认真
李小红,2023002,三年级一班,女,13800138002,活泼开朗
王小华,2023003,三年级二班,男,13800138003,积极向上
```

## 🔧 技术支持

如果在使用过程中遇到问题，请：
1. 检查文件格式是否正确
2. 确认数据填写是否符合要求
3. 联系技术支持获取帮助

## 📈 最佳实践

1. **分批导入**：建议每次导入不超过50名学生，确保稳定性
2. **数据备份**：导入前请备份原始数据文件
3. **测试导入**：可先用少量数据测试导入流程
4. **及时检查**：导入后及时检查学生信息是否正确
