/**
 * 生产环境部署脚本
 * 自动切换配置文件，清理开发文件，准备生产环境
 */

const fs = require('fs');
const path = require('path');

console.log('🚀 开始生产环境部署准备...');

// 1. 备份当前配置
console.log('📦 备份当前配置文件...');
try {
  if (fs.existsSync('app.json')) {
    fs.copyFileSync('app.json', 'app.dev.json');
    console.log('✅ app.json 已备份为 app.dev.json');
  }
  
  if (fs.existsSync('project.config.json')) {
    fs.copyFileSync('project.config.json', 'project.dev.config.json');
    console.log('✅ project.config.json 已备份为 project.dev.config.json');
  }
} catch (error) {
  console.error('❌ 备份配置文件失败:', error.message);
  process.exit(1);
}

// 2. 切换到生产环境配置
console.log('🔄 切换到生产环境配置...');
try {
  if (fs.existsSync('app.production.json')) {
    fs.copyFileSync('app.production.json', 'app.json');
    console.log('✅ 已切换到生产环境 app.json');
  }
  
  if (fs.existsSync('project.production.config.json')) {
    fs.copyFileSync('project.production.config.json', 'project.config.json');
    console.log('✅ 已切换到生产环境 project.config.json');
  }
} catch (error) {
  console.error('❌ 切换生产环境配置失败:', error.message);
  process.exit(1);
}

// 3. 检查文件大小
console.log('📊 检查关键文件大小...');
const checkFileSize = (filePath) => {
  if (fs.existsSync(filePath)) {
    const stats = fs.statSync(filePath);
    const sizeInMB = (stats.size / (1024 * 1024)).toFixed(2);
    console.log(`   ${filePath}: ${sizeInMB} MB`);
    return stats.size;
  }
  return 0;
};

// 检查主要文件
const mainFiles = [
  'app.js',
  'app.wxss',
  'app.json'
];

let totalSize = 0;
mainFiles.forEach(file => {
  totalSize += checkFileSize(file);
});

// 检查pages目录
const pagesDir = 'pages';
if (fs.existsSync(pagesDir)) {
  const getDirectorySize = (dirPath) => {
    let size = 0;
    const files = fs.readdirSync(dirPath);
    
    files.forEach(file => {
      const filePath = path.join(dirPath, file);
      const stats = fs.statSync(filePath);
      
      if (stats.isDirectory()) {
        size += getDirectorySize(filePath);
      } else {
        size += stats.size;
      }
    });
    
    return size;
  };
  
  const pagesSize = getDirectorySize(pagesDir);
  totalSize += pagesSize;
  console.log(`   pages目录: ${(pagesSize / (1024 * 1024)).toFixed(2)} MB`);
}

// 检查components目录
const componentsDir = 'components';
if (fs.existsSync(componentsDir)) {
  const componentsSize = getDirectorySize(componentsDir);
  totalSize += componentsSize;
  console.log(`   components目录: ${(componentsSize / (1024 * 1024)).toFixed(2)} MB`);
}

// 检查utils目录
const utilsDir = 'utils';
if (fs.existsSync(utilsDir)) {
  const utilsSize = getDirectorySize(utilsDir);
  totalSize += utilsSize;
  console.log(`   utils目录: ${(utilsSize / (1024 * 1024)).toFixed(2)} MB`);
}

const totalSizeInMB = (totalSize / (1024 * 1024)).toFixed(2);
console.log(`📦 预估总大小: ${totalSizeInMB} MB`);

if (totalSize > 2 * 1024 * 1024) {
  console.warn('⚠️  警告: 预估大小超过2MB，可能需要进一步优化');
} else {
  console.log('✅ 大小检查通过');
}

// 4. 生成部署检查清单
console.log('📋 生成部署检查清单...');
const checklist = `
# 生产环境部署检查清单

## 配置检查
- [x] 已切换到生产环境配置文件
- [ ] 云开发环境ID确认: cloud1-4g85f8xlb8166ff1
- [ ] 小程序AppID确认: wx3de03090b8e8a734
- [ ] 版本号更新

## 功能测试
- [ ] 用户登录功能
- [ ] AI评语生成功能
- [ ] 学生管理功能
- [ ] 数据同步功能
- [ ] 云函数调用正常

## 性能检查
- [x] 源码包大小: ${totalSizeInMB} MB
- [ ] 页面加载速度测试
- [ ] 网络请求超时设置

## 安全检查
- [ ] 敏感信息清理
- [ ] API密钥安全
- [ ] 用户数据保护

## 上线前最后检查
- [ ] 测试页面已移除
- [ ] 调试信息已关闭
- [ ] 错误处理完善
- [ ] 用户体验优化

生成时间: ${new Date().toLocaleString()}
`;

fs.writeFileSync('DEPLOYMENT_CHECKLIST.md', checklist);
console.log('✅ 部署检查清单已生成: DEPLOYMENT_CHECKLIST.md');

console.log('\n🎉 生产环境部署准备完成！');
console.log('📝 请查看 DEPLOYMENT_CHECKLIST.md 完成最后的检查');
console.log('🔧 在微信开发者工具中重新编译预览');

function getDirectorySize(dirPath) {
  let size = 0;
  const files = fs.readdirSync(dirPath);
  
  files.forEach(file => {
    const filePath = path.join(dirPath, file);
    const stats = fs.statSync(filePath);
    
    if (stats.isDirectory()) {
      size += getDirectorySize(filePath);
    } else {
      size += stats.size;
    }
  });
  
  return size;
}
