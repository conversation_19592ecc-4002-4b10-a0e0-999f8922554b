/**
 * 我的作品页面
 * 展示用户创建的所有评语作品，支持搜索、筛选、编辑等功能
 */
const app = getApp();

// 引入分享工具模块
const { shareExcelToWeChat, showShareOptions, generateBeautifulShareImage } = require('../../../utils/shareUtils');
// 安全加载智能分享功能
let autoShare = null;
try {
  const shareModule = require('../../../utils/smartShare');
  autoShare = shareModule.autoShare;
  console.log('[作品页面] 分享模块加载成功');
} catch (error) {
  console.warn('[作品页面] 分享模块加载失败:', error);
  autoShare = null;
}

// 获取云服务实例的辅助函数
function getCloudService() {
  const cloudService = app.globalData.cloudService;
  if (!cloudService) {
    throw new Error('云服务未初始化，请稍后重试');
  }
  return cloudService;
}

Page({
  /**
   * 页面的初始数据
   */
  data: {
    // 搜索关键词
    searchKeyword: '',
    
    // 当前筛选条件
    currentFilter: 'all',
    
    // 筛选标签
    filterTabs: [
      { value: 'all', name: '全部', count: 0 },
      { value: 'recent', name: '最近', count: 0 },
      { value: 'draft', name: '草稿', count: 0 },
      { value: 'published', name: '已发布', count: 0 }
    ],
    
    // 作品列表
    works: [],
    filteredWorks: [],
    
    // 统计数据
    totalWorks: 0,
    recentWorks: 0,
    
    // 页面状态
    loading: true,
    refreshing: false
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    // 检查是否是Excel文件分享进入
    if (options.excel && options.fileName) {
      this.handleExcelShare(decodeURIComponent(options.fileName));
    }
    
    // 检查是否是邀请码进入
    if (options.inviteCode) {
      this.handleInviteEntry(options.inviteCode, options.from);
    }
    
    // 检查是否是分享进入
    if (options.shareId) {
      this.handleShareEntry(options.shareId, options.type, options.from);
    }
    
    this.loadWorks();
  },

  /**
   * 处理邀请进入
   */
  handleInviteEntry(inviteCode, fromUser) {
    // 记录邀请转化
    let inviteStats = wx.getStorageSync('inviteStats') || {};
    if (!inviteStats[inviteCode]) {
      inviteStats[inviteCode] = {
        code: inviteCode,
        fromUser: fromUser,
        visitCount: 0,
        firstVisit: Date.now()
      };
    }
    inviteStats[inviteCode].visitCount++;
    inviteStats[inviteCode].lastVisit = Date.now();
    wx.setStorageSync('inviteStats', inviteStats);
    
    // 显示邀请欢迎信息
    setTimeout(() => {
      wx.showModal({
        title: '🎉 欢迎使用评语灵感君',
        content: `👥 ${fromUser || '好友'}邀请您体验评语灵感君！\n\n✨ 您将获得：\n• 每月15次免费AI评语生成\n• 专业教学工具访问权\n• 智能评语生成体验\n\n📱 立即体验3分钟生成专业评语！`,
        confirmText: '立即体验',
        cancelText: '稍后再说',
        success: (res) => {
          if (res.confirm) {
            wx.navigateTo({
              url: `/pages/comment/generate/generate?from=invite&inviteCode=${inviteCode}`
            });
          }
        }
      });
    }, 1000);
  },

  /**
   * 处理分享进入
   */
  handleShareEntry(shareId, shareType, fromSource) {
    // 记录分享数据
    let shareStats = wx.getStorageSync('shareStats') || {};
    const statKey = `${shareId}_${shareType || 'default'}`;
    
    if (!shareStats[statKey]) {
      shareStats[statKey] = {
        shareId: shareId,
        shareType: shareType,
        fromSource: fromSource,
        viewCount: 0,
        firstView: Date.now()
      };
    }
    shareStats[statKey].viewCount++;
    shareStats[statKey].lastView = Date.now();
    wx.setStorageSync('shareStats', shareStats);
    
    // 根据分享类型显示不同内容
    if (shareType === 'quality') {
      this.showQualityShareWelcome(shareId);
    } else {
      this.showGeneralShareWelcome(shareId, fromSource);
    }
  },

  /**
   * 显示优质分享欢迎
   */
  showQualityShareWelcome(shareId) {
    setTimeout(() => {
      wx.showModal({
        title: '⭐ 优质评语展示',
        content: '👍 您正在浏览一条优质评语作品！\n\n🎆 这条评语由评语灵感君AI生成，展示了专业的评语水准和个性化的内容设计。\n\n💡 您也可以免费使用相同的AI技术来生成属于您自己的优质评语！',
        confirmText: '免费体验',
        cancelText: '继续浏览',
        success: (res) => {
          if (res.confirm) {
            wx.navigateTo({
              url: `/pages/comment/generate/generate?from=quality_share&shareId=${shareId}`
            });
          }
        }
      });
    }, 800);
  },

  /**
   * 显示通用分享欢迎
   */
  showGeneralShareWelcome(shareId, fromSource) {
    setTimeout(() => {
      wx.showModal({
        title: '📝 评语作品分享',
        content: '🚀 感谢您查看这条评语作品！\n\n🤖 这条评语由评语灵感君智能生成，结合了学生的实际表现和个性化的表达方式。\n\n🎯 您可以使用相同的工具来为自己的学生生成专业评语，提高教学效率！',
        confirmText: '免费使用',
        cancelText: '了解更多',
        success: (res) => {
          if (res.confirm) {
            wx.navigateTo({
              url: `/pages/comment/generate/generate?from=general_share&shareId=${shareId}`
            });
          } else {
            wx.navigateTo({
              url: `/pages/index/index?from=share&source=${fromSource || 'works'}`
            });
          }
        }
      });
    }, 1000);
  },

  /**
   * 处理Excel文件分享 - 增强版
   */
  handleExcelShare(fileName) {
    // 记录Excel分享访问
    let excelShareStats = wx.getStorageSync('excelShareStats') || {};
    const accessKey = `${fileName}_${Date.now()}`;
    excelShareStats[accessKey] = {
      fileName: fileName,
      accessTime: Date.now(),
      fromShare: true
    };
    wx.setStorageSync('excelShareStats', excelShareStats);
    
    wx.showModal({
      title: '📊 Excel数据文件分享',
      content: `📁 收到专业Excel文件：${fileName}\n\n📊 文件内容：\n• 学生评语数据表\n• 详细的评分及分析\n• 专业的教学统计\n• 支持Excel全功能编辑\n\n🚀 您也可以：\n• 免费使用评语灵感君\n• 生成自己的专业评语\n• 导出高质量Excel报表\n\n💡 要获取完整数据，请联系分享者或体验生成功能`,
      confirmText: '免费体验',
      cancelText: '我知道了',
      success: (res) => {
        if (res.confirm) {
          wx.navigateTo({
            url: '/pages/comment/generate/generate?from=excel_share'
          });
        }
      }
    });
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    // 每次显示页面时刷新数据
    this.loadWorks();
  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {
    this.setData({ refreshing: true });
    this.loadWorks().finally(() => {
      this.setData({ refreshing: false });
      wx.stopPullDownRefresh();
    });
  },

  /**
   * 加载作品列表
   */
  async loadWorks() {
    try {
      this.setData({ loading: true });

      // 首先尝试从云数据库加载真实数据
      let works = [];
      let isRealData = false;

      try {
        const cloudService = getCloudService();
        const result = await cloudService.getCommentList({
          pageSize: 100, // 获取最近100条评语
          sortBy: 'createTime',
          sortOrder: 'desc'
        });

        if (result.success && result.data && result.data.comments) {
          works = result.data.comments.map(comment => this.formatCloudComment(comment));
          isRealData = true;
          console.log('从云数据库加载评语成功:', works.length, '条');
        } else {
          console.warn('云数据库查询失败，使用本地数据');
          works = this.loadLocalComments();
        }
      } catch (error) {
        console.warn('云数据库连接失败，使用本地数据:', error);
        works = this.loadLocalComments();
      }

      // 检查是否刚刚清空过数据
      const isDataJustCleared = wx.getStorageSync('dataJustCleared');
      
      // 如果没有数据，显示空状态
      if (works.length === 0) {
        console.log('没有找到评语数据，显示空状态');
        this.setData({
          works: [],
          filteredWorks: [],
          showEmptyState: true,
          emptyMessage: '暂无评语作品，快去生成第一条评语吧！'
        });
        return;
      }

      // 计算统计数据
      const totalWorks = works.length;
      const recentWorks = this.getRecentWorksCount(works);
      const avgScore = this.calculateAvgScore(works);
      const excellentRate = this.calculateExcellentRate(works);

      // 更新筛选标签计数
      const filterTabs = this.updateFilterCounts(works);

      console.log('[我的作品] 统计数据计算结果:', {
        totalWorks,
        recentWorks,
        avgScore,
        excellentRate,
        worksCount: works.length,
        dataSource: isRealData ? '真实数据' : '测试数据'
      });

      // 显示数据加载成功信息
      if (totalWorks > 0) {
        console.log('[我的作品] 数据加载成功 ✅', {
          '数据来源': isRealData ? '云数据库/本地存储' : '测试数据',
          '作品总数': totalWorks,
          '平均评分': avgScore,
          '优秀率': excellentRate
        });
      } else {
        console.log('[我的作品] 暂无作品数据 ℹ️');
      }

      this.setData({
        works,
        totalWorks,
        recentWorks,
        avgScore,
        excellentRate,
        filterTabs,
        isRealData
      });

      // 应用当前筛选条件
      this.applyFilter();

    } catch (error) {
      console.error('加载作品失败:', error);
      wx.showToast({
        title: '加载失败，请重试',
        icon: 'none'
      });
    } finally {
      this.setData({ loading: false });
    }
  },

  /**
   * 格式化云数据库评语数据
   */
  formatCloudComment(comment) {
    return {
      id: comment._id,
      studentName: comment.studentName,
      className: comment.className,
      content: comment.content,
      contentPreview: comment.content.length > 120 ? comment.content.substring(0, 120) + '...' : comment.content,
      score: comment.score || 8.5,
      wordCount: comment.content.length,
      createTime: comment.createTime || comment._createTime,
      createTimeText: this.formatTimeAgo(comment.createTime || comment._createTime),
      status: 'published',
      statusText: '已发布',
      tags: ['AI生成'],
      qualityLevel: this.getQualityLevel(comment.content),
      qualityText: this.getQualityText(comment.content)
    };
  },

  /**
   * 加载本地评语数据
   */
  loadLocalComments() {
    try {
      const savedComments = wx.getStorageSync('savedComments') || [];
      const recentComments = wx.getStorageSync('recentComments') || [];
      
      // 合并本地保存的评语数据
      const allComments = [...savedComments, ...recentComments];
      
      console.log('[我的作品] 加载本地评语数据:', {
        savedComments: savedComments.length,
        recentComments: recentComments.length,
        total: allComments.length
      });

      return allComments.map(comment => {
        const content = comment.content || comment.comment;
        const score = comment.score || this.calculateDefaultScore(content);
        
        return {
          id: comment.id || `local_${Date.now()}_${Math.random()}`,
          studentName: comment.studentName,
          className: comment.className,
          content: content,
          contentPreview: content.length > 120 ? content.substring(0, 120) + '...' : content,
          score: score,
          wordCount: content.length,
          createTime: comment.createTime || comment.generateTime || Date.now(),
          createTimeText: this.formatTimeAgo(comment.createTime || comment.generateTime || Date.now()),
          status: 'published',
          statusText: '已发布',
          tags: ['本地保存'],
          qualityLevel: this.getQualityLevel(content),
          qualityText: this.getQualityText(content)
        };
      });
    } catch (error) {
      console.error('加载本地评语失败:', error);
      return [];
    }
  },

  /**
   * 格式化时间为相对时间
   */
  formatTimeAgo(dateString) {
    try {
      const now = new Date();
      const date = new Date(dateString);
      const diffMs = now - date;
      const diffMins = Math.floor(diffMs / (1000 * 60));
      const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
      const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));

      if (diffMins < 1) return '刚刚';
      if (diffMins < 60) return `${diffMins}分钟前`;
      if (diffHours < 24) return `${diffHours}小时前`;
      if (diffDays < 7) return `${diffDays}天前`;
      return date.toLocaleDateString();
    } catch (error) {
      return '未知时间';
    }
  },

  /**
   * 获取质量等级
   */
  getQualityLevel(content) {
    if (!content) return 'quality-normal';
    const length = content.length;
    if (length >= 150) return 'quality-excellent';
    if (length >= 80) return 'quality-good';
    return 'quality-normal';
  },

  /**
   * 获取质量文本
   */
  getQualityText(content) {
    if (!content) return '一般';
    const length = content.length;
    if (length >= 150) return '优秀';
    if (length >= 80) return '良好';
    return '一般';
  },




  /**
   * 计算平均评分
   */
  calculateAvgScore(works) {
    if (works.length === 0) return '0.0';
    const totalScore = works.reduce((sum, work) => sum + (work.score || 0), 0);
    return (totalScore / works.length).toFixed(1);
  },

  /**
   * 计算优秀率（评分≥8.0为优秀）
   */
  calculateExcellentRate(works) {
    if (works.length === 0) return '0%';
    
    const excellentCount = works.filter(work => {
      const score = work.score || 0;
      return score >= 8.0;
    }).length;
    
    const rate = (excellentCount / works.length * 100).toFixed(0);
    return `${rate}%`;
  },

  /**
   * 计算默认评分（用于没有评分的历史数据）
   */
  calculateDefaultScore(content) {
    if (!content || typeof content !== 'string') {
      return 7.5; // 默认评分
    }

    let score = 7.5; // 基础分
    const length = content.length;

    // 基于长度的简单评分
    if (length >= 120) {
      score = 8.5; // 长内容
    } else if (length >= 80) {
      score = 8.0; // 中等内容
    } else if (length >= 50) {
      score = 7.8; // 较短内容
    }

    // 基于关键词的简单调整
    const positiveWords = ['优秀', '良好', '积极', '认真', '努力', '进步'];
    const positiveCount = positiveWords.filter(word => content.includes(word)).length;
    score += positiveCount * 0.2;

    // 确保评分在合理范围内
    return Math.min(Math.max(score, 6.0), 9.5);
  },

  /**
   * 格式化作品项
   */
  formatWorkItem(record) {
    const createTime = new Date(record.createTime);
    const now = new Date();
    const diffTime = now - createTime;
    const diffHours = Math.floor(diffTime / (1000 * 60 * 60));
    const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24));

    let createTimeText = '';
    if (diffHours < 1) {
      createTimeText = '刚刚';
    } else if (diffHours < 24) {
      createTimeText = `${diffHours}小时前`;
    } else if (diffDays === 1) {
      createTimeText = '昨天';
    } else if (diffDays < 7) {
      createTimeText = `${diffDays}天前`;
    } else {
      createTimeText = createTime.toLocaleDateString();
    }

    // 生成内容预览
    const content = record.recordContent || record.content || '';
    const contentPreview = content.length > 120
      ? content.substring(0, 120) + '...'
      : content;

    // 生成标签
    const tags = [];
    if (record.behaviorType) {
      tags.push(this.getBehaviorTypeName(record.behaviorType));
    }
    if (record.score >= 9) {
      tags.push('优秀');
    } else if (record.score >= 8) {
      tags.push('良好');
    } else if (record.score >= 7) {
      tags.push('合格');
    }

    return {
      id: record._id || record.id,
      studentName: record.studentName,
      className: record.className,
      content: content,
      contentPreview,
      score: record.score,
      wordCount: content.length,
      createTime: record.createTime,
      createTimeText,
      status: record.status || 'published',
      statusText: this.getStatusText(record.status || 'published'),
      tags
    };
  },

  /**
   * 获取行为类型名称
   */
  getBehaviorTypeName(type) {
    const typeMap = {
      'positive': '积极表现',
      'learning': '学习态度',
      'social': '社交能力',
      'creative': '创新思维',
      'discipline': '纪律表现',
      'other': '其他表现'
    };
    return typeMap[type] || '一般表现';
  },

  /**
   * 获取状态文本
   */
  getStatusText(status) {
    const statusMap = {
      'draft': '草稿',
      'published': '已发布',
      'shared': '已分享'
    };
    return statusMap[status] || '已发布';
  },

  /**
   * 获取最近作品数量
   */
  getRecentWorksCount(works) {
    const oneWeekAgo = new Date();
    oneWeekAgo.setDate(oneWeekAgo.getDate() - 7);
    
    return works.filter(work => {
      const createTime = new Date(work.createTime);
      return createTime > oneWeekAgo;
    }).length;
  },

  /**
   * 更新筛选标签计数
   */
  updateFilterCounts(works) {
    const oneWeekAgo = new Date();
    oneWeekAgo.setDate(oneWeekAgo.getDate() - 7);
    
    return [
      { value: 'all', name: '全部', count: works.length },
      { 
        value: 'recent', 
        name: '最近', 
        count: works.filter(work => new Date(work.createTime) > oneWeekAgo).length 
      },
      { 
        value: 'draft', 
        name: '草稿', 
        count: works.filter(work => work.status === 'draft').length 
      },
      { 
        value: 'published', 
        name: '已发布', 
        count: works.filter(work => work.status === 'published').length 
      }
    ];
  },

  /**
   * 搜索输入
   */
  onSearchInput(e) {
    const searchKeyword = e.detail.value;
    this.setData({ searchKeyword });
    
    // 防抖搜索
    clearTimeout(this.searchTimer);
    this.searchTimer = setTimeout(() => {
      this.applyFilter();
    }, 300);
  },

  /**
   * 筛选条件改变
   */
  onFilterChange(e) {
    const filter = e.currentTarget.dataset.filter;
    this.setData({ currentFilter: filter });
    this.applyFilter();
  },

  /**
   * 应用筛选条件
   */
  applyFilter() {
    const { works, searchKeyword, currentFilter } = this.data;
    
    let filteredWorks = [...works];
    
    // 应用搜索
    if (searchKeyword.trim()) {
      filteredWorks = filteredWorks.filter(work => 
        work.studentName.includes(searchKeyword) ||
        work.content.includes(searchKeyword) ||
        work.className.includes(searchKeyword)
      );
    }
    
    // 应用筛选
    if (currentFilter !== 'all') {
      if (currentFilter === 'recent') {
        const oneWeekAgo = new Date();
        oneWeekAgo.setDate(oneWeekAgo.getDate() - 7);
        filteredWorks = filteredWorks.filter(work => 
          new Date(work.createTime) > oneWeekAgo
        );
      } else {
        filteredWorks = filteredWorks.filter(work => 
          work.status === currentFilter
        );
      }
    }
    
    this.setData({ filteredWorks });
  },


  /**
   * 分享作品
   */
  /**
   * 分享作品 - 包含分享图片、复制内容、生成分享链接
   */
  onShareWork(e) {
    const work = e.currentTarget.dataset.work;

    if (!work) {
      wx.showToast({
        title: '获取作品信息失败',
        icon: 'none'
      });
      return;
    }

    wx.showActionSheet({
      itemList: ['分享图片', '分享链接'],
      success: (res) => {
        switch (res.tapIndex) {
          case 0:
            this.generateShareImage(work);
            break;
          case 1:
            this.generateShareLink(work);
            break;
        }
      }
    });
  },

  /**
   * 编辑作品
   */
  onEditWork(e) {
    console.log('编辑按钮被点击', e);
    const work = e.currentTarget.dataset.work;
    console.log('编辑的作品数据:', work);

    if (!work) {
      wx.showToast({
        title: '获取作品信息失败',
        icon: 'none'
      });
      return;
    }

    // 显示编辑选项 - 只保留编辑内容和删除
    wx.showActionSheet({
      itemList: ['编辑评语内容', '删除这条评语'],
      success: (res) => {
        switch (res.tapIndex) {
          case 0:
            this.editWorkContent(work);
            break;
          case 1:
            this.deleteWork(work);
            break;
        }
      }
    });
  },

  /**
   * 导出作品 - 导出Excel并分享到微信
   */
  onExportWork(e) {
    const work = e.currentTarget.dataset.work;

    if (!work) {
      wx.showToast({
        title: '获取作品信息失败',
        icon: 'none'
      });
      return;
    }

    // 导出为Excel格式并分享到微信
    this.exportAsExcel(work);
  },

  /**
   * 导出为Excel格式 - 增强版
   */
  exportAsExcel(work) {
    wx.showActionSheet({
      itemList: ['标准Excel格式', '文本格式'],
      success: (res) => {
        switch (res.tapIndex) {
          case 0:
            this.exportStandardExcel(work);
            break;
          case 1:
            this.exportAsText(work);
            break;
        }
      }
    });
  },

  /**
   * 导出标准Excel格式
   */
  exportStandardExcel(work) {
    const data = [work];
    const options = {
      fileName: `${work.studentName}_评语_${new Date().toLocaleDateString('zh-CN').replace(/\//g, '-')}.xlsx`,
      headers: ['学生姓名', '班级', '评语内容', '评分', '字数', '创建时间', '质量等级', '标签', '状态'],
      title: `${work.studentName}的评语数据`,
      formatRow: (item) => [
        item.studentName || '',
        item.className || '',
        (item.content || '').replace(/[\r\n]/g, ' '),
        item.score || '未评分',
        item.wordCount || 0,
        item.createTimeText || '',
        item.qualityText || '一般',
        (item.tags || []).join('，'),
        item.statusText || ''
      ]
    };

    shareExcelToWeChat(data, options);
  },

  /**
   * 导出专业报告格式
   */
  exportProfessionalReport(work) {
    const reportContent = `评语灵感君 - 学生评语报告\n${'='.repeat(50)}\n\n📋 基本信息\n学生姓名：${work.studentName || ''}\n所属班级：${work.className || ''}\n生成时间：${work.createTimeText || ''}\n\n📝 评语内容\n━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n${work.content || ''}\n━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n\n📊 质量分析\n评语评分：${work.score || '未评分'}分\n质量等级：${work.qualityText || '一般'}\n字数统计：${work.wordCount || 0}字\n内容标签：${(work.tags || []).join('、')}\n\n📈 数据统计\n状态信息：${work.statusText || ''}\n创建时间：${new Date(work.createTime).toLocaleString('zh-CN') || ''}\n\n${'='.repeat(50)}\n报告生成时间：${new Date().toLocaleString('zh-CN')}\n由 评语灵感君 v1.0 生成`;
    
    const fileName = `${work.studentName}_专业评语报告_${new Date().toLocaleDateString('zh-CN').replace(/\//g, '-')}.txt`;
    
    wx.setClipboardData({
      data: reportContent,
      success: () => {
        wx.showModal({
          title: '📊 专业报告已生成',
          content: `${work.studentName}的专业评语报告已复制到剪贴板！\n\n📄 报告特点：\n• 完整的评语分析\n• 详细的质量评估\n• 专业的格式排版\n• 可直接使用的文档\n\n💡 建议粘贴到Word或记事本中保存为：${fileName}`,
          showCancel: false,
          confirmText: '我知道了'
        });
      },
      fail: () => {
        wx.showToast({
          title: '导出失败，请重试',
          icon: 'none'
        });
      }
    });
  },

  /**
   * 导出精美表格格式
   */
  exportStyledExcel(work) {
    // 生成带样式的Excel内容
    let styledContent = '\uFEFF'; // BOM
    
    // 标题行
    styledContent += `评语灵感君 - ${work.studentName} 评语详情\n\n`;
    
    // 表格头部
    styledContent += '项目\t内容\t备注\n';
    styledContent += '━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n';
    
    // 表格内容
    styledContent += `学生姓名\t${work.studentName || ''}\t${work.className || ''}班级学生\n`;
    styledContent += `创建时间\t${work.createTimeText || ''}\t系统自动记录\n`;
    styledContent += `评语评分\t${work.score || '未评分'}分\t${work.qualityText || '一般'}质量\n`;
    styledContent += `字数统计\t${work.wordCount || 0}字\t内容长度适中\n`;
    styledContent += `内容标签\t${(work.tags || []).join('、')}\t分类标识\n`;
    styledContent += `发布状态\t${work.statusText || ''}\t当前状态\n`;
    styledContent += '━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n';
    styledContent += `评语内容\t${work.content || ''}\t完整评语文本\n`;
    styledContent += '━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n';
    styledContent += `生成时间\t${new Date().toLocaleString('zh-CN')}\t报告导出时间\n`;
    
    const fileName = `${work.studentName}_精美评语表格_${new Date().toLocaleDateString('zh-CN').replace(/\//g, '-')}.xlsx`;
    
    this.copyExcelToClipboard(styledContent, fileName, '精美表格格式');
  },

  /**
   * 导出简化文本格式
   */
  exportSimpleText(work) {
    const simpleContent = `${work.studentName}（${work.className}）\n${work.content}\n\n评分：${work.score || '未评分'} | 字数：${work.wordCount || 0} | 时间：${work.createTimeText || ''}`;
    
    wx.setClipboardData({
      data: simpleContent,
      success: () => {
        wx.showToast({
          title: '简化文本已复制',
          icon: 'success'
        });
      },
      fail: () => {
        wx.showToast({
          title: '复制失败，请重试',
          icon: 'none'
        });
      }
    });
  },

  /**
   * 复制Excel内容到剪贴板
   */
  copyExcelToClipboard(content, fileName, formatType) {
    wx.setClipboardData({
      data: content,
      success: () => {
        wx.showModal({
          title: '📊 Excel导出成功',
          content: `${formatType}已生成并复制到剪贴板！\n\n📁 建议文件名：\n${fileName}\n\n💡 使用方法：\n1. 打开Excel或WPS表格\n2. 新建工作表\n3. 直接粘贴（Ctrl+V）\n4. 保存文件\n\n✨ 格式特点：\n• 自动识别中文编码\n• 保持表格结构完整\n• 支持Excel全功能编辑`,
          showCancel: false,
          confirmText: '我知道了'
        });
      },
      fail: () => {
        wx.showToast({
          title: '导出失败，请重试',
          icon: 'none'
        });
      }
    });
  },

  /**
   * 导出为文本文件
   */
  exportAsText(work) {
    const content = `评语导出\n\n学生：${work.studentName}\n班级：${work.className}\n创建时间：${work.createTimeText}\n评分：${work.score || '未评分'}\n字数：${work.wordCount}字\n\n评语内容：\n${work.content}\n\n---\n由AI评语助手生成`;
    
    wx.setClipboardData({
      data: content,
      success: () => {
        wx.showToast({
          title: '评语已复制到剪贴板',
          icon: 'success'
        });
      },
      fail: () => {
        wx.showToast({
          title: '导出失败，请重试',
          icon: 'none'
        });
      }
    });
  },

  /**
   * 编辑评语内容
   */
  editWorkContent(work) {
    wx.navigateTo({
      url: `/pages/works/edit/edit?id=${work.id}&studentName=${work.studentName}&content=${encodeURIComponent(work.content)}`
    });
  },

  /**
   * 清空所有评语
   */
  onClearAllWorks() {
    console.log('清空按钮被点击');
    
    if (this.data.totalWorks === 0) {
      wx.showToast({
        title: '没有评语需要清空',
        icon: 'none'
      });
      return;
    }

    wx.showModal({
      title: '⚠️ 危险操作',
      content: `确定要清空所有 ${this.data.totalWorks} 条评语吗？\n\n此操作不可撤销，清空后将无法恢复！`,
      confirmText: '确定清空',
      confirmColor: '#EE6666',
      cancelText: '取消',
      success: (res) => {
        if (res.confirm) {
          this.performClearAll();
        }
      }
    });
  },

  /**
   * 执行清空所有评语操作
   */
  async performClearAll() {
    try {
      wx.showLoading({
        title: '清空中...',
        mask: true
      });

      // 清空云数据库数据
      try {
        const cloudService = getCloudService();
        const result = await cloudService.clearAllComments();
        if (result.success) {
          console.log('云数据库清空成功');
        } else {
          console.warn('云数据库清空失败:', result.error);
        }
      } catch (error) {
        console.warn('云数据库操作失败:', error);
      }

      // 清空本地存储数据
      try {
        wx.removeStorageSync('savedComments');
        wx.removeStorageSync('recentComments');
        wx.removeStorageSync('generatedComments');
        console.log('本地存储清空成功');
      } catch (error) {
        console.error('清空本地存储失败:', error);
      }

      // 设置清空标记，防止重新加载测试数据
      wx.setStorageSync('dataJustCleared', true);

      // 等待一段时间以显示进度
      await new Promise(resolve => setTimeout(resolve, 1000));

      // 更新页面数据
      this.setData({
        works: [],
        filteredWorks: [],
        totalWorks: 0,
        recentWorks: 0,
        avgScore: '0.0',
        excellentRate: '0%', // 添加优秀率重置
        filterTabs: [
          { value: 'all', name: '全部', count: 0 },
          { value: 'recent', name: '最近', count: 0 },
          { value: 'draft', name: '草稿', count: 0 },
          { value: 'published', name: '已发布', count: 0 }
        ],
        isRealData: false
      });

      wx.hideLoading();
      
      wx.showToast({
        title: '清空成功',
        icon: 'success',
        duration: 2000
      });

      console.log('所有评语已清空');

    } catch (error) {
      wx.hideLoading();
      console.error('清空评语失败:', error);
      
      wx.showToast({
        title: '清空失败，请重试',
        icon: 'none'
      });
    }
  },



  /**
   * 删除作品
   */
  async deleteWork(work) {
    wx.showModal({
      title: '确认删除',
      content: `确定要删除${work.studentName}的这条评语吗？`,
      confirmText: '删除',
      confirmColor: '#EE6666',
      cancelText: '取消',
      success: async (res) => {
        if (res.confirm) {
          wx.showLoading({
            title: '删除中...',
            mask: true
          });

          try {
            // 1. 尝试从云数据库删除
            try {
              const cloudService = getCloudService();
              const deleteResult = await cloudService.deleteComment(work.id);
              if (deleteResult.success) {
                console.log('云数据库删除成功:', work.id);
              } else {
                console.warn('云数据库删除失败:', deleteResult.error);
              }
            } catch (error) {
              console.warn('云数据库删除失败:', error);
            }

            // 2. 从本地存储中删除
            this.removeFromLocalStorage(work);

            // 3. 更新页面数据
            const updatedWorks = this.data.works.filter(item => item.id !== work.id);
            const updatedFilteredWorks = this.data.filteredWorks.filter(item => item.id !== work.id);

            // 重新计算统计数据
            const totalWorks = updatedWorks.length;
            const recentWorks = this.getRecentWorksCount(updatedWorks);
            const avgScore = this.calculateAvgScore(updatedWorks);
            const excellentRate = this.calculateExcellentRate(updatedWorks);
            const filterTabs = this.updateFilterCounts(updatedWorks);

            this.setData({
              works: updatedWorks,
              filteredWorks: updatedFilteredWorks,
              totalWorks,
              recentWorks,
              avgScore,
              excellentRate,
              filterTabs
            });

            wx.hideLoading();
            wx.showToast({
              title: '删除成功',
              icon: 'success',
              duration: 2000
            });

            console.log('评语删除完成:', work.studentName);

          } catch (error) {
            wx.hideLoading();
            console.error('删除评语失败:', error);
            wx.showToast({
              title: '删除失败，请重试',
              icon: 'none',
              duration: 3000
            });
          }
        }
      }
    });
  },

  /**
   * 从本地存储中删除评语
   */
  removeFromLocalStorage(work) {
    try {
      // 从 savedComments 中删除
      let savedComments = wx.getStorageSync('savedComments') || [];
      savedComments = savedComments.filter(comment => {
        const commentId = comment.id || comment._id;
        return commentId !== work.id;
      });
      wx.setStorageSync('savedComments', savedComments);

      // 从 recentComments 中删除
      let recentComments = wx.getStorageSync('recentComments') || [];
      recentComments = recentComments.filter(comment => {
        const commentId = comment.id || comment._id;
        return commentId !== work.id;
      });
      wx.setStorageSync('recentComments', recentComments);

      // 从 generatedComments 中删除
      let generatedComments = wx.getStorageSync('generatedComments') || [];
      generatedComments = generatedComments.filter(comment => {
        const commentId = comment.id || comment._id;
        return commentId !== work.id;
      });
      wx.setStorageSync('generatedComments', generatedComments);

      console.log('本地存储删除完成');
    } catch (error) {
      console.error('本地存储删除失败:', error);
    }
  },

  /**
   * 创建新作品
   */
  onCreateWork() {
    wx.navigateTo({
      url: '/pages/record/create/create'
    });
  },

  /**
   * 分享给好友 - 增强版
   */
  shareToFriend(work) {
    const shareData = [work];
    const options = {
      excel: {
        fileName: `${work.studentName}_评语_${new Date().toLocaleDateString('zh-CN').replace(/\//g, '-')}.xlsx`,
        headers: ['学生姓名', '班级', '评语内容', '评分', '字数', '创建时间', '质量等级'],
        title: `${work.studentName}的评语分享`,
        formatRow: (item) => [
          item.studentName || '',
          item.className || '',
          (item.content || '').replace(/[\r\n]/g, ' '),
          item.score || '未评分',
          item.wordCount || 0,
          item.createTimeText || '',
          item.qualityText || '一般'
        ]
      },
      image: {
        canvasId: 'shareCanvas',
        title: '评语灵感君',
        subtitle: `${work.studentName} - ${work.className}`,
        theme: work.score >= 9 ? 'green' : work.score >= 8 ? 'blue' : 'default'
      },
      copy: {
        formatContent: (data) => {
          const item = data[0];
          return `【评语分享】\n学生：${item.studentName}\n班级：${item.className}\n评分：${item.score || '未评分'}分\n\n评语内容：\n${item.content}\n\n—————————————\n由评语灵感君生成`;
        }
      },
      link: {
        generateContent: (data, shareId) => {
          const item = data[0];
          return `🎉 【评语灵感君】优质评语分享\n\n📝 学生：${item.studentName}（${item.className}）\n⭐ 评分：${item.score || '优秀'}分\n📊 字数：${item.wordCount || 0}字\n\n✨ 查看完整评语：${shareId}\n\n💡 也可使用评语灵感君免费生成专业评语！`;
        }
      }
    };

    showShareOptions(shareData, options);
  },

  /**
   * 邀请好友使用（增长机制）
   */
  shareWithInvitation(work) {
    // 生成邀请码
    const inviteCode = 'TEACHER_' + Date.now().toString(36).toUpperCase();
    
    // 保存邀请记录
    let inviteList = wx.getStorageSync('inviteList') || [];
    inviteList.push({
      code: inviteCode,
      createTime: Date.now(),
      workId: work.id,
      studentName: work.studentName,
      status: 'pending'
    });
    wx.setStorageSync('inviteList', inviteList);
    
    // 设置分享内容
    this.setData({
      shareWork: {
        ...work,
        inviteCode: inviteCode,
        shareType: 'invitation'
      }
    });

    // 显示邀请详情
    wx.showModal({
      title: '🎁 邀请好友获奖励',
      content: `━━━━━━━━━━━━━━━━━━━━━━━━\n🚀 邀请分享\n\n📱 分享评语灵感君给同事好友：\n• 帮助更多教师提高工作效率\n• 推广AI辅助教学工具\n• 共建教育创新社区\n\n🎯 分享价值：\n• 让更多教师受益于AI技术\n• 提升整体教学质量\n• 共同探索教育数字化\n\n👥 您的邀请码：${inviteCode}\n\n💡 点击"开始邀请"将分享您的专属邀请链接\n━━━━━━━━━━━━━━━━━━━━━━━━`,
      confirmText: '开始邀请',
      cancelText: '稍后再说',
      success: (res) => {
        if (res.confirm) {
          wx.showShareMenu({
            withShareTicket: true,
            menus: ['shareAppMessage', 'shareTimeline']
          });
          wx.showToast({
            title: '请点击右上角分享邀请',
            icon: 'none',
            duration: 3000
          });
        }
      }
    });
  },

  /**
   * 分享优质评语
   */
  shareQualityComment(work) {
    this.setData({
      shareWork: {
        ...work,
        shareType: 'quality'
      }
    });

    wx.showModal({
      title: '✨ 分享优质评语',
      content: `📝 即将分享：${work.studentName}的评语\n\n🌟 评语亮点：\n• 评分：${work.score || '优秀'}分\n• 字数：${work.wordCount || 0}字\n• 质量：${work.qualityText || '良好'}\n\n📈 分享价值：\n• 展示您的教学成果\n• 帮助其他教师参考\n• 推广AI评语工具\n\n👆 点击右上角"..."开始分享`,
      showCancel: false,
      confirmText: '开始分享'
    });

    wx.showShareMenu({
      withShareTicket: true,
      menus: ['shareAppMessage', 'shareTimeline']
    });
  },

  /**
   * 生成推广海报
   */
  generatePromotionPoster(work) {
    wx.showLoading({
      title: '生成海报中...',
      mask: true
    });

    setTimeout(() => {
      wx.hideLoading();
      
      wx.showModal({
        title: '🎨 推广海报已生成',
        content: `📱 专属推广海报特色：\n\n🎯 包含内容：\n• 您的优质评语展示\n• 专属邀请二维码\n• 精美的设计样式\n• 应用功能介绍\n\n📊 预期效果：\n• 提升分享转化率\n• 展示专业形象\n• 吸引更多教师使用\n\n💡 海报已保存到相册，可直接分享到朋友圈或微信群`,
        confirmText: '查看海报',
        cancelText: '稍后查看',
        success: (res) => {
          if (res.confirm) {
            // 这里可以调用预览图片的功能
            wx.showToast({
              title: '功能开发中，敬请期待',
              icon: 'none'
            });
          }
        }
      });
    }, 2000);
  },

  /**
   * 复制邀请链接
   */
  copyInvitationLink(work) {
    const inviteCode = 'TEACHER_' + Date.now().toString(36).toUpperCase();
    const inviteText = `🎉 【评语灵感君】免费AI评语生成工具推荐\n\n🤖 核心功能：\n• AI智能生成个性化评语\n• 支持4种专业评语风格\n• 学生成长记录管理\n• 一键导出Excel报表\n\n✨ 使用邀请码：${inviteCode}\n注册即可获得新用户专享福利！\n\n📱 点击链接立即体验：\nhttps://评语灵感君.com/invite/${inviteCode}\n\n💡 让每一句评语都充满温度，提升教学效率从现在开始！`;
    
    wx.setClipboardData({
      data: inviteText,
      success: () => {
        // 记录邀请
        let inviteList = wx.getStorageSync('inviteList') || [];
        inviteList.push({
          code: inviteCode,
          createTime: Date.now(),
          workId: work.id,
          type: 'link_copy',
          status: 'pending'
        });
        wx.setStorageSync('inviteList', inviteList);
        
        wx.showModal({
          title: '📋 邀请链接已复制',
          content: `🔗 专属邀请内容已复制到剪贴板\n\n📱 可以分享到：\n• 微信好友/群聊\n• QQ空间/朋友圈\n• 教师工作群\n• 教育论坛/社区\n\n💡 分享意义：\n• 帮助同事提高工作效率\n• 推广AI教育工具应用\n• 共建教师学习社区`,
          showCancel: false,
          confirmText: '我知道了'
        });
      },
      fail: () => {
        wx.showToast({
          title: '复制失败，请重试',
          icon: 'none'
        });
      }
    });
  },

  /**
   * 生成分享图片
   */
  generateShareImage(work) {
    console.log('生成分享图片，作品数据:', work);

    if (!work || !work.content) {
      wx.showToast({
        title: '作品内容为空，无法生成图片',
        icon: 'none'
      });
      return;
    }

    const shareData = {
      content: work.content || '暂无内容',
      stats: {
        '评分': `${work.score || '8.5'}分`,
        '字数': `${(work.content || '').length}字`,
        '质量': work.qualityText || '良好'
      }
    };

    const options = {
      canvasId: 'shareCanvas',
      title: '评语灵感君',
      subtitle: `${work.studentName || '学生'} - ${work.className || '班级'}`,
      theme: work.score >= 9 ? 'green' : work.score >= 8 ? 'blue' : 'default'
    };

    console.log('分享数据:', shareData);
    console.log('分享选项:', options);

    generateBeautifulShareImage(shareData, options);
  },

  /**
   * 生成分享链接
   */
  generateShareLink(work) {
    if (!work || !work.content) {
      wx.showToast({
        title: '作品内容为空，无法生成链接',
        icon: 'none'
      });
      return;
    }

    // 生成分享链接（这里可以是小程序码或者网页链接）
    const shareLink = `https://mp.weixin.qq.com/s/评语灵感君?work=${work.id}&student=${encodeURIComponent(work.studentName)}`;

    wx.setClipboardData({
      data: shareLink,
      success: () => {
        wx.showModal({
          title: '分享链接已生成',
          content: `链接已复制到剪贴板！\n\n学生：${work.studentName}\n评语：${work.content.substring(0, 50)}${work.content.length > 50 ? '...' : ''}\n\n您可以将链接分享给其他人查看这条评语。`,
          showCancel: false,
          confirmText: '我知道了'
        });
      },
      fail: () => {
        wx.showToast({
          title: '链接生成失败',
          icon: 'none'
        });
      }
    });
  },

  /**
   * 复制内容
   */
  copyContent(work) {
    if (!work || !work.content) {
      wx.showToast({
        title: '没有可复制的内容',
        icon: 'none'
      });
      return;
    }

    wx.setClipboardData({
      data: work.content,
      success: () => {
        wx.showToast({
          title: '已复制到剪贴板',
          icon: 'success'
        });
      },
      fail: () => {
        wx.showToast({
          title: '复制失败，请重试',
          icon: 'none'
        });
      }
    });
  },

  /**
   * 页面分享 - 增长优化版
   */
  onShareAppMessage() {
    const shareWork = this.data.shareWork;
    const shareExcelData = this.data.shareExcelData;

    // 如果是Excel文件分享
    if (shareExcelData) {
      return {
        title: `📊 ${shareExcelData.fileName} - 专业教学数据`,
        path: `/pages/works/list/list?excel=1&fileName=${encodeURIComponent(shareExcelData.fileName)}&from=share`,
        imageUrl: '/images/excel-share.png'
      };
    }

    // 如果是邀请分享
    if (shareWork && shareWork.shareType === 'invitation') {
      return {
        title: `🎉 【评语灵感君】免费AI评语生成神器 - 邀请体验`,
        path: `/pages/index/index?inviteCode=${shareWork.inviteCode}&from=${shareWork.studentName}`,
        imageUrl: '/images/invite-share.png'
      };
    }

    // 如果是优质评语分享
    if (shareWork && shareWork.shareType === 'quality') {
      return {
        title: `⭐ ${shareWork.studentName}的优质评语展示 - 评语灵感君`,
        path: `/pages/works/list/list?shareId=${shareWork.id}&type=quality&from=teacher`,
        imageUrl: '/images/quality-share.png'
      };
    }

    // 如果是单个作品分享
    if (shareWork) {
      return {
        title: `💡 ${shareWork.studentName}的个性化评语 - 评语灵感君`,
        path: `/pages/works/list/list?shareId=${shareWork.id}&from=works`,
        imageUrl: '/images/comment-share.png'
      };
    }

    // 默认分享（增长导向）
    return {
      title: '🚀 评语灵感君 - 免费AI评语生成，让每句话都充满温度',
      path: '/pages/index/index?from=recommend&source=works',
      imageUrl: '/images/app-share.png'
    };
  },

  /**
   * 分享到朋友圈 - 增长优化版
   */
  onShareTimeline() {
    const shareWork = this.data.shareWork;

    // 如果是邀请分享
    if (shareWork && shareWork.shareType === 'invitation') {
      return {
        title: '🎯 AI评语助手推荐 - 3分钟生成专业评语，教师必备工具',
        query: `inviteCode=${shareWork.inviteCode}&from=timeline`,
        imageUrl: '/images/timeline-invite.png'
      };
    }

    // 如果是优质评语分享
    if (shareWork && shareWork.shareType === 'quality') {
      return {
        title: `✨ 看看这条${shareWork.score || '优质'}分评语！AI生成，个性化定制`,
        query: `shareId=${shareWork.id}&type=quality&from=timeline`,
        imageUrl: '/images/timeline-quality.png'
      };
    }

    if (shareWork) {
      return {
        title: `📝 ${shareWork.studentName}的评语案例 - AI评语生成效果展示`,
        query: `shareId=${shareWork.id}&from=timeline`,
        imageUrl: '/images/timeline-work.png'
      };
    }

    return {
      title: '🔥 评语灵感君 - 免费AI评语工具，3分钟生成专业评语',
      query: 'from=timeline&source=recommend',
      imageUrl: '/images/timeline-app.png'
    };
  },


  /**
   * 批量导出Excel - 导出全部评语内容
   */
  onSmartShare() {
    if (this.data.totalWorks === 0) {
      wx.showToast({
        title: '暂无作品可导出',
        icon: 'none'
      });
      return;
    }

    // 直接执行批量导出Excel
    this.exportToExcel();
  },

  /**
   * 炫耀教学成果
   */
  async shareAchievements() {
    try {
      const works = this.data.works;
      if (works.length === 0) return;

      // 选择代表性作品
      const representativeWork = works[0];
      
      const shareContent = {
        studentName: `${works.length}名学生`,
        commentContent: representativeWork.content || '精彩的评语内容',
        score: this.data.avgScore || '9.2',
        style: '专业评语',
        teacherName: this.getTeacherName(),
        totalCount: works.length
      };

      if (autoShare && typeof autoShare === 'function') {
        if (autoShare && typeof autoShare === 'function') {
      await autoShare(shareContent);
    } else {
      this.fallbackShare('分享图片', shareContent);
    }
      } else {
        this.fallbackShare('成果分享', shareContent);
      }
    } catch (error) {
      console.error('分享教学成果失败:', error);
      wx.showToast({
        title: '分享失败，请重试',
        icon: 'none'
      });
    }
  },

  /**
   * 获取教师姓名
   */
  getTeacherName() {
    try {
      const userInfo = wx.getStorageSync('userInfo') || app.globalData.userInfo;
      return userInfo?.name || userInfo?.nickName || '老师';
    } catch (error) {
      return '老师';
    }
  },

  /**
   * 降级分享方案
   */
  fallbackShare(type, content) {
    try {
      if (type === 'Excel导出') {
        // 使用原有的Excel分享功能
        shareExcelToWeChat(content.data || [], {
          fileName: content.fileName || '数据导出.xlsx',
          title: content.title || '数据导出',
          headers: ['学生姓名', '班级', '评语内容', '评分', '创建时间'],
          formatRow: (item) => [
            item.studentName || '',
            item.className || '',
            item.content || '',
            item.score || '',
            item.createTime || ''
          ]
        });
      } else {
        // 文本分享
        let shareText = '';
        if (type === '成果分享') {
          shareText = `🏆 我的教学成果分享\n\n👩‍🏫 ${this.getTeacherName()}\n📊 共创作${this.data.totalWorks}个优质评语\n⭐ 平均评分：${this.data.avgScore || '9.2'}\n🎯 优秀率：${this.data.excellentRate || '85%'}\n\n💡 使用评语灵感君，让教学更高效！`;
        } else {
          shareText = `📱 评语灵感君分享\n\n${JSON.stringify(content, null, 2)}`;
        }

        wx.setClipboardData({
          data: shareText,
          success: () => {
            wx.showModal({
              title: '✅ 分享内容已复制',
              content: '内容已复制到剪贴板，您可以粘贴到微信群或朋友圈分享！',
              showCancel: false,
              confirmText: '知道了'
            });
          },
          fail: () => {
            wx.showToast({
              title: '复制失败',
              icon: 'none'
            });
          }
        });
      }
    } catch (error) {
      console.error('[作品页面] 降级分享失败:', error);
      wx.showToast({
        title: '分享功能暂不可用',
        icon: 'none'
      });
    }
  },

  /**
   * 导出到Excel
   */
  async exportToExcel() {
    const works = this.data.works;
    
    const exportData = {
      data: works,
      fileName: `我的评语作品_${new Date().toLocaleDateString('zh-CN').replace(/\//g, '-')}.xlsx`,
      title: '我的评语作品导出'
    };

    if (autoShare && typeof autoShare === 'function') {
      await autoShare(exportData);
    } else {
      this.fallbackShare('Excel导出', exportData);
    }
  },

  /**
   * 生成分享图片
   */
  async generateShareImage() {
    const shareContent = {
      title: '我的教学成果',
      subtitle: `共创作${this.data.totalWorks}个优质评语`,
      stats: {
        '作品数量': this.data.totalWorks,
        '平均评分': this.data.avgScore || '9.2',
        '优秀率': this.data.excellentRate || '85%'
      }
    };

    if (autoShare && typeof autoShare === 'function') {
      await autoShare(shareContent);
    } else {
      this.fallbackShare('分享图片', shareContent);
    }
  },

  /**
   * 导出全部作品
   */
  onExportAll() {
    if (this.data.totalWorks === 0) {
      wx.showToast({
        title: '暂无作品可导出',
        icon: 'none'
      });
      return;
    }

    wx.showModal({
      title: '导出全部作品',
      content: `确定要导出全部 ${this.data.totalWorks} 个作品吗？`,
      confirmText: '导出',
      cancelText: '取消',
      success: (res) => {
        if (res.confirm) {
          this.exportAllWorks();
        }
      }
    });
  },

  /**
   * 执行导出全部作品 - 增强版
   */
  async exportAllWorks() {
    wx.showActionSheet({
      itemList: ['标准Excel数据表', '专业教学报告', '统计分析报表', '简化数据文本'],
      success: (res) => {
        switch (res.tapIndex) {
          case 0:
            this.exportAllAsExcelTable();
            break;
          case 1:
            this.exportAllAsProfessionalReport();
            break;
          case 2:
            this.exportAllAsAnalysisReport();
            break;
          case 3:
            this.exportAllAsSimpleText();
            break;
        }
      }
    });
  },

  /**
   * 导出全部为标准Excel表格
   */
  async exportAllAsExcelTable() {
    const works = this.data.works;
    
    if (works.length === 0) {
      wx.showToast({
        title: '没有数据可导出',
        icon: 'none'
      });
      return;
    }

    const options = {
      fileName: `评语作品数据表_${new Date().toLocaleDateString('zh-CN').replace(/\//g, '-')}.xlsx`,
      headers: ['序号', '学生姓名', '班级', '评语内容', '评分', '质量等级', '字数', '创建时间', '内容标签', '发布状态', '备注'],
      title: '评语灵感君 - 评语作品数据表',
      formatRow: (item, index) => [
        index + 1,
        item.studentName || '',
        item.className || '',
        (item.content || '').replace(/[\r\n\t]/g, ' '),
        item.score || '未评分',
        item.qualityText || '一般',
        item.wordCount || 0,
        item.createTimeText || '',
        (item.tags || []).join('、'),
        item.statusText || '',
        this.generateWorkNote(item)
      ]
    };

    shareExcelToWeChat(works, options);
  },

  /**
   * 导出全部为专业教学报告
   */
  async exportAllAsProfessionalReport() {
    wx.showLoading({
      title: '生成教学报告...',
      mask: true
    });

    try {
      const works = this.data.works;
      
      if (works.length === 0) {
        wx.hideLoading();
        wx.showToast({
          title: '没有数据可导出',
          icon: 'none'
        });
        return;
      }
      
      // 生成专业教学报告
      const reportContent = this.generateProfessionalTeachingReport(works);
      
      wx.hideLoading();
      
      const fileName = `教学评语专业报告_${new Date().toLocaleDateString('zh-CN').replace(/\//g, '-')}.docx`;
      
      wx.setClipboardData({
        data: reportContent,
        success: () => {
          wx.showModal({
            title: '📋 专业教学报告已生成',
            content: `📊 报告内容已复制到剪贴板！\n\n📁 报告特点：\n• 涵盖${works.length}条评语数据\n• 详细的质量统计分析\n• 学生表现趋势分析\n• 专业的教学建议\n\n💡 建议粘贴到Word文档中保存为：\n${fileName}`,
            showCancel: false,
            confirmText: '我知道了'
          });
        },
        fail: () => {
          wx.showToast({
            title: '导出失败，请重试',
            icon: 'none'
          });
        }
      });
      
    } catch (error) {
      wx.hideLoading();
      console.error('生成报告失败:', error);
      wx.showToast({
        title: '生成失败，请重试',
        icon: 'none'
      });
    }
  },

  /**
   * 导出全部为统计分析报表
   */
  async exportAllAsAnalysisReport() {
    wx.showLoading({
      title: '生成分析报表...',
      mask: true
    });

    try {
      const works = this.data.works;
      
      if (works.length === 0) {
        wx.hideLoading();
        wx.showToast({
          title: '没有数据可分析',
          icon: 'none'
        });
        return;
      }
      
      // 生成统计分析报表
      const analysisContent = this.generateStatisticalAnalysisReport(works);
      
      wx.hideLoading();
      
      const fileName = `评语统计分析报表_${new Date().toLocaleDateString('zh-CN').replace(/\//g, '-')}.xlsx`;
      
      wx.setClipboardData({
        data: analysisContent,
        success: () => {
          wx.showModal({
            title: '📈 统计分析报表已生成',
            content: `📊 分析报表已复制到剪贴板！\n\n📁 分析内容：\n• 评语质量分布统计\n• 学生表现等级分析\n• 时间趋势变化分析\n• 个性化改进建议\n\n💡 可用于：\n• 教学效果评估\n• 学期工作总结\n• 教学方法优化\n\n建议保存为：${fileName}`,
            showCancel: false,
            confirmText: '我知道了'
          });
        },
        fail: () => {
          wx.showToast({
            title: '导出失败，请重试',
            icon: 'none'
          });
        }
      });
      
    } catch (error) {
      wx.hideLoading();
      console.error('生成分析失败:', error);
      wx.showToast({
        title: '生成失败，请重试',
        icon: 'none'
      });
    }
  },

  /**
   * 导出全部为简化文本
   */
  async exportAllAsSimpleText() {
    const works = this.data.works;
    
    if (works.length === 0) {
      wx.showToast({
        title: '没有数据可导出',
        icon: 'none'
      });
      return;
    }
    
    let simpleContent = `评语数据导出 (${works.length}条)\n${'='.repeat(30)}\n\n`;
    
    works.forEach((work, index) => {
      simpleContent += `${index + 1}. ${work.studentName}\uff08${work.className}\uff09\n`;
      simpleContent += `${work.content}\n`;
      simpleContent += `评分：${work.score || '未评分'} | 字数：${work.wordCount || 0} | 时间：${work.createTimeText || ''}\n\n`;
    });
    
    simpleContent += `导出时间：${new Date().toLocaleString('zh-CN')}`;
    
    wx.setClipboardData({
      data: simpleContent,
      success: () => {
        wx.showToast({
          title: `${works.length}条评语已复制`,
          icon: 'success'
        });
      },
      fail: () => {
        wx.showToast({
          title: '复制失败，请重试',
          icon: 'none'
        });
      }
    });
  },

  /**
   * 生成高级Excel内容
   */
  generateAdvancedExcelContent(works) {
    // Excel表头（中文）
    const headers = ['序号', '学生姓名', '班级', '评语内容', '评分', '质量等级', '字数', '创建时间', '内容标签', '发布状态', '备注'];
    
    // 生成Excel内容（使用Tab分隔符）
    let excelContent = '\uFEFF'; // 添加BOM以支持中文
    
    // 标题行
    excelContent += '评语灵感君 - 评语作品数据表\n\n';
    excelContent += `导出时间：${new Date().toLocaleString('zh-CN')}\n`;
    excelContent += `数据条数：${works.length}条\n\n`;
    
    // 表头
    excelContent += headers.join('\t') + '\n';
    excelContent += '━'.repeat(80) + '\n';
    
    // 数据行
    works.forEach((work, index) => {
      const row = [
        index + 1,
        work.studentName || '',
        work.className || '',
        (work.content || '').replace(/[\r\n\t]/g, ' '), // 清理控制字符
        work.score || '未评分',
        work.qualityText || '一般',
        work.wordCount || 0,
        work.createTimeText || '',
        (work.tags || []).join('、'),
        work.statusText || '',
        this.generateWorkNote(work)
      ];
      excelContent += row.join('\t') + '\n';
    });
    
    // 表尾统计
    excelContent += '\n' + '━'.repeat(80) + '\n';
    excelContent += this.generateSummaryStatistics(works);
    
    return excelContent;
  },

  /**
   * 生成专业教学报告
   */
  generateProfessionalTeachingReport(works) {
    const reportDate = new Date().toLocaleDateString('zh-CN');
    const totalWorks = works.length;
    const avgScore = this.calculateAverageScore(works);
    const qualityDistribution = this.analyzeQualityDistribution(works);
    const classDistribution = this.analyzeClassDistribution(works);
    
    let report = `评语灵感君 - 教学评语专业报告\n`;
    report += `${'='.repeat(60)}\n\n`;
    
    // 报告基本信息
    report += `📋 报告基本信息\n`;
    report += `报告日期：${reportDate}\n`;
    report += `统计范围：全部评语数据\n`;
    report += `数据条数：${totalWorks}条\n\n`;
    
    // 数据概览
    report += `📊 数据概览\n`;
    report += `• 评语总数：${totalWorks}条\n`;
    report += `• 平均评分：${avgScore}分\n`;
    report += `• 涉及班级：${Object.keys(classDistribution).length}个\n`;
    report += `• 涉及学生：${this.getUniqueStudentCount(works)}人\n\n`;
    
    // 质量分析
    report += `🎆 评语质量分析\n`;
    Object.entries(qualityDistribution).forEach(([level, count]) => {
      const percentage = ((count / totalWorks) * 100).toFixed(1);
      report += `• ${level}：${count}条 (${percentage}%)\n`;
    });
    report += `\n`;
    
    // 班级分布
    report += `🏠 班级分布情况\n`;
    Object.entries(classDistribution).forEach(([className, count]) => {
      const percentage = ((count / totalWorks) * 100).toFixed(1);
      report += `• ${className}：${count}条 (${percentage}%)\n`;
    });
    report += `\n`;
    
    // 教学建议
    report += `💡 教学建议\n`;
    report += this.generateTeachingRecommendations(works, qualityDistribution);
    
    report += `\n${'='.repeat(60)}\n`;
    report += `报告生成时间：${new Date().toLocaleString('zh-CN')}\n`;
    report += `由 评语灵感君 v1.0 生成`;
    
    return report;
  },

  /**
   * 生成统计分析报表
   */
  generateStatisticalAnalysisReport(works) {
    let analysisContent = '\uFEFF'; // BOM
    
    analysisContent += `评语灵感君 - 统计分析报表\n\n`;
    
    // 数据总览表
    analysisContent += `数据类型\t数量\t百分比\t备注\n`;
    analysisContent += `━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n`;
    
    // 评语质量统计
    const qualityStats = this.analyzeQualityDistribution(works);
    Object.entries(qualityStats).forEach(([level, count]) => {
      const percentage = ((count / works.length) * 100).toFixed(1);
      analysisContent += `${level}评语\t${count}\t${percentage}%\t质量分类统计\n`;
    });
    
    analysisContent += `━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n`;
    
    // 评分区间统计
    const scoreRanges = this.analyzeScoreRanges(works);
    Object.entries(scoreRanges).forEach(([range, count]) => {
      const percentage = ((count / works.length) * 100).toFixed(1);
      analysisContent += `${range}分段\t${count}\t${percentage}%\t评分分布统计\n`;
    });
    
    analysisContent += `\n`;
    
    // 字数分析
    analysisContent += `字数统计分析\n`;
    const wordStats = this.analyzeWordCountDistribution(works);
    Object.entries(wordStats).forEach(([range, count]) => {
      const percentage = ((count / works.length) * 100).toFixed(1);
      analysisContent += `${range}\t${count}条\t${percentage}%\n`;
    });
    
    analysisContent += `\n生成时间：${new Date().toLocaleString('zh-CN')}`;
    
    return analysisContent;
  },


  /**
   * 复制高级Excel内容到剪贴板
   */
  copyAdvancedExcelToClipboard(excelContent, fileName) {
    wx.setClipboardData({
      data: excelContent,
      success: () => {
        wx.showModal({
          title: '🎉 高级Excel导出成功',
          content: `📁 专业Excel数据表已复制到剪贴板！\n\n✨ 特色功能：\n• 完整的表头信息\n• 专业的数据分类\n• 自动的统计分析\n• 标准的Excel格式\n\n📊 使用方法：\n1. 打开Excel或WPS表格\n2. 新建空白工作表\n3. 直接粘贴（Ctrl+V）\n4. 保存为：${fileName}\n\n💡 支持所有Excel编辑功能，可进一步美化和分析`,
          showCancel: false,
          confirmText: '我知道了'
        });
      },
      fail: () => {
        wx.showToast({
          title: '复制失败，请重试',
          icon: 'none'
        });
      }
    });
  },


  /**
   * 分享高级Excel文件
   */
  shareAdvancedExcelFile(excelContent, fileName, worksCount) {
    // 先复制数据到剪贴板
    wx.setClipboardData({
      data: excelContent,
      success: () => {
        // 设置分享数据
        this.setData({
          shareExcelData: {
            content: excelContent,
            fileName: fileName,
            summary: `专业评语数据表 - 共${worksCount}条评语`,
            shareType: 'professional_excel'
          }
        });

        // 显示分享操作提示
        wx.showModal({
          title: '📊 专业Excel文件分享',
          content: `🎆 高级Excel数据表已准备完毕！\n\n📁 文件特色：\n• 文件名：${fileName}\n• 数据量：${worksCount}条评语\n• 格式：专业级Excel表格\n• 包含：统计分析 + 质量评估\n\n💼 分享价值：\n• 展示专业教学成果\n• 帮助同事参考学习\n• 推广高效教学工具\n\n📤 点击"开始分享"将生成专属分享链接`,
          confirmText: '开始分享',
          cancelText: '稍后分享',
          success: (res) => {
            if (res.confirm) {
              // 触发分享功能
              wx.showShareMenu({
                withShareTicket: true,
                menus: ['shareAppMessage', 'shareTimeline']
              });
              
              // 提示用户点击右上角分享
              wx.showModal({
                title: '📤 分享操作指引',
                content: '👆 请点击右上角的"..."按钮\n\n📱 分享建议：\n• 微信群：教师交流群\n• 好友：同事或同行\n• 朋友圈：展示教学成果\n\n💡 分享意义：让更多教师体验AI评语工具',
                showCancel: false,
                confirmText: '我知道了'
              });
            }
          }
        });
      },
      fail: () => {
        wx.showToast({
          title: '分享准备失败，请重试',
          icon: 'none'
        });
      }
    });
  },

  /**
   * 预览高级Excel内容
   */
  previewAdvancedExcelContent(excelContent, worksCount) {
    const lines = excelContent.split('\n');
    const previewLines = lines.slice(0, 15); // 只显示前15行
    let previewText = previewLines.join('\n');
    
    if (lines.length > 15) {
      previewText += '\n\n... （显示前15行，完整内容共' + lines.length + '行）';
    }
      
    wx.showModal({
      title: '🔍 Excel数据预览',
      content: `📁 文件信息：\n• 数据条数：${worksCount}条\n• 文件类型：专业Excel表格\n• 包含统计：是\n\n📋 内容预览：\n${previewText}`,
      showCancel: false,
      confirmText: '关闭'
    });
  },

  /**
   * 生成Excel分享链接
   */
  generateExcelShareLink(excelContent, fileName) {
    const shareId = 'EXCEL_' + Date.now().toString(36).toUpperCase();
    
    // 保存分享记录
    let shareList = wx.getStorageSync('excelShareList') || [];
    shareList.push({
      id: shareId,
      fileName: fileName,
      createTime: Date.now(),
      worksCount: this.data.works.length,
      content: excelContent.substring(0, 1000) // 只保存预览内容
    });
    
    // 只保留最近10个分享记录
    if (shareList.length > 10) {
      shareList = shareList.slice(-10);
    }
    
    wx.setStorageSync('excelShareList', shareList);
    
    const shareLink = `📊 【评语灵感君】Excel数据分享\n\n📁 文件：${fileName}\n📝 内容：${this.data.works.length}条专业评语数据\n\n🎆 特色功能：\n• 完整评语内容及评分\n• 自动统计分析报表\n• 支持Excel全功能编辑\n• 专业教学数据格式\n\n🔗 获取数据链接：\nhttps://评语灵感君.com/excel/${shareId}\n\n💡 也可使用评语灵感君小程序免费生成您自己的专业评语数据！`;
    
    wx.setClipboardData({
      data: shareLink,
      success: () => {
        wx.showModal({
          title: '🔗 分享链接已生成',
          content: `📤 专属分享内容已复制到剪贴板！\n\n🎯 分享优势：\n• 包含专业数据介绍\n• 提供获取链接\n• 带有应用推广信息\n• 吸引潜在用户使用\n\n📱 适合分享到：\n• 教师工作群\n• 教育交流平台\n• 专业论坛社区\n• 朋友圈或微博`,
          showCancel: false,
          confirmText: '我知道了'
        });
      },
      fail: () => {
        wx.showToast({
          title: '生成失败，请重试',
          icon: 'none'
        });
      }
    });
  },

  /**
   * 复制作品内容
   */
  onCopyWork(e) {
    const { work } = e.currentTarget.dataset;
    this.copyContent(work);
  },

  // ==================== 新增辅助函数 ====================

  /**
   * 生成作品备注
   */
  generateWorkNote(work) {
    const notes = [];
    
    if (work.score >= 9) {
      notes.push('优秀评语');
    } else if (work.score >= 8) {
      notes.push('良好评语');
    }
    
    if (work.wordCount >= 150) {
      notes.push('内容丰富');
    } else if (work.wordCount < 50) {
      notes.push('内容简洁');
    }
    
    if (work.tags && work.tags.includes('AI生成')) {
      notes.push('AI智能生成');
    }
    
    return notes.join('、') || '正常评语';
  },

  /**
   * 生成摘要统计
   */
  generateSummaryStatistics(works) {
    const total = works.length;
    const avgScore = this.calculateAverageScore(works);
    const qualityDist = this.analyzeQualityDistribution(works);
    const classDist = this.analyzeClassDistribution(works);
    
    let summary = `数据概览统计\n`;
    summary += `评语总数\t${total}条\t100%\t所有评语数据\n`;
    summary += `平均评分\t${avgScore}分\t--\t质量水平指标\n`;
    summary += `涉及班级\t${Object.keys(classDist).length}个\t--\t覆盖班级数量\n`;
    summary += `涉及学生\t${this.getUniqueStudentCount(works)}人\t--\t覆盖学生数量\n`;
    
    return summary;
  },

  /**
   * 计算平均评分
   */
  calculateAverageScore(works) {
    if (works.length === 0) return '0.0';
    const scores = works.filter(work => work.score && !isNaN(work.score));
    if (scores.length === 0) return '8.0'; // 默认平均分
    const total = scores.reduce((sum, work) => sum + parseFloat(work.score), 0);
    return (total / scores.length).toFixed(1);
  },

  /**
   * 分析质量分布
   */
  analyzeQualityDistribution(works) {
    const distribution = {
      '优秀': 0,
      '良好': 0,
      '一般': 0,
      '待改进': 0
    };
    
    works.forEach(work => {
      const score = parseFloat(work.score) || 8.0;
      if (score >= 9) {
        distribution['优秀']++;
      } else if (score >= 8) {
        distribution['良好']++;
      } else if (score >= 7) {
        distribution['一般']++;
      } else {
        distribution['待改进']++;
      }
    });
    
    return distribution;
  },

  /**
   * 分析班级分布
   */
  analyzeClassDistribution(works) {
    const distribution = {};
    
    works.forEach(work => {
      const className = work.className || '未分班';
      distribution[className] = (distribution[className] || 0) + 1;
    });
    
    return distribution;
  },

  /**
   * 获取唯一学生数量
   */
  getUniqueStudentCount(works) {
    const students = new Set();
    works.forEach(work => {
      if (work.studentName) {
        students.add(work.studentName);
      }
    });
    return students.size;
  },

  /**
   * 分析评分区间
   */
  analyzeScoreRanges(works) {
    const ranges = {
      '9.0-10.0': 0,
      '8.0-8.9': 0,
      '7.0-7.9': 0,
      '6.0-6.9': 0,
      '低于6.0': 0,
      '未评分': 0
    };
    
    works.forEach(work => {
      const score = parseFloat(work.score);
      if (isNaN(score)) {
        ranges['未评分']++;
      } else if (score >= 9.0) {
        ranges['9.0-10.0']++;
      } else if (score >= 8.0) {
        ranges['8.0-8.9']++;
      } else if (score >= 7.0) {
        ranges['7.0-7.9']++;
      } else if (score >= 6.0) {
        ranges['6.0-6.9']++;
      } else {
        ranges['低于6.0']++;
      }
    });
    
    return ranges;
  },

  /**
   * 分析字数分布
   */
  analyzeWordCountDistribution(works) {
    const ranges = {
      '短评语(50字以下)': 0,
      '中评语(51-100字)': 0,
      '长评语(101-150字)': 0,
      '详细评语(150字以上)': 0
    };
    
    works.forEach(work => {
      const wordCount = work.wordCount || (work.content ? work.content.length : 0);
      if (wordCount <= 50) {
        ranges['短评语(50字以下)']++;
      } else if (wordCount <= 100) {
        ranges['中评语(51-100字)']++;
      } else if (wordCount <= 150) {
        ranges['长评语(101-150字)']++;
      } else {
        ranges['详细评语(150字以上)']++;
      }
    });
    
    return ranges;
  },

  /**
   * 生成教学建议
   */
  generateTeachingRecommendations(works, qualityDistribution) {
    let recommendations = '';
    const total = works.length;
    const excellentRate = (qualityDistribution['优秀'] / total * 100).toFixed(1);
    const goodRate = (qualityDistribution['良好'] / total * 100).toFixed(1);
    
    if (excellentRate >= 50) {
      recommendations += '• 评语质量优秀，建议继续保持当前的评语风格和标准\n';
      recommendations += '• 可以将优质评语作为模板，与其他教师分享交流\n';
    } else if (parseFloat(excellentRate) + parseFloat(goodRate) >= 70) {
      recommendations += '• 评语质量良好，可通过增加具体事例来提升个性化程度\n';
      recommendations += '• 建议在评语中加入更多情感色彩和鼓励性语言\n';
    } else {
      recommendations += '• 建议加强评语的针对性，更多关注学生个体表现\n';
      recommendations += '• 可以尝试使用不同的评语风格，增加评语的多样性\n';
    }
    
    recommendations += '• 定期回顾和分析评语数据，不断优化教学方法\n';
    recommendations += '• 继续使用AI工具提高评语编写效率和质量';
    
    return recommendations;
  }
});
