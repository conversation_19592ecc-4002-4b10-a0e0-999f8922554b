const express = require('express');
const path = require('path');
const app = express();
const port = 8080;

// 禁用所有安全headers
app.use((req, res, next) => {
  res.removeHeader('X-Powered-By');
  res.removeHeader('Content-Security-Policy');
  res.removeHeader('X-Content-Security-Policy');
  res.removeHeader('X-WebKit-CSP');
  next();
});

// 静态文件服务
app.use(express.static(path.join(__dirname, 'dist')));

// SPA回退
app.get('*', (req, res) => {
  res.sendFile(path.join(__dirname, 'dist', 'index.html'));
});

app.listen(port, () => {
  console.log(`🚀 Server running at http://localhost:${port}`);
  console.log('📝 No CSP restrictions applied!');
});