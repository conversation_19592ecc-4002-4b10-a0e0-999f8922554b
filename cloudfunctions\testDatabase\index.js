// 数据库诊断云函数
// 检查数据库集合和数据情况

const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()

// 通用响应格式
const createResponse = (code = 200, message = 'success', data = null) => {
  return {
    code,
    message,
    data,
    timestamp: new Date().toISOString()
  }
}

// 数据库诊断工具
const diagnosticHandlers = {
  // 检查所有集合
  async checkAllCollections() {
    const collections = ['users', 'students', 'comments', 'classes', 'ai_usage']
    const results = {}
    
    for (const collection of collections) {
      try {
        const countResult = await db.collection(collection).count()
        const sampleResult = await db.collection(collection).limit(3).get()
        
        results[collection] = {
          exists: true,
          count: countResult.total,
          sample: sampleResult.data.map(item => {
            // 只返回字段名，不返回完整数据（保护隐私）
            return Object.keys(item)
          })
        }
      } catch (error) {
        results[collection] = {
          exists: false,
          error: error.message,
          count: 0,
          sample: []
        }
      }
    }
    
    return results
  },
  
  // 检查具体集合的数据结构
  async checkCollectionStructure(collectionName) {
    try {
      const result = await db.collection(collectionName).limit(1).get()
      
      if (result.data.length > 0) {
        const sample = result.data[0]
        const structure = {}
        
        for (const key in sample) {
          structure[key] = typeof sample[key]
        }
        
        return {
          exists: true,
          structure,
          sampleFields: Object.keys(sample)
        }
      } else {
        return {
          exists: true,
          structure: {},
          sampleFields: [],
          message: '集合存在但无数据'
        }
      }
    } catch (error) {
      return {
        exists: false,
        error: error.message
      }
    }
  },
  
  // 检查常见字段名
  async checkFieldNames() {
    const results = {}
    
    // 检查users集合的字段名
    try {
      const usersResult = await db.collection('users').limit(3).get()
      results.users = {
        count: usersResult.data.length,
        commonFields: usersResult.data.length > 0 ? Object.keys(usersResult.data[0]) : []
      }
    } catch (error) {
      results.users = { error: error.message }
    }
    
    // 检查students集合的字段名
    try {
      const studentsResult = await db.collection('students').limit(3).get()
      results.students = {
        count: studentsResult.data.length,
        commonFields: studentsResult.data.length > 0 ? Object.keys(studentsResult.data[0]) : []
      }
    } catch (error) {
      results.students = { error: error.message }
    }
    
    // 检查comments集合的字段名
    try {
      const commentsResult = await db.collection('comments').limit(3).get()
      results.comments = {
        count: commentsResult.data.length,
        commonFields: commentsResult.data.length > 0 ? Object.keys(commentsResult.data[0]) : []
      }
    } catch (error) {
      results.comments = { error: error.message }
    }
    
    return results
  },
  
  // 获取数据库环境信息
  async getEnvironmentInfo() {
    try {
      return {
        env: cloud.DYNAMIC_CURRENT_ENV,
        timestamp: new Date().toISOString(),
        region: process.env.TENCENTCLOUD_REGION || 'unknown'
      }
    } catch (error) {
      return {
        error: error.message
      }
    }
  }
}

// 主函数
exports.main = async (event, context) => {
  console.log('🔍 Database Diagnostic 诊断请求:', event)
  
  try {
    const { action = 'checkAllCollections', params = {} } = event
    
    let result = null
    
    switch (action) {
      case 'checkAllCollections':
        result = await diagnosticHandlers.checkAllCollections()
        break
        
      case 'checkCollectionStructure':
        result = await diagnosticHandlers.checkCollectionStructure(params.collection)
        break
        
      case 'checkFieldNames':
        result = await diagnosticHandlers.checkFieldNames()
        break
        
      case 'getEnvironmentInfo':
        result = await diagnosticHandlers.getEnvironmentInfo()
        break
        
      default:
        throw new Error(`未知的诊断操作: ${action}`)
    }
    
    console.log('✅ Database Diagnostic 诊断完成:', action)
    return createResponse(200, 'success', result)
    
  } catch (error) {
    console.error('❌ Database Diagnostic 诊断失败:', error)
    return createResponse(500, error.message, null)
  }
}