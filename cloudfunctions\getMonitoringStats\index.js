/**
 * 监控数据统计分析云函数
 * 2025年企业级监控系统
 */

const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()
const _ = db.command

/**
 * 云函数入口函数
 */
exports.main = async (event, context) => {
  const { action, params = {} } = event
  
  try {
    console.log('监控统计查询:', { action, params })
    
    switch (action) {
      case 'getDashboard':
        return await getDashboardStats(params)
      case 'getErrorStats':
        return await getErrorStats(params)
      case 'getPerformanceStats':
        return await getPerformanceStats(params)
      case 'getUserBehaviorStats':
        return await getUserBehaviorStats(params)
      case 'getAlerts':
        return await getAlerts(params)
      case 'getHealthCheck':
        return await getHealthCheck()
      default:
        return {
          success: false,
          error: `未知的操作: ${action}`
        }
    }

  } catch (error) {
    console.error('监控统计查询失败:', error)
    return {
      success: false,
      error: error.message || '监控统计查询失败'
    }
  }
}

/**
 * 获取监控仪表板统计
 */
async function getDashboardStats(params) {
  const { days = 7 } = params
  const endDate = new Date()
  const startDate = new Date(endDate.getTime() - days * 24 * 60 * 60 * 1000)
  
  try {
    // 并行获取各种统计数据
    const [
      errorStats,
      performanceStats,
      userStats,
      alertStats,
      systemHealth
    ] = await Promise.all([
      getRecentErrorStats(startDate, endDate),
      getRecentPerformanceStats(startDate, endDate),
      getRecentUserStats(startDate, endDate),
      getRecentAlertStats(startDate, endDate),
      getCurrentSystemHealth()
    ])

    return {
      success: true,
      data: {
        timeRange: { startDate, endDate, days },
        errors: errorStats,
        performance: performanceStats,
        users: userStats,
        alerts: alertStats,
        health: systemHealth,
        lastUpdated: new Date()
      }
    }
  } catch (error) {
    throw new Error(`获取仪表板统计失败: ${error.message}`)
  }
}

/**
 * 获取错误统计
 */
async function getErrorStats(params) {
  const { days = 7, category } = params
  const endDate = new Date()
  const startDate = new Date(endDate.getTime() - days * 24 * 60 * 60 * 1000)
  
  try {
    // 构建查询条件
    const where = {
      type: 'error',
      createTime: _.gte(startDate).and(_.lte(endDate))
    }
    
    if (category) {
      where['data.category'] = category
    }

    // 获取错误记录
    const errors = await db.collection('monitoring_logs')
      .where(where)
      .orderBy('createTime', 'desc')
      .limit(1000)
      .get()

    // 分析错误数据
    const analysis = analyzeErrors(errors.data)
    
    return {
      success: true,
      data: {
        total: errors.data.length,
        timeRange: { startDate, endDate, days },
        analysis,
        recentErrors: errors.data.slice(0, 10)
      }
    }
  } catch (error) {
    throw new Error(`获取错误统计失败: ${error.message}`)
  }
}

/**
 * 获取性能统计
 */
async function getPerformanceStats(params) {
  const { days = 7, type } = params
  const endDate = new Date()
  const startDate = new Date(endDate.getTime() - days * 24 * 60 * 60 * 1000)
  
  try {
    const where = {
      type: 'performance',
      createTime: _.gte(startDate).and(_.lte(endDate))
    }
    
    if (type) {
      where['data.type'] = type
    }

    const performance = await db.collection('monitoring_logs')
      .where(where)
      .orderBy('createTime', 'desc')
      .limit(1000)
      .get()

    const analysis = analyzePerformance(performance.data)
    
    return {
      success: true,
      data: {
        total: performance.data.length,
        timeRange: { startDate, endDate, days },
        analysis,
        recentMetrics: performance.data.slice(0, 20)
      }
    }
  } catch (error) {
    throw new Error(`获取性能统计失败: ${error.message}`)
  }
}

/**
 * 获取用户行为统计
 */
async function getUserBehaviorStats(params) {
  const { days = 7 } = params
  const endDate = new Date()
  const startDate = new Date(endDate.getTime() - days * 24 * 60 * 60 * 1000)
  
  try {
    const events = await db.collection('monitoring_logs')
      .where({
        type: 'event',
        createTime: _.gte(startDate).and(_.lte(endDate))
      })
      .orderBy('createTime', 'desc')
      .limit(5000)
      .get()

    const analysis = analyzeUserBehavior(events.data)
    
    return {
      success: true,
      data: {
        total: events.data.length,
        timeRange: { startDate, endDate, days },
        analysis
      }
    }
  } catch (error) {
    throw new Error(`获取用户行为统计失败: ${error.message}`)
  }
}

/**
 * 获取告警信息
 */
async function getAlerts(params) {
  const { status = 'all', limit = 50 } = params
  
  try {
    let where = {}
    if (status !== 'all') {
      where.status = status
    }

    const alerts = await db.collection('alerts')
      .where(where)
      .orderBy('createTime', 'desc')
      .limit(limit)
      .get()

    return {
      success: true,
      data: {
        alerts: alerts.data,
        total: alerts.data.length
      }
    }
  } catch (error) {
    throw new Error(`获取告警信息失败: ${error.message}`)
  }
}

/**
 * 获取系统健康检查
 */
async function getHealthCheck() {
  try {
    const now = new Date()
    const oneHourAgo = new Date(now.getTime() - 60 * 60 * 1000)
    
    // 检查最近一小时的系统状态
    const [errorCount, performanceIssues, activeUsers] = await Promise.all([
      // 错误数量
      db.collection('monitoring_logs')
        .where({
          type: 'error',
          'data.level': 'error',
          createTime: _.gte(oneHourAgo)
        })
        .count(),
      
      // 性能问题
      db.collection('monitoring_logs')
        .where({
          type: 'performance',
          'data.duration': _.gt(3000), // 超过3秒的操作
          createTime: _.gte(oneHourAgo)
        })
        .count(),
      
      // 活跃用户
      db.collection('monitoring_logs')
        .where({
          type: 'event',
          createTime: _.gte(oneHourAgo)
        })
        .field({ userId: true })
        .get()
    ])

    // 计算健康分数
    let healthScore = 100
    
    if (errorCount.total > 10) healthScore -= 30
    else if (errorCount.total > 5) healthScore -= 15
    
    if (performanceIssues.total > 20) healthScore -= 25
    else if (performanceIssues.total > 10) healthScore -= 10
    
    // 获取唯一用户数
    const uniqueUsers = new Set(activeUsers.data.map(u => u.userId)).size
    
    let status = 'healthy'
    if (healthScore < 70) status = 'critical'
    else if (healthScore < 85) status = 'warning'
    
    return {
      success: true,
      data: {
        status,
        score: healthScore,
        metrics: {
          errors: errorCount.total,
          performanceIssues: performanceIssues.total,
          activeUsers: uniqueUsers
        },
        timestamp: now
      }
    }
  } catch (error) {
    throw new Error(`系统健康检查失败: ${error.message}`)
  }
}

/**
 * 分析错误数据
 */
function analyzeErrors(errors) {
  const categories = {}
  const levels = {}
  const timeline = {}
  
  errors.forEach(error => {
    const data = error.data
    const date = error.createTime.toISOString().split('T')[0]
    
    // 分类统计
    categories[data.category] = (categories[data.category] || 0) + 1
    
    // 级别统计
    levels[data.level] = (levels[data.level] || 0) + 1
    
    // 时间线统计
    timeline[date] = (timeline[date] || 0) + 1
  })
  
  return {
    byCategory: Object.entries(categories)
      .map(([category, count]) => ({ category, count }))
      .sort((a, b) => b.count - a.count),
    byLevel: Object.entries(levels)
      .map(([level, count]) => ({ level, count }))
      .sort((a, b) => b.count - a.count),
    timeline: Object.entries(timeline)
      .map(([date, count]) => ({ date, count }))
      .sort((a, b) => a.date.localeCompare(b.date))
  }
}

/**
 * 分析性能数据
 */
function analyzePerformance(metrics) {
  const types = {}
  const timeline = {}
  let totalDuration = 0
  let slowOperations = 0
  
  metrics.forEach(metric => {
    const data = metric.data
    const date = metric.createTime.toISOString().split('T')[0]
    
    // 类型统计
    if (!types[data.type]) {
      types[data.type] = { count: 0, totalDuration: 0, avgDuration: 0 }
    }
    types[data.type].count++
    types[data.type].totalDuration += data.duration
    types[data.type].avgDuration = types[data.type].totalDuration / types[data.type].count
    
    // 时间线统计
    if (!timeline[date]) {
      timeline[date] = { count: 0, avgDuration: 0, totalDuration: 0 }
    }
    timeline[date].count++
    timeline[date].totalDuration += data.duration
    timeline[date].avgDuration = timeline[date].totalDuration / timeline[date].count
    
    totalDuration += data.duration
    if (data.duration > 2000) slowOperations++
  })
  
  const avgDuration = metrics.length > 0 ? totalDuration / metrics.length : 0
  
  return {
    overview: {
      totalOperations: metrics.length,
      avgDuration: Math.round(avgDuration),
      slowOperations,
      slowOperationRate: metrics.length > 0 ? (slowOperations / metrics.length * 100).toFixed(1) : 0
    },
    byType: Object.entries(types)
      .map(([type, stats]) => ({ 
        type, 
        count: stats.count, 
        avgDuration: Math.round(stats.avgDuration) 
      }))
      .sort((a, b) => b.avgDuration - a.avgDuration),
    timeline: Object.entries(timeline)
      .map(([date, stats]) => ({ 
        date, 
        count: stats.count, 
        avgDuration: Math.round(stats.avgDuration) 
      }))
      .sort((a, b) => a.date.localeCompare(b.date))
  }
}

/**
 * 分析用户行为数据
 */
function analyzeUserBehavior(events) {
  const eventTypes = {}
  const pages = {}
  const users = new Set()
  const timeline = {}
  
  events.forEach(event => {
    const data = event.data
    const date = event.createTime.toISOString().split('T')[0]
    
    // 事件类型统计
    eventTypes[data.event] = (eventTypes[data.event] || 0) + 1
    
    // 页面访问统计
    if (data.properties && data.properties.page) {
      pages[data.properties.page] = (pages[data.properties.page] || 0) + 1
    }
    
    // 用户统计
    if (event.userId) {
      users.add(event.userId)
    }
    
    // 时间线统计
    timeline[date] = (timeline[date] || 0) + 1
  })
  
  return {
    overview: {
      totalEvents: events.length,
      uniqueUsers: users.size,
      avgEventsPerUser: users.size > 0 ? (events.length / users.size).toFixed(1) : 0
    },
    topEvents: Object.entries(eventTypes)
      .map(([event, count]) => ({ event, count }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 10),
    topPages: Object.entries(pages)
      .map(([page, count]) => ({ page, count }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 10),
    timeline: Object.entries(timeline)
      .map(([date, count]) => ({ date, count }))
      .sort((a, b) => a.date.localeCompare(b.date))
  }
}

/**
 * 获取最近错误统计
 */
async function getRecentErrorStats(startDate, endDate) {
  const errors = await db.collection('monitoring_logs')
    .where({
      type: 'error',
      createTime: _.gte(startDate).and(_.lte(endDate))
    })
    .count()
  
  return { total: errors.total }
}

/**
 * 获取最近性能统计
 */
async function getRecentPerformanceStats(startDate, endDate) {
  const performance = await db.collection('monitoring_logs')
    .where({
      type: 'performance',
      createTime: _.gte(startDate).and(_.lte(endDate))
    })
    .get()
  
  const slowOperations = performance.data.filter(p => p.data.duration > 2000).length
  
  return {
    total: performance.data.length,
    slowOperations,
    avgDuration: performance.data.length > 0 
      ? Math.round(performance.data.reduce((sum, p) => sum + p.data.duration, 0) / performance.data.length)
      : 0
  }
}

/**
 * 获取最近用户统计
 */
async function getRecentUserStats(startDate, endDate) {
  const events = await db.collection('monitoring_logs')
    .where({
      type: 'event',
      createTime: _.gte(startDate).and(_.lte(endDate))
    })
    .field({ userId: true })
    .get()
  
  const uniqueUsers = new Set(events.data.map(e => e.userId)).size
  
  return {
    totalEvents: events.data.length,
    uniqueUsers
  }
}

/**
 * 获取最近告警统计
 */
async function getRecentAlertStats(startDate, endDate) {
  const alerts = await db.collection('alerts')
    .where({
      createTime: _.gte(startDate).and(_.lte(endDate))
    })
    .get()
  
  const critical = alerts.data.filter(a => a.type === 'critical_error').length
  const pending = alerts.data.filter(a => a.status === 'pending').length
  
  return {
    total: alerts.data.length,
    critical,
    pending
  }
}

/**
 * 获取当前系统健康状态
 */
async function getCurrentSystemHealth() {
  const oneHourAgo = new Date(Date.now() - 60 * 60 * 1000)
  
  const [errors, performance] = await Promise.all([
    db.collection('monitoring_logs')
      .where({
        type: 'error',
        'data.level': 'error',
        createTime: _.gte(oneHourAgo)
      })
      .count(),
    
    db.collection('monitoring_logs')
      .where({
        type: 'performance',
        'data.duration': _.gt(3000),
        createTime: _.gte(oneHourAgo)
      })
      .count()
  ])
  
  let status = 'healthy'
  if (errors.total > 10 || performance.total > 20) {
    status = 'critical'
  } else if (errors.total > 5 || performance.total > 10) {
    status = 'warning'
  }
  
  return { status, errors: errors.total, slowOperations: performance.total }
}
