# 🚀 评语灵感君部署指南

## 📋 部署概览

本指南将帮助你完成评语灵感君小程序的完整部署，包括：
- 微信小程序端部署
- 云函数服务部署  
- 管理后台部署
- 生产环境配置

## 🔧 准备工作

### 1. 账号准备
- [ ] **微信小程序账号**：已认证的小程序账号
- [ ] **微信云开发**：已开通云开发服务
- [ ] **豆包AI账号**：已获取API密钥（或其他AI服务商）
- [ ] **域名备案**：管理后台域名（可选）

### 2. 开发环境
- [ ] **Node.js** >= 16.0.0
- [ ] **微信开发者工具** 最新稳定版
- [ ] **Git** 版本控制工具
- [ ] **代码编辑器**（推荐VS Code）

### 3. 获取必要信息
```bash
# 需要收集的信息
小程序AppID: wx3de03090b8e8a734
小程序密钥: [从微信公众平台获取]
云环境ID: cloud1-4g85f8xlb8166ff1
豆包API密钥: [从豆包开放平台获取]
```

## 🏗️ 部署步骤

### 第一步：项目初始化

#### 1.1 克隆项目
```bash
git clone https://github.com/chanwarmsun/wechat3.0.git
cd wechat3.0
```

#### 1.2 安装依赖
```bash
# 安装小程序依赖
npm install

# 安装管理后台依赖
cd admin-v2
npm install
cd ..
```

#### 1.3 环境配置
```bash
# 复制环境变量模板
cp .env.example .env

# 编辑环境变量文件
vim .env  # 或使用其他编辑器
```

填入以下配置：
```env
# 基础配置
NODE_ENV=production
VERSION=3.0.0

# 微信小程序配置
MINIPROGRAM_APP_ID=wx3de03090b8e8a734
MINIPROGRAM_APP_SECRET=你的小程序密钥

# 云开发配置
CLOUD_ENV_ID=cloud1-4g85f8xlb8166ff1
CLOUD_REGION=ap-shanghai

# 豆包AI配置
DOUBAO_API_KEY=你的豆包API密钥
DOUBAO_API_URL=https://ark.cn-beijing.volces.com/api/v3/chat/completions
DOUBAO_MODEL=doubao-pro-4k

# 安全配置
ENCRYPTION_KEY=生成32位随机字符串
JWT_SECRET=生成JWT密钥

# 性能配置
MAX_CONCURRENT_REQUESTS=5
REQUEST_TIMEOUT=30000
CACHE_TTL=3600
```

### 第二步：云开发配置

#### 2.1 开通云开发
1. 打开微信开发者工具
2. 导入项目（选择项目根目录）
3. 点击"云开发"按钮
4. 开通云开发服务（选择按量付费）
5. 记录云环境ID

#### 2.2 配置云开发环境
```bash
# 在微信开发者工具中
# 1. 点击云开发控制台
# 2. 进入数据库管理
# 3. 创建以下集合：
```

必需的数据库集合：
- `users` - 用户信息
- `students` - 学生信息  
- `classes` - 班级信息
- `records` - 行为记录
- `comments` - 评语记录
- `ai_configs` - AI配置
- `ai_usage` - AI使用记录
- `system_config` - 系统配置

#### 2.3 初始化数据库
```bash
# 运行数据库初始化脚本
node scripts/init-database.js
```

### 第三步：云函数部署

#### 3.1 部署核心云函数
在微信开发者工具中，右键以下云函数文件夹并选择"上传并部署"：

**必需的云函数：**
- [ ] `login` - 用户登录
- [ ] `getUserId` - 获取用户ID
- [ ] `getStudents` - 获取学生列表
- [ ] `callDoubaoAPI` - AI服务调用
- [ ] `generateComment` - 评语生成
- [ ] `adminAPI` - 管理后台API
- [ ] `initDatabase` - 数据库初始化

#### 3.2 配置云函数环境变量
在云开发控制台中，为每个云函数配置环境变量：
```
DOUBAO_API_KEY=你的豆包API密钥
ENCRYPTION_KEY=你的加密密钥
JWT_SECRET=你的JWT密钥
```

#### 3.3 测试云函数
```bash
# 运行云函数测试
node scripts/test-cloud-functions.js
```

### 第四步：管理后台部署

#### 4.1 构建管理后台
```bash
cd admin-v2

# 安装依赖
npm install

# 构建生产版本
npm run build
```

#### 4.2 部署到云开发静态托管
1. 在云开发控制台中开通静态网站托管
2. 上传`admin-v2/build`目录中的所有文件
3. 配置默认首页为`index.html`
4. 配置404页面为`index.html`（支持SPA路由）

#### 4.3 配置域名（可选）
1. 在云开发控制台中添加自定义域名
2. 配置SSL证书
3. 设置CNAME记录

### 第五步：小程序配置

#### 5.1 配置服务器域名
在微信公众平台中配置以下域名：
```
request合法域名：
- https://你的云环境ID.tcb.qcloud.la
- https://ark.cn-beijing.volces.com

uploadFile合法域名：
- https://你的云环境ID.tcb.qcloud.la

downloadFile合法域名：
- https://你的云环境ID.tcb.qcloud.la
```

#### 5.2 配置业务域名
```
业务域名：
- https://你的管理后台域名
```

#### 5.3 上传小程序代码
1. 在微信开发者工具中点击"上传"
2. 填写版本号和项目备注
3. 上传成功后在微信公众平台提交审核

### 第六步：部署验证

#### 6.1 运行部署检查
```bash
# 运行完整的部署检查
node scripts/pre-deploy-check.js

# 检查结果应该显示：
# ✅ 所有检查项通过
# ⚠️ 警告项已处理
# ❌ 错误项已修复
```

#### 6.2 功能测试
- [ ] **用户登录**：微信授权登录正常
- [ ] **学生管理**：添加、编辑、删除学生信息
- [ ] **AI生成**：评语生成功能正常
- [ ] **数据同步**：各页面数据实时同步
- [ ] **管理后台**：配置管理功能正常

#### 6.3 性能测试
```bash
# 运行性能测试
node scripts/performance-test.js

# 检查指标：
# - 云函数响应时间 < 2秒
# - 数据库查询时间 < 500ms
# - AI API响应时间 < 30秒
# - 页面加载时间 < 3秒
```

## 🔧 生产环境优化

### 1. 性能优化
```bash
# 启用缓存
# 在云函数中配置Redis缓存（可选）

# 数据库索引优化
# 在云开发控制台中为常用查询字段创建索引

# CDN加速
# 为静态资源配置CDN加速
```

### 2. 监控配置
```bash
# 配置错误监控
# 集成Sentry或其他监控服务

# 配置性能监控
# 启用云开发自带的监控功能

# 配置告警
# 设置异常情况的邮件或短信告警
```

### 3. 安全加固
```bash
# 配置访问控制
# 设置云函数的访问权限

# 配置数据库安全规则
# 在云开发控制台中配置数据库安全规则

# 配置API限流
# 防止恶意调用和攻击
```

## 🚨 常见问题

### Q1: 云函数部署失败
**解决方案：**
```bash
# 检查云函数代码语法
# 检查依赖包是否正确安装
# 检查云开发环境是否正常

# 重新部署
右键云函数文件夹 -> 删除并重新上传
```

### Q2: AI服务调用失败
**解决方案：**
```bash
# 检查API密钥是否正确
# 检查网络连接是否正常
# 检查API调用频率是否超限

# 测试API连接
node scripts/test-ai-api.js
```

### Q3: 管理后台无法访问
**解决方案：**
```bash
# 检查静态托管是否开通
# 检查文件是否正确上传
# 检查域名配置是否正确

# 重新部署
npm run build
# 重新上传build目录
```

### Q4: 小程序审核被拒
**常见原因：**
- 缺少用户协议和隐私政策
- 功能描述不清晰
- 存在违规内容

**解决方案：**
- 完善用户协议和隐私政策
- 优化功能描述和截图
- 移除可能违规的内容

## 📞 技术支持

如果在部署过程中遇到问题，可以通过以下方式获取帮助：

- **GitHub Issues**: [提交问题](https://github.com/chanwarmsun/wechat3.0/issues)
- **技术文档**: [查看文档](https://github.com/chanwarmsun/wechat3.0/wiki)
- **社区讨论**: [参与讨论](https://github.com/chanwarmsun/wechat3.0/discussions)

## 🎉 部署完成

恭喜！你已经成功部署了评语灵感君小程序系统。

**下一步：**
1. 通过管理后台配置AI模型和提示词模板
2. 邀请教师用户开始使用
3. 监控系统运行状态
4. 根据用户反馈持续优化

**记住：**
- 定期备份数据库
- 监控API使用量和成本
- 及时更新系统和依赖
- 关注用户反馈和建议

---

🚀 **让AI赋能教育，让评语更有温度！**
