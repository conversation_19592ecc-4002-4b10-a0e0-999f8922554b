@echo off
chcp 65001 >nul
echo ====================================
echo 初始化提示词模板到数据库
echo ====================================
echo.

echo 🚀 开始初始化提示词模板...
echo.

echo 📝 运行初始化脚本...
node init-prompt-templates.js

if %errorlevel% neq 0 (
    echo ❌ 初始化失败
    echo.
    echo 💡 请确认：
    echo   1. 已安装Node.js和npm
    echo   2. 已安装wx-server-sdk依赖
    echo   3. 微信开发者工具已登录
    echo   4. 云开发环境已配置
    echo.
    pause
    exit /b 1
)

echo.
echo ✅ 提示词模板初始化完成！
echo.
echo 📋 已初始化的模板：
echo   - 温和亲切型 (gentle)
echo   - 鼓励激励型 (encouraging) 
echo   - 详细具体型 (detailed)
echo   - 综合发展型 (comprehensive)
echo   - 正式规范型 (formal)
echo.
echo 💡 现在可以在AI配置页面查看和编辑这些模板了！
echo.
pause