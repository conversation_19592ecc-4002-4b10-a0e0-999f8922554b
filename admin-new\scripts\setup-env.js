#!/usr/bin/env node

/**
 * 环境配置设置脚本
 * 跨平台复制环境配置文件
 */

const fs = require('fs')
const path = require('path')

// 颜色输出函数
const colors = {
  green: (text) => `\x1b[32m${text}\x1b[0m`,
  yellow: (text) => `\x1b[33m${text}\x1b[0m`,
  blue: (text) => `\x1b[34m${text}\x1b[0m`,
  cyan: (text) => `\x1b[36m${text}\x1b[0m`,
  bold: (text) => `\x1b[1m${text}\x1b[0m`
}

const main = () => {
  const rootDir = path.resolve(__dirname, '..')
  const exampleFile = path.join(rootDir, '.env.example')
  const localFile = path.join(rootDir, '.env.local')
  
  console.log(colors.bold(colors.cyan('🔧 Setting up environment configuration...\n')))
  
  try {
    // 检查 .env.example 是否存在
    if (!fs.existsSync(exampleFile)) {
      console.log(colors.yellow('❌ .env.example file not found!'))
      process.exit(1)
    }
    
    // 检查 .env.local 是否已存在
    if (fs.existsSync(localFile)) {
      console.log(colors.yellow('⚠️  .env.local already exists!'))
      
      // 创建备份
      const backupFile = path.join(rootDir, '.env.local.backup')
      fs.copyFileSync(localFile, backupFile)
      console.log(colors.blue(`📋 Created backup: .env.local.backup`))
    }
    
    // 复制文件
    fs.copyFileSync(exampleFile, localFile)
    console.log(colors.green('✅ Successfully created .env.local from .env.example'))
    
    // 读取并显示需要配置的关键项
    const content = fs.readFileSync(localFile, 'utf8')
    const needsConfig = []
    
    content.split('\n').forEach(line => {
      line = line.trim()
      if (line.includes('your-') || line.includes('your_')) {
        const [key] = line.split('=')
        if (key && !key.startsWith('#')) {
          needsConfig.push(key.trim())
        }
      }
    })
    
    if (needsConfig.length > 0) {
      console.log(colors.yellow('\n⚠️  Please configure the following variables in .env.local:'))
      needsConfig.forEach(key => {
        console.log(colors.yellow(`   • ${key}`))
      })
    }
    
    console.log(colors.bold(colors.green('\n🎉 Environment setup complete!')))
    console.log(colors.cyan('💡 Next steps:'))
    console.log(colors.cyan('   1. Edit .env.local with your actual configuration values'))
    console.log(colors.cyan('   2. Run: npm run env:check'))
    console.log(colors.cyan('   3. Run: npm install'))
    console.log(colors.cyan('   4. Run: npm run dev'))
    
  } catch (error) {
    console.log(colors.yellow(`❌ Error: ${error.message}`))
    process.exit(1)
  }
}

main()