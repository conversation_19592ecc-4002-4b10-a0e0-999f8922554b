/* 快速记录 - 莫兰迪简约设计 */
.morandi-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #F0F2F5 0%, #E4E7ED 100%);
  padding: 16rpx 16rpx 32rpx 16rpx; /* 底部减少padding，保存按钮已移入容器内 */
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
}

/* 页面标题 */
.page-header {
  margin-bottom: 48rpx;
}

.page-title {
  font-size: 64rpx;
  font-weight: 700;
  color: #2C3E50;
  margin-bottom: 16rpx;
  letter-spacing: 2rpx;
  text-shadow: 0 2rpx 8rpx rgba(84, 112, 198, 0.1);
}

.page-subtitle {
  font-size: 28rpx;
  color: #606266;
  font-weight: 400;
  margin-bottom: 32rpx;
}

/* 功能卡片 - 统一设计 */
.function-card {
  background: rgba(255, 255, 255, 0.85);
  border-radius: 32rpx;
  padding: 32rpx;
  margin-bottom: 32rpx;
  box-shadow: 0 4rpx 20rpx rgba(84, 112, 198, 0.1);
  border: 1rpx solid rgba(84, 112, 198, 0.08);
  backdrop-filter: blur(10rpx);
  transition: all 0.3s ease;
}

.function-card:active {
  transform: translateY(2rpx);
  background: rgba(255, 255, 255, 0.95);
  box-shadow: 0 2rpx 12rpx rgba(84, 112, 198, 0.15);
}

/* 卡片头部 */
.card-header {
  display: flex;
  align-items: center;
  margin-bottom: 24rpx;
  gap: 24rpx;
}

.card-icon-wrapper {
  flex-shrink: 0;
}

.card-icon {
  width: 88rpx;
  height: 88rpx;
  border-radius: 22rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 36rpx;
  color: #ffffff;
  box-shadow: 0 6rpx 20rpx rgba(84, 112, 198, 0.25);
  transition: all 0.3s ease;
}

.student-icon {
  background: linear-gradient(135deg, #5470C6 0%, #73C0DE 100%);
}

.behavior-icon {
  background: linear-gradient(135deg, #FAC858 0%, #FC8452 100%);
}

.action-icon {
  background: linear-gradient(135deg, #91CC75 0%, #5CB85C 100%);
}

.score-icon {
  background: linear-gradient(135deg, #EE6666 0%, #FF9F7F 100%);
}

.desc-icon {
  background: linear-gradient(135deg, #73C0DE 0%, #3BA272 100%);
}

.image-icon {
  background: linear-gradient(135deg, #9A60B4 0%, #EA7CCC 100%);
}

.icon-text {
  font-size: 36rpx;
  font-weight: 600;
}

.card-info {
  flex: 1;
}

.card-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #2C3E50;
  margin-bottom: 8rpx;
  line-height: 1.3;
}

.card-desc {
  font-size: 24rpx;
  color: #606266;
  line-height: 1.4;
}

/* 已选择学生卡片 */
.selected-student-card {
  display: flex;
  align-items: center;
  gap: 16rpx;
  padding: 16rpx 20rpx;
  background: rgba(248, 249, 250, 0.8);
  border-radius: 16rpx;
  border: 1rpx solid rgba(84, 112, 198, 0.1);
  margin-top: 8rpx;
}

.student-avatar-wrapper {
  flex-shrink: 0;
}

.student-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  overflow: hidden;
  box-shadow: 0 4rpx 12rpx rgba(84, 112, 198, 0.2);
}

.avatar-image {
  width: 100%;
  height: 100%;
  border-radius: 50%;
}

.avatar-placeholder {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  background: linear-gradient(135deg, #5470C6, #73C0DE);
  display: flex;
  align-items: center;
  justify-content: center;
}

.avatar-text {
  font-size: 28rpx;
  font-weight: 600;
  color: white;
}

.student-info {
  flex: 1;
}

.student-name {
  font-size: 32rpx;
  font-weight: 600;
  color: #2C3E50;
  margin-bottom: 8rpx;
}

.student-class {
  font-size: 24rpx;
  color: #606266;
}

.change-btn {
  padding: 12rpx 20rpx;
  background: rgba(84, 112, 198, 0.1);
  border-radius: 16rpx;
  border: 1rpx solid rgba(84, 112, 198, 0.2);
}

.change-text {
  font-size: 24rpx;
  color: #5470C6;
  font-weight: 500;
}

/* 学生选择器 */
.student-selector {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx;
  background: rgba(84, 112, 198, 0.05);
  border-radius: 24rpx;
  border: 2rpx dashed rgba(84, 112, 198, 0.2);
  transition: all 0.3s ease;
  margin-top: 16rpx;
}

.student-selector:active {
  background: rgba(84, 112, 198, 0.1);
  border-color: rgba(84, 112, 198, 0.3);
  transform: scale(0.98);
}

.selector-content {
  flex: 1;
}

.selector-text {
  font-size: 28rpx;
  color: #2C3E50;
  font-weight: 500;
  margin-bottom: 4rpx;
}

.selector-hint {
  font-size: 22rpx;
  color: #606266;
  opacity: 0.8;
}

.selector-arrow {
  font-size: 24rpx;
  color: #5470C6;
  font-weight: 600;
}

/* 行为类型网格 */
.behavior-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16rpx;
  margin-top: 16rpx;
}

.behavior-option {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 24rpx;
  background: rgba(248, 249, 250, 0.8);
  border-radius: 20rpx;
  border: 2rpx solid transparent;
  transition: all 0.3s ease;
  text-align: center;
}

.behavior-option.active {
  background: rgba(84, 112, 198, 0.1);
  border-color: #5470C6;
  box-shadow: 0 2rpx 12rpx rgba(84, 112, 198, 0.15);
}

.behavior-emoji {
  font-size: 40rpx;
  margin-bottom: 12rpx;
}

.behavior-name {
  font-size: 26rpx;
  color: #2C3E50;
  font-weight: 500;
}

/* 具体行为标签 */
.action-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 12rpx;
  margin-top: 16rpx;
  margin-bottom: 16rpx;
}

.action-tag {
  padding: 12rpx 20rpx;
  background: rgba(248, 249, 250, 0.8);
  border-radius: 20rpx;
  border: 2rpx solid transparent;
  font-size: 24rpx;
  color: #606266;
  transition: all 0.3s ease;
}

.action-tag.active {
  background: rgba(84, 112, 198, 0.1);
  border-color: #5470C6;
  color: #5470C6;
  font-weight: 500;
}

.custom-input {
  margin-top: 16rpx;
}

.behavior-type {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 24rpx;
  border-radius: 16rpx;
  background: #f8f9fa;
  border: 2rpx solid transparent;
  transition: all 0.3s ease;
}

.behavior-type.active {
  background: rgba(64, 128, 255, 0.1);
  border-color: #4080FF;
}

.type-icon {
  width: 56rpx;
  height: 56rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 12rpx;
}

.type-icon.positive { background: linear-gradient(135deg, #52C873, #7DD87F); }
.type-icon.negative { background: linear-gradient(135deg, #FF5247, #FF7875); }
.type-icon.neutral { background: linear-gradient(135deg, #FFAA33, #FFB84D); }

.type-name {
  font-size: 26rpx;
  color: #333;
  font-weight: 500;
}

/* 具体行为 */
.quick-actions {
  display: flex;
  flex-wrap: wrap;
  gap: 12rpx;
  margin-bottom: 24rpx;
}

.quick-action {
  padding: 16rpx 24rpx;
  background: #f8f9fa;
  border-radius: 20rpx;
  font-size: 26rpx;
  color: #666;
  border: 2rpx solid transparent;
  transition: all 0.3s ease;
}

.quick-action.active {
  background: rgba(64, 128, 255, 0.1);
  color: #4080FF;
  border-color: #4080FF;
}

.action-input {
  background: #f8f9fa !important;
  border-radius: 16rpx !important;
  padding: 24rpx !important;
  font-size: 28rpx !important;
}

/* 评分区域 */
.score-value {
  font-size: 28rpx;
  color: #4080FF;
  font-weight: 600;
}

.score-slider {
  margin: 32rpx 0;
}

.score-labels {
  display: flex;
  justify-content: space-between;
  margin-top: 16rpx;
}

.score-label {
  font-size: 24rpx;
  color: #999;
}

/* 描述区域 */
.description-textarea {
  background: #f8f9fa !important;
  border-radius: 16rpx !important;
  padding: 24rpx !important;
  font-size: 28rpx !important;
  min-height: 120rpx !important;
}

/* 记录内容容器 */
.record-container {
  margin-top: 16rpx;
}

.record-textarea {
  background: rgba(248, 249, 250, 0.8) !important;
  border-radius: 16rpx !important;
  border: 1rpx solid rgba(84, 112, 198, 0.1) !important;
  min-height: 160rpx !important;
  font-size: 28rpx !important;
  line-height: 1.6 !important;
  padding: 24rpx !important;
}

/* 简化的记录卡片 */
.record-card {
  background: #FFFFFF;
  border-radius: 24rpx;
  margin-bottom: 12rpx;
  box-shadow: 0 8rpx 32rpx rgba(84, 112, 198, 0.08);
  border: 1rpx solid rgba(84, 112, 198, 0.08);
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

/* 简化标题 */
.record-title {
  background: linear-gradient(135deg, #5470C6 0%, #73C0DE 100%);
  padding: 24rpx 32rpx;
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.title-icon {
  font-size: 28rpx;
}

.title-text {
  font-size: 28rpx;
  font-weight: 700;
  color: #FFFFFF;
  flex: 1;
}

/* 输入区域 */
.input-area {
  padding: 24rpx 24rpx 48rpx 24rpx;
  background: #FFFFFF;
  display: flex;
  flex-direction: column;
}

.content-input {
  width: 100%;
  min-height: 200rpx;
  padding: 20rpx;
  font-size: 28rpx;
  line-height: 1.5;
  color: #2C3E50;
  background: rgba(248, 249, 250, 0.6);
  border: 2rpx solid rgba(84, 112, 198, 0.1);
  border-radius: 12rpx;
  outline: none;
  resize: none;
  box-sizing: border-box;
  transition: all 0.3s ease;
}

.content-input:focus {
  border-color: #5470C6;
  background: #FFFFFF;
  box-shadow: 0 4rpx 16rpx rgba(84, 112, 198, 0.12);
}

.content-input::placeholder {
  color: #C0C4CC;
  font-size: 28rpx;
}

/* 底部工具栏 */
.input-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 16rpx;
  padding-top: 16rpx;
}

.char-count {
  font-size: 24rpx;
  color: #C0C4CC;
}

.upload-btn {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 12rpx 20rpx;
  background: rgba(84, 112, 198, 0.08);
  border-radius: 20rpx;
  transition: all 0.3s ease;
}

.upload-btn:active {
  background: rgba(84, 112, 198, 0.15);
  transform: scale(0.95);
}

.upload-icon {
  font-size: 20rpx;
}

.upload-label {
  font-size: 24rpx;
  color: #5470C6;
  font-weight: 500;
}

/* 评分滑杆区域 */
.score-section {
  margin-top: 20rpx;
  padding: 20rpx 0 0;
  min-height: 100rpx;
}

.score-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.score-label {
  font-size: 28rpx;
  font-weight: 600;
  color: #2C3E50;
}

.score-value-display {
  display: flex;
  align-items: baseline;
  gap: 4rpx;
  padding: 10rpx 16rpx;
  background: linear-gradient(135deg, rgba(84, 112, 198, 0.1) 0%, rgba(115, 192, 222, 0.1) 100%);
  border-radius: 16rpx;
  min-width: 88rpx;
  width: 88rpx;
  justify-content: center;
  flex-shrink: 0;
}

.score-number {
  font-size: 36rpx;
  font-weight: 700;
  color: #5470C6;
  line-height: 1;
}

.score-text {
  font-size: 20rpx;
  color: #606266;
  font-weight: 500;
}

.score-slider-area {
  position: relative;
  padding: 0 32rpx 16rpx 32rpx;
  margin-bottom: 48rpx; /* 增加底部间距 */
}

/* 自定义原生slider样式 */
.custom-slider {
  width: 100%;
  height: 20rpx;
  margin: 0;
  box-sizing: border-box;
}

.score-marks {
  display: flex;
  justify-content: space-between;
  margin-top: 24rpx;
  padding: 0 20rpx 16rpx 20rpx; /* 减少底部padding */
  position: relative;
  z-index: 1;
}

.mark-label {
  font-size: 26rpx; /* 稍微增大字体 */
  color: #909399;
  font-weight: 500;
  text-align: center;
  line-height: 1.3;
  min-height: 40rpx; /* 增加最小高度 */
  display: flex;
  align-items: center;
  justify-content: center;
}

.main-record-container {
  margin-top: 24rpx;
}

/* 图片展示区域 */
.images-section {
  padding: 0 32rpx 24rpx;
  background: #FFFFFF;
}

.images-list {
  display: flex;
  flex-wrap: wrap;
  gap: 12rpx;
}

.image-preview {
  position: relative;
  width: 120rpx;
  height: 120rpx;
  border-radius: 12rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx rgba(84, 112, 198, 0.1);
}

.preview-img {
  width: 100%;
  height: 100%;
}

.remove-btn {
  position: absolute;
  top: -8rpx;
  right: -8rpx;
  width: 36rpx;
  height: 36rpx;
  background: #FF5247;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 12rpx rgba(255, 82, 71, 0.4);
  z-index: 10;
  /* 增加点击区域 */
  padding: 4rpx;
  margin: -4rpx;
  /* 添加点击反馈 */
  transition: all 0.2s ease;
}

.remove-btn:active {
  transform: scale(0.9);
  background: #E6463D;
  box-shadow: 0 2rpx 8rpx rgba(255, 82, 71, 0.6);
}

.remove-icon {
  font-size: 20rpx;
  color: #FFFFFF;
  font-weight: 700;
  line-height: 1;
  pointer-events: none; /* 防止图标本身阻挡点击 */
}







/* 保存按钮 - 紧跟容器底部 */
.submit-btn {
  margin: 32rpx 32rpx 32rpx 32rpx;
  height: 96rpx;
  background: linear-gradient(135deg, #5470C6 0%, #73C0DE 100%);
  border-radius: 24rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 8rpx 24rpx rgba(84, 112, 198, 0.3);
  transition: all 0.3s ease;
}

.submit-btn:active {
  transform: translateY(2rpx);
  box-shadow: 0 6rpx 20rpx rgba(84, 112, 198, 0.35);
}

.submit-btn.loading {
  background: linear-gradient(135deg, #4a5f96 0%, #5fa0b5 100%);
  box-shadow: 0 4rpx 16rpx rgba(84, 112, 198, 0.3);
  pointer-events: none;
}

.submit-btn.disabled {
  background: linear-gradient(135deg, #c0c4cc 0%, #d3d7dd 100%);
  box-shadow: 0 2rpx 8rpx rgba(192, 196, 204, 0.2);
  cursor: not-allowed;
  pointer-events: none;
}

.submit-content {
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.submit-icon {
  font-size: 24rpx;
  color: #FFFFFF;
  font-weight: 600;
}

.submit-text {
  font-size: 30rpx;
  color: #FFFFFF;
  font-weight: 600;
}

.record-icon {
  background: linear-gradient(135deg, #73C0DE 0%, #3BA272 100%);
}



/* 图片上传区域 */
.image-section {
  background: white;
  border-radius: 20rpx;
  padding: 32rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.06);
}

.image-count {
  font-size: 24rpx;
  color: #999;
  font-weight: 400;
}

.image-list {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
}

.image-item {
  position: relative;
  width: 160rpx;
  height: 160rpx;
  border-radius: 16rpx;
  overflow: hidden;
}

.uploaded-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.image-delete {
  position: absolute;
  top: 8rpx;
  right: 8rpx;
  width: 32rpx;
  height: 32rpx;
  background: rgba(0, 0, 0, 0.6);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.image-upload {
  width: 160rpx;
  height: 160rpx;
  border: 2rpx dashed #d0d0d0;
  border-radius: 16rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: #fafafa;
  transition: all 0.3s ease;
}

.image-upload:active {
  background: #f0f0f0;
  transform: scale(0.95);
}

.upload-text {
  font-size: 24rpx;
  color: #999;
  margin-top: 8rpx;
}



/* 底部弹窗样式 */
.bottom-popup-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.bottom-popup-mask.show {
  opacity: 1;
  visibility: visible;
}

.bottom-popup {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  border-radius: 32rpx 32rpx 0 0;
  max-height: 70vh;
  transform: translateY(100%);
  transition: transform 0.3s ease;
  display: flex;
  flex-direction: column;
}

.bottom-popup.show {
  transform: translateY(0);
}

/* 拖拽指示器 */
.drag-indicator {
  width: 80rpx;
  height: 8rpx;
  background: rgba(0, 0, 0, 0.1);
  border-radius: 4rpx;
  margin: 16rpx auto 8rpx;
}

/* 弹窗标题栏 */
.popup-title-bar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx 32rpx;
  border-bottom: 1rpx solid rgba(84, 112, 198, 0.1);
}

.popup-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #2C3E50;
}

.close-icon {
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background: rgba(84, 112, 198, 0.1);
  font-size: 32rpx;
  color: #5470C6;
  transition: all 0.3s ease;
}

.close-icon:active {
  background: rgba(84, 112, 198, 0.2);
  transform: scale(0.9);
}

/* 搜索容器 */
.search-container {
  padding: 24rpx 32rpx;
  border-bottom: 1rpx solid rgba(84, 112, 198, 0.1);
}

.search-box {
  position: relative;
  background: rgba(248, 249, 250, 0.8);
  border-radius: 24rpx;
  border: 1rpx solid rgba(84, 112, 198, 0.1);
  width: 100%;
  min-height: 80rpx;
  display: flex;
  align-items: center;
}

.search-input {
  width: 100%;
  padding: 24rpx 80rpx 24rpx 32rpx;
  font-size: 28rpx;
  color: #2C3E50;
  background: transparent;
  border: none;
  outline: none;
  box-sizing: border-box;
  height: 80rpx;
  line-height: 80rpx;
}

.search-input::placeholder {
  color: #909399;
  font-size: 28rpx;
}

.search-clear {
  position: absolute;
  right: 16rpx;
  top: 50%;
  transform: translateY(-50%);
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background: rgba(84, 112, 198, 0.1);
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.search-clear.show {
  opacity: 1;
  visibility: visible;
}

.clear-icon {
  font-size: 24rpx;
  color: #5470C6;
}

.search-tip {
  margin-top: 16rpx;
  text-align: center;
}

.tip-text {
  font-size: 24rpx;
  color: #606266;
}

/* 学生列表 */
.student-scroll-list {
  flex: 1;
  max-height: 50vh;
}

.student-row {
  display: flex;
  align-items: center;
  padding: 24rpx 32rpx;
  transition: all 0.15s cubic-bezier(0.4, 0, 0.2, 1);
  border-bottom: 1rpx solid rgba(84, 112, 198, 0.05);
  border-radius: 12rpx;
  margin: 4rpx 0;
}

.student-row:active {
  background: rgba(84, 112, 198, 0.08);
  transform: scale(0.98);
  transition: all 0.08s ease-out;
}

.student-row.selected {
  background: rgba(84, 112, 198, 0.12);
  border: 2rpx solid rgba(84, 112, 198, 0.2);
  box-shadow: 0 2rpx 8rpx rgba(84, 112, 198, 0.1);
  transform: scale(1.01);
}

.student-row .student-info {
  display: flex;
  align-items: center;
  flex: 1;
}

.student-row .student-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  background: linear-gradient(135deg, #5470C6, #73C0DE);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 24rpx;
  font-size: 28rpx;
  font-weight: 600;
  color: white;
  flex-shrink: 0;
}

.student-row .student-details {
  flex: 1;
}

.student-row .student-name {
  font-size: 32rpx;
  font-weight: 600;
  color: #2C3E50;
  margin-bottom: 8rpx;
}

.student-row .student-class {
  font-size: 24rpx;
  color: #606266;
}

/* 注意：选择学生的样式已移至 app.wxss 全局样式中 */

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80rpx 40rpx;
  text-align: center;
}

.empty-icon {
  font-size: 80rpx;
  margin-bottom: 24rpx;
  opacity: 0.6;
}

.empty-text {
  font-size: 28rpx;
  color: #606266;
  margin-bottom: 12rpx;
}

.empty-hint {
  font-size: 24rpx;
  color: #909399;
}

/* 保存成功弹窗样式 */
.save-success-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.save-success-mask.show {
  opacity: 1;
  visibility: visible;
}

.save-success-dialog {
  background: #fff;
  border-radius: 32rpx;
  padding: 48rpx 32rpx 32rpx 32rpx;
  margin: 32rpx;
  max-width: 600rpx;
  width: 90%;
  box-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.15);
  transform: translateY(50rpx) scale(0.9);
  transition: all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
}

.save-success-dialog.show {
  transform: translateY(0) scale(1);
}

/* 成功图标 */
.success-icon-wrapper {
  display: flex;
  justify-content: center;
  margin-bottom: 32rpx;
}

.success-icon {
  width: 120rpx;
  height: 120rpx;
  background: linear-gradient(135deg, #67C23A, #85CE61);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 60rpx;
  color: #fff;
  font-weight: bold;
  box-shadow: 0 8rpx 24rpx rgba(103, 194, 58, 0.3);
  animation: successPulse 0.6s ease-out;
}

@keyframes successPulse {
  0% {
    transform: scale(0.8);
    opacity: 0.8;
  }
  50% {
    transform: scale(1.1);
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

/* 弹窗内容 */
.dialog-content {
  text-align: center;
  margin-bottom: 48rpx;
}

.dialog-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #2C3E50;
  margin-bottom: 16rpx;
}

.dialog-desc {
  font-size: 28rpx;
  color: #606266;
  line-height: 1.5;
}

.student-name {
  color: #5470C6;
  font-weight: 600;
}

/* 操作按钮 */
.dialog-actions {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.action-row {
  display: flex;
  gap: 16rpx;
}

.action-btn {
  flex: 1;
  height: 88rpx;
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12rpx;
  font-size: 28rpx;
  font-weight: 600;
  border: none;
  transition: all 0.15s cubic-bezier(0.4, 0.0, 0.2, 1);
  position: relative;
  overflow: hidden;
  font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Display', sans-serif;
}

.action-btn::after {
  border: none;
}

/* 莫兰迪风格按钮设计 - 低饱和度配色 */

/* 主要操作按钮 - 查看详情 (主色调) */
.action-btn.primary {
  background: linear-gradient(135deg, #5470C6, #73C0DE);
  color: #fff;
  box-shadow: 0 2rpx 8rpx rgba(84, 112, 198, 0.2);
}

.action-btn.primary:active {
  background: linear-gradient(135deg, #4A63B3, #6BB0C7);
  transform: scale(0.98);
}

/* 次要操作按钮 - 重新编辑 (低调灰蓝) */
.action-btn.secondary {
  background: rgba(84, 112, 198, 0.08);
  color: #5470C6;
  border: 1rpx solid rgba(84, 112, 198, 0.2);
}

.action-btn.secondary:active {
  background: rgba(84, 112, 198, 0.15);
  transform: scale(0.98);
}

/* 继续记录按钮 - 使用辅助色 */
.action-btn.continue {
  background: #91CC75;
  color: #fff;
  box-shadow: 0 2rpx 8rpx rgba(145, 204, 117, 0.2);
}

.action-btn.continue:active {
  background: #82B866;
  transform: scale(0.98);
}

/* 记录其他学生按钮 - 使用强调色 */
.action-btn.new-record {
  background: #FAC858;
  color: #fff;
  box-shadow: 0 2rpx 8rpx rgba(250, 200, 88, 0.2);
}

.action-btn.new-record:active {
  background: #E8B64F;
  transform: scale(0.98);
}

.btn-icon {
  font-size: 32rpx;
}

.btn-text {
  font-size: 28rpx;
}