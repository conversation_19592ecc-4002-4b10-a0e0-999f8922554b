<!--
  记录详情页面
-->
<view class="record-detail-page">
  <!-- 记录基本信息 -->
  <view class="record-info-card">
    <view class="record-header">
      <view class="student-info">
        <view class="student-avatar">
          <image
            wx:if="{{recordInfo.studentAvatar}}"
            src="{{recordInfo.studentAvatar}}"
            class="avatar-image"
            mode="aspectFill"
          />
          <view wx:else class="avatar-placeholder">
            <text class="avatar-text">{{recordInfo.surname || (recordInfo.studentName ? recordInfo.studentName.charAt(0) : '学')}}</text>
          </view>
        </view>
        <view class="student-details" bindtap="showActionSheet">
          <view class="student-name clickable">{{recordInfo.studentName}}</view>
          <view class="student-class">{{recordInfo.className}}</view>
        </view>
      </view>

      <view class="record-score {{recordInfo.behaviorType}}">
        <text class="score-text">{{recordInfo.score}}分</text>
      </view>
    </view>

    <view class="behavior-info">
      <view class="behavior-type {{recordInfo.behaviorType}}">
        <van-icon name="{{recordInfo.typeIcon}}" size="16px" color="#fff" />
        <text class="type-text">{{recordInfo.typeName}}</text>
      </view>
      <view class="behavior-action">{{recordInfo.action}}</view>
    </view>

    <view wx:if="{{recordInfo.description}}" class="record-description">
      <view class="desc-title">详细描述</view>
      <view class="desc-content">{{recordInfo.description}}</view>
    </view>

    <view class="record-meta">
      <view class="meta-item">
        <van-icon name="clock-o" size="14px" color="#999" />
        <text class="meta-text">记录时间: {{recordInfo.createTime}}</text>
      </view>
      <view class="meta-item">
        <van-icon name="user-o" size="14px" color="#999" />
        <text class="meta-text">记录人: {{recordInfo.teacherName}}</text>
      </view>
    </view>
  </view>

  <!-- 图片证据 -->
  <view wx:if="{{recordInfo.images && recordInfo.images.length > 0}}" class="images-section">
    <view class="section-title">图片证据</view>
    <view class="image-list">
      <view
        wx:for="{{recordInfo.images}}"
        wx:key="index"
        class="image-item"
        data-index="{{index}}"
        bindtap="previewImage"
      >
        <image
          src="{{item}}"
          class="record-image"
          mode="aspectFill"
          binderror="onImageError"
          data-src="{{item}}"
        />
      </view>
    </view>
  </view>

  <!-- 相关记录 -->
  <view wx:if="{{relatedRecords.length > 0}}" class="related-section">
    <view class="section-title">该学生的其他记录</view>
    <view class="related-list">
      <view
        wx:for="{{relatedRecords}}"
        wx:key="id"
        class="related-item"
        data-record="{{item}}"
        bindtap="goToRecord"
      >
        <view class="related-icon {{item.behaviorType}}">
          <van-icon name="{{item.typeIcon}}" size="12px" color="#fff" />
        </view>
        <view class="related-content">
          <view class="related-action">{{item.action}}</view>
          <view class="related-time">{{item.createTime}}</view>
        </view>
        <view class="related-score">{{item.score}}分</view>
      </view>
    </view>
  </view>

  <!-- 操作历史 -->
  <view wx:if="{{operationHistory.length > 0}}" class="history-section">
    <view class="section-title">操作历史</view>
    <view class="history-list">
      <view
        wx:for="{{operationHistory}}"
        wx:key="id"
        class="history-item"
      >
        <view class="history-icon">
          <van-icon name="{{item.icon}}" size="14px" color="#4080FF" />
        </view>
        <view class="history-content">
          <view class="history-action">{{item.action}}</view>
          <view class="history-time">{{item.time}}</view>
        </view>
        <view class="history-user">{{item.user}}</view>
      </view>
    </view>
  </view>



  <!-- 操作菜单 -->
  <van-action-sheet
    show="{{showActionSheet}}"
    title="更多操作"
    actions="{{actionSheetActions}}"
    bind:close="hideActionSheet"
    bind:select="onActionSelect"
  />

  <!-- 删除确认弹窗 -->
  <van-dialog
    show="{{showDeleteDialog}}"
    title="确认删除"
    message="确定要删除这条记录吗？删除后无法恢复。"
    show-cancel-button
    confirm-button-text="删除"
    confirm-button-color="#FF5247"
    bind:confirm="confirmDelete"
    bind:cancel="cancelDelete"
  />
</view>