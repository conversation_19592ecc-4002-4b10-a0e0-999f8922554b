/**
 * 云函数诊断脚本
 * 精确定位为什么仍显示备用方案
 * 在小程序开发者工具控制台中运行
 */

console.log('🔍 开始诊断云函数问题...');

async function diagnoseCloudFunction() {
  try {
    // 步骤1: 验证数据库配置
    console.log('\n📋 步骤1: 验证数据库中的AI配置');
    const configResult = await wx.cloud.database().collection('system_config')
      .where({ type: 'ai_config', status: 'active' })
      .orderBy('updateTime', 'desc')
      .limit(1)
      .get();
    
    if (configResult.data && configResult.data.length > 0) {
      const config = configResult.data[0];
      console.log('✅ 数据库配置正常:', {
        id: config._id,
        hasApiKey: !!config.apiKey,
        apiKeyPrefix: config.apiKey ? config.apiKey.substring(0, 8) + '...' : 'None',
        model: config.model,
        apiUrl: config.apiUrl,
        status: config.status,
        updateTime: config.updateTime
      });
    } else {
      console.log('❌ 数据库中没有找到活跃的AI配置');
      return;
    }

    // 步骤2: 测试 doubaoAI 云函数
    console.log('\n🧪 步骤2: 测试 doubaoAI 云函数');
    try {
      const doubaoResult = await wx.cloud.callFunction({
        name: 'doubaoAI',
        data: {
          prompt: '请简单回复：配置测试成功',
          temperature: 0.1,
          max_tokens: 20
        }
      });
      
      console.log('doubaoAI 云函数响应:', doubaoResult);
      
      if (doubaoResult.result) {
        const result = doubaoResult.result;
        console.log('doubaoAI 响应详情:', {
          success: result.success,
          isFallback: result.isFallback,
          message: result.message,
          hasContent: !!(result.data && result.data.content),
          contentPreview: result.data && result.data.content ? result.data.content.substring(0, 50) : 'None'
        });
        
        if (result.isFallback) {
          console.log('⚠️ doubaoAI 使用了备用方案');
        } else {
          console.log('✅ doubaoAI 使用真实API成功');
        }
      }
    } catch (error) {
      console.error('❌ doubaoAI 云函数调用失败:', error);
    }

    // 步骤3: 测试 callDoubaoAPI 云函数
    console.log('\n🧪 步骤3: 测试 callDoubaoAPI 云函数');
    try {
      const callDoubaoResult = await wx.cloud.callFunction({
        name: 'callDoubaoAPI',
        data: {
          prompt: '请简单回复：API测试成功',
          temperature: 0.1,
          max_tokens: 20
        }
      });
      
      console.log('callDoubaoAPI 云函数响应:', callDoubaoResult);
      
      if (callDoubaoResult.result) {
        const result = callDoubaoResult.result;
        console.log('callDoubaoAPI 响应详情:', {
          success: result.success,
          isFallback: result.isFallback,
          message: result.message,
          hasContent: !!(result.data && result.data.content),
          contentPreview: result.data && result.data.content ? result.data.content.substring(0, 50) : 'None'
        });
        
        if (result.isFallback) {
          console.log('⚠️ callDoubaoAPI 使用了备用方案');
        } else {
          console.log('✅ callDoubaoAPI 使用真实API成功');
        }
      }
    } catch (error) {
      console.error('❌ callDoubaoAPI 云函数调用失败:', error);
    }

    // 步骤4: 检查小程序端调用
    console.log('\n🎯 步骤4: 模拟小程序端AI评语生成调用');
    try {
      // 模拟generate.js中的调用方式
      const generateResult = await wx.cloud.callFunction({
        name: 'doubaoAI',
        data: {
          prompt: `你是一位拥有15年教龄、经验丰富、充满爱心且善于观察的中职班主任。你的任务是根据提供的学生日常表现素材，为学生撰写一份温馨亲切、充满关怀的学期综合评语。

学生姓名：
<student_name>
小明同学
</student_name>

学生本学期的日常表现素材如下：
<performance_material>
测试用表现材料
</performance_material>

请严格遵循以下要求撰写评语：
1. **评语结构**：采用"亲切问候 + 优点赞扬 + 温馨建议 + 暖心祝福"的结构。
2. **语气风格**：语言要温馨、亲切、充满爱意。
3. **个性化**：评语必须尊重、保留且紧密结合提供的素材。

请直接生成完整的评语内容，100-150字之间。`,
          model: 'doubao-seed-1-6-flash-250715',
          temperature: 0.7,
          max_tokens: 300
        }
      });
      
      console.log('模拟小程序生成结果:', generateResult);
      
      if (generateResult.result) {
        const result = generateResult.result;
        if (result.success && !result.isFallback) {
          console.log('🎉 小程序端调用成功！生成了真实AI评语');
          console.log('生成的评语:', result.data.content);
        } else if (result.isFallback) {
          console.log('⚠️ 小程序端仍在使用备用方案');
          console.log('备用评语内容:', result.data.content);
        } else {
          console.log('❌ 小程序端调用失败');
        }
      }
    } catch (error) {
      console.error('❌ 模拟小程序调用失败:', error);
    }

    // 步骤5: 诊断结论和建议
    console.log('\n📊 诊断总结:');
    console.log('1. 数据库配置：已验证正确');
    console.log('2. 云函数状态：已测试');
    console.log('3. API调用：已测试');
    
    console.log('\n💡 如果仍显示备用方案，可能的原因：');
    console.log('1. 云函数代码未正确部署到云端');
    console.log('2. 豆包AI API密钥在云端环境中无效');
    console.log('3. 网络连接问题导致API调用失败');
    console.log('4. 云函数运行时环境问题');
    
    console.log('\n🔧 建议解决方案：');
    console.log('1. 重新部署 doubaoAI 和 callDoubaoAPI 云函数');
    console.log('2. 检查云函数日志，查看具体错误信息');
    console.log('3. 在云开发控制台中测试云函数');

  } catch (error) {
    console.error('❌ 诊断过程出错:', error);
  }
}

// 执行诊断
diagnoseCloudFunction().then(() => {
  console.log('\n✅ 诊断完成！请查看上方的详细结果');
}).catch(error => {
  console.error('❌ 诊断失败:', error);
});

console.log('\n📝 使用说明:');
console.log('1. 在小程序开发者工具控制台中运行此脚本');
console.log('2. 查看每个步骤的测试结果');
console.log('3. 根据结果确定问题所在');
console.log('4. 如果所有测试都显示备用方案，说明需要重新部署云函数');