/**
 * 评语灵感君小程序全局样式
 */

/* ==================== CSS变量定义 ==================== */
:root {
  /* 莫兰迪色系 - 科技iOS风格 */
  --primary-400: #5470C6;
  --secondary-400: #91CC75;
  --accent-400: #FAC858;
  --success-400: #73C0DE;
  --warning-400: #FC8452;
  --error-400: #EE6666;

  /* 莫兰迪中性色 */
  --white: #FFFFFF;
  --gray-50: #F8F9FA;
  --gray-100: #F0F2F5;
  --gray-200: #E4E7ED;
  --gray-300: #DCDFE6;
  --gray-400: #C0C4CC;
  --gray-500: #909399;
  --gray-600: #606266;
  --gray-700: #303133;
  --gray-800: #2C3E50;

  /* 字体 */
  --font-family-base: -apple-system, BlinkMacSystemFont, '<PERSON><PERSON><PERSON> UI', <PERSON><PERSON>, sans-serif;
  --font-size-xs: 22rpx;
  --font-size-sm: 26rpx;
  --font-size-base: 30rpx;
  --font-size-lg: 34rpx;
  --font-size-xl: 38rpx;
  --font-size-2xl: 42rpx;
  --font-size-3xl: 52rpx;

  /* 字重 */
  --font-weight-light: 300;
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;

  /* 行高 */
  --line-height-tight: 1.2;
  --line-height-normal: 1.5;
  --line-height-relaxed: 1.8;

  /* 间距 */
  --spacing-base: 32rpx;
}

/* ==================== 全局重置样式 ==================== */
page {
  font-family: var(--font-family-base);
  font-size: var(--font-size-base);
  line-height: var(--line-height-normal);
  color: var(--gray-700);
  background-color: var(--gray-50);
  height: 100%;
}

/* 重置默认样式 */
view, text, button, input, textarea, image {
  box-sizing: border-box;
}

/* 清除按钮默认样式 */
button {
  background: none;
  border: none;
  outline: none;
  font-size: inherit;
  font-family: inherit;
}

button:after {
  display: none;
}

/* 清除输入框默认样式 */
input, textarea {
  font-family: inherit;
  font-size: inherit;
}

/* ==================== 全局工具类 ==================== */

/* 文本对齐 */
.text-left { text-align: left !important; }
.text-center { text-align: center !important; }
.text-right { text-align: right !important; }

/* 文本颜色 */
.text-primary { color: var(--primary-400) !important; }
.text-secondary { color: var(--secondary-400) !important; }
.text-success { color: var(--success-400) !important; }
.text-warning { color: var(--warning-400) !important; }
.text-error { color: var(--error-400) !important; }
.text-muted { color: var(--gray-500) !important; }

/* 背景颜色 */
.bg-primary { background-color: var(--primary-400) !important; }
.bg-secondary { background-color: var(--secondary-400) !important; }
.bg-accent { background-color: var(--accent-400) !important; }
.bg-success { background-color: var(--success-400) !important; }
.bg-warning { background-color: var(--warning-400) !important; }
.bg-error { background-color: var(--error-400) !important; }
.bg-white { background-color: var(--white) !important; }
.bg-gray { background-color: var(--gray-100) !important; }

/* 字体大小 */
.text-xs { font-size: var(--font-size-xs) !important; }
.text-sm { font-size: var(--font-size-sm) !important; }
.text-base { font-size: var(--font-size-base) !important; }
.text-lg { font-size: var(--font-size-lg) !important; }
.text-xl { font-size: var(--font-size-xl) !important; }
.text-2xl { font-size: var(--font-size-2xl) !important; }
.text-3xl { font-size: var(--font-size-3xl) !important; }

/* 字体粗细 */
.font-light { font-weight: var(--font-weight-light) !important; }
.font-normal { font-weight: var(--font-weight-normal) !important; }
.font-medium { font-weight: var(--font-weight-medium) !important; }
.font-semibold { font-weight: var(--font-weight-semibold) !important; }
.font-bold { font-weight: var(--font-weight-bold) !important; }

/* 行高 */
.leading-tight { line-height: var(--line-height-tight) !important; }
.leading-normal { line-height: var(--line-height-normal) !important; }
.leading-relaxed { line-height: var(--line-height-relaxed) !important; }

/* ==================== 布局工具类 ==================== */

/* 容器 */
.container {
  width: 100%;
  margin: 0 auto;
  padding: 0 var(--spacing-base);
}

.container-sm {
  max-width: 640rpx;
}

.container-md {
  max-width: 768rpx;
}

.container-lg {
  max-width: 1024rpx;
}

/* 安全区域 */
.safe-area-top {
  padding-top: env(safe-area-inset-top);
}

.safe-area-bottom {
  padding-bottom: env(safe-area-inset-bottom);
}

/* ==================== 动画类 ==================== */

/* 淡入淡出 */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes fadeOut {
  from { opacity: 1; }
  to { opacity: 0; }
}

.fade-in {
  animation: fadeIn 0.3s ease-in-out;
}

.fade-out {
  animation: fadeOut 0.3s ease-in-out;
}

/* 滑动 */
@keyframes slideInUp {
  from { transform: translateY(100%); }
  to { transform: translateY(0); }
}

@keyframes slideInDown {
  from { transform: translateY(-100%); }
  to { transform: translateY(0); }
}

.slide-in-up {
  animation: slideInUp 0.3s ease-out;
}

.slide-in-down {
  animation: slideInDown 0.3s ease-out;
}

/* 缩放 */
@keyframes scaleIn {
  from { transform: scale(0.8); opacity: 0; }
  to { transform: scale(1); opacity: 1; }
}

.scale-in {
  animation: scaleIn 0.2s ease-out;
}

/* ==================== 状态类 ==================== */

/* 加载状态 */
.loading {
  pointer-events: none;
  opacity: 0.6;
}

/* 禁用状态 */
.disabled {
  pointer-events: none;
  opacity: 0.5;
}

/* ==================== 通用选择学生样式 ==================== */

/* 学生行选择指示器 - 全局通用样式 */
.student-row .selection-indicator {
  width: 48rpx;
  height: 48rpx;
  border-radius: 50%;
  border: 2rpx solid #E4E7ED;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.15s cubic-bezier(0.4, 0, 0.2, 1);
  flex-shrink: 0;
  background: #ffffff;
}

.student-row .selection-indicator.active {
  background: var(--primary-400, #5470C6);
  border-color: var(--primary-400, #5470C6);
  box-shadow: 0 2rpx 8rpx rgba(84, 112, 198, 0.3);
  transform: scale(1.05);
}

.student-row .check-mark {
  font-size: 24rpx;
  color: white;
  font-weight: 600;
  opacity: 0;
  transition: opacity 0.1s cubic-bezier(0.4, 0, 0.2, 1);
  line-height: 1;
}

.student-row .selection-indicator.active .check-mark {
  opacity: 1;
}

/* 选中行的视觉反馈 */
.student-row.selected {
  background: rgba(84, 112, 198, 0.08);
  border-radius: 12rpx;
  transition: all 0.15s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 隐藏 */
.hidden {
  display: none !important;
}

.invisible {
  visibility: hidden !important;
}

/* ==================== 响应式工具类 ==================== */

/* 小屏幕隐藏 */
@media (max-width: 375px) {
  .hidden-sm {
    display: none !important;
  }
}

/* 大屏幕隐藏 */
@media (min-width: 768px) {
  .hidden-lg {
    display: none !important;
  }
}

/* ==================== 特殊效果 ==================== */

/* 毛玻璃效果 */
.backdrop-blur {
  background: rgba(255, 255, 255, 0.8);
}

/* 渐变文字 */
.gradient-text {
  background: linear-gradient(135deg, #4080FF 0%, #5ECBC8 100%);
  color: #4080FF;
}

/* 阴影效果 */
.shadow-text {
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
}
