const { spawn } = require('child_process');
const path = require('path');

// 使用微信开发者工具API清空数据
async function clearDataViaCloud() {
  console.log('🧹 开始清空云端测试数据...');
  
  const cmd = `npx cloudbase@latest database delete --env="your-env-id" --collection students`;
  
  const child = spawn('cmd', ['/c', 'npx cloudbase@latest database delete --env=cloud1-4g85f8xlb8166ff1 --collection students --condition {}'], {
    cwd: "D:\\G盘（软件）\\cursor开发文件\\评语灵感君"
  });

  child.stdout.on('data', (data) => {
    console.log(`stdout: ${data}`);
  });

  child.stderr.on('data', (data) => {
    console.error(`stderr: ${data}`);
  });

  child.on('close', (code) => {
    console.log(`子进程退出，退出码 ${code}`);
    clearAllCollections();
  });
}

async function clearAllCollections() {
  const collections = [
    'users', 'classes', 'students', 'records', 
    'comments', 'settings', 'admins', 'ai_usage', 'user_limits', 'logs'
  ];

  for (const collection of collections) {
    console.log(`正在清空 ${collection}...`);
  }
}

clearDataViaCloud();