# 云函数部署指南

## 📋 概述

为了解决wx-server-sdk在本地环境中的认证问题，我们创建了专门的云函数来处理管理后台的数据查询。

## 🚀 部署步骤

### 1. 上传云函数

1. 打开微信开发者工具
2. 打开你的小程序项目
3. 将 `cloudfunctions/adminDataQuery` 文件夹复制到你的小程序项目的 `cloudfunctions` 目录下
4. 在微信开发者工具中右键点击 `adminDataQuery` 文件夹
5. 选择"上传并部署：云端安装依赖"

### 2. 配置云函数权限

1. 登录微信小程序后台
2. 进入"开发" -> "云开发" -> "云函数"
3. 找到 `adminDataQuery` 云函数
4. 确保云函数有访问数据库的权限

### 3. 测试云函数

在微信开发者工具的云开发控制台中测试云函数：

```javascript
// 测试数据库连接
wx.cloud.callFunction({
  name: 'adminDataQuery',
  data: {
    action: 'testConnection'
  }
}).then(res => {
  console.log('云函数测试结果:', res)
})

// 测试仪表板数据
wx.cloud.callFunction({
  name: 'adminDataQuery',
  data: {
    action: 'getDashboardStats'
  }
}).then(res => {
  console.log('仪表板数据:', res)
})
```

## 🔧 本地API服务器配置

部署云函数后，需要修改本地API服务器以调用云函数：

### 方案A：HTTP API调用云函数

如果你的小程序支持HTTP API调用云函数，可以直接通过HTTP请求调用。

### 方案B：模拟云函数调用

在本地环境中模拟云函数的返回结果，用于开发和测试。

## 📊 支持的数据查询

云函数支持以下数据查询：

- `getDashboardStats` - 获取仪表板统计数据
- `getRecentActivities` - 获取最近活动记录
- `getStudents` - 获取学生数据
- `getComments` - 获取评语记录
- `testConnection` - 测试数据库连接

## 🎯 预期效果

部署成功后：

1. ✅ 云函数可以直接访问你的数据库集合
2. ✅ 不再有wx-server-sdk认证问题
3. ✅ 管理后台可以获取真实的数据库数据
4. ✅ 所有统计数据都是实时的

## 🔍 故障排除

### 问题1：云函数部署失败
- 检查网络连接
- 确保微信开发者工具已登录
- 检查云开发环境是否正确

### 问题2：数据库访问权限不足
- 检查云函数的数据库访问权限
- 确保云开发环境ID正确
- 检查数据库集合是否存在

### 问题3：云函数调用失败
- 检查云函数名称是否正确
- 检查传入参数格式
- 查看云函数日志排查错误

## 📞 技术支持

如果遇到问题，请提供：
1. 错误信息截图
2. 云函数日志
3. 数据库集合结构
4. 微信开发者工具版本

这样我可以快速帮你解决问题！
