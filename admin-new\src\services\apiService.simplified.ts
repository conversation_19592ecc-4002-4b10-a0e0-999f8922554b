/**
 * 简化的API服务 - 直接调用云函数HTTP触发器
 * 替换复杂的数据桥接服务
 */

import { apiClient } from './apiClient'
import type { ApiResponse } from '@/types'

class SimplifiedApiService {
  constructor() {
    console.log('🎯 简化API服务初始化 - 直接HTTP调用云函数')
  }

  /**
   * 健康检查
   */
  async healthCheck(): Promise<any> {
    try {
      const response = await apiClient.post('/', {
        action: 'healthCheck',
        source: 'admin-dashboard'
      })
      return response.data
    } catch (error) {
      console.error('健康检查失败:', error)
      throw error
    }
  }

  /**
   * 获取仪表板统计数据
   */
  async getDashboardStats(): Promise<any> {
    try {
      const response = await apiClient.post('/', {
        action: 'data.getDashboardStats'
      })
      return response.data
    } catch (error) {
      console.error('获取仪表板统计失败:', error)
      throw error
    }
  }

  /**
   * 获取最近活动记录
   */
  async getRecentActivities(limit = 10): Promise<any[]> {
    try {
      const response = await apiClient.post('/', {
        action: 'data.getRecentActivities',
        limit
      })
      return response.data?.data || []
    } catch (error) {
      console.error('获取活动记录失败:', error)
      return []
    }
  }

  /**
   * 获取用户列表
   */
  async getUsersList(): Promise<any[]> {
    try {
      const response = await apiClient.post('/', {
        action: 'data.getStudents'
      })
      return response.data?.data?.students || []
    } catch (error) {
      console.error('获取用户列表失败:', error)
      return []
    }
  }

  /**
   * 获取评语列表
   */
  async getCommentsList(limit = 50): Promise<any[]> {
    try {
      const response = await apiClient.post('/', {
        action: 'data.getRecords',
        params: { limit }
      })
      return response.data?.data?.records || []
    } catch (error) {
      console.error('获取评语列表失败:', error)
      return []
    }
  }

  /**
   * 测试数据库连接
   */
  async testConnection(): Promise<any> {
    try {
      const response = await apiClient.post('/', {
        action: 'data.testConnection'
      })
      return response.data
    } catch (error) {
      console.error('数据库连接测试失败:', error)
      throw error
    }
  }

  /**
   * 获取系统性能指标
   */
  async getSystemMetrics(): Promise<any> {
    try {
      const response = await apiClient.post('/', {
        action: 'data.getSystemMetrics'
      })
      return response.data?.data || {}
    } catch (error) {
      console.error('获取系统指标失败:', error)
      return {}
    }
  }

  /**
   * 获取学生数据
   */
  async getStudents(params: any = {}): Promise<any> {
    try {
      const response = await apiClient.post('/', {
        action: 'data.getStudents',
        params
      })
      return response.data
    } catch (error) {
      console.error('获取学生数据失败:', error)
      throw error
    }
  }

  /**
   * 获取评语记录
   */
  async getRecords(params: any = {}): Promise<any> {
    try {
      const response = await apiClient.post('/', {
        action: 'data.getRecords',
        params
      })
      return response.data
    } catch (error) {
      console.error('获取评语记录失败:', error)
      throw error
    }
  }

  /**
   * 获取班级列表
   */
  async getClasses(params: any = {}): Promise<any> {
    try {
      const response = await apiClient.post('/', {
        action: 'data.getClasses',
        params
      })
      return response.data
    } catch (error) {
      console.error('获取班级列表失败:', error)
      throw error
    }
  }
}

// 导出单例实例
export const simplifiedApiService = new SimplifiedApiService()
export default simplifiedApiService