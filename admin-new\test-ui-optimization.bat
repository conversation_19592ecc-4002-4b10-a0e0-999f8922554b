@echo off
echo.
echo 🎉 UI优化测试启动
echo ========================================
echo.
echo ✅ 已完成的优化:
echo   1. 导航栏品牌名称字体优化
echo   2. 性能监控诊断功能完全移除
echo   3. AI配置、数据管理、系统设置页面样式统一
echo   4. 数据管理页面内容完善
echo   5. 暗色模式全局生效修复
echo.
echo 🎨 现在你可以看到:
echo   - 更大、更粗的品牌名称字体
echo   - 无性能监控干扰的清洁界面
echo   - 统一的页面设计风格
echo   - 完整的暗色模式支持
echo.
echo 📍 访问地址: http://localhost:8080
echo 🌙 可以点击右上角月亮图标测试暗色模式
echo.

REM 清理缓存
if exist "node_modules\.vite" rmdir /s /q "node_modules\.vite"

echo 正在启动优化后的管理后台...
echo.

npm run dev

pause