// 在小程序中创建 user_profiles 集合的代码
// 将此代码粘贴到小程序的某个页面的 onLoad 方法中执行一次

async function createUserProfilesCollection() {
  try {
    console.log('开始创建 user_profiles 集合...')
    
    // 初始化云开发
    if (!wx.cloud) {
      console.error('❌ 云开发未初始化')
      return
    }
    
    const db = wx.cloud.database()
    
    // 尝试创建一条测试数据来触发集合创建
    const testResult = await db.collection('user_profiles').add({
      data: {
        _test: true,
        createTime: new Date(),
        note: '测试数据，用于创建集合'
      }
    })
    
    console.log('✅ user_profiles 集合创建成功:', testResult)
    
    // 删除测试数据
    await db.collection('user_profiles').doc(testResult._id).remove()
    console.log('✅ 测试数据已清理')
    
    console.log('🎉 user_profiles 集合配置完成！')
    
    wx.showToast({
      title: '集合创建成功',
      icon: 'success'
    })
    
    return true
    
  } catch (error) {
    console.error('❌ 创建集合失败:', error)
    
    wx.showToast({
      title: '创建失败: ' + error.message,
      icon: 'none',
      duration: 3000
    })
    
    return false
  }
}

// 导出函数供页面使用
module.exports = {
  createUserProfilesCollection
}