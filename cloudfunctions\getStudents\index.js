/**
 * 获取学生列表云函数
 * 为教师提供学生信息查询服务
 */

const cloud = require('wx-server-sdk')

// 初始化云开发
cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()

/**
 * 云函数入口函数
 */
exports.main = async (event, context) => {
  console.log('获取学生列表请求:', event)
  
  try {
    // 获取用户信息
    const { OPENID } = cloud.getWXContext()
    const { page = 1, limit = 20, keyword = '', classId = '' } = event
    
    if (!OPENID) {
      throw new Error('用户未登录')
    }
    
    // 计算分页参数
    const skip = (page - 1) * limit
    
    // 构建查询条件
    let query = db.collection('students').where({
      teacherId: OPENID
    })
    
    // 添加关键词搜索
    if (keyword) {
      query = query.where({
        name: db.RegExp({
          regexp: keyword,
          options: 'i'
        })
      })
    }
    
    // 添加班级筛选
    if (classId) {
      query = query.where({
        classId: classId
      })
    }
    
    // 获取总数
    const countResult = await query.count()
    
    // 获取学生列表
    const listResult = await query
      .orderBy('updateTime', 'desc')
      .skip(skip)
      .limit(limit)
      .get()
    
    // 格式化学生数据
    const students = listResult.data.map(student => ({
      id: student._id,
      name: student.name,
      gender: student.gender || '未知',
      class: student.class || student.className,
      classId: student.classId,
      grade: student.grade,
      age: student.age,
      personality: student.personality || {},
      subjects: student.subjects || [],
      commentsCount: student.commentsCount || 0,
      lastUpdate: student.updateTime || student.createTime,
      createTime: student.createTime,
      status: student.status || 'active'
    }))
    
    console.log(`成功获取学生列表: ${students.length}条记录`)
    
    return {
      code: 200,
      message: '获取成功',
      data: {
        list: students,
        pagination: {
          total: countResult.total,
          page: page,
          limit: limit,
          pages: Math.ceil(countResult.total / limit)
        }
      }
    }
    
  } catch (error) {
    console.error('获取学生列表失败:', error)
    
    return {
      code: 500,
      message: error.message || '获取学生列表失败',
      data: {
        list: [],
        pagination: {
          total: 0,
          page: 1,
          limit: 20,
          pages: 0
        }
      }
    }
  }
}