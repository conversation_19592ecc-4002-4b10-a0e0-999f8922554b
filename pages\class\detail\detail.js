/**
 * 班级详情页面
 * 基于微信云开发
 */
const { cloudService } = require('../../../services/cloudService');

Page({
  /**
   * 页面的初始数据
   */
  data: {
    classId: null,
    classInfo: {},
    recentActivities: [],
    loading: true
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    const { id } = options;
    if (id) {
      this.setData({ classId: id });
      this.loadClassDetail(id);
    }
  },

  /**
   * 加载班级详情
   */
  async loadClassDetail(classId) {
    try {
      this.setData({ loading: true });

      let classInfo = null;

      try {
        const classList = await cloudService.getClassList();
        if (classList.success) {
          const cloudClass = classList.data.find(item => item._id === classId);
          if (cloudClass) {
            // 获取班级统计数据
            const [studentResult, recordResult, commentResult] = await Promise.all([
              cloudService.getStudentList({ classId }),
              cloudService.getRecordList({ classId }),
              cloudService.getCommentList({ classId })
            ]);

            classInfo = {
              id: cloudClass._id,
              name: cloudClass.className,
              description: cloudClass.description || `${cloudClass.grade}${cloudClass.subject}班级`,
              grade: cloudClass.grade,
              gradeText: cloudClass.grade,
              subject: cloudClass.subject,
              subjectText: cloudClass.subject,
              status: 'active',
              statusText: '正常',
              studentCount: studentResult.success ? studentResult.data.length : 0,
              totalRecords: recordResult.success ? recordResult.data.length : 0,
              commentCount: commentResult.success ? commentResult.data.length : 0,
              createTime: cloudClass.createTime,
              createTimeText: this.formatTime(cloudClass.createTime)
            };
          } else {
            throw new Error('班级不存在');
          }
        } else {
          throw new Error(classList.error || '获取班级信息失败');
        }
      } catch (error) {
        console.error('获取班级详情失败:', error);
        wx.showToast({
          title: '班级不存在',
          icon: 'none'
        });
        setTimeout(() => {
          wx.navigateBack();
        }, 1500);
        return;
      }

      // 获取最近活动（基于真实记录数据）
      const recentActivities = await this.loadRecentActivities(classId);

      this.setData({
        classInfo,
        recentActivities,
        loading: false
      });

      // 设置页面标题
      wx.setNavigationBarTitle({
        title: classInfo.name
      });

    } catch (error) {
      this.setData({ loading: false });
      wx.showToast({
        title: '加载失败',
        icon: 'none'
      });
    }
  },

  /**
   * 加载最近活动
   */
  async loadRecentActivities(classId) {
    try {
      // 获取班级最近的记录
      const recordResult = await cloudService.getRecordList({
        classId: classId,
        pageSize: 10
      });

      if (recordResult.success && recordResult.data.length > 0) {
        // 获取学生信息用于显示姓名
        const studentResult = await cloudService.getStudentList();
        const students = studentResult.success ? studentResult.data : [];

        return recordResult.data.map(record => {
          const student = students.find(s => s._id === record.studentId);
          const studentName = student ? student.name : '未知学生';

          return {
            id: record._id,
            title: `${studentName} - ${record.action}`,
            description: record.description || '无详细描述',
            type: record.behaviorType,
            icon: this.getBehaviorIcon(record.behaviorType),
            timeText: this.formatTime(record.createTime)
          };
        });
      } else {
        return [];
      }
    } catch (error) {
      console.error('加载最近活动失败:', error);
      return [];
    }
  },

  /**
   * 获取行为类型图标
   */
  getBehaviorIcon(behaviorType) {
    const iconMap = {
      'positive': 'like-o',
      'negative': 'warning-o',
      'neutral': 'info-o',
      'academic': 'edit'
    };
    return iconMap[behaviorType] || 'info-o';
  },

  /**
   * 格式化时间
   */
  formatTime(timeStr) {
    const now = new Date();
    const time = new Date(timeStr);
    const diff = now - time;

    if (diff < 60000) { // 1分钟内
      return '刚刚';
    } else if (diff < 3600000) { // 1小时内
      return `${Math.floor(diff / 60000)}分钟前`;
    } else if (diff < 86400000) { // 1天内
      return `${Math.floor(diff / 3600000)}小时前`;
    } else {
      return `${Math.floor(diff / 86400000)}天前`;
    }
  },

  /**
   * 跳转到学生管理
   */
  goToStudentList() {
    wx.navigateTo({
      url: `/pages/student/list/list?classId=${this.data.classId}`
    });
  },

  /**
   * 跳转到记录管理
   */
  goToRecordList() {
    wx.navigateTo({
      url: `/pages/record/list/list?classId=${this.data.classId}`
    });
  },

  /**
   * 跳转到评语管理
   */
  goToCommentList() {
    wx.navigateTo({
      url: `/pages/comment/list/list?classId=${this.data.classId}`
    });
  },

  /**
   * 跳转到数据分析
   */
  goToAnalytics() {
    wx.navigateTo({
      url: `/pages/analytics/class/class?id=${this.data.classId}`
    });
  },

  /**
   * 编辑班级
   */
  editClass() {
    wx.navigateTo({
      url: `/pages/class/create/create?mode=edit&id=${this.data.classId}`
    });
  },

  /**
   * 快速记录
   */
  quickRecord() {
    wx.navigateTo({
      url: `/pages/record/create/create?classId=${this.data.classId}`
    });
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  },

  /**
   * 格式化时间
   */
  formatTime(timestamp) {
    if (!timestamp) return '未知';

    const now = new Date();
    const time = new Date(timestamp);
    const diff = now - time;

    const days = Math.floor(diff / (1000 * 60 * 60 * 24));
    const months = Math.floor(days / 30);

    if (months > 0) {
      return `${months}个月前`;
    } else if (days > 0) {
      return `${days}天前`;
    } else {
      return '今天';
    }
  }
})