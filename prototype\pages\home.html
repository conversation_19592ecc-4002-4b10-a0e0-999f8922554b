<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智能首页</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
            background: linear-gradient(135deg, #F0F2F5 0%, #E4E7ED 100%);
            min-height: 100vh;
            color: #333;
        }

        .page-container {
            padding: 20px 16px;
            max-width: 375px;
            margin: 0 auto;
        }

        /* 用户头像区域 */
        .header-section {
            display: flex;
            align-items: center;
            margin-bottom: 32px;
            padding-top: 20px;
        }

        .user-avatar {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background: linear-gradient(135deg, #5470C6 0%, #73C0DE 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 24px;
            font-weight: 600;
            margin-right: 16px;
            box-shadow: 0 8px 24px rgba(84, 112, 198, 0.3);
        }

        .greeting-content {
            flex: 1;
        }

        .greeting-main {
            font-size: 20px;
            font-weight: 600;
            color: #333;
            margin-bottom: 4px;
        }

        .greeting-sub {
            font-size: 14px;
            color: #666;
            line-height: 1.4;
        }

        /* 今日统计 */
        .stats-section {
            background: rgba(255, 255, 255, 0.9);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 24px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.3);
        }

        .stats-title {
            font-size: 16px;
            font-weight: 600;
            color: #333;
            margin-bottom: 16px;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 16px;
        }

        .stat-item {
            text-align: center;
        }

        .stat-number {
            font-size: 24px;
            font-weight: 700;
            color: #5470C6;
            display: block;
            margin-bottom: 4px;
        }

        .stat-label {
            font-size: 12px;
            color: #666;
        }

        /* 核心功能卡片 */
        .function-section {
            margin-bottom: 24px;
        }

        .function-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 16px;
        }

        .function-card {
            background: rgba(255, 255, 255, 0.9);
            border-radius: 16px;
            padding: 20px;
            text-align: center;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.3);
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .function-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }

        .function-card.primary {
            grid-column: span 2;
            background: linear-gradient(135deg, #5470C6 0%, #73C0DE 100%);
            color: white;
        }

        .card-icon {
            width: 48px;
            height: 48px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 12px;
            font-size: 24px;
        }

        .primary .card-icon {
            background: rgba(255, 255, 255, 0.2);
        }

        .function-card:not(.primary) .card-icon {
            background: linear-gradient(135deg, #5470C6 0%, #73C0DE 100%);
            color: white;
        }

        .card-title {
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 4px;
            display: block;
        }

        .card-desc {
            font-size: 12px;
            opacity: 0.8;
            display: block;
        }

        /* 最近动态 */
        .recent-section {
            background: rgba(255, 255, 255, 0.9);
            border-radius: 16px;
            padding: 20px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.3);
        }

        .recent-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }

        .recent-title {
            font-size: 16px;
            font-weight: 600;
            color: #333;
        }

        .recent-more {
            font-size: 14px;
            color: #5470C6;
            cursor: pointer;
        }

        .recent-item {
            padding: 12px 0;
            border-bottom: 1px solid rgba(0, 0, 0, 0.05);
        }

        .recent-item:last-child {
            border-bottom: none;
        }

        .recent-student {
            font-size: 14px;
            font-weight: 600;
            color: #333;
            margin-bottom: 4px;
        }

        .recent-content {
            font-size: 13px;
            color: #666;
            line-height: 1.4;
            margin-bottom: 4px;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }

        .recent-time {
            font-size: 12px;
            color: #999;
        }

        /* 底部导航 */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-top: 1px solid rgba(0, 0, 0, 0.05);
            padding: 8px 0 20px;
        }

        .nav-grid {
            display: grid;
            grid-template-columns: repeat(5, 1fr);
            max-width: 375px;
            margin: 0 auto;
        }

        .nav-item {
            text-align: center;
            padding: 8px;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .nav-item.active {
            color: #5470C6;
        }

        .nav-icon {
            font-size: 20px;
            margin-bottom: 4px;
        }

        .nav-label {
            font-size: 10px;
            font-weight: 500;
        }

        .page-container {
            padding-bottom: 100px;
        }
    </style>
</head>
<body>
    <div class="page-container">
        <!-- 用户问候区域 -->
        <div class="header-section">
            <div class="user-avatar">
                <i class="fas fa-user"></i>
            </div>
            <div class="greeting-content">
                <div class="greeting-main">早上好，张老师</div>
                <div class="greeting-sub">让每一句评语都充满温度</div>
            </div>
        </div>

        <!-- 今日统计 -->
        <div class="stats-section">
            <div class="stats-title">今日统计</div>
            <div class="stats-grid">
                <div class="stat-item">
                    <span class="stat-number">12</span>
                    <span class="stat-label">条评语</span>
                </div>
                <div class="stat-item">
                    <span class="stat-number">156</span>
                    <span class="stat-label">分钟节省</span>
                </div>
                <div class="stat-item">
                    <span class="stat-number">9.2</span>
                    <span class="stat-label">质量评分</span>
                </div>
            </div>
        </div>

        <!-- 核心功能 -->
        <div class="function-section">
            <div class="function-grid">
                <div class="function-card primary">
                    <div class="card-icon">
                        <i class="fas fa-magic"></i>
                    </div>
                    <span class="card-title">AI智能生成</span>
                    <span class="card-desc">3分钟生成专业评语</span>
                </div>
                
                <div class="function-card">
                    <div class="card-icon">
                        <i class="fas fa-bolt"></i>
                    </div>
                    <span class="card-title">快速记录</span>
                    <span class="card-desc">记录学生表现</span>
                </div>
                
                <div class="function-card">
                    <div class="card-icon">
                        <i class="fas fa-file-alt"></i>
                    </div>
                    <span class="card-title">我的作品</span>
                    <span class="card-desc">管理评语作品</span>
                </div>
            </div>
        </div>

        <!-- 最近动态 -->
        <div class="recent-section">
            <div class="recent-header">
                <div class="recent-title">最近动态</div>
                <div class="recent-more">查看全部</div>
            </div>
            
            <div class="recent-item">
                <div class="recent-student">李小明</div>
                <div class="recent-content">该生在本学期表现优异，学习态度端正，积极参与课堂讨论，作业完成质量较高...</div>
                <div class="recent-time">2分钟前</div>
            </div>
            
            <div class="recent-item">
                <div class="recent-student">王小红</div>
                <div class="recent-content">该生性格开朗，乐于助人，在班级活动中表现积极，与同学关系融洽...</div>
                <div class="recent-time">15分钟前</div>
            </div>
        </div>
    </div>

    <!-- 底部导航 -->
    <div class="bottom-nav">
        <div class="nav-grid">
            <div class="nav-item active">
                <div class="nav-icon"><i class="fas fa-home"></i></div>
                <div class="nav-label">首页</div>
            </div>
            <div class="nav-item">
                <div class="nav-icon"><i class="fas fa-magic"></i></div>
                <div class="nav-label">生成</div>
            </div>
            <div class="nav-item">
                <div class="nav-icon"><i class="fas fa-file-alt"></i></div>
                <div class="nav-label">作品</div>
            </div>
            <div class="nav-item">
                <div class="nav-icon"><i class="fas fa-chart-line"></i></div>
                <div class="nav-label">报告</div>
            </div>
            <div class="nav-item">
                <div class="nav-icon"><i class="fas fa-cog"></i></div>
                <div class="nav-label">设置</div>
            </div>
        </div>
    </div>
</body>
</html>
