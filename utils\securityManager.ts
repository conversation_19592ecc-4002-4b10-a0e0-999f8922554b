/**
 * 安全管理器
 * 2025年企业级安全防护系统
 */

import { z } from 'zod'
import { monitoring } from './monitoring'

// 安全配置类型
interface SecurityConfig {
  enableEncryption: boolean
  enableInputValidation: boolean
  enableRateLimit: boolean
  enableAuditLog: boolean
  maxRequestsPerMinute: number
  encryptionKey: string
  sensitiveFields: string[]
}

// 安全事件类型
interface SecurityEvent {
  type: 'rate_limit' | 'invalid_input' | 'unauthorized_access' | 'data_breach'
  severity: 'low' | 'medium' | 'high' | 'critical'
  description: string
  userId?: string
  ip?: string
  userAgent?: string
  timestamp: number
  metadata?: Record<string, any>
}

// 输入验证规则
const ValidationRules = {
  // 用户名：2-20个字符，中文、英文、数字
  username: z.string().min(2).max(20).regex(/^[\u4e00-\u9fa5a-zA-Z0-9_]+$/),
  
  // 手机号：11位数字，1开头
  phone: z.string().regex(/^1[3-9]\d{9}$/),
  
  // 邮箱：标准邮箱格式
  email: z.string().email(),
  
  // 密码：8-32位，包含字母和数字
  password: z.string().min(8).max(32).regex(/^(?=.*[a-zA-Z])(?=.*\d).+$/),
  
  // 学生姓名：2-10个中文字符
  studentName: z.string().min(2).max(10).regex(/^[\u4e00-\u9fa5]+$/),
  
  // 班级名称：2-50个字符
  className: z.string().min(2).max(50),
  
  // 评语内容：10-1000个字符
  commentContent: z.string().min(10).max(1000),
  
  // ID：非空字符串
  id: z.string().min(1),
  
  // 标签：1-20个字符的数组
  tags: z.array(z.string().min(1).max(20)).min(1).max(10)
}

class SecurityManager {
  private config: SecurityConfig
  private rateLimitMap: Map<string, number[]> = new Map()
  private auditLogs: SecurityEvent[] = []
  private encryptionKey: string

  constructor(config: Partial<SecurityConfig> = {}) {
    this.config = {
      enableEncryption: true,
      enableInputValidation: true,
      enableRateLimit: true,
      enableAuditLog: true,
      maxRequestsPerMinute: 100,
      encryptionKey: this.generateEncryptionKey(),
      sensitiveFields: ['password', 'phone', 'email', 'idCard'],
      ...config
    }

    this.encryptionKey = this.config.encryptionKey
    this.init()
  }

  /**
   * 初始化安全管理器
   */
  private init(): void {
    // 启动定期清理
    setInterval(() => {
      this.cleanupRateLimitData()
      this.cleanupAuditLogs()
    }, 60000) // 1分钟

    // 设置全局错误处理
    this.setupGlobalErrorHandler()

    console.log('🔒 Security Manager initialized')
  }

  /**
   * 生成加密密钥
   */
  private generateEncryptionKey(): string {
    return Array.from({ length: 32 }, () => 
      Math.floor(Math.random() * 256).toString(16).padStart(2, '0')
    ).join('')
  }

  /**
   * 输入验证
   */
  validateInput<T extends keyof typeof ValidationRules>(
    type: T,
    value: any
  ): { valid: boolean; error?: string; sanitized?: any } {
    if (!this.config.enableInputValidation) {
      return { valid: true, sanitized: value }
    }

    try {
      const rule = ValidationRules[type]
      const sanitized = rule.parse(value)
      
      this.logSecurityEvent({
        type: 'invalid_input',
        severity: 'low',
        description: `输入验证通过: ${type}`,
        metadata: { type, value: this.maskSensitiveData(value) }
      })

      return { valid: true, sanitized }
    } catch (error) {
      const errorMessage = error instanceof z.ZodError 
        ? error.errors.map(e => e.message).join(', ')
        : '输入验证失败'

      this.logSecurityEvent({
        type: 'invalid_input',
        severity: 'medium',
        description: `输入验证失败: ${type} - ${errorMessage}`,
        metadata: { type, value: this.maskSensitiveData(value), error: errorMessage }
      })

      return { valid: false, error: errorMessage }
    }
  }

  /**
   * 批量输入验证
   */
  validateInputs(inputs: Array<{ type: keyof typeof ValidationRules; value: any }>): {
    valid: boolean
    errors: Record<string, string>
    sanitized: Record<string, any>
  } {
    const errors: Record<string, string> = {}
    const sanitized: Record<string, any> = {}
    
    for (const input of inputs) {
      const result = this.validateInput(input.type, input.value)
      if (!result.valid) {
        errors[input.type] = result.error!
      } else {
        sanitized[input.type] = result.sanitized
      }
    }

    return {
      valid: Object.keys(errors).length === 0,
      errors,
      sanitized
    }
  }

  /**
   * 速率限制检查
   */
  checkRateLimit(identifier: string): { allowed: boolean; remaining: number } {
    if (!this.config.enableRateLimit) {
      return { allowed: true, remaining: this.config.maxRequestsPerMinute }
    }

    const now = Date.now()
    const windowStart = now - 60000 // 1分钟窗口
    
    // 获取或创建请求记录
    let requests = this.rateLimitMap.get(identifier) || []
    
    // 清理过期请求
    requests = requests.filter(timestamp => timestamp > windowStart)
    
    // 检查是否超过限制
    if (requests.length >= this.config.maxRequestsPerMinute) {
      this.logSecurityEvent({
        type: 'rate_limit',
        severity: 'high',
        description: `速率限制触发: ${identifier}`,
        metadata: { identifier, requestCount: requests.length, limit: this.config.maxRequestsPerMinute }
      })

      return { allowed: false, remaining: 0 }
    }

    // 记录新请求
    requests.push(now)
    this.rateLimitMap.set(identifier, requests)

    return { 
      allowed: true, 
      remaining: this.config.maxRequestsPerMinute - requests.length 
    }
  }

  /**
   * 数据加密
   */
  encrypt(data: string): string {
    if (!this.config.enableEncryption) {
      return data
    }

    try {
      // 简单的XOR加密（生产环境应使用更强的加密算法）
      const key = this.encryptionKey
      let encrypted = ''
      
      for (let i = 0; i < data.length; i++) {
        const keyChar = key.charCodeAt(i % key.length)
        const dataChar = data.charCodeAt(i)
        encrypted += String.fromCharCode(dataChar ^ keyChar)
      }
      
      return btoa(encrypted) // Base64编码
    } catch (error) {
      console.error('数据加密失败:', error)
      return data
    }
  }

  /**
   * 数据解密
   */
  decrypt(encryptedData: string): string {
    if (!this.config.enableEncryption) {
      return encryptedData
    }

    try {
      const encrypted = atob(encryptedData) // Base64解码
      const key = this.encryptionKey
      let decrypted = ''
      
      for (let i = 0; i < encrypted.length; i++) {
        const keyChar = key.charCodeAt(i % key.length)
        const encryptedChar = encrypted.charCodeAt(i)
        decrypted += String.fromCharCode(encryptedChar ^ keyChar)
      }
      
      return decrypted
    } catch (error) {
      console.error('数据解密失败:', error)
      return encryptedData
    }
  }

  /**
   * 敏感数据脱敏
   */
  maskSensitiveData(data: any): any {
    if (typeof data !== 'object' || data === null) {
      return data
    }

    const masked = { ...data }
    
    for (const field of this.config.sensitiveFields) {
      if (masked[field]) {
        if (field === 'phone') {
          masked[field] = masked[field].replace(/(\d{3})\d{4}(\d{4})/, '$1****$2')
        } else if (field === 'email') {
          masked[field] = masked[field].replace(/(.{2}).*(@.*)/, '$1****$2')
        } else if (field === 'idCard') {
          masked[field] = masked[field].replace(/(\d{6})\d{8}(\d{4})/, '$1********$2')
        } else {
          masked[field] = '****'
        }
      }
    }

    return masked
  }

  /**
   * SQL注入防护
   */
  sanitizeSQLInput(input: string): string {
    if (typeof input !== 'string') {
      return input
    }

    // 移除或转义危险字符
    return input
      .replace(/'/g, "''")
      .replace(/;/g, '')
      .replace(/--/g, '')
      .replace(/\/\*/g, '')
      .replace(/\*\//g, '')
      .replace(/xp_/gi, '')
      .replace(/sp_/gi, '')
  }

  /**
   * XSS防护
   */
  sanitizeHTMLInput(input: string): string {
    if (typeof input !== 'string') {
      return input
    }

    return input
      .replace(/</g, '&lt;')
      .replace(/>/g, '&gt;')
      .replace(/"/g, '&quot;')
      .replace(/'/g, '&#x27;')
      .replace(/\//g, '&#x2F;')
  }

  /**
   * 权限检查
   */
  checkPermission(userId: string, resource: string, action: string): boolean {
    // 这里应该实现具体的权限检查逻辑
    // 可以集成RBAC或ABAC权限模型
    
    this.logSecurityEvent({
      type: 'unauthorized_access',
      severity: 'medium',
      description: `权限检查: ${userId} 尝试 ${action} ${resource}`,
      userId,
      metadata: { resource, action }
    })

    // 简单的权限检查示例
    return true // 实际应用中需要实现具体逻辑
  }

  /**
   * 记录安全事件
   */
  private logSecurityEvent(event: Omit<SecurityEvent, 'timestamp'>): void {
    if (!this.config.enableAuditLog) {
      return
    }

    const securityEvent: SecurityEvent = {
      ...event,
      timestamp: Date.now()
    }

    this.auditLogs.push(securityEvent)

    // 高危事件立即上报
    if (event.severity === 'high' || event.severity === 'critical') {
      monitoring.captureError({
        message: `安全事件: ${event.description}`,
        category: 'security',
        level: 'error',
        context: securityEvent
      })
    }

    // 保存到本地存储
    this.saveAuditLogsToStorage()
  }

  /**
   * 保存审计日志到存储
   */
  private saveAuditLogsToStorage(): void {
    try {
      const logs = this.auditLogs.slice(-100) // 只保留最近100条
      wx.setStorageSync('security_audit_logs', logs)
    } catch (error) {
      console.warn('保存审计日志失败:', error)
    }
  }

  /**
   * 设置全局错误处理
   */
  private setupGlobalErrorHandler(): void {
    // 监听未捕获的错误
    const originalError = console.error
    console.error = (...args: any[]) => {
      this.logSecurityEvent({
        type: 'unauthorized_access',
        severity: 'medium',
        description: `全局错误: ${args.join(' ')}`,
        metadata: { args }
      })
      originalError.apply(console, args)
    }
  }

  /**
   * 清理速率限制数据
   */
  private cleanupRateLimitData(): void {
    const now = Date.now()
    const windowStart = now - 60000

    for (const [identifier, requests] of this.rateLimitMap.entries()) {
      const validRequests = requests.filter(timestamp => timestamp > windowStart)
      
      if (validRequests.length === 0) {
        this.rateLimitMap.delete(identifier)
      } else {
        this.rateLimitMap.set(identifier, validRequests)
      }
    }
  }

  /**
   * 清理审计日志
   */
  private cleanupAuditLogs(): void {
    const maxAge = 7 * 24 * 60 * 60 * 1000 // 7天
    const cutoff = Date.now() - maxAge
    
    this.auditLogs = this.auditLogs.filter(log => log.timestamp > cutoff)
  }

  /**
   * 获取安全报告
   */
  getSecurityReport(): {
    rateLimitStats: { identifier: string; requestCount: number }[]
    recentEvents: SecurityEvent[]
    config: SecurityConfig
  } {
    const rateLimitStats = Array.from(this.rateLimitMap.entries()).map(
      ([identifier, requests]) => ({
        identifier,
        requestCount: requests.length
      })
    )

    return {
      rateLimitStats,
      recentEvents: this.auditLogs.slice(-20),
      config: this.config
    }
  }

  /**
   * 更新配置
   */
  updateConfig(newConfig: Partial<SecurityConfig>): void {
    this.config = { ...this.config, ...newConfig }
    
    this.logSecurityEvent({
      type: 'unauthorized_access',
      severity: 'low',
      description: '安全配置已更新',
      metadata: { newConfig }
    })
  }

  /**
   * 生成安全令牌
   */
  generateSecureToken(payload: Record<string, any>): string {
    const header = { alg: 'HS256', typ: 'JWT' }
    const now = Date.now()
    const tokenPayload = {
      ...payload,
      iat: now,
      exp: now + 24 * 60 * 60 * 1000 // 24小时过期
    }

    const headerEncoded = btoa(JSON.stringify(header))
    const payloadEncoded = btoa(JSON.stringify(tokenPayload))
    const signature = this.encrypt(`${headerEncoded}.${payloadEncoded}`)

    return `${headerEncoded}.${payloadEncoded}.${signature}`
  }

  /**
   * 验证安全令牌
   */
  verifySecureToken(token: string): { valid: boolean; payload?: any; error?: string } {
    try {
      const [headerEncoded, payloadEncoded, signature] = token.split('.')
      
      if (!headerEncoded || !payloadEncoded || !signature) {
        return { valid: false, error: '令牌格式无效' }
      }

      // 验证签名
      const expectedSignature = this.encrypt(`${headerEncoded}.${payloadEncoded}`)
      if (signature !== expectedSignature) {
        return { valid: false, error: '令牌签名无效' }
      }

      // 解析载荷
      const payload = JSON.parse(atob(payloadEncoded))
      
      // 检查过期时间
      if (payload.exp && Date.now() > payload.exp) {
        return { valid: false, error: '令牌已过期' }
      }

      return { valid: true, payload }
    } catch (error) {
      return { valid: false, error: '令牌解析失败' }
    }
  }

  /**
   * 销毁安全管理器
   */
  destroy(): void {
    this.rateLimitMap.clear()
    this.auditLogs.length = 0
    
    console.log('🔒 Security Manager destroyed')
  }
}

// 创建全局安全管理器实例
const securityManager = new SecurityManager()

export { SecurityManager, securityManager, ValidationRules }
export type { SecurityConfig, SecurityEvent }
