/**
 * AI配置直接同步脚本 - 绕过权限验证
 * 直接向 system_config 集合写入配置
 * 在小程序开发者工具的控制台中运行此脚本
 */

console.log('🚀 开始直接同步AI配置（绕过权限验证）...');

// 最新的AI配置数据
const aiConfigData = {
  type: 'ai_config',
  status: 'active',
  model: 'doubao-seed-1-6-flash-250715',
  provider: 'bytedance',
  apiKey: '4d73215c-5512-418b-8749-db9514df3c75',
  apiUrl: 'https://ark.cn-beijing.volces.com/api/v3/chat/completions',
  temperature: 0.7,
  maxTokens: 2000,
  topP: 0.9,
  frequencyPenalty: 0,
  presencePenalty: 0,
  enableStream: false,
  enableCache: true,
  timeout: 30,
  createTime: new Date(),
  updateTime: new Date(),
  createTimestamp: Date.now(),
  updateTimestamp: Date.now()
};

console.log('📋 配置内容:', aiConfigData);

// 第一步：先清理旧的活跃配置
console.log('🧹 第一步：清理旧的活跃配置...');

wx.cloud.database().collection('system_config')
  .where({
    type: 'ai_config',
    status: 'active'
  })
  .get()
  .then(oldConfigs => {
    console.log('找到旧配置数量:', oldConfigs.data.length);
    
    if (oldConfigs.data.length > 0) {
      // 将旧配置设为非活跃
      const updatePromises = oldConfigs.data.map(config => {
        return wx.cloud.database().collection('system_config').doc(config._id).update({
          data: {
            status: 'inactive',
            updateTime: new Date(),
            updateTimestamp: Date.now()
          }
        });
      });
      
      return Promise.all(updatePromises);
    } else {
      console.log('✅ 没有旧配置需要清理');
      return Promise.resolve();
    }
  })
  .then(() => {
    console.log('✅ 旧配置清理完成');
    console.log('📝 第二步：添加新配置...');
    
    // 第二步：添加新配置
    return wx.cloud.database().collection('system_config').add({
      data: aiConfigData
    });
  })
  .then(result => {
    console.log('✅ 新配置添加成功!');
    console.log('📄 配置ID:', result._id);
    console.log('⏰ 创建时间:', new Date().toLocaleString());
    
    // 第三步：验证配置是否生效
    console.log('🔍 第三步：验证配置是否生效...');
    return wx.cloud.database().collection('system_config')
      .where({
        type: 'ai_config',
        status: 'active'
      })
      .orderBy('updateTime', 'desc')
      .limit(1)
      .get();
  })
  .then(verifyResult => {
    console.log('📋 验证结果:', verifyResult);
    
    if (verifyResult.data && verifyResult.data.length > 0) {
      const savedConfig = verifyResult.data[0];
      console.log('✅ 配置验证成功!');
      console.log('🔑 API密钥正确:', savedConfig.apiKey === aiConfigData.apiKey ? '是' : '否');
      console.log('🤖 模型名称正确:', savedConfig.model === aiConfigData.model ? '是' : '否');
      console.log('🌐 API端点正确:', savedConfig.apiUrl === aiConfigData.apiUrl ? '是' : '否');
      console.log('📊 配置状态:', savedConfig.status);
      console.log('⏱️ 更新时间:', savedConfig.updateTime);
      
      // 第四步：测试云函数是否能读取到配置
      console.log('🧪 第四步：测试云函数配置读取...');
      return wx.cloud.callFunction({
        name: 'callDoubaoAPI',
        data: {
          prompt: '你好，这是一个配置测试请求，请简单回复"配置读取成功"。',
          style: 'warm',
          length: 'short',
          temperature: 0.7,
          max_tokens: 50,
          studentName: '测试同学',
          performanceMaterial: '这是配置测试'
        }
      });
    } else {
      throw new Error('配置验证失败：未找到活跃配置');
    }
  })
  .then(testResult => {
    console.log('🧪 云函数配置读取测试结果:', testResult);
    
    if (testResult.result && testResult.result.success) {
      if (testResult.result.isFallback) {
        console.warn('⚠️  注意：仍在使用备用方案');
        console.log('备用内容:', testResult.result.data.content);
        console.log('🔧 可能原因：');
        console.log('  1. 云函数缓存，需要等待几分钟');
        console.log('  2. API调用仍然失败，请检查网络');
        console.log('  3. 模型名称格式需要再次确认');
      } else {
        console.log('🎉 完美！真实AI调用成功！');
        console.log('AI回复内容:', testResult.result.data.content);
      }
    } else {
      console.error('❌ 云函数测试失败:', testResult.result);
    }
    
    console.log('\n🎯 同步完成总结:');
    console.log('✅ 配置数据库写入：成功');
    console.log('✅ 配置验证读取：成功');
    console.log(`📊 云函数调用：${testResult.result && testResult.result.success ? '成功' : '需要排查'}`);
    console.log('\n💡 如果仍显示备用方案，请：');
    console.log('1. 等待2-3分钟让云函数缓存更新');
    console.log('2. 重新测试AI评语生成功能');
    console.log('3. 检查网络连接是否稳定');
  })
  .catch(error => {
    console.error('❌ 直接同步过程中出错:', error);
    console.log('\n🔧 故障排查建议:');
    console.log('1. 检查网络连接');
    console.log('2. 确认数据库权限');
    console.log('3. 重试执行脚本');
    console.log('\n📞 如需帮助，请联系技术支持并提供错误信息。');
  });

console.log('⏳ 脚本已启动，请等待执行结果...');