# 🔧 腾讯云控制台配置详细步骤（小白版）

## 准备工作
- 确保你有腾讯云账号
- 确保你有云开发环境的访问权限
- 准备环境ID: `cloud1-4g85f8xlb8166ff1`

---

## 第一步：登录腾讯云控制台

1. **打开浏览器**，访问：https://console.cloud.tencent.com/tcb
2. **登录你的腾讯云账号**
3. 看到云开发控制台首页

---

## 第二步：选择正确的环境

1. **在左上角**，你会看到环境选择器
2. **点击环境选择器下拉菜单**
3. **找到并选择** `cloud1-4g85f8xlb8166ff1`
4. **确认**环境名称显示为 `cloud1-4g85f8xlb8166ff1`

---

## 第三步：配置数据库安全规则

### 3.1 进入安全规则页面
1. **在左侧菜单**，点击 `数据库`
2. **再点击** `安全规则`
3. 你会看到数据库安全规则页面

### 3.2 添加新规则
1. **点击** `添加规则` 按钮（通常是蓝色按钮）
2. **在弹出的对话框中填写**：
   - **集合名称**：输入 `*` （表示所有集合）
   - **操作类型**：选择 `read`（读取）
   - **规则内容**：输入 `auth.uid != null`
3. **点击** `确定` 或 `保存`

### 3.3 确认规则生效
- 你应该能看到新添加的规则显示在列表中
- 状态应该是"已启用"

---

## 第四步：配置云函数权限（最重要！）

### 4.1 进入云函数页面
1. **在左侧菜单**，点击 `云函数`
2. 你会看到所有已部署的云函数列表

### 4.2 为 getUsageStats 配置权限
1. **找到** `getUsageStats` 云函数
2. **点击函数名称**进入详情页
3. **点击** `权限管理` 或 `访问权限` 标签页
4. **找到** `匿名访问` 或 `Anonymous Access` 选项
5. **开启匿名访问开关**（通常是一个滑动按钮）
6. **保存设置**

### 4.3 为 dataQuery 配置权限
1. **返回云函数列表**
2. **找到** `dataQuery` 云函数
3. **重复上面4.2的步骤**开启匿名访问

### 4.4 为 adminAPI 配置权限（如果存在）
1. **找到** `adminAPI` 云函数
2. **重复上面4.2的步骤**开启匿名访问

---

## 第五步：配置Web安全域名

### 5.1 进入安全配置页面
1. **在左侧菜单**，点击 `环境`
2. **点击** `安全配置`
3. **找到** `WEB安全域名` 部分

### 5.2 添加本地开发域名
1. **点击** `添加域名` 按钮
2. **逐个添加以下域名**：
   - `localhost:5173`
   - `127.0.0.1:5173`  
   - `localhost:3000`
3. **每添加一个都要点击确认**

### 5.3 确认域名生效
- 确认所有域名都显示在白名单中
- 状态应该是"已启用"

---

## 第六步：验证配置

### 6.1 检查配置完成情况
- ✅ 数据库安全规则：`*` 集合 `read` 权限 `auth.uid != null`
- ✅ 云函数匿名访问：`getUsageStats`、`dataQuery`、`adminAPI`
- ✅ WEB安全域名：`localhost:5173`、`127.0.0.1:5173`、`localhost:3000`

### 6.2 测试连接
1. **回到你的项目目录**
2. **运行**：
   ```bash
   cd admin-new
   npm run dev
   ```
3. **打开浏览器**访问 `http://localhost:5173`
4. **打开浏览器控制台**（F12），查看是否有：
   - ✅ `匿名登录成功`
   - ✅ `云函数调用成功: dataQuery`
   - ✅ `获取到真实数据`

---

## 常见问题解决

### 问题1：找不到环境
- 确认你的账号有该环境的访问权限
- 联系项目管理员添加你为协作者

### 问题2：云函数列表为空
- 确认云函数已正确部署
- 运行 `tcb fn list --env cloud1-4g85f8xlb8166ff1` 检查

### 问题3：权限配置找不到
- 不同版本的控制台界面可能略有不同
- 寻找 "访问控制"、"权限管理"、"安全设置" 等类似名称

### 问题4：域名配置无效
- 确保域名格式正确（不要加 http://）
- 等待几分钟让配置生效

---

## 🎯 配置完成标志

当你看到以下日志时，说明配置成功：

```
🔍 当前登录状态: {isAnonymous: true, hasUser: true, uid: "anonymous_xxx"}
✅ 云函数调用成功: dataQuery
📊 获取到真实数据: {totalUsers: 125, todayComments: 89}
```

**恭喜！现在你的Dashboard将显示小程序的真实数据！**