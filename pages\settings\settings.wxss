/* 智能设置页面 - 严格按照原型图设计 */

.page-container {
  padding: 40rpx 32rpx 200rpx;
  max-width: 750rpx;
  margin: 0 auto;
  background: linear-gradient(135deg, #F0F2F5 0%, #E4E7ED 100%);
  min-height: 100vh;
}

/* 页面头部 */
.page-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 48rpx;
  padding-top: 20rpx;
}

.header-title {
  font-size: 40rpx;
  font-weight: 600;
  color: #333;
}

.header-action {
  color: #5470C6;
  font-size: 28rpx;
  cursor: pointer;
}

/* 用户信息卡片 */
.user-card {
  background: linear-gradient(135deg, #5470C6 0%, #73C0DE 100%);
  border-radius: 40rpx;
  padding: 48rpx;
  margin-bottom: 48rpx;
  color: white;
  position: relative;
  overflow: hidden;
}

.user-card::before {
  content: '';
  position: absolute;
  top: -50%;
  right: -50%;
  width: 400rpx;
  height: 400rpx;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
}

.user-info {
  display: flex;
  align-items: center;
  position: relative;
  z-index: 1;
}

.user-avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  margin-right: 32rpx;
  position: relative;
  overflow: hidden;
}

.avatar-image {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  border: 4rpx solid rgba(255, 255, 255, 0.8);
  box-shadow: 0 16rpx 48rpx rgba(84, 112, 198, 0.3);
}

/* 头像占位符样式（与首页保持一致） */
.avatar-placeholder {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  background: linear-gradient(135deg, #5470C6 0%, #73C0DE 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  border: 4rpx solid rgba(255, 255, 255, 0.8);
  box-shadow: 0 16rpx 48rpx rgba(84, 112, 198, 0.3);
}

.avatar-text {
  font-size: 48rpx;
  font-weight: 600;
  color: white;
  text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.2);
}


.user-details {
  flex: 1;
}

.user-name {
  font-size: 48rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 8rpx;
  line-height: 1.3;
}

.user-level {
  font-size: 24rpx;
  opacity: 0.9;
  background: rgba(255, 255, 255, 0.2);
  padding: 4rpx 16rpx;
  border-radius: 24rpx;
  display: inline-block;
}

/* 设置分组 */
.settings-group {
  background: rgba(255, 255, 255, 0.9);
  border-radius: 32rpx;
  margin-bottom: 40rpx;
  backdrop-filter: blur(20rpx);
  border: 1rpx solid rgba(255, 255, 255, 0.3);
  overflow: hidden;
}

.group-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #666;
  padding: 32rpx 40rpx 16rpx;
  text-transform: uppercase;
  letter-spacing: 1rpx;
}

.setting-item {
  display: flex;
  align-items: center;
  padding: 32rpx 40rpx;
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.05);
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.setting-item:last-child {
  border-bottom: none;
}

.setting-item:hover {
  background: rgba(84, 112, 198, 0.05);
}

.setting-icon {
  width: 64rpx;
  height: 64rpx;
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 24rpx;
  font-size: 32rpx;
  color: white;
}

.setting-icon.ai {
  background: linear-gradient(135deg, #5470C6 0%, #73C0DE 100%);
}

.setting-icon.data {
  background: linear-gradient(135deg, #52C41A 0%, #73D13D 100%);
}

.setting-icon.security {
  background: linear-gradient(135deg, #FA8C16 0%, #FFC53D 100%);
}

.setting-icon.help {
  background: linear-gradient(135deg, #722ED1 0%, #B37FEB 100%);
}



.setting-icon.about {
  background: linear-gradient(135deg, #13C2C2 0%, #36CFC9 100%);
}

.setting-content {
  flex: 1;
}

.setting-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 4rpx;
}

.setting-desc {
  font-size: 24rpx;
  color: #666;
  line-height: 1.3;
}

.setting-action {
  color: #999;
  font-size: 28rpx;
}

/* 开关按钮 */
.toggle-switch {
  position: relative;
  width: 88rpx;
  height: 48rpx;
  background: #E4E7ED;
  border-radius: 24rpx;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.toggle-switch.active {
  background: #5470C6;
}

.toggle-switch::before {
  content: '';
  position: absolute;
  top: 4rpx;
  left: 4rpx;
  width: 40rpx;
  height: 40rpx;
  background: white;
  border-radius: 50%;
  transition: transform 0.3s ease;
  box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.1);
}

.toggle-switch.active::before {
  transform: translateX(40rpx);
}

/* 版本信息 */
.version-info {
  text-align: center;
  padding: 40rpx 0 60rpx;
  color: #999;
}

.version-number {
  font-size: 32rpx;
  font-weight: 600;
  margin-bottom: 10rpx;
  color: #5470C6;
}

.version-desc {
  font-size: 26rpx;
  margin-bottom: 8rpx;
  color: #666;
  line-height: 1.4;
}

.version-detail {
  font-size: 22rpx;
  opacity: 0.7;
  color: #999;
}

/* 危险操作样式 */
.setting-icon.danger {
  background: linear-gradient(135deg, #FF6B6B, #FF5252);
}

/* 个性化设置图标 */
.setting-icon.personalize {
  background: linear-gradient(135deg, #4ECDC4, #44A08D);
}