@echo off
echo 🚀 启动管理后台服务 - 修复版本
echo ================================

REM 首先检查端口
netstat -ano | findstr 8080 > nul
if %errorlevel% == 0 (echo ⚠️  端口8080被占用，将使用动态端口)

REM 清除缓存
echo 📦 清除Vite缓存...
rmdir /s /q node_modules\.vite 2>nul

REM 设置环境变量
set VITE_HMR_PORT=8081
set VITE_DEV_SERVER_PORT=8080

REM 使用清理配置启动
echo 🌟 启动React应用...
call npx vite --config vite.config.fixed.ts --port 8080 --host --strictPort=false --force

pause