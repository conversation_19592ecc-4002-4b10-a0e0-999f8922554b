<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>我的作品</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
            background: linear-gradient(135deg, #F0F2F5 0%, #E4E7ED 100%);
            min-height: 100vh;
            color: #333;
        }

        .page-container {
            padding: 20px 16px 100px;
            max-width: 375px;
            margin: 0 auto;
        }

        /* 页面头部 */
        .page-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 24px;
            padding-top: 10px;
        }

        .header-title {
            font-size: 20px;
            font-weight: 600;
            color: #333;
        }

        .header-action {
            color: #5470C6;
            font-size: 14px;
            cursor: pointer;
        }

        /* 统计概览 */
        .stats-overview {
            background: linear-gradient(135deg, #5470C6 0%, #73C0DE 100%);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 24px;
            color: white;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 16px;
            text-align: center;
        }

        .stat-number {
            font-size: 24px;
            font-weight: 700;
            display: block;
            margin-bottom: 4px;
        }

        .stat-label {
            font-size: 12px;
            opacity: 0.9;
        }

        /* 搜索和筛选 */
        .search-section {
            background: rgba(255, 255, 255, 0.9);
            border-radius: 16px;
            padding: 16px;
            margin-bottom: 20px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.3);
        }

        .search-bar {
            position: relative;
            margin-bottom: 12px;
        }

        .search-input {
            width: 100%;
            padding: 12px 16px 12px 40px;
            border: 1px solid #E4E7ED;
            border-radius: 12px;
            font-size: 14px;
            background: white;
            outline: none;
            transition: border-color 0.3s ease;
        }

        .search-input:focus {
            border-color: #5470C6;
        }

        .search-icon {
            position: absolute;
            left: 12px;
            top: 50%;
            transform: translateY(-50%);
            color: #999;
        }

        .filter-tabs {
            display: flex;
            gap: 8px;
        }

        .filter-tab {
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 12px;
            background: #F8F9FA;
            color: #666;
            cursor: pointer;
            transition: all 0.3s ease;
            border: none;
        }

        .filter-tab.active {
            background: #5470C6;
            color: white;
        }

        /* 作品列表 */
        .works-list {
            display: flex;
            flex-direction: column;
            gap: 16px;
        }

        .work-item {
            background: rgba(255, 255, 255, 0.9);
            border-radius: 16px;
            padding: 16px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.3);
            transition: all 0.3s ease;
        }

        .work-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }

        .work-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 12px;
        }

        .work-info {
            flex: 1;
        }

        .work-student {
            font-size: 16px;
            font-weight: 600;
            color: #333;
            margin-bottom: 4px;
        }

        .work-meta {
            display: flex;
            align-items: center;
            gap: 12px;
            font-size: 12px;
            color: #666;
        }

        .work-score {
            background: linear-gradient(135deg, #52C41A 0%, #73D13D 100%);
            color: white;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: 600;
        }

        .work-content {
            background: #F8F9FA;
            border-radius: 12px;
            padding: 12px;
            margin-bottom: 12px;
            font-size: 14px;
            line-height: 1.5;
            color: #333;
            display: -webkit-box;
            -webkit-line-clamp: 3;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }

        .work-actions {
            display: flex;
            gap: 8px;
        }

        .action-btn {
            flex: 1;
            padding: 8px 12px;
            border: 1px solid #E4E7ED;
            border-radius: 8px;
            background: white;
            font-size: 12px;
            color: #666;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 4px;
        }

        .action-btn:hover {
            border-color: #5470C6;
            color: #5470C6;
        }

        .action-btn.primary {
            background: #5470C6;
            color: white;
            border-color: #5470C6;
        }

        /* 质量标签 */
        .quality-tag {
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: 500;
        }

        .quality-excellent {
            background: #F6FFED;
            color: #52C41A;
            border: 1px solid #B7EB8F;
        }

        .quality-good {
            background: #E6F4FF;
            color: #1890FF;
            border: 1px solid #91D5FF;
        }

        .quality-normal {
            background: #FFF7E6;
            color: #FA8C16;
            border: 1px solid #FFD591;
        }

        /* 空状态 */
        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: #999;
        }

        .empty-icon {
            font-size: 48px;
            margin-bottom: 16px;
            opacity: 0.5;
        }

        .empty-title {
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 8px;
        }

        .empty-desc {
            font-size: 14px;
            line-height: 1.4;
        }

        /* 底部导航 */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-top: 1px solid rgba(0, 0, 0, 0.05);
            padding: 8px 0 20px;
        }

        .nav-grid {
            display: grid;
            grid-template-columns: repeat(5, 1fr);
            max-width: 375px;
            margin: 0 auto;
        }

        .nav-item {
            text-align: center;
            padding: 8px;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .nav-item.active {
            color: #5470C6;
        }

        .nav-icon {
            font-size: 20px;
            margin-bottom: 4px;
        }

        .nav-label {
            font-size: 10px;
            font-weight: 500;
        }
    </style>
</head>
<body>
    <div class="page-container">
        <!-- 页面头部 -->
        <div class="page-header">
            <div class="header-title">我的作品</div>
            <div class="header-action">导出全部</div>
        </div>

        <!-- 统计概览 -->
        <div class="stats-overview">
            <div class="stats-grid">
                <div class="stat-item">
                    <span class="stat-number">156</span>
                    <span class="stat-label">总作品数</span>
                </div>
                <div class="stat-item">
                    <span class="stat-number">9.1</span>
                    <span class="stat-label">平均评分</span>
                </div>
                <div class="stat-item">
                    <span class="stat-number">89%</span>
                    <span class="stat-label">优秀率</span>
                </div>
            </div>
        </div>

        <!-- 搜索筛选 -->
        <div class="search-section">
            <div class="search-bar">
                <input type="text" class="search-input" placeholder="搜索学生姓名或评语内容...">
                <i class="fas fa-search search-icon"></i>
            </div>
            
            <div class="filter-tabs">
                <button class="filter-tab active">全部</button>
                <button class="filter-tab">本周</button>
                <button class="filter-tab">本月</button>
                <button class="filter-tab">优秀</button>
                <button class="filter-tab">待改进</button>
            </div>
        </div>

        <!-- 作品列表 -->
        <div class="works-list">
            <div class="work-item">
                <div class="work-header">
                    <div class="work-info">
                        <div class="work-student">李小明</div>
                        <div class="work-meta">
                            <span><i class="fas fa-clock"></i> 2小时前</span>
                            <span class="quality-tag quality-excellent">优秀</span>
                        </div>
                    </div>
                    <div class="work-score">9.2分</div>
                </div>
                
                <div class="work-content">
                    李小明同学在本学期表现优异，学习态度端正，积极参与课堂讨论。该生思维活跃，善于思考，作业完成质量较高，字迹工整。在团队合作中表现出良好的协调能力，乐于帮助同学。建议在课外阅读方面继续加强，拓宽知识面，相信会有更大的进步空间。
                </div>
                
                <div class="work-actions">
                    <button class="action-btn">
                        <i class="fas fa-edit"></i> 编辑
                    </button>
                    <button class="action-btn">
                        <i class="fas fa-copy"></i> 复制
                    </button>
                    <button class="action-btn">
                        <i class="fas fa-share"></i> 分享
                    </button>
                    <button class="action-btn primary">
                        <i class="fas fa-download"></i> 导出
                    </button>
                </div>
            </div>

            <div class="work-item">
                <div class="work-header">
                    <div class="work-info">
                        <div class="work-student">王小红</div>
                        <div class="work-meta">
                            <span><i class="fas fa-clock"></i> 1天前</span>
                            <span class="quality-tag quality-good">良好</span>
                        </div>
                    </div>
                    <div class="work-score">8.7分</div>
                </div>
                
                <div class="work-content">
                    王小红同学性格开朗，乐于助人，在班级活动中表现积极，与同学关系融洽。学习方面能够按时完成作业，但在主动思考和创新方面还有提升空间。建议多参与课堂讨论，培养独立思考能力，相信会有更好的表现。
                </div>
                
                <div class="work-actions">
                    <button class="action-btn">
                        <i class="fas fa-edit"></i> 编辑
                    </button>
                    <button class="action-btn">
                        <i class="fas fa-copy"></i> 复制
                    </button>
                    <button class="action-btn">
                        <i class="fas fa-share"></i> 分享
                    </button>
                    <button class="action-btn primary">
                        <i class="fas fa-download"></i> 导出
                    </button>
                </div>
            </div>

            <div class="work-item">
                <div class="work-header">
                    <div class="work-info">
                        <div class="work-student">张小强</div>
                        <div class="work-meta">
                            <span><i class="fas fa-clock"></i> 3天前</span>
                            <span class="quality-tag quality-normal">待改进</span>
                        </div>
                    </div>
                    <div class="work-score">7.8分</div>
                </div>
                
                <div class="work-content">
                    张小强同学在体育方面表现突出，具有良好的运动天赋和团队协作精神。但在学习方面需要更多的专注和努力，特别是在课堂纪律和作业完成方面。希望能够平衡好学习和运动的关系，全面发展。
                </div>
                
                <div class="work-actions">
                    <button class="action-btn">
                        <i class="fas fa-edit"></i> 编辑
                    </button>
                    <button class="action-btn">
                        <i class="fas fa-copy"></i> 复制
                    </button>
                    <button class="action-btn">
                        <i class="fas fa-share"></i> 分享
                    </button>
                    <button class="action-btn primary">
                        <i class="fas fa-download"></i> 导出
                    </button>
                </div>
            </div>

            <div class="work-item">
                <div class="work-header">
                    <div class="work-info">
                        <div class="work-student">刘小美</div>
                        <div class="work-meta">
                            <span><i class="fas fa-clock"></i> 1周前</span>
                            <span class="quality-tag quality-excellent">优秀</span>
                        </div>
                    </div>
                    <div class="work-score">9.5分</div>
                </div>
                
                <div class="work-content">
                    刘小美同学是班级的学习标兵，不仅成绩优异，而且品德高尚。她总是主动帮助学习有困难的同学，展现出很强的责任心和爱心。在各项活动中都能起到模范带头作用，是老师的得力助手，同学们的好榜样。
                </div>
                
                <div class="work-actions">
                    <button class="action-btn">
                        <i class="fas fa-edit"></i> 编辑
                    </button>
                    <button class="action-btn">
                        <i class="fas fa-copy"></i> 复制
                    </button>
                    <button class="action-btn">
                        <i class="fas fa-share"></i> 分享
                    </button>
                    <button class="action-btn primary">
                        <i class="fas fa-download"></i> 导出
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 底部导航 -->
    <div class="bottom-nav">
        <div class="nav-grid">
            <div class="nav-item">
                <div class="nav-icon"><i class="fas fa-home"></i></div>
                <div class="nav-label">首页</div>
            </div>
            <div class="nav-item">
                <div class="nav-icon"><i class="fas fa-magic"></i></div>
                <div class="nav-label">生成</div>
            </div>
            <div class="nav-item active">
                <div class="nav-icon"><i class="fas fa-file-alt"></i></div>
                <div class="nav-label">作品</div>
            </div>
            <div class="nav-item">
                <div class="nav-icon"><i class="fas fa-chart-line"></i></div>
                <div class="nav-label">报告</div>
            </div>
            <div class="nav-item">
                <div class="nav-icon"><i class="fas fa-cog"></i></div>
                <div class="nav-label">设置</div>
            </div>
        </div>
    </div>

    <script>
        // 筛选标签交互
        document.querySelectorAll('.filter-tab').forEach(tab => {
            tab.addEventListener('click', () => {
                document.querySelectorAll('.filter-tab').forEach(t => t.classList.remove('active'));
                tab.classList.add('active');
            });
        });

        // 搜索功能
        document.querySelector('.search-input').addEventListener('input', function(e) {
            const searchTerm = e.target.value.toLowerCase();
            const workItems = document.querySelectorAll('.work-item');
            
            workItems.forEach(item => {
                const studentName = item.querySelector('.work-student').textContent.toLowerCase();
                const content = item.querySelector('.work-content').textContent.toLowerCase();
                
                if (studentName.includes(searchTerm) || content.includes(searchTerm)) {
                    item.style.display = 'block';
                } else {
                    item.style.display = 'none';
                }
            });
        });
    </script>
</body>
</html>
