<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>云函数状态检查</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 900px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 { color: #2c3e50; text-align: center; }
        .function-card {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            margin: 10px 0;
            background: #f9f9f9;
        }
        .function-name {
            font-weight: bold;
            font-size: 18px;
            color: #2c3e50;
            margin-bottom: 10px;
        }
        .status {
            padding: 5px 10px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
            display: inline-block;
            margin: 5px 0;
        }
        .status.checking { background: #fff3cd; color: #856404; }
        .status.success { background: #d4edda; color: #155724; }
        .status.error { background: #f8d7da; color: #721c24; }
        .status.warning { background: #d1ecf1; color: #0c5460; }
        
        button {
            background: #3498db;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #2980b9; }
        button:disabled { background: #bdc3c7; cursor: not-allowed; }
        
        .log {
            background: #2c3e50;
            color: #ecf0f1;
            padding: 10px;
            border-radius: 5px;
            font-family: monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
            margin-top: 10px;
        }
        
        .instructions {
            background: #e8f4fd;
            border-left: 4px solid #3498db;
            padding: 15px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>☁️ 云函数状态检查</h1>
        
        <div class="instructions">
            <h3>📋 操作说明</h3>
            <p><strong>如果AdminAPI显示"更新中"错误：</strong></p>
            <ol>
                <li>等待2-3分钟让当前更新完成</li>
                <li>在微信开发者工具中查看云函数列表状态</li>
                <li>状态变为"正常"后再重新部署</li>
                <li>或者直接删除AdminAPI云函数后重新创建</li>
            </ol>
        </div>
        
        <div style="text-align: center; margin: 20px 0;">
            <button onclick="checkAllFunctions()">检查所有云函数</button>
            <button onclick="autoRefresh()" id="autoBtn">开启自动刷新</button>
            <button onclick="clearLogs()">清空日志</button>
        </div>
        
        <div id="functionsContainer">
            <!-- 云函数状态卡片将在这里显示 -->
        </div>
        
        <div class="log" id="logContainer">
            等待检查云函数状态...
        </div>
    </div>

    <script>
        const FUNCTIONS = [
            {
                name: 'testAPI',
                url: 'https://cloud1-4g85f8xlb8166ff1.tcb.qcloud.la/test',
                description: '简单测试API - 验证基础连通性'
            },
            {
                name: 'adminAPI_v2',
                url: 'https://cloud1-4g85f8xlb8166ff1.tcb.qcloud.la/admin-v2',
                description: '管理后台API v2.0 - 全新版本，避免冲突'
            },
            {
                name: 'adminAPI (旧版)',
                url: 'https://cloud1-4g85f8xlb8166ff1.tcb.qcloud.la/admin',
                description: '原版管理后台API - 可能存在冲突问题'
            }
        ]
        
        let autoRefreshInterval = null
        
        function log(message) {
            const logContainer = document.getElementById('logContainer')
            const timestamp = new Date().toLocaleTimeString()
            logContainer.textContent += `[${timestamp}] ${message}\n`
            logContainer.scrollTop = logContainer.scrollHeight
        }
        
        function createFunctionCard(func) {
            return `
                <div class="function-card" id="card-${func.name}">
                    <div class="function-name">${func.name}</div>
                    <div style="color: #666; margin-bottom: 10px;">${func.description}</div>
                    <div style="font-size: 12px; color: #888; margin-bottom: 10px;">URL: ${func.url}</div>
                    <div class="status checking" id="status-${func.name}">检查中...</div>
                    <div id="details-${func.name}" style="margin-top: 10px; font-size: 12px;"></div>
                </div>
            `
        }
        
        async function checkFunction(func) {
            const statusEl = document.getElementById(`status-${func.name}`)
            const detailsEl = document.getElementById(`details-${func.name}`)
            
            try {
                log(`🔍 检查 ${func.name}...`)
                
                // 1. 检查OPTIONS请求（CORS预检）
                const optionsResponse = await fetch(func.url, {
                    method: 'OPTIONS'
                })
                
                if (optionsResponse.ok) {
                    statusEl.className = 'status success'
                    statusEl.textContent = '✅ CORS配置正常'
                    detailsEl.innerHTML = `
                        <div style="color: #155724;">
                            ✅ OPTIONS请求成功 (${optionsResponse.status})<br>
                            ✅ CORS头配置正确<br>
                            ✅ 云函数状态正常
                        </div>
                    `
                    log(`✅ ${func.name} CORS配置正常`)
                    
                    // 2. 尝试POST请求
                    try {
                        const postResponse = await fetch(func.url, {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json'
                            },
                            body: JSON.stringify({
                                action: 'test',
                                timestamp: Date.now()
                            })
                        })
                        
                        if (postResponse.ok) {
                            const result = await postResponse.json()
                            detailsEl.innerHTML += `<br>✅ POST请求成功，返回数据正常`
                            log(`✅ ${func.name} POST请求成功`)
                        } else {
                            detailsEl.innerHTML += `<br>⚠️ POST请求失败: ${postResponse.status}`
                            log(`⚠️ ${func.name} POST请求失败: ${postResponse.status}`)
                        }
                    } catch (postError) {
                        detailsEl.innerHTML += `<br>⚠️ POST请求异常: ${postError.message}`
                        log(`⚠️ ${func.name} POST请求异常: ${postError.message}`)
                    }
                    
                } else {
                    statusEl.className = 'status warning'
                    statusEl.textContent = `⚠️ 状态异常 (${optionsResponse.status})`
                    detailsEl.innerHTML = `
                        <div style="color: #856404;">
                            ⚠️ OPTIONS请求失败: ${optionsResponse.status}<br>
                            可能原因：云函数正在更新或配置错误
                        </div>
                    `
                    log(`⚠️ ${func.name} 状态异常: ${optionsResponse.status}`)
                }
                
            } catch (error) {
                if (error.message.includes('Failed to fetch')) {
                    statusEl.className = 'status error'
                    statusEl.textContent = '❌ 连接失败'
                    detailsEl.innerHTML = `
                        <div style="color: #721c24;">
                            ❌ 网络连接失败<br>
                            可能原因：<br>
                            • 云函数正在更新中<br>
                            • CORS配置未生效<br>
                            • 云函数未部署或已删除<br>
                            • 网络连接问题
                        </div>
                    `
                    log(`❌ ${func.name} 连接失败: ${error.message}`)
                } else {
                    statusEl.className = 'status error'
                    statusEl.textContent = '❌ 检查失败'
                    detailsEl.innerHTML = `
                        <div style="color: #721c24;">
                            ❌ 检查过程出错: ${error.message}
                        </div>
                    `
                    log(`❌ ${func.name} 检查失败: ${error.message}`)
                }
            }
        }
        
        async function checkAllFunctions() {
            log('🚀 开始检查所有云函数状态...')
            
            const container = document.getElementById('functionsContainer')
            container.innerHTML = FUNCTIONS.map(createFunctionCard).join('')
            
            // 并行检查所有函数
            const promises = FUNCTIONS.map(func => checkFunction(func))
            await Promise.all(promises)
            
            log('✅ 所有云函数检查完成')
        }
        
        function autoRefresh() {
            const btn = document.getElementById('autoBtn')
            
            if (autoRefreshInterval) {
                clearInterval(autoRefreshInterval)
                autoRefreshInterval = null
                btn.textContent = '开启自动刷新'
                btn.style.background = '#3498db'
                log('⏹️ 自动刷新已停止')
            } else {
                autoRefreshInterval = setInterval(checkAllFunctions, 10000) // 每10秒刷新
                btn.textContent = '停止自动刷新'
                btn.style.background = '#e74c3c'
                log('🔄 自动刷新已开启（每10秒）')
            }
        }
        
        function clearLogs() {
            document.getElementById('logContainer').textContent = ''
        }
        
        // 页面加载时自动检查
        window.addEventListener('load', () => {
            setTimeout(checkAllFunctions, 1000)
        })
    </script>
</body>
</html>
