{"root": true, "env": {"browser": true, "es2020": true, "node": true}, "extends": ["eslint:recommended", "@typescript-eslint/recommended", "plugin:react/recommended", "plugin:react-hooks/recommended", "plugin:jsx-a11y/recommended"], "ignorePatterns": ["dist", ".eslintrc.cjs", "vite.config.ts"], "parser": "@typescript-eslint/parser", "parserOptions": {"ecmaVersion": "latest", "sourceType": "module", "ecmaFeatures": {"jsx": true}}, "plugins": ["react", "react-hooks", "react-refresh", "@typescript-eslint", "jsx-a11y"], "settings": {"react": {"version": "detect"}}, "rules": {"react/react-in-jsx-scope": "off", "react/prop-types": "off", "react-refresh/only-export-components": ["warn", {"allowConstantExport": true}], "@typescript-eslint/no-unused-vars": ["warn", {"argsIgnorePattern": "^_"}], "@typescript-eslint/no-explicit-any": "warn", "@typescript-eslint/explicit-function-return-type": "off", "@typescript-eslint/explicit-module-boundary-types": "off", "@typescript-eslint/no-non-null-assertion": "warn", "prefer-const": "error", "no-var": "error", "no-console": ["warn", {"allow": ["warn", "error"]}], "eqeqeq": ["error", "always"], "curly": ["error", "all"], "jsx-a11y/anchor-is-valid": "off", "jsx-a11y/click-events-have-key-events": "warn", "jsx-a11y/no-static-element-interactions": "warn"}}