@echo off
chcp 65001 >nul
echo.
echo ========================================
echo    🚀 启动CSP兼容开发环境
echo ========================================
echo.

echo 📋 检查环境...
if not exist node_modules (
    echo ❌ 未找到node_modules，正在安装依赖...
    npm install
    if errorlevel 1 (
        echo ❌ 依赖安装失败！
        pause
        exit /b 1
    )
)

echo.
echo 🔧 启动Vite开发服务器（后台，端口8081）...
start /B npm run dev

echo ⏳ 等待Vite服务器启动...
timeout /t 5 /nobreak >nul

echo.
echo 🔒 启动CSP兼容代理服务器（端口8082）...
echo 💡 这将解决Content Security Policy错误
echo 🌐 请访问: http://localhost:8082
echo.

npm run dev:csp

echo.
echo 🛑 服务器已停止
pause
