# 依赖文件
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# 环境变量文件
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# 微信小程序相关
project.private.config.json
.vscode/
.idea/

# 日志文件
logs/
*.log

# 临时文件
.tmp/
.temp/
*.tmp
*.temp

# 系统文件
.DS_Store
Thumbs.db
*.swp
*.swo

# 构建输出
dist/
build/
.next/

# 缓存文件
.cache/
.parcel-cache/

# 测试覆盖率
coverage/

# 备份文件
*.backup
*_backup.*
*.bak

# AI工具相关
.claude/
.promptx/

# 管理后台构建文件
admin-v2/build/
admin-v2/dist/

# 云函数配置文件（包含敏感信息）
cloudfunctions/*/config.json
cloudfunctions/*/cloudbaserc.json

# 数据库备份
*.sql
*.db
*.sqlite

# 图片缓存
*.cache

# 开发工具配置
.husky/
.eslintcache
.prettierrc.js
.eslintrc.js

# 文档草稿
docs/draft/
*.draft.md

# 测试文件
test-*.md
test-*.html
test-*.js

# 紧急修复脚本
emergency-*.js
fix-*.js
immediate-*.js

# 手动文件
手动*.json
新建*.html
新版*.md

# 参考文件
参考*/
