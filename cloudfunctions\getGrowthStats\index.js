/**
 * 获取增长统计数据云函数
 * 为管理后台提供增长数据展示
 */

const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()
const _ = db.command

// 内存缓存
const cache = new Map()
const CACHE_DURATION = 5 * 60 * 1000 // 5分钟缓存

/**
 * 云函数入口函数
 */
exports.main = async (event, context) => {
  const { timeRange = '30d', action = 'getGrowthStats' } = event
  
  try {
    console.log('获取增长统计数据:', { timeRange, action })
    
    // 检查缓存
    const cacheKey = `${action}_${timeRange}_${context.OPENID || 'anonymous'}`
    const cached = cache.get(cacheKey)
    if (cached && (Date.now() - cached.timestamp) < CACHE_DURATION) {
      console.log('返回缓存数据:', cacheKey)
      return cached.data
    }
    
    let result
    switch (action) {
      case 'getGrowthStats':
        result = await getGrowthStats(timeRange)
        break
      case 'getGrowthTrends':
        result = await getGrowthTrends(timeRange)
        break
      case 'getUserBehaviorStats':
        result = await getUserBehaviorStats(timeRange)
        break
      default:
        result = await getGrowthStats(timeRange)
    }
    
    // 缓存结果
    if (result.success) {
      cache.set(cacheKey, {
        data: result,
        timestamp: Date.now()
      })
      
      // 清理过期缓存
      for (const [key, value] of cache.entries()) {
        if (Date.now() - value.timestamp > CACHE_DURATION) {
          cache.delete(key)
        }
      }
    }
    
    return result
    
  } catch (error) {
    console.error('获取增长统计失败:', error)
    return {
      success: false,
      error: error.message || '获取增长统计失败'
    }
  }
}

/**
 * 获取增长统计数据
 */
async function getGrowthStats(timeRange) {
  const { startDate, endDate } = getTimeRange(timeRange)
  
  try {
    // 并行获取各种统计数据
    const [
      totalUsers,
      newUsers,
      activeUsers,
      totalComments,
      todayComments,
      shareEvents,
      retentionData,
      freeUsageData
    ] = await Promise.all([
      getUserCount(),
      getNewUserCount(startDate, endDate),
      getActiveUserCount(startDate, endDate),
      getTotalCommentCount(),
      getTodayCommentCount(),
      getShareStats(startDate, endDate),
      getRetentionStats(startDate, endDate),
      getFreeUsageStats(startDate, endDate)
    ])

    // 计算增长指标
    const growthRate = calculateGrowthRate(newUsers, timeRange)
    const retentionRate = calculateRetentionRate(retentionData)
    const shareConversionRate = calculateShareConversionRate(shareEvents)
    
    return {
      success: true,
      data: {
        // 核心增长指标
        overview: {
          totalUsers: totalUsers.total || 0,
          newUsers: newUsers.count || 0,
          activeUsers: activeUsers.count || 0,
          growthRate: growthRate,
          retentionRate: retentionRate,
          shareConversionRate: shareConversionRate
        },
        
        // 用户活跃数据
        userActivity: {
          totalComments: totalComments.total || 0,
          todayComments: todayComments.count || 0,
          avgCommentsPerUser: totalUsers.total > 0 ? Math.round((totalComments.total || 0) / totalUsers.total * 10) / 10 : 0,
          activeUserRatio: totalUsers.total > 0 ? Math.round((activeUsers.count || 0) / totalUsers.total * 100) : 0
        },
        
        // 分享传播数据
        shareData: {
          totalShares: shareEvents.totalShares || 0,
          shareSuccess: shareEvents.shareSuccess || 0,
          shareConversionRate: shareConversionRate,
          viralCoefficient: calculateViralCoefficient(shareEvents, newUsers)
        },
        
        // 免费使用数据
        freeUsage: {
          totalFreeUsage: freeUsageData.totalUsage || 0,
          avgFreePerUser: freeUsageData.avgPerUser || 0,
          freeToActiveConversion: freeUsageData.conversionRate || 0
        },
        
        // 时间范围
        timeRange: {
          startDate,
          endDate,
          period: timeRange
        }
      }
    }
    
  } catch (error) {
    throw new Error(`获取增长统计失败: ${error.message}`)
  }
}

/**
 * 获取用户总数
 */
async function getUserCount() {
  try {
    // 尝试从多个可能的用户数据源获取
    const [commentsUsers, studentsCount] = await Promise.all([
      db.collection('comments').field({ teacherId: true }).get(),
      db.collection('students').count()
    ])
    
    // 从评语数据中提取唯一用户
    const uniqueTeachers = new Set()
    commentsUsers.data.forEach(comment => {
      if (comment.teacherId) {
        uniqueTeachers.add(comment.teacherId)
      }
    })
    
    return {
      total: Math.max(uniqueTeachers.size, studentsCount.total || 0)
    }
  } catch (error) {
    console.warn('获取用户总数失败:', error)
    return { total: 0 }
  }
}

/**
 * 获取新用户数量
 */
async function getNewUserCount(startDate, endDate) {
  try {
    const newComments = await db.collection('comments')
      .where({
        createTime: _.gte(startDate).and(_.lte(endDate))
      })
      .field({ teacherId: true, createTime: true })
      .get()
    
    // 统计在时间范围内首次创建评语的用户
    const userFirstComment = {}
    newComments.data.forEach(comment => {
      if (comment.teacherId) {
        if (!userFirstComment[comment.teacherId] || 
            comment.createTime < userFirstComment[comment.teacherId]) {
          userFirstComment[comment.teacherId] = comment.createTime
        }
      }
    })
    
    return {
      count: Object.keys(userFirstComment).length
    }
  } catch (error) {
    console.warn('获取新用户数量失败:', error)
    return { count: 0 }
  }
}

/**
 * 获取活跃用户数量
 */
async function getActiveUserCount(startDate, endDate) {
  try {
    const activeComments = await db.collection('comments')
      .where({
        createTime: _.gte(startDate).and(_.lte(endDate))
      })
      .field({ teacherId: true })
      .get()
    
    const activeUsers = new Set()
    activeComments.data.forEach(comment => {
      if (comment.teacherId) {
        activeUsers.add(comment.teacherId)
      }
    })
    
    return {
      count: activeUsers.size
    }
  } catch (error) {
    console.warn('获取活跃用户数量失败:', error)
    return { count: 0 }
  }
}

/**
 * 获取评语总数
 */
async function getTotalCommentCount() {
  try {
    const result = await db.collection('comments').count()
    return result
  } catch (error) {
    console.warn('获取评语总数失败:', error)
    return { total: 0 }
  }
}

/**
 * 获取今日评语数量
 */
async function getTodayCommentCount() {
  try {
    const today = new Date()
    today.setHours(0, 0, 0, 0)
    
    const result = await db.collection('comments')
      .where({
        createTime: _.gte(today)
      })
      .count()
    
    return result
  } catch (error) {
    console.warn('获取今日评语数量失败:', error)
    return { count: 0 }
  }
}

/**
 * 获取分享统计
 */
async function getShareStats(startDate, endDate) {
  try {
    // 从监控日志中获取分享事件
    const shareEvents = await db.collection('monitoring_logs')
      .where({
        type: 'event',
        'data.event': _.in(['share_triggered', 'share_success']),
        createTime: _.gte(startDate).and(_.lte(endDate))
      })
      .get()
    
    let totalShares = 0
    let shareSuccess = 0
    
    shareEvents.data.forEach(event => {
      if (event.data.event === 'share_triggered') {
        totalShares++
      } else if (event.data.event === 'share_success') {
        shareSuccess++
      }
    })
    
    return {
      totalShares,
      shareSuccess
    }
  } catch (error) {
    console.warn('获取分享统计失败:', error)
    return { totalShares: 0, shareSuccess: 0 }
  }
}

/**
 * 获取留存统计
 */
async function getRetentionStats(startDate, endDate) {
  try {
    // 简化的留存计算 - 基于评语创建时间
    const comments = await db.collection('comments')
      .where({
        createTime: _.gte(startDate).and(_.lte(endDate))
      })
      .field({ teacherId: true, createTime: true })
      .get()
    
    const userActivity = {}
    comments.data.forEach(comment => {
      if (comment.teacherId) {
        const date = comment.createTime.toISOString().split('T')[0]
        if (!userActivity[comment.teacherId]) {
          userActivity[comment.teacherId] = []
        }
        if (!userActivity[comment.teacherId].includes(date)) {
          userActivity[comment.teacherId].push(date)
        }
      }
    })
    
    // 计算多日活跃用户比例
    const multiDayUsers = Object.values(userActivity).filter(dates => dates.length > 1).length
    const totalUsers = Object.keys(userActivity).length
    
    return {
      multiDayUsers,
      totalUsers,
      retentionRate: totalUsers > 0 ? multiDayUsers / totalUsers : 0
    }
  } catch (error) {
    console.warn('获取留存统计失败:', error)
    return { multiDayUsers: 0, totalUsers: 0, retentionRate: 0 }
  }
}

/**
 * 获取免费使用统计
 */
async function getFreeUsageStats(startDate, endDate) {
  try {
    // 这里需要根据实际的免费使用数据结构来实现
    // 目前返回模拟数据
    return {
      totalUsage: 150,
      avgPerUser: 12,
      conversionRate: 0.25
    }
  } catch (error) {
    console.warn('获取免费使用统计失败:', error)
    return { totalUsage: 0, avgPerUser: 0, conversionRate: 0 }
  }
}

/**
 * 计算增长率
 */
function calculateGrowthRate(newUsers, timeRange) {
  const days = timeRange === '7d' ? 7 : timeRange === '30d' ? 30 : 90
  const dailyGrowth = (newUsers.count || 0) / days
  return Math.round(dailyGrowth * 100) / 100
}

/**
 * 计算留存率
 */
function calculateRetentionRate(retentionData) {
  return Math.round((retentionData.retentionRate || 0) * 100)
}

/**
 * 计算分享转化率
 */
function calculateShareConversionRate(shareEvents) {
  if (shareEvents.totalShares === 0) return 0
  return Math.round((shareEvents.shareSuccess / shareEvents.totalShares) * 100)
}

/**
 * 计算病毒系数
 */
function calculateViralCoefficient(shareEvents, newUsers) {
  if ((newUsers.count || 0) === 0) return 0
  return Math.round((shareEvents.shareSuccess || 0) / (newUsers.count || 1) * 100) / 100
}

/**
 * 获取时间范围
 */
function getTimeRange(timeRange) {
  const endDate = new Date()
  let startDate
  
  switch (timeRange) {
    case '7d':
      startDate = new Date(endDate.getTime() - 7 * 24 * 60 * 60 * 1000)
      break
    case '30d':
      startDate = new Date(endDate.getTime() - 30 * 24 * 60 * 60 * 1000)
      break
    case '90d':
      startDate = new Date(endDate.getTime() - 90 * 24 * 60 * 60 * 1000)
      break
    default:
      startDate = new Date(endDate.getTime() - 30 * 24 * 60 * 60 * 1000)
  }
  
  return { startDate, endDate }
}
