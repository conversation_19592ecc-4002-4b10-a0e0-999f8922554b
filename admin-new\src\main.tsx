import React from 'react'
import ReactDOM from 'react-dom/client'
import App from './App'
import 'antd/dist/reset.css'

// 初始化云开发服务
import './utils/cloudbaseConfig'

console.log('🚀 管理后台启动 - 云函数调用模式')
console.log('💰 成本优化: HTTP调用 → 云函数调用 (19.9元/月 → 0元/月)')

const rootElement = document.getElementById('root')
if (!rootElement) {
  throw new Error('找不到root元素！')
}

const root = ReactDOM.createRoot(rootElement)
root.render(<App />)