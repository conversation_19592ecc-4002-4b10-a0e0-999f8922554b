@echo off
echo.
echo =============================================
echo   评语灵感君管理后台 v2.0 启动器
echo =============================================
echo.

REM 检查node_modules是否存在
if not exist "node_modules" (
    echo 📦 Installing dependencies...
    call npm install
    if errorlevel 1 (
        echo ❌ Installation failed. This might be the rollup bug.
        echo 🔧 Running fix-deps.bat to resolve the issue...
        call fix-deps.bat
        if errorlevel 1 (
            echo ❌ Failed to install dependencies
            echo 💡 Please run fix-deps.bat manually
            pause
            exit /b 1
        )
    )
    echo ✅ Dependencies installed successfully
    echo.
)

REM 检查环境配置
echo 🔧 Validating environment configuration...
call node scripts\validate-env.js
if errorlevel 1 (
    echo.
    echo ⚠️  Configuration issues found, but you can still continue.
    echo 💡 The most important thing is that .env.local exists and is configured.
    echo.
)

echo 🚀 Starting development server...
echo.
echo 📱 The application will open at: http://localhost:3000
echo 🛑 Press Ctrl+C to stop the server
echo.

call npm run dev