/* AI魔法生成 - 原型图风格设计 */
.prototype-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #F0F2F5 0%, #E4E7ED 100%);
  padding: 40rpx 32rpx 200rpx;
  color: #333;
}

/* 页面头部 */
.page-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 48rpx;
  padding-top: 20rpx;
}

.header-title {
  font-size: 40rpx;
  font-weight: 600;
  color: #333;
}

.header-action {
  color: #5470C6;
  font-size: 28rpx;
  cursor: pointer;
}

/* 魔法进度指示器 */
.magic-progress {
  background: linear-gradient(135deg, #5470C6 0%, #73C0DE 100%);
  border-radius: 32rpx;
  padding: 40rpx;
  margin-bottom: 48rpx;
  color: white;
  text-align: center;
}

.progress-icon {
  font-size: 64rpx;
  margin-bottom: 24rpx;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.1); }
}

.magic-icon {
  font-size: 64rpx;
}

.progress-title {
  font-size: 36rpx;
  font-weight: 600;
  margin-bottom: 16rpx;
}

.progress-desc {
  font-size: 28rpx;
  opacity: 0.9;
  margin-bottom: 32rpx;
}

.progress-bar {
  height: 8rpx;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 4rpx;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: white;
  border-radius: 4rpx;
  width: 0%;
  transition: width 0.3s ease;
}

/* 学生选择区域 */
.student-section {
  background: rgba(255, 255, 255, 0.9);
  border-radius: 32rpx;
  padding: 40rpx 32rpx;
  margin-bottom: 40rpx;
  backdrop-filter: blur(20rpx);
  border: 1rpx solid rgba(255, 255, 255, 0.3);
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 32rpx;
  display: flex;
  align-items: center;
}

.section-icon {
  margin-right: 16rpx;
  color: #5470C6;
  font-size: 32rpx;
}

.section-text {
  color: #333;
}

/* 学生选择按钮 */
.student-selector {
  background: #F8F9FA;
  border: 2rpx dashed #E4E7ED;
  border-radius: 24rpx;
  padding: 40rpx 32rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  cursor: pointer;
  transition: all 0.3s ease;
}

.student-selector:hover {
  border-color: #5470C6;
  background: rgba(84, 112, 198, 0.05);
}

.selector-content {
  flex: 1;
}

.selector-text {
  font-size: 32rpx;
  color: #333;
  font-weight: 500;
  display: block;
  margin-bottom: 8rpx;
}

.selector-hint {
  font-size: 24rpx;
  color: #999;
}

.selector-arrow {
  font-size: 32rpx;
  color: #5470C6;
  font-weight: 600;
}

/* 已选学生信息 */
.selected-student-info {
  margin-bottom: 24rpx;
}

.student-info-card {
  background: #E6F4FF;
  border: 2rpx solid #5470C6;
  border-radius: 24rpx;
  padding: 24rpx;
  display: flex;
  align-items: center;
  gap: 24rpx;
}

.student-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  background: linear-gradient(135deg, #5470C6 0%, #73C0DE 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 32rpx;
  font-weight: 600;
}

.student-details {
  flex: 1;
}

.student-name {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  display: block;
  margin-bottom: 4rpx;
}

.student-class {
  font-size: 24rpx;
  color: #666;
}

.regenerate-badge {
  background: #5470C6;
  color: white;
  padding: 8rpx 16rpx;
  border-radius: 16rpx;
  font-size: 24rpx;
  font-weight: 500;
}

/* 已选学生列表 */
.selected-students {
  margin-bottom: 24rpx;
}

.student-chips {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
  margin-bottom: 24rpx;
}

.student-chip {
  background: #E6F4FF;
  border: 1rpx solid #5470C6;
  border-radius: 20rpx;
  padding: 12rpx 16rpx;
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.student-chip .student-name {
  font-size: 28rpx;
  color: #5470C6;
  font-weight: 500;
}

.remove-btn {
  width: 32rpx;
  height: 32rpx;
  border-radius: 50%;
  background: #5470C6;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20rpx;
  cursor: pointer;
}

.add-more-btn {
  background: #F8F9FA;
  border: 2rpx dashed #E4E7ED;
  border-radius: 20rpx;
  padding: 16rpx 24rpx;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
}

.add-more-btn:hover {
  border-color: #5470C6;
  background: rgba(84, 112, 198, 0.05);
}

.add-text {
  font-size: 28rpx;
  color: #5470C6;
  font-weight: 500;
}

/* 评语配置 */
.config-section {
  background: rgba(255, 255, 255, 0.9);
  border-radius: 32rpx;
  padding: 40rpx 32rpx;
  margin-bottom: 40rpx;
  backdrop-filter: blur(20rpx);
  border: 1rpx solid rgba(255, 255, 255, 0.3);
}

.config-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 24rpx;
}

.config-item {
  background: #F8F9FA;
  border: 4rpx solid transparent;
  border-radius: 24rpx;
  padding: 32rpx;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
}

.config-item.selected {
  background: #E6F4FF;
  border-color: #5470C6;
  color: #5470C6;
}

.config-icon {
  font-size: 40rpx;
  margin-bottom: 16rpx;
}

.config-title {
  font-size: 28rpx;
  font-weight: 600;
  margin-bottom: 8rpx;
}

.config-desc {
  font-size: 24rpx;
  opacity: 0.7;
}

/* 生成按钮 */
.generate-button {
  width: 100%;
  background: linear-gradient(135deg, #5470C6 0%, #73C0DE 100%);
  border: none;
  border-radius: 32rpx;
  padding: 32rpx;
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-bottom: 40rpx;
  box-shadow: 0 8rpx 32rpx rgba(84, 112, 198, 0.3);
}

.button-content {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 16rpx;
  line-height: 1;
}

.generate-button:active {
  transform: translateY(2rpx);
  box-shadow: 0 4rpx 16rpx rgba(84, 112, 198, 0.4);
}

.generate-button.disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
  box-shadow: 0 4rpx 16rpx rgba(84, 112, 198, 0.2);
}

.button-icon {
  font-size: 32rpx;
  line-height: 32rpx;
  height: 32rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.button-text {
  font-size: 32rpx;
  font-weight: 600;
  line-height: 32rpx;
  height: 32rpx;
  display: flex;
  align-items: center;
}

/* 生成结果 */
.result-section {
  background: rgba(255, 255, 255, 0.9);
  border-radius: 32rpx;
  padding: 40rpx 32rpx;
  backdrop-filter: blur(20rpx);
  border: 1rpx solid rgba(255, 255, 255, 0.3);
  display: none;
}

.result-section.show {
  display: block;
  animation: slideUp 0.5s ease;
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(40rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.result-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32rpx;
}

.result-score {
  background: linear-gradient(135deg, #52C41A 0%, #73D13D 100%);
  color: white;
  padding: 8rpx 24rpx;
  border-radius: 40rpx;
  font-size: 24rpx;
  font-weight: 600;
}

.result-content {
  background: #F8F9FA;
  border-radius: 24rpx;
  padding: 32rpx;
  margin-bottom: 32rpx;
  max-height: 300rpx; /* 限制最大高度，超出部分滚动 */
  min-height: 200rpx; /* 确保最小高度 */
}

.content-text {
  line-height: 1.6;
  font-size: 28rpx;
  color: #333;
  word-break: break-all; /* 确保长文本正确换行 */
}

.result-actions {
  display: flex;
  gap: 24rpx;
}

.action-button {
  flex: 1;
  padding: 24rpx;
  border: 1rpx solid #E4E7ED;
  border-radius: 24rpx;
  background: white;
  font-size: 28rpx;
  cursor: pointer;
  transition: all 0.3s ease;
}

.action-button.primary {
  background: #5470C6;
  color: white;
  border-color: #5470C6;
}

.action-button.viral-share {
  background: linear-gradient(135deg, #FF6B9D, #FFA726);
  color: white;
  border: none;
  font-weight: 600;
  animation: viralPulse 2s infinite;
}

/* 炫耀分享按钮动画 */
@keyframes viralPulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.02); }
  100% { transform: scale(1); }
}

/* 功能卡片 - 统一设计 */
.function-card {
  background: rgba(255, 255, 255, 0.85);
  border-radius: 32rpx;
  padding: 32rpx;
  margin-bottom: 32rpx;
  box-shadow: 0 4rpx 20rpx rgba(84, 112, 198, 0.1);
  border: 1rpx solid rgba(84, 112, 198, 0.08);
  backdrop-filter: blur(10rpx);
  transition: all 0.3s ease;
}

.function-card:active {
  transform: translateY(2rpx);
  background: rgba(255, 255, 255, 0.95);
  box-shadow: 0 2rpx 12rpx rgba(84, 112, 198, 0.15);
}

/* 卡片头部 */
.card-header {
  display: flex;
  align-items: center;
  margin-bottom: 24rpx;
  gap: 24rpx;
}

.card-icon-wrapper {
  flex-shrink: 0;
}

.card-icon {
  width: 88rpx;
  height: 88rpx;
  border-radius: 22rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 36rpx;
  color: #ffffff;
  box-shadow: 0 6rpx 20rpx rgba(84, 112, 198, 0.25);
  transition: all 0.3s ease;
}

.student-icon {
  background: linear-gradient(135deg, #5470C6 0%, #73C0DE 100%);
}

.settings-icon {
  background: linear-gradient(135deg, #91CC75 0%, #5CB85C 100%);
}

.result-icon {
  background: linear-gradient(135deg, #FAC858 0%, #FC8452 100%);
}

.icon-text {
  font-size: 36rpx;
  font-weight: 600;
}

.card-info {
  flex: 1;
}

.card-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #2C3E50;
  margin-bottom: 8rpx;
  line-height: 1.3;
}

.card-desc {
  font-size: 24rpx;
  color: #606266;
  line-height: 1.4;
}

/* 学生选择器 - 优化版 */
.student-selector {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx;
  background: rgba(84, 112, 198, 0.05);
  border-radius: 24rpx;
  border: 2rpx dashed rgba(84, 112, 198, 0.2);
  transition: all 0.3s ease;
  margin-top: 16rpx;
}

.student-selector:active {
  background: rgba(84, 112, 198, 0.1);
  border-color: rgba(84, 112, 198, 0.3);
  transform: scale(0.98);
}

.selector-content {
  flex: 1;
}

.selector-text {
  font-size: 28rpx;
  color: #2C3E50;
  font-weight: 500;
  margin-bottom: 4rpx;
}

.selector-hint {
  font-size: 22rpx;
  color: #606266;
  opacity: 0.8;
}

.selector-arrow {
  font-size: 24rpx;
  color: #5470C6;
  font-weight: 600;
}

/* 已选学生 */
.selected-students {
  margin-top: 16rpx;
}

.student-chips {
  display: flex;
  flex-wrap: wrap;
  gap: 12rpx;
  margin-bottom: 16rpx;
}

.student-chip {
  display: flex;
  align-items: center;
  background: rgba(84, 112, 198, 0.1);
  border-radius: 20rpx;
  padding: 8rpx 16rpx;
  gap: 8rpx;
}

.student-name {
  font-size: 24rpx;
  color: #2C3E50;
  font-weight: 500;
}

.remove-btn {
  width: 32rpx;
  height: 32rpx;
  border-radius: 50%;
  background: rgba(84, 112, 198, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20rpx;
  color: #5470C6;
  font-weight: 600;
}

.add-more-btn {
  padding: 16rpx 24rpx;
  background: rgba(84, 112, 198, 0.05);
  border-radius: 20rpx;
  border: 1rpx dashed rgba(84, 112, 198, 0.3);
  text-align: center;
}

.add-text {
  font-size: 24rpx;
  color: #5470C6;
  font-weight: 500;
}

/* 设置区域 - 恢复原始布局 */
.setting-group {
  display: flex;
  flex-direction: column;
  gap: 32rpx;
}

.setting-item {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.setting-label {
  font-size: 28rpx;
  color: #2C3E50;
  font-weight: 600;
}

/* 风格选择器 - 网格布局 */
.style-selector {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16rpx;
}

.style-option {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20rpx;
  background: rgba(248, 249, 250, 0.8);
  border-radius: 16rpx;
  border: 2rpx solid transparent;
  transition: all 0.3s ease;
  text-align: center;
  cursor: pointer; /* 增加指针样式 */
}

.style-option:active {
  transform: scale(0.98); /* 添加点击反馈 */
}

.style-option.active {
  background: rgba(84, 112, 198, 0.15); /* 增强选中状态的背景色 */
  border-color: #5470C6;
  box-shadow: 0 4rpx 16rpx rgba(84, 112, 198, 0.2); /* 增强阴影效果 */
  transform: scale(1.02); /* 轻微放大选中项 */
}

.style-emoji {
  font-size: 32rpx;
  margin-bottom: 8rpx;
}

.style-name {
  font-size: 24rpx;
  color: #2C3E50;
  font-weight: 500;
}

/* 长度选择器 - 横向布局 */
.length-selector {
  display: flex;
  gap: 16rpx;
}

.length-option {
  flex: 1;
  padding: 16rpx 20rpx;
  background: rgba(248, 249, 250, 0.8);
  border-radius: 16rpx;
  border: 2rpx solid transparent;
  text-align: center;
  transition: all 0.3s ease;
  cursor: pointer; /* 增加指针样式 */
}

.length-option:active {
  transform: scale(0.98); /* 添加点击反馈 */
}

.length-option.active {
  background: rgba(84, 112, 198, 0.15); /* 增强选中状态的背景色 */
  border-color: #5470C6;
  box-shadow: 0 4rpx 16rpx rgba(84, 112, 198, 0.2); /* 增强阴影效果 */
  transform: scale(1.02); /* 轻微放大选中项 */
}

.length-text {
  font-size: 26rpx;
  color: #606266;
  font-weight: 500;
}

/* 设置区域 */
.setting-group {
  display: flex;
  flex-direction: column;
  gap: 32rpx;
}

.setting-item {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.setting-label {
  font-size: 28rpx;
  color: #2C3E50;
  font-weight: 600;
}



/* 生成按钮区域 */
.generate-section {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
  margin: 24rpx 0;
}

/* AI生成按钮 - 统一设计风格 */
.generate-btn {
  width: 100%;
  height: 96rpx;
  border-radius: 24rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 16rpx;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  text-align: center;
  border: none;
  position: relative;
  overflow: hidden;
}

.generate-btn.primary {
  background: linear-gradient(135deg, #4080FF 0%, #6BA3FF 100%);
  box-shadow: 0 8rpx 24rpx rgba(64, 128, 255, 0.3);
  color: white;
}

.generate-btn.primary:active {
  transform: translateY(2rpx) scale(0.98);
  box-shadow: 0 4rpx 16rpx rgba(64, 128, 255, 0.2);
}

.generate-btn.disabled {
  background: rgba(192, 196, 204, 0.5);
  box-shadow: none;
  transform: none;
  color: rgba(255, 255, 255, 0.6);
}

/* 按钮文字样式 */
.generate-btn-text {
  font-size: 32rpx;
  font-weight: 600;
  color: white;
  line-height: 1;
  display: flex;
  align-items: center;
  justify-content: center;
}

.generate-btn.disabled .generate-btn-text {
  color: rgba(255, 255, 255, 0.6);
}

.btn-icon {
  font-size: 32rpx;
}

.btn-text {
  font-size: 36rpx;
  color: white;
  font-weight: 600;
  text-align: center;
}

/* 结果操作按钮 */
.result-actions {
  display: flex;
  gap: 16rpx;
}

.result-action-btn {
  flex: 1;
  padding: 20rpx;
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.result-action-btn.primary {
  background: linear-gradient(135deg, #5470C6 0%, #73C0DE 100%);
  box-shadow: 0 4rpx 16rpx rgba(84, 112, 198, 0.2);
}

.result-action-btn.secondary {
  background: rgba(255, 255, 255, 0.9);
  border: 2rpx solid rgba(84, 112, 198, 0.3);
}

.result-action-btn:active {
  transform: translateY(1rpx);
}

.action-btn-text {
  font-size: 28rpx;
  font-weight: 600;
}

.result-action-btn.primary .action-btn-text {
  color: white;
}

.result-action-btn.secondary .action-btn-text {
  color: #5470C6;
}

/* 结果区域 */
.result-section {
  margin-top: 24rpx;
}

.result-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24rpx;
}

.result-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #2C3E50;
}

.result-count {
  font-size: 24rpx;
  color: #5470C6;
  font-weight: 500;
  background: rgba(84, 112, 198, 0.1);
  padding: 8rpx 16rpx;
  border-radius: 16rpx;
}

.comment-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
  margin-bottom: 32rpx;
}

.comment-card {
  background: rgba(255, 255, 255, 0.85);
  border-radius: 16rpx;
  padding: 24rpx;
  border: 1rpx solid rgba(84, 112, 198, 0.08);
  backdrop-filter: blur(10rpx);
}

.comment-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}

.comment-header .student-name {
  font-size: 28rpx;
  font-weight: 600;
  color: #2C3E50;
}

.comment-actions {
  display: flex;
  gap: 16rpx;
}

.action-btn {
  font-size: 24rpx;
  color: #5470C6;
  font-weight: 500;
  padding: 8rpx 12rpx;
  background: rgba(84, 112, 198, 0.1);
  border-radius: 12rpx;
}

.comment-content {
  font-size: 28rpx;
  line-height: 1.6;
  color: #2C3E50;
  margin-bottom: 16rpx;
  background: rgba(248, 249, 250, 0.8);
  padding: 20rpx;
  border-radius: 12rpx;
}

.comment-footer {
  display: flex;
  justify-content: flex-end;
}

.comment-info {
  font-size: 22rpx;
  color: #909399;
}

/* 保存按钮 */
.save-section {
  margin-top: 24rpx;
}

.save-btn {
  width: 100%;
  padding: 20rpx;
  background: rgba(255, 255, 255, 0.85);
  border: 2rpx solid rgba(84, 112, 198, 0.3);
  border-radius: 16rpx;
  text-align: center;
  transition: all 0.3s ease;
}

.save-btn:active {
  background: rgba(84, 112, 198, 0.1);
  border-color: #5470C6;
}

.save-text {
  font-size: 30rpx;
  color: #5470C6;
  font-weight: 600;
}

/* 底部弹窗样式 */
.bottom-popup-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.bottom-popup-mask.show {
  opacity: 1;
  visibility: visible;
}

.bottom-popup {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  border-radius: 32rpx 32rpx 0 0;
  max-height: 70vh;
  transform: translateY(100%);
  transition: transform 0.3s ease;
  display: flex;
  flex-direction: column;
}

.bottom-popup.show {
  transform: translateY(0);
}

/* 拖拽指示器 */
.drag-indicator {
  width: 80rpx;
  height: 8rpx;
  background: rgba(0, 0, 0, 0.1);
  border-radius: 4rpx;
  margin: 16rpx auto 8rpx;
}

/* 弹窗标题栏 */
.popup-title-bar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx 32rpx;
  border-bottom: 1rpx solid rgba(84, 112, 198, 0.1);
}

.popup-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #2C3E50;
}

.close-icon {
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background: rgba(84, 112, 198, 0.1);
  font-size: 32rpx;
  color: #5470C6;
  transition: all 0.3s ease;
}

.close-icon:active {
  background: rgba(84, 112, 198, 0.2);
  transform: scale(0.9);
}

/* 搜索容器 */
.search-container {
  padding: 24rpx 32rpx;
  border-bottom: 1rpx solid rgba(84, 112, 198, 0.1);
}

.search-box {
  position: relative;
  background: rgba(248, 249, 250, 0.8);
  border-radius: 24rpx;
  border: 1rpx solid rgba(84, 112, 198, 0.1);
  width: 100%;
  min-height: 80rpx;
  display: flex;
  align-items: center;
}

.search-input {
  width: 100%;
  padding: 24rpx 80rpx 24rpx 32rpx;
  font-size: 28rpx;
  color: #2C3E50;
  background: transparent;
  border: none;
  outline: none;
  box-sizing: border-box;
  height: 80rpx;
  line-height: 80rpx;
}

.search-input::placeholder {
  color: #909399;
  font-size: 28rpx;
}

.search-clear {
  position: absolute;
  right: 16rpx;
  top: 50%;
  transform: translateY(-50%);
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background: rgba(84, 112, 198, 0.1);
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.search-clear.show {
  opacity: 1;
  visibility: visible;
}

.clear-icon {
  font-size: 24rpx;
  color: #5470C6;
}

.search-tip {
  margin-top: 16rpx;
  text-align: center;
}

.tip-text {
  font-size: 24rpx;
  color: #606266;
}

/* 弹窗操作栏 */
.popup-operations {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16rpx 32rpx;
  border-bottom: 1rpx solid rgba(84, 112, 198, 0.1);
  background: rgba(248, 249, 250, 0.8);
  flex-shrink: 0;
}

.operation-left {
  flex: 1;
}

.selected-info {
  font-size: 26rpx;
  color: #5470C6;
  font-weight: 600;
}

.operation-right {
  display: flex;
  gap: 16rpx;
}

.operation-btn {
  padding: 8rpx 16rpx;
  background: rgba(84, 112, 198, 0.1);
  border-radius: 16rpx;
  border: 1rpx solid rgba(84, 112, 198, 0.2);
  transition: all 0.2s ease;
}

.operation-btn:active {
  background: rgba(84, 112, 198, 0.2);
  transform: scale(0.95);
}

.operation-text {
  font-size: 24rpx;
  color: #5470C6;
  font-weight: 500;
}

/* 学生列表 */
.student-scroll-list {
  flex: 1;
  max-height: calc(70vh - 300rpx); /* 减去标题栏(80rpx)、搜索栏(100rpx)、操作栏(60rpx)和底部按钮(120rpx)的高度 */
  min-height: 200rpx;
  overflow-y: auto;
}

.student-row {
  display: flex;
  align-items: center;
  padding: 24rpx 32rpx;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border-bottom: 1rpx solid rgba(84, 112, 198, 0.05);
  border-radius: 12rpx;
  margin-bottom: 8rpx;
  cursor: pointer;
  -webkit-tap-highlight-color: transparent;
}

.student-row:active {
  background: rgba(84, 112, 198, 0.12);
  transform: scale(0.98);
  box-shadow: 0 1rpx 4rpx rgba(84, 112, 198, 0.2);
  transition: all 0.08s ease-out;
}

.student-row.selected {
  background: rgba(84, 112, 198, 0.15);
  border: 2rpx solid rgba(84, 112, 198, 0.3);
  box-shadow: 0 4rpx 16rpx rgba(84, 112, 198, 0.15);
  transform: scale(1.01);
  transition: all 0.15s cubic-bezier(0.4, 0, 0.2, 1);
}

.student-row.selected .student-name {
  color: #2C3E50 !important;
  font-weight: 700 !important;
}

.student-row.selected .student-class {
  color: #5470C6 !important;
  font-weight: 500 !important;
}

.student-row .student-info {
  display: flex;
  align-items: center;
  flex: 1;
}

.student-row .student-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  background: linear-gradient(135deg, #5470C6, #73C0DE);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 24rpx;
  font-size: 28rpx;
  font-weight: 600;
  color: white;
  flex-shrink: 0;
}

.student-row .student-details {
  flex: 1;
}

.student-row .student-name {
  font-size: 32rpx;
  font-weight: 600;
  color: #2C3E50;
  margin-bottom: 8rpx;
}

.student-row .student-class {
  font-size: 24rpx;
  color: #606266;
}

/* 移除重复定义，使用下面的优化版本 */

/* 注意：选择学生的样式已移至 app.wxss 全局样式中 */

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80rpx 40rpx;
  text-align: center;
}

.empty-icon {
  font-size: 80rpx;
  margin-bottom: 24rpx;
  opacity: 0.6;
}

.empty-text {
  font-size: 28rpx;
  color: #606266;
  margin-bottom: 12rpx;
}

.empty-hint {
  font-size: 24rpx;
  color: #909399;
}

/* 底部按钮 */
.popup-footer-bar {
  padding: 24rpx 32rpx 32rpx;
  border-top: 1rpx solid rgba(84, 112, 198, 0.1);
  flex-shrink: 0; /* 确保不会被压缩 */
  background: #fff;
  min-height: 120rpx; /* 确保有足够的高度 */
  position: relative;
  z-index: 10; /* 确保在最上层 */
}

.footer-buttons {
  display: flex;
  gap: 16rpx;
}

.cancel-button {
  flex: 1;
  height: 88rpx;
  background: rgba(248, 249, 250, 0.8);
  border-radius: 44rpx;
  border: 1rpx solid rgba(84, 112, 198, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.cancel-button:active {
  background: rgba(84, 112, 198, 0.1);
  transform: scale(0.98);
}

.cancel-text {
  font-size: 30rpx;
  color: #5470C6;
  font-weight: 600;
}

.confirm-button {
  flex: 1;
  height: 88rpx;
  background: #E0E0E0;
  border-radius: 44rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.confirm-button.active {
  background: linear-gradient(135deg, #5470C6 0%, #73C0DE 100%);
  box-shadow: 0 6rpx 20rpx rgba(84, 112, 198, 0.25);
}

.confirm-text {
  font-size: 30rpx;
  color: #999;
  font-weight: 600;
  transition: color 0.3s ease;
}

.confirm-button.active .confirm-text {
  color: white;
}

/* 生成结果样式 */
.result-section {
  margin-top: 32rpx;
}

.comment-list {
  margin-top: 16rpx;
}

.comment-item {
  background: rgba(248, 249, 250, 0.8);
  border-radius: 20rpx;
  padding: 24rpx;
  margin-bottom: 16rpx;
  border: 1rpx solid rgba(84, 112, 198, 0.08);
}

.comment-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}

.student-info {
  flex: 1;
}

.student-name {
  font-size: 28rpx;
  color: #2C3E50;
  font-weight: 600;
  margin-bottom: 4rpx;
}

.student-class {
  font-size: 22rpx;
  color: #606266;
}

.comment-meta {
  font-size: 22rpx;
  color: #5470C6;
}

.comment-content {
  font-size: 26rpx;
  color: #2C3E50;
  line-height: 1.6;
  margin-bottom: 16rpx;
  padding: 16rpx;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 16rpx;
}

.comment-actions {
  display: flex;
  gap: 12rpx;
  justify-content: flex-end;
}

.action-btn {
  padding: 12rpx 20rpx;
  border-radius: 16rpx;
  font-size: 22rpx;
  font-weight: 500;
  transition: all 0.3s ease;
}

.action-btn.secondary {
  background: rgba(84, 112, 198, 0.1);
  color: #5470C6;
  border: 1rpx solid rgba(84, 112, 198, 0.2);
}

.action-btn:active {
  transform: scale(0.95);
}

/* 保存按钮 */
.save-section {
  margin-top: 24rpx;
  padding-top: 24rpx;
  border-top: 1rpx solid rgba(84, 112, 198, 0.1);
}

.save-btn {
  width: 100%;
  padding: 32rpx;
  border-radius: 24rpx;
  background: linear-gradient(135deg, #91CC75 0%, #5CB85C 100%);
  box-shadow: 0 8rpx 32rpx rgba(145, 204, 117, 0.3);
  transition: all 0.3s ease;
}

.save-btn:active {
  transform: translateY(2rpx);
  box-shadow: 0 4rpx 16rpx rgba(145, 204, 117, 0.2);
}

/* 生成进度样式 */
.progress-section {
  margin-top: 32rpx;
}

.progress-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 48rpx 40rpx;
  text-align: center;
}

.progress-icon {
  margin-bottom: 20rpx;
}

.progress-text {
  font-size: 30rpx;
  color: #2C3E50;
  font-weight: 600;
  margin-bottom: 8rpx;
}

.progress-detail {
  font-size: 24rpx;
  color: #5470C6;
  font-weight: 500;
  margin-bottom: 24rpx;
}




/* 当前学生信息 */
.current-student {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 16rpx 24rpx;
  background: rgba(84, 112, 198, 0.08);
  border-radius: 20rpx;
  margin-top: 16rpx;
}

.student-label {
  font-size: 24rpx;
  color: #606266;
  font-weight: 500;
}

.student-name {
  font-size: 26rpx;
  color: #2C3E50;
  font-weight: 600;
}

/* 成功弹窗样式 */
.success-popup-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.success-popup-mask.show {
  opacity: 1;
  visibility: visible;
}

.success-popup {
  background: white;
  border-radius: 32rpx;
  padding: 48rpx 40rpx;
  margin: 40rpx;
  max-width: 600rpx;
  width: 100%;
  box-shadow: 0 16rpx 48rpx rgba(0, 0, 0, 0.2);
  transform: scale(0.8) translateY(40rpx);
  transition: all 0.3s ease;
}

.success-popup.show {
  transform: scale(1) translateY(0);
}

.success-icon {
  font-size: 80rpx;
  text-align: center;
  margin-bottom: 24rpx;
}

.success-title {
  font-size: 36rpx;
  font-weight: 700;
  color: #2C3E50;
  text-align: center;
  margin-bottom: 16rpx;
}

.success-content {
  text-align: center;
  margin-bottom: 40rpx;
}

.success-text {
  font-size: 28rpx;
  color: #2C3E50;
  display: block;
  margin-bottom: 8rpx;
}

.success-detail {
  font-size: 24rpx;
  color: #EE6666;
}

.success-actions {
  display: flex;
  gap: 12rpx;
  margin-bottom: 24rpx;
}

.success-btn {
  flex: 1;
  padding: 24rpx 16rpx;
  border-radius: 20rpx;
  text-align: center;
  font-size: 26rpx;
  font-weight: 600;
  transition: all 0.3s ease;
  min-width: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  white-space: nowrap;
  overflow: hidden;
}

.success-btn.secondary {
  background: rgba(84, 112, 198, 0.1);
  color: #5470C6;
  border: 1rpx solid rgba(84, 112, 198, 0.2);
}

.success-btn.primary {
  background: linear-gradient(135deg, #5470C6 0%, #73C0DE 100%);
  color: white;
  box-shadow: 0 4rpx 16rpx rgba(84, 112, 198, 0.3);
}

.success-btn.viral-share {
  background: linear-gradient(135deg, #FF6B9D, #FFA726);
  color: white;
  box-shadow: 0 4rpx 16rpx rgba(255, 107, 157, 0.3);
  animation: viralPulse 2s infinite;
}

.success-btn:active {
  transform: scale(0.95);
}

.success-footer {
  border-top: 1rpx solid rgba(84, 112, 198, 0.1);
  padding-top: 24rpx;
  text-align: center;
}

.footer-btn {
  padding: 16rpx 32rpx;
}

.footer-text {
  font-size: 26rpx;
  color: #606266;
  text-decoration: underline;
}

/* 响应式适配 */
@media (max-width: 750rpx) {
  .morandi-page {
    padding: 32rpx 24rpx;
  }

  .style-selector {
    grid-template-columns: 1fr;
  }

  .length-selector {
    flex-direction: column;
  }

  .success-popup {
    margin: 20rpx;
  }
}

/* 评语预览样式 */
.comment-preview {
  margin-top: 20rpx;
  max-height: 400rpx;
  overflow-y: scroll;
  background: #f8f9fa;
  border-radius: 8rpx;
  padding: 20rpx;
}

.preview-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 15rpx;
}

.comment-preview .comment-item {
  margin-bottom: 20rpx;
  padding: 15rpx;
  background: white;
  border-radius: 6rpx;
  border-left: 4rpx solid #007aff;
}

.comment-preview .comment-student {
  font-size: 26rpx;
  font-weight: bold;
  color: #007aff;
  margin-bottom: 8rpx;
}

.comment-preview .comment-content {
  font-size: 24rpx;
  color: #666;
  line-height: 1.6;
}

/* 重新生成模式样式 */
.selected-student-info {
  margin: 32rpx 0;
}

.student-info-card {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 24rpx;
  padding: 40rpx;
  display: flex;
  align-items: center;
  color: white;
  position: relative;
  overflow: hidden;
  box-shadow: 0 16rpx 48rpx rgba(102, 126, 234, 0.3);
}

.student-info-card::before {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 200rpx;
  height: 200rpx;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  transform: translate(60rpx, -60rpx);
}

.student-avatar {
  width: 100rpx;
  height: 100rpx;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 40rpx;
  font-weight: bold;
  margin-right: 32rpx;
  border: 4rpx solid rgba(255, 255, 255, 0.3);
  position: relative;
  z-index: 1;
}

.student-details {
  flex: 1;
  position: relative;
  z-index: 1;
}

.student-name {
  font-size: 36rpx;
  font-weight: bold;
  display: block;
  margin-bottom: 8rpx;
}

.student-class {
  font-size: 28rpx;
  opacity: 0.9;
}

.regenerate-badge {
  background: rgba(255, 255, 255, 0.2);
  padding: 12rpx 24rpx;
  border-radius: 40rpx;
  font-size: 24rpx;
  font-weight: 500;
  border: 2rpx solid rgba(255, 255, 255, 0.3);
  position: relative;
  z-index: 1;
}

/* 生成进度条样式 */
.generate-progress-section {
  background: rgba(255, 255, 255, 0.9);
  border-radius: 24rpx;
  padding: 32rpx;
  margin: 32rpx 0;
  backdrop-filter: blur(10rpx);
  border: 1rpx solid rgba(255, 255, 255, 0.3);
  animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(20rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.progress-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}

.progress-text {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

.progress-percent {
  font-size: 32rpx;
  color: #5470C6;
  font-weight: 600;
}

.progress-current-student {
  margin-bottom: 16rpx;
  padding: 16rpx 20rpx;
  background: linear-gradient(135deg, #5470C6 0%, #73C0DE 100%);
  border-radius: 16rpx;
  text-align: center;
}

.current-student-text {
  font-size: 26rpx;
  color: white;
  font-weight: 500;
}

.progress-bar-container {
  margin-bottom: 16rpx;
  padding: 0;
}

.progress-bar-bg {
  height: 8rpx;
  background: rgba(84, 112, 198, 0.2);
  border-radius: 4rpx;
  overflow: hidden;
}

.progress-bar-fill {
  height: 100%;
  background: linear-gradient(135deg, #5470C6 0%, #73C0DE 100%);
  border-radius: 4rpx;
  transition: width 0.5s ease;
  position: relative;
}

.progress-bar-fill::after {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 20rpx;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.5));
  animation: shine 1.5s infinite;
}

@keyframes shine {
  0% { opacity: 0; }
  50% { opacity: 1; }
  100% { opacity: 0; }
}

.progress-status {
  text-align: center;
}

.status-text {
  font-size: 24rpx;
  color: #666;
  opacity: 0.8;
}

/* 强制修复进度条样式 */
.generate-progress-section .progress-bar-container {
  margin-bottom: 16rpx !important;
  padding: 0 !important;
  width: 100% !important;
  max-width: none !important;
}

.generate-progress-section .progress-bar-bg {
  height: 8rpx !important;
  background: rgba(84, 112, 198, 0.2) !important;
  border-radius: 4rpx !important;
  overflow: hidden !important;
  width: 100% !important;
}

.generate-progress-section .progress-bar-fill {
  height: 100% !important;
  background: linear-gradient(135deg, #5470C6 0%, #73C0DE 100%) !important;
  border-radius: 4rpx !important;
  transition: width 0.5s ease !important;
  position: relative !important;
}