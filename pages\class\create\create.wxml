<!--
  班级创建/编辑页面
-->
<view class="class-create-page">
  <!-- 表单内容 -->
  <view class="form-container">
    <view class="form-section">
      <view class="form-title">{{isEdit ? '编辑班级' : '创建班级'}}</view>

      <view class="form-item">
        <view class="form-label">班级名称 *</view>
        <van-field
          value="{{formData.name}}"
          placeholder="请输入班级名称"
          maxlength="50"
          show-word-limit
          error-message="{{errors.name}}"
          bind:input="onNameInput"
          custom-class="form-input"
          border="{{false}}"
        />
      </view>

      <view class="form-item">
        <view class="form-label">班级描述</view>
        <van-field
          type="textarea"
          value="{{formData.description}}"
          placeholder="请输入班级描述（可选）"
          maxlength="200"
          show-word-limit
          error-message="{{errors.description}}"
          bind:input="onDescriptionInput"
          custom-class="form-textarea"
          border="{{false}}"
        />
      </view>

      <view class="form-item">
        <view class="form-label">年级 *</view>
        <van-field
          value="{{formData.grade}}"
          placeholder="请输入年级，如：三年级、高一、大二等"
          error-message="{{errors.grade}}"
          bind:input="onGradeInput"
          custom-class="form-input"
          border="{{false}}"
          maxlength="20"
        />
      </view>

      <view class="form-item">
        <view class="form-label">学科 *</view>
        <van-field
          value="{{formData.subject}}"
          placeholder="请输入学科，如：数学、语文、英语等"
          error-message="{{errors.subject}}"
          bind:input="onSubjectInput"
          custom-class="form-input"
          border="{{false}}"
          maxlength="20"
        />
      </view>
    </view>
  </view>

  <!-- 底部操作栏 -->
  <view class="bottom-actions">
    <van-button
      type="default"
      size="large"
      custom-class="cancel-btn"
      bindtap="onCancel"
    >
      取消
    </van-button>
    <van-button
      type="primary"
      size="large"
      custom-class="submit-btn"
      loading="{{submitting}}"
      bindtap="onSubmit"
    >
      {{isEdit ? '保存' : '创建'}}
    </van-button>
  </view>

</view>