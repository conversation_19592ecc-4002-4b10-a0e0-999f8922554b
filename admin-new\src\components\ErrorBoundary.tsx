import React from 'react'
import { But<PERSON>, Result } from 'antd'

interface ErrorBoundaryState {
  hasError: boolean
  error: Error | null
  errorInfo: React.ErrorInfo | null
}

interface ErrorBoundaryProps {
  children: React.ReactNode
  fallback?: React.ComponentType<{ error: Error; retry: () => void }>
}

class ErrorBoundary extends React.Component<ErrorBoundaryProps, ErrorBoundaryState> {
  constructor(props: ErrorBoundaryProps) {
    super(props)
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null
    }
  }

  static getDerivedStateFromError(error: Error): Partial<ErrorBoundaryState> {
    return {
      hasError: true,
      error
    }
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('ErrorBoundary caught an error:', error, errorInfo)
    
    this.setState({
      error,
      errorInfo
    })

    // 这里可以添加错误上报逻辑
    this.reportError(error, errorInfo)
  }

  private reportError = (error: Error, errorInfo: React.ErrorInfo) => {
    try {
      // 收集错误信息
      const errorReport = {
        message: error.message,
        stack: error.stack,
        componentStack: errorInfo.componentStack,
        timestamp: new Date().toISOString(),
        userAgent: navigator.userAgent,
        url: window.location.href,
        userId: this.getCurrentUserId()
      }

      // 发送错误报告到监控服务
      // 这里可以集成如 Sentry、LogRocket 等服务
      console.error('Error Report:', errorReport)
      
      // 如果有错误上报API，可以在这里调用
      // errorReportingService.report(errorReport)
    } catch (reportingError) {
      console.error('Failed to report error:', reportingError)
    }
  }

  private getCurrentUserId = (): string | null => {
    try {
      // 从认证状态中获取用户ID
      const authState = localStorage.getItem('auth-storage')
      if (authState) {
        const parsed = JSON.parse(authState)
        return parsed.state?.user?.id || null
      }
    } catch {
      // 忽略解析错误
    }
    return null
  }

  private handleRetry = () => {
    this.setState({
      hasError: false,
      error: null,
      errorInfo: null
    })
  }

  private handleReload = () => {
    window.location.reload()
  }

  render() {
    if (this.state.hasError) {
      const { fallback: Fallback } = this.props
      const { error } = this.state

      // 如果提供了自定义错误界面组件
      if (Fallback && error) {
        return <Fallback error={error} retry={this.handleRetry} />
      }

      // 默认错误界面
      return (
        <div className="min-h-screen flex items-center justify-center bg-gray-50">
          <div className="max-w-md w-full">
            <Result
              status="error"
              title="页面出现错误"
              subTitle={
                import.meta.env.DEV 
                  ? `${error?.message || '未知错误'}` 
                  : "抱歉，页面出现了错误。请尝试刷新页面或联系技术支持。"
              }
              extra={[
                <Button type="primary" key="retry" onClick={this.handleRetry}>
                  重试
                </Button>,
                <Button key="reload" onClick={this.handleReload}>
                  刷新页面
                </Button>
              ]}
            />
            
            {/* 开发环境下显示详细错误信息 */}
            {import.meta.env.DEV && error && (
              <details className="mt-6 p-4 bg-red-50 border border-red-200 rounded-lg">
                <summary className="cursor-pointer font-medium text-red-800 mb-2">
                  错误详情 (仅在开发环境显示)
                </summary>
                <div className="text-sm text-red-700 space-y-2">
                  <div>
                    <strong>错误信息:</strong>
                    <pre className="mt-1 text-xs bg-red-100 p-2 rounded overflow-auto">
                      {error.message}
                    </pre>
                  </div>
                  {error.stack && (
                    <div>
                      <strong>错误堆栈:</strong>
                      <pre className="mt-1 text-xs bg-red-100 p-2 rounded overflow-auto max-h-40">
                        {error.stack}
                      </pre>
                    </div>
                  )}
                  {this.state.errorInfo?.componentStack && (
                    <div>
                      <strong>组件堆栈:</strong>
                      <pre className="mt-1 text-xs bg-red-100 p-2 rounded overflow-auto max-h-40">
                        {this.state.errorInfo.componentStack}
                      </pre>
                    </div>
                  )}
                </div>
              </details>
            )}
          </div>
        </div>
      )
    }

    return this.props.children
  }
}

// 高阶组件：为任何组件添加错误边界
export const withErrorBoundary = <P extends object>(
  Component: React.ComponentType<P>,
  fallback?: React.ComponentType<{ error: Error; retry: () => void }>
) => {
  const WrappedComponent = (props: P) => (
    <ErrorBoundary fallback={fallback}>
      <Component {...props} />
    </ErrorBoundary>
  )
  
  WrappedComponent.displayName = `withErrorBoundary(${Component.displayName || Component.name})`
  
  return WrappedComponent
}

// Hook：用于手动触发错误边界
export const useErrorHandler = () => {
  const [, setError] = React.useState<Error | null>(null)
  
  return React.useCallback((error: Error) => {
    setError(() => {
      throw error
    })
  }, [])
}

// 简化的错误边界组件用于特定场景
export const SimpleErrorBoundary: React.FC<{
  children: React.ReactNode
  message?: string
}> = ({ children, message = "出现了错误" }) => {
  return (
    <ErrorBoundary
      fallback={({ error, retry }) => (
        <div className="p-4 text-center border border-red-200 bg-red-50 rounded-lg">
          <p className="text-red-600 mb-3">{message}</p>
          {import.meta.env.DEV && (
            <p className="text-xs text-red-500 mb-3">{error.message}</p>
          )}
          <button 
            onClick={retry}
            className="btn btn-sm btn-primary"
          >
            重试
          </button>
        </div>
      )}
    >
      {children}
    </ErrorBoundary>
  )
}

export default ErrorBoundary