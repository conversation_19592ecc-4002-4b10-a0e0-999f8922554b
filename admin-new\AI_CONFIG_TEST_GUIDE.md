# AI配置页面测试指南

## 🎯 测试目标

验证admin-new管理后台AI配置页面的所有功能是否正常工作，特别是：

1. ✅ AI模型删除功能修复
2. ✅ AI配置与生成评语系统的数据同步
3. ✅ 测试云函数按钮移除
4. ✅ 所有按钮与数据库连接状态检查

## 🚀 快速启动

### 方法1：使用批处理脚本（推荐）
```bash
# 双击运行
start-ai-config-test.bat
```

### 方法2：手动启动
```bash
# 终端1：启动API服务器
node local-api-server.cjs

# 终端2：启动前端应用
npm run dev
```

## 🧪 测试步骤

### 1. 基础连接测试
- [ ] 打开 http://localhost:5173
- [ ] 进入AI配置页面
- [ ] 检查页面是否正常加载
- [ ] 查看API服务器控制台是否有请求日志

### 2. AI模型管理测试

#### 2.1 查看现有模型
- [ ] 检查是否显示默认的AI模型列表
- [ ] 验证模型信息显示正确（名称、提供商、状态等）

#### 2.2 添加新模型
- [ ] 点击"添加模型"按钮
- [ ] 填写模型信息：
  - 模型名称：测试模型
  - 提供商：bytedance
  - 模型版本：doubao-lite-4k
  - API密钥：test-key-123
  - 启用状态：开启
- [ ] 点击"添加模型"
- [ ] 验证模型添加成功
- [ ] 检查API服务器日志显示创建请求

#### 2.3 编辑模型
- [ ] 点击某个模型的"编辑"按钮
- [ ] 修改模型名称
- [ ] 点击"保存"
- [ ] 验证修改成功
- [ ] 检查API服务器日志显示更新请求

#### 2.4 删除模型（重点测试）
- [ ] 点击某个模型的"删除"按钮
- [ ] 确认删除操作
- [ ] 验证模型从列表中消失
- [ ] **刷新页面，确认模型不会重新出现**
- [ ] 检查API服务器日志显示删除请求

#### 2.5 测试AI连接
- [ ] 确保有至少一个激活的模型
- [ ] 点击"测试连接"按钮
- [ ] 验证测试成功提示
- [ ] 检查响应时间和Token消耗信息

### 3. 系统参数配置测试

#### 3.1 参数设置
- [ ] 切换到"系统参数"标签页
- [ ] 修改以下参数：
  - 温度参数：0.8
  - 最大Token数：1500
  - Top P：0.8
  - 启用缓存：开启
- [ ] 点击"保存配置"

#### 3.2 配置同步验证
- [ ] 验证保存成功提示
- [ ] 检查提示信息是否包含"配置已同步到生成评语系统"
- [ ] 检查API服务器日志显示配置保存请求

### 4. 界面检查

#### 4.1 按钮状态检查
- [ ] 确认"测试云函数"按钮已被移除
- [ ] 所有按钮都有正确的图标和文字
- [ ] 按钮点击有正确的加载状态

#### 4.2 数据显示检查
- [ ] 模型列表显示完整
- [ ] 状态标签颜色正确（激活/非激活）
- [ ] 提示词模板列表正常显示

## 🔍 问题排查

### API连接问题
如果出现网络错误：
1. 检查API服务器是否在 http://localhost:3000 运行
2. 查看浏览器控制台的网络请求
3. 检查CORS设置

### 数据不持久问题
如果删除的模型重新出现：
1. 检查localStorage中的数据
2. 确认API服务器返回正确的响应
3. 验证前端状态更新逻辑

### 页面加载问题
如果页面无法加载：
1. 检查前端服务器是否在 http://localhost:5173 运行
2. 查看浏览器控制台的错误信息
3. 确认所有依赖已正确安装

## 📊 测试结果记录

### 功能测试结果
- [ ] AI模型删除功能：正常/异常
- [ ] AI配置数据同步：正常/异常  
- [ ] 测试云函数按钮：已移除/仍存在
- [ ] 数据库连接状态：正常/异常

### 性能测试结果
- API响应时间：___ ms
- 页面加载时间：___ ms
- 内存使用情况：正常/异常

## 🎉 测试完成

如果所有测试项都通过，说明AI配置页面的优化已经完成，可以进行生产部署。

## 📝 注意事项

1. 当前使用的是模拟API服务器，实际部署时需要连接真实的云函数
2. 所有测试数据在重启后会重置
3. 生产环境中需要配置正确的API密钥和端点地址
