import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'
import path from 'path'

// 专门为静态部署优化的配置
export default defineConfig({
  // 使用自定义HTML模板
  root: __dirname,

  plugins: [
    react({
      // 完全禁用Babel，使用esbuild
      babel: false,
      fastRefresh: false
    })
  ],
  
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src')
    }
  },

  server: {
    port: 3000,
    host: '127.0.0.1',
    strictPort: true,
    open: false
  },

  build: {
    target: 'es2015', // 更广泛的浏览器支持
    outDir: 'dist-static',
    assetsDir: 'assets',
    sourcemap: false,
    minify: 'esbuild', // 使用esbuild压缩，更稳定

    rollupOptions: {
      // 使用自定义HTML模板
      input: {
        main: path.resolve(__dirname, 'index-static.html')
      },
      output: {
        // 强制使用传统格式，避免ES模块问题
        format: 'iife', // 立即执行函数表达式，不依赖模块加载器
        entryFileNames: 'assets/app.js', // 固定文件名
        chunkFileNames: 'assets/[name].js',
        assetFileNames: 'assets/[name].[ext]',

        // 将所有代码打包到一个文件中，避免动态导入
        manualChunks: undefined,

        // 全局变量名
        name: 'PingyuAdmin'
      }
    },
    
    // 禁用代码分割，确保所有代码在一个文件中
    chunkSizeWarningLimit: 5000
  },

  // 使用相对路径
  base: './',

  esbuild: {
    target: 'es2015',
    logOverride: { 'this-is-undefined-in-esm': 'silent' }
  },

  optimizeDeps: {
    force: true,
    include: ['react', 'react-dom', 'antd'],
    esbuildOptions: {
      target: 'es2015'
    }
  }
})
