@echo off
echo ====================================================
echo         强制删除有问题的文件！！！
echo ====================================================

echo 当前目录：%cd%
echo.

echo 检查文件存在状态：
dir "src\utils\errorHandler.*"
echo.

echo 强制删除 errorHandler.ts...
attrib -r "src\utils\errorHandler.ts" 2>nul
del /f /q "src\utils\errorHandler.ts" 2>nul

echo 再次检查：
if exist "src\utils\errorHandler.ts" (
    echo ❌ 文件仍然存在！尝试更强力的删除...
    powershell -Command "Remove-Item 'src\utils\errorHandler.ts' -Force"
) else (
    echo ✅ 文件已成功删除！
)

echo.
echo 最终检查所有 errorHandler 文件：
dir "src\utils\errorHandler.*"

echo.
echo 清理缓存...
if exist "node_modules\.vite" rmdir /s /q "node_modules\.vite"
if exist "dist" rmdir /s /q "dist"

echo.
echo 启动开发服务器...
npm run dev