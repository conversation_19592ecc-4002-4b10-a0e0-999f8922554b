/**
 * 生产部署前安全检查脚本
 * 确保所有关键配置和依赖都已正确设置
 */

const fs = require('fs');
const path = require('path');

class PreDeployChecker {
  constructor() {
    this.errors = [];
    this.warnings = [];
    this.passed = [];
  }

  /**
   * 执行所有检查
   */
  async runAllChecks() {
    console.log('🔍 开始生产部署前安全检查...\n');

    // 1. 环境配置检查
    this.checkEnvironmentConfig();
    
    // 2. 云函数配置检查
    this.checkCloudFunctions();
    
    // 3. 数据库配置检查
    this.checkDatabaseConfig();
    
    // 4. AI服务配置检查
    this.checkAIConfig();
    
    // 5. 安全配置检查
    this.checkSecurityConfig();
    
    // 6. 性能配置检查
    this.checkPerformanceConfig();
    
    // 7. 监控配置检查
    this.checkMonitoringConfig();

    // 输出检查结果
    this.outputResults();
    
    return this.errors.length === 0;
  }

  /**
   * 检查环境配置
   */
  checkEnvironmentConfig() {
    console.log('📋 检查环境配置...');
    
    // 检查云开发配置文件
    if (!fs.existsSync('config/cloudConfig.js')) {
      this.errors.push('缺少云开发配置文件: config/cloudConfig.js');
    } else {
      this.passed.push('云开发配置文件存在');
    }
    
    // 检查环境变量配置
    const requiredEnvVars = [
      'MINIPROGRAM_APP_ID',
      'CLOUD_ENV_ID',
      'DOUBAO_API_KEY'
    ];
    
    requiredEnvVars.forEach(envVar => {
      if (!process.env[envVar]) {
        this.errors.push(`缺少环境变量: ${envVar}`);
      } else {
        this.passed.push(`环境变量 ${envVar} 已配置`);
      }
    });
  }

  /**
   * 检查云函数配置
   */
  checkCloudFunctions() {
    console.log('☁️ 检查云函数配置...');
    
    const requiredFunctions = [
      'login',
      'getUserId', 
      'getStudents',
      'callDoubaoAPI',
      'generateComment',
      'adminAPI'
    ];
    
    requiredFunctions.forEach(funcName => {
      const funcPath = path.join('cloudfunctions', funcName);
      if (!fs.existsSync(funcPath)) {
        this.errors.push(`缺少云函数: ${funcName}`);
      } else {
        // 检查package.json
        const packagePath = path.join(funcPath, 'package.json');
        if (!fs.existsSync(packagePath)) {
          this.warnings.push(`云函数 ${funcName} 缺少 package.json`);
        } else {
          this.passed.push(`云函数 ${funcName} 配置完整`);
        }
      }
    });
  }

  /**
   * 检查数据库配置
   */
  checkDatabaseConfig() {
    console.log('🗄️ 检查数据库配置...');
    
    // 检查数据库集合配置
    if (!fs.existsSync('cloudbase/database/collections.json')) {
      this.warnings.push('缺少数据库集合配置文件');
    } else {
      this.passed.push('数据库集合配置文件存在');
    }
    
    // 检查初始化脚本
    if (!fs.existsSync('cloudfunctions/initDatabase')) {
      this.errors.push('缺少数据库初始化云函数');
    } else {
      this.passed.push('数据库初始化云函数存在');
    }
  }

  /**
   * 检查AI服务配置
   */
  checkAIConfig() {
    console.log('🤖 检查AI服务配置...');
    
    // 检查豆包API配置
    if (!process.env.DOUBAO_API_KEY) {
      this.errors.push('缺少豆包API密钥环境变量: DOUBAO_API_KEY');
    } else {
      this.passed.push('豆包API密钥已配置');
    }
    
    // 检查AI配置文件
    const aiConfigExists = fs.existsSync('config/aiConfig.js') || 
                          fs.existsSync('config/environments.ts');
    if (!aiConfigExists) {
      this.warnings.push('缺少AI服务配置文件');
    } else {
      this.passed.push('AI服务配置文件存在');
    }
  }

  /**
   * 检查安全配置
   */
  checkSecurityConfig() {
    console.log('🔒 检查安全配置...');
    
    // 检查输入验证
    if (!fs.existsSync('utils/validation.js')) {
      this.warnings.push('缺少输入验证工具');
    } else {
      this.passed.push('输入验证工具存在');
    }
    
    // 检查错误处理
    if (!fs.existsSync('utils/errorHandler.js')) {
      this.warnings.push('缺少错误处理工具');
    } else {
      this.passed.push('错误处理工具存在');
    }
  }

  /**
   * 检查性能配置
   */
  checkPerformanceConfig() {
    console.log('⚡ 检查性能配置...');
    
    // 检查性能优化工具
    if (!fs.existsSync('utils/performanceOptimizer.ts')) {
      this.warnings.push('缺少性能优化工具');
    } else {
      this.passed.push('性能优化工具存在');
    }
  }

  /**
   * 检查监控配置
   */
  checkMonitoringConfig() {
    console.log('📊 检查监控配置...');
    
    // 检查监控工具
    if (!fs.existsSync('utils/monitoring.ts')) {
      this.warnings.push('缺少监控工具');
    } else {
      this.passed.push('监控工具存在');
    }
  }

  /**
   * 输出检查结果
   */
  outputResults() {
    console.log('\n' + '='.repeat(60));
    console.log('📋 生产部署前检查结果');
    console.log('='.repeat(60));
    
    if (this.passed.length > 0) {
      console.log('\n✅ 通过的检查项:');
      this.passed.forEach(item => console.log(`  ✓ ${item}`));
    }
    
    if (this.warnings.length > 0) {
      console.log('\n⚠️ 警告项:');
      this.warnings.forEach(item => console.log(`  ⚠ ${item}`));
    }
    
    if (this.errors.length > 0) {
      console.log('\n❌ 错误项（必须修复）:');
      this.errors.forEach(item => console.log(`  ✗ ${item}`));
    }
    
    console.log('\n' + '='.repeat(60));
    console.log(`总计: ${this.passed.length} 通过, ${this.warnings.length} 警告, ${this.errors.length} 错误`);
    
    if (this.errors.length === 0) {
      console.log('🎉 所有关键检查项都已通过，可以进行部署！');
    } else {
      console.log('🚨 存在严重错误，请修复后再部署！');
    }
  }
}

// 执行检查
if (require.main === module) {
  const checker = new PreDeployChecker();
  checker.runAllChecks().then(success => {
    process.exit(success ? 0 : 1);
  });
}

module.exports = PreDeployChecker;
