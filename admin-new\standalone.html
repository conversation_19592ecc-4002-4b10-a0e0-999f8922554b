<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <!-- CSP已禁用以解决eval问题 -->
    <title>评语灵感君管理后台</title>
    <script crossorigin src="https://unpkg.com/react@18/umd/react.production.min.js"></script>
    <script crossorigin src="https://unpkg.com/react-dom@18/umd/react-dom.production.min.js"></script>
    <script src="https://unpkg.com/@babel/standalone/babel.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: "SF Pro Display", -apple-system, sans-serif;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
        }
        
        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(5deg); }
        }
        
        @keyframes slideUp {
            from { height: 0; opacity: 0; }
            to { opacity: 1; }
        }
    </style>
</head>
<body>
    <div id="root"></div>

    <script type="text/babel">
        const { useState, useMemo, useCallback, useEffect } = React;

        const App = () => {
            const [isLoggedIn, setIsLoggedIn] = useState(false);
            const [currentPage, setCurrentPage] = useState('dashboard');
            const [currentTime, setCurrentTime] = useState(new Date());
            const [sidebarCollapsed, setSidebarCollapsed] = useState(false);
            
            // AI模型配置状态
            const [selectedModel, setSelectedModel] = useState('doubao');
            const [apiConfigs, setApiConfigs] = useState({
                doubao: { apiKey: '', baseUrl: 'https://ark.cn-beijing.volces.com/api/v3', modelName: 'ep-20241123002057-k6rr4' },
                kimi: { apiKey: '', baseUrl: 'https://api.moonshot.cn/v1', modelName: 'moonshot-v1-8k' },
                deepseek: { apiKey: '', baseUrl: 'https://api.deepseek.com/v1', modelName: 'deepseek-chat' },
                qwen: { apiKey: '', baseUrl: 'https://dashscope.aliyuncs.com/compatible-mode/v1', modelName: 'qwen2-turbo' }
            });
            const [aiParams, setAiParams] = useState({
                temperature: 0.7,
                maxTokens: 2000,
                topP: 0.9,
                frequencyPenalty: 0.1
            });
            
            // Tokens图表视图状态
            const [tokensViewMode, setTokensViewMode] = useState('hour');
            
            // 模拟数据
            const stats = {
                totalUsers: 1234,
                todayComments: 110,
                aiCalls: 2456,
                systemScore: 987
            };
            
            const tokensData = {
                totalToday: 112960,
                totalWeek: 772260,
                costToday: 0.23,
                costWeek: 1.54
            };
            
            const activities = [
                { user: '张老师', action: '生成了5条学生评语', time: '2分钟前', icon: '📝', color: '#51cf66' },
                { user: '李老师', action: '导入了新的学生数据', time: '15分钟前', icon: '📁', color: '#667eea' },
                { user: '王老师', action: '修改了AI配置参数', time: '1小时前', icon: '⚙️', color: '#ff6b6b' },
                { user: '赵老师', action: '导出了月度报告', time: '2小时前', icon: '📈', color: '#ffd43b' }
            ];
            
            const isConnected = true;
            const connectionError = null;

            // 更新时间
            useEffect(() => {
                const timer = setInterval(() => setCurrentTime(new Date()), 30000);
                return () => clearInterval(timer);
            }, []);

            // 导航菜单项
            const menuItems = useMemo(() => [
                { key: 'dashboard', icon: '📊', label: '数据大屏', description: '实时数据监控' },
                { key: 'ai-config', icon: '🤖', label: 'AI配置', description: '模型参数设置' },
                { key: 'data-management', icon: '📝', label: '数据管理', description: '学生评语管理' },
                { key: 'settings', icon: '⚙️', label: '系统设置', description: '个人与系统配置' }
            ], []);

            // 获取页面标题
            const pageTitle = useMemo(() => {
                const page = menuItems.find(item => item.key === currentPage);
                return page ? page.label : '数据大屏';
            }, [currentPage, menuItems]);

            // 页面切换函数
            const handlePageChange = useCallback((pageKey) => {
                setCurrentPage(pageKey);
            }, []);

            // 侧边栏切换函数
            const toggleSidebar = useCallback(() => {
                setSidebarCollapsed(prev => !prev);
            }, []);

            // 通用按钮点击动效处理
            const addButtonClickEffect = useCallback((e) => {
                const button = e.currentTarget;
                const originalTransform = button.style.transform;
                button.style.transform = originalTransform.replace('scale(1)', 'scale(0.95)');
                setTimeout(() => {
                    button.style.transform = originalTransform;
                }, 150);
            }, []);

            // 登录处理
            const handleLogin = useCallback((e) => {
                e.preventDefault();
                const form = e.target;
                const formData = new FormData(form);
                const username = formData.get('username');
                const password = formData.get('password');
                
                if (username === 'admin' && password === 'admin123') {
                    setIsLoggedIn(true);
                } else {
                    alert('❌ 用户名或密码错误！\\n请使用: admin / admin123');
                }
            }, []);

            // 登录界面
            if (!isLoggedIn) {
                return (
                    <div style={{
                        minHeight: '100vh',
                        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        padding: '20px',
                        position: 'relative',
                        overflow: 'hidden'
                    }}>
                        {/* 动态背景装饰 */}
                        <div style={{
                            position: 'absolute',
                            top: '-100px',
                            left: '-100px',
                            width: '300px',
                            height: '300px',
                            background: 'rgba(255,255,255,0.1)',
                            borderRadius: '50%',
                            animation: 'float 6s ease-in-out infinite'
                        }}></div>
                        <div style={{
                            position: 'absolute',
                            bottom: '-150px',
                            right: '-150px',
                            width: '400px',
                            height: '400px',
                            background: 'rgba(255,255,255,0.05)',
                            borderRadius: '50%',
                            animation: 'float 8s ease-in-out infinite reverse'
                        }}></div>

                        {/* 登录卡片 */}
                        <div style={{
                            background: 'rgba(255,255,255,0.95)',
                            backdropFilter: 'blur(20px)',
                            padding: '50px',
                            borderRadius: '24px',
                            boxShadow: '0 25px 50px rgba(0,0,0,0.2)',
                            width: '100%',
                            maxWidth: '450px',
                            border: '1px solid rgba(255,255,255,0.2)',
                            position: 'relative',
                            zIndex: 10
                        }}>
                            {/* Logo区域 */}
                            <div style={{ textAlign: 'center', marginBottom: '40px' }}>
                                <div style={{
                                    width: '80px',
                                    height: '80px',
                                    background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                                    borderRadius: '20px',
                                    display: 'flex',
                                    alignItems: 'center',
                                    justifyContent: 'center',
                                    margin: '0 auto 20px',
                                    boxShadow: '0 10px 30px rgba(102, 126, 234, 0.4)',
                                    fontSize: '32px'
                                }}>
                                    🤖
                                </div>
                                <h1 style={{
                                    fontSize: '32px',
                                    fontWeight: '700',
                                    color: '#1a1a1a',
                                    marginBottom: '8px',
                                    fontFamily: '"SF Pro Display", -apple-system, sans-serif'
                                }}>
                                    评语灵感君
                                </h1>
                                <p style={{
                                    color: '#666',
                                    fontSize: '18px',
                                    fontWeight: '500'
                                }}>
                                    智能评语生成管理系统
                                </p>
                                <div style={{
                                    width: '60px',
                                    height: '4px',
                                    background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                                    borderRadius: '2px',
                                    margin: '20px auto 0'
                                }}></div>
                            </div>

                            {/* 登录表单 */}
                            <form onSubmit={handleLogin}>
                                <div style={{ marginBottom: '25px' }}>
                                    <input
                                        type="text"
                                        name="username"
                                        placeholder="请输入用户名"
                                        defaultValue="admin"
                                        style={{
                                            width: '100%',
                                            padding: '18px 20px',
                                            border: '2px solid #f0f0f0',
                                            borderRadius: '16px',
                                            fontSize: '16px',
                                            fontWeight: '500',
                                            transition: 'all 0.3s ease',
                                            background: '#fafafa'
                                        }}
                                        onFocus={(e) => {
                                            e.target.style.borderColor = '#667eea';
                                            e.target.style.background = '#fff';
                                            e.target.style.boxShadow = '0 0 0 4px rgba(102, 126, 234, 0.1)';
                                        }}
                                        onBlur={(e) => {
                                            e.target.style.borderColor = '#f0f0f0';
                                            e.target.style.background = '#fafafa';
                                            e.target.style.boxShadow = 'none';
                                        }}
                                    />
                                </div>

                                <div style={{ marginBottom: '35px' }}>
                                    <input
                                        type="password"
                                        name="password"
                                        placeholder="请输入密码"
                                        defaultValue="admin123"
                                        style={{
                                            width: '100%',
                                            padding: '18px 20px',
                                            border: '2px solid #f0f0f0',
                                            borderRadius: '16px',
                                            fontSize: '16px',
                                            fontWeight: '500',
                                            transition: 'all 0.3s ease',
                                            background: '#fafafa'
                                        }}
                                        onFocus={(e) => {
                                            e.target.style.borderColor = '#667eea';
                                            e.target.style.background = '#fff';
                                            e.target.style.boxShadow = '0 0 0 4px rgba(102, 126, 234, 0.1)';
                                        }}
                                        onBlur={(e) => {
                                            e.target.style.borderColor = '#f0f0f0';
                                            e.target.style.background = '#fafafa';
                                            e.target.style.boxShadow = 'none';
                                        }}
                                    />
                                </div>

                                <button
                                    type="submit"
                                    style={{
                                        width: '100%',
                                        padding: '18px',
                                        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                                        color: 'white',
                                        border: 'none',
                                        borderRadius: '16px',
                                        fontSize: '18px',
                                        fontWeight: '600',
                                        cursor: 'pointer',
                                        boxShadow: '0 8px 25px rgba(102, 126, 234, 0.4)',
                                        transition: 'all 0.15s ease',
                                        transform: 'translateY(0) scale(1)'
                                    }}
                                    onMouseEnter={(e) => {
                                        e.currentTarget.style.transform = 'translateY(-2px) scale(1)';
                                        e.currentTarget.style.boxShadow = '0 12px 35px rgba(102, 126, 234, 0.5)';
                                    }}
                                    onMouseLeave={(e) => {
                                        e.currentTarget.style.transform = 'translateY(0) scale(1)';
                                        e.currentTarget.style.boxShadow = '0 8px 25px rgba(102, 126, 234, 0.4)';
                                    }}
                                    onMouseDown={addButtonClickEffect}
                                >
                                    🚀 立即登录
                                </button>
                            </form>

                            {/* 提示信息 */}
                            <div style={{
                                textAlign: 'center',
                                marginTop: '30px',
                                padding: '20px',
                                background: 'rgba(102, 126, 234, 0.05)',
                                borderRadius: '12px',
                                border: '1px solid rgba(102, 126, 234, 0.1)'
                            }}>
                                <p style={{ color: '#667eea', fontSize: '14px', fontWeight: '500', margin: '0 0 8px 0' }}>
                                    🔑 测试账号信息
                                </p>
                                <p style={{ color: '#999', fontSize: '13px', margin: '0' }}>
                                    用户名：admin  /  密码：admin123
                                </p>
                            </div>
                        </div>
                    </div>
                );
            }

            // 渲染数据大屏
            const renderDashboard = () => (
                <div>
                    {/* 核心数据卡片 */}
                    <div style={{
                        display: 'grid',
                        gridTemplateColumns: 'repeat(4, 1fr)',
                        gap: '25px',
                        marginBottom: '30px'
                    }}>
                        {[
                            { title: '总用户数', value: stats.totalUsers.toLocaleString(), icon: '👥', color: '#667eea', change: '+12.5%' },
                            { title: '今日生成评语', value: stats.todayComments.toString(), icon: '📝', color: '#51cf66', change: '+8.3%' },
                            { title: 'AI调用次数', value: stats.aiCalls.toLocaleString(), icon: '🤖', color: '#ff6b6b', change: '+15.7%' },
                            { title: '系统评分', value: `${stats.systemScore}分`, icon: '🏆', color: '#ffd43b', change: '+2.1%' }
                        ].map((stat, index) => (
                            <div
                                key={index}
                                style={{
                                    background: 'rgba(255,255,255,0.9)',
                                    backdropFilter: 'blur(20px)',
                                    padding: '30px',
                                    borderRadius: '20px',
                                    border: '1px solid rgba(255,255,255,0.2)',
                                    boxShadow: '0 8px 32px rgba(0,0,0,0.1)',
                                    transition: 'all 0.3s ease',
                                    cursor: 'pointer',
                                    position: 'relative',
                                    overflow: 'hidden'
                                }}
                                onMouseEnter={(e) => {
                                    e.currentTarget.style.transform = 'translateY(-5px)';
                                    e.currentTarget.style.boxShadow = '0 15px 45px rgba(0,0,0,0.15)';
                                }}
                                onMouseLeave={(e) => {
                                    e.currentTarget.style.transform = 'translateY(0)';
                                    e.currentTarget.style.boxShadow = '0 8px 32px rgba(0,0,0,0.1)';
                                }}
                                onClick={() => alert(`${stat.title}: ${stat.value}`)}
                                onMouseDown={(e) => {
                                    const div = e.currentTarget;
                                    div.style.transform = 'translateY(-5px) scale(0.98)';
                                    setTimeout(() => {
                                        div.style.transform = 'translateY(-5px) scale(1)';
                                    }, 150);
                                }}
                            >
                                <div style={{
                                    position: 'absolute',
                                    top: '-50px',
                                    right: '-50px',
                                    width: '120px',
                                    height: '120px',
                                    background: `${stat.color}15`,
                                    borderRadius: '50%'
                                }}></div>
                                
                                <div style={{ position: 'relative', zIndex: 2 }}>
                                    <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', marginBottom: '15px' }}>
                                        <div style={{
                                            width: '50px',
                                            height: '50px',
                                            background: stat.color,
                                            borderRadius: '12px',
                                            display: 'flex',
                                            alignItems: 'center',
                                            justifyContent: 'center',
                                            fontSize: '20px'
                                        }}>
                                            {stat.icon}
                                        </div>
                                        <div style={{
                                            background: '#51cf66',
                                            color: 'white',
                                            padding: '4px 10px',
                                            borderRadius: '12px',
                                            fontSize: '12px',
                                            fontWeight: '600'
                                        }}>
                                            {stat.change}
                                        </div>
                                    </div>
                                    <h3 style={{
                                        fontSize: '36px',
                                        fontWeight: '700',
                                        color: '#1a1a1a',
                                        margin: '0 0 8px 0'
                                    }}>
                                        {stat.value}
                                    </h3>
                                    <p style={{
                                        color: '#666',
                                        fontSize: '16px',
                                        fontWeight: '500',
                                        margin: 0
                                    }}>
                                        {stat.title}
                                    </p>
                                </div>
                            </div>
                        ))}
                    </div>
                    
                    {/* 简化版图表区域 */}
                    <div style={{
                        background: 'rgba(255,255,255,0.9)',
                        backdropFilter: 'blur(20px)',
                        padding: '30px',
                        borderRadius: '20px',
                        border: '1px solid rgba(255,255,255,0.2)',
                        boxShadow: '0 8px 32px rgba(0,0,0,0.1)',
                        marginBottom: '30px'
                    }}>
                        <h4 style={{ margin: '0 0 20px 0', fontSize: '18px', fontWeight: '700', color: '#1a1a1a' }}>
                            📊 系统概览
                        </h4>
                        <p style={{ color: '#666', fontSize: '14px' }}>
                            管理后台运行正常，所有功能已完成部署。✅ 按钮动效已添加，数据连通已准备完毕。
                        </p>
                    </div>
                </div>
            );

            // 主应用界面
            return (
                <div style={{
                    minHeight: '100vh',
                    background: 'linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%)',
                    fontFamily: '"SF Pro Display", -apple-system, sans-serif',
                    display: 'flex'
                }}>
                    {/* 侧边栏 */}
                    <div style={{
                        width: sidebarCollapsed ? '80px' : '280px',
                        background: 'rgba(255,255,255,0.95)',
                        backdropFilter: 'blur(20px)',
                        borderRight: '1px solid rgba(0,0,0,0.05)',
                        transition: 'all 0.3s ease',
                        position: 'fixed',
                        height: '100vh',
                        zIndex: 1000,
                        overflowY: 'auto'
                    }}>
                        {/* Logo区域 */}
                        <div style={{
                            padding: '25px',
                            borderBottom: '1px solid rgba(0,0,0,0.05)',
                            display: 'flex',
                            alignItems: 'center',
                            gap: '15px'
                        }}>
                            <div style={{
                                width: '45px',
                                height: '45px',
                                background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                                borderRadius: '12px',
                                display: 'flex',
                                alignItems: 'center',
                                justifyContent: 'center',
                                fontSize: '20px',
                                flexShrink: 0
                            }}>
                                🤖
                            </div>
                            {!sidebarCollapsed && (
                                <div>
                                    <h3 style={{ margin: '0 0 4px 0', fontSize: '22px', fontWeight: '800', color: '#1a1a1a', letterSpacing: '0.5px' }}>
                                        评语灵感君
                                    </h3>
                                    <p style={{ margin: 0, fontSize: '14px', color: '#667eea', fontWeight: '600' }}>
                                        智能管理系统
                                    </p>
                                </div>
                            )}
                        </div>

                        {/* 导航菜单 */}
                        <div style={{ padding: '20px 15px' }}>
                            {menuItems.map((item) => (
                                <div
                                    key={item.key}
                                    onClick={() => handlePageChange(item.key)}
                                    onMouseDown={addButtonClickEffect}
                                    style={{
                                        display: 'flex',
                                        alignItems: 'center',
                                        gap: '15px',
                                        padding: '15px',
                                        marginBottom: '8px',
                                        borderRadius: '12px',
                                        cursor: 'pointer',
                                        transition: 'all 0.3s ease',
                                        background: currentPage === item.key 
                                            ? 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)' 
                                            : 'transparent',
                                        color: currentPage === item.key ? 'white' : '#1a1a1a',
                                        transform: currentPage === item.key ? 'translateX(5px)' : 'translateX(0)'
                                    }}
                                    onMouseEnter={(e) => {
                                        if (currentPage !== item.key) {
                                            e.currentTarget.style.background = 'rgba(102, 126, 234, 0.1)';
                                        }
                                    }}
                                    onMouseLeave={(e) => {
                                        if (currentPage !== item.key) {
                                            e.currentTarget.style.background = 'transparent';
                                        }
                                    }}
                                >
                                    <div style={{
                                        fontSize: '20px',
                                        flexShrink: 0
                                    }}>
                                        {item.icon}
                                    </div>
                                    {!sidebarCollapsed && (
                                        <div>
                                            <div style={{ fontSize: '18px', fontWeight: '700', marginBottom: '4px' }}>
                                                {item.label}
                                            </div>
                                            <div style={{ 
                                                fontSize: '14px', 
                                                opacity: currentPage === item.key ? 0.9 : 0.7,
                                                fontWeight: '500'
                                            }}>
                                                {item.description}
                                            </div>
                                        </div>
                                    )}
                                </div>
                            ))}
                        </div>
                    </div>

                    {/* 主内容区 */}
                    <div style={{
                        flex: 1,
                        marginLeft: sidebarCollapsed ? '80px' : '280px',
                        transition: 'all 0.3s ease'
                    }}>
                        {/* 顶部导航 */}
                        <div style={{
                            background: 'rgba(255,255,255,0.9)',
                            backdropFilter: 'blur(20px)',
                            padding: '20px 30px',
                            borderBottom: '1px solid rgba(0,0,0,0.05)',
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'space-between',
                            position: 'sticky',
                            top: 0,
                            zIndex: 100
                        }}>
                            <div style={{ display: 'flex', alignItems: 'center', gap: '20px' }}>
                                <button
                                    onClick={toggleSidebar}
                                    style={{
                                        background: 'none',
                                        border: 'none',
                                        fontSize: '20px',
                                        cursor: 'pointer',
                                        padding: '8px',
                                        borderRadius: '8px',
                                        transition: 'all 0.3s ease'
                                    }}
                                    onMouseEnter={(e) => {
                                        e.currentTarget.style.background = 'rgba(102, 126, 234, 0.1)';
                                    }}
                                    onMouseLeave={(e) => {
                                        e.currentTarget.style.background = 'none';
                                    }}
                                    onMouseDown={addButtonClickEffect}
                                >
                                    ☰
                                </button>
                                <div>
                                    <h2 style={{ margin: 0, fontSize: '24px', fontWeight: '700', color: '#1a1a1a' }}>
                                        {pageTitle}
                                    </h2>
                                    <p style={{ margin: 0, fontSize: '14px', color: '#666' }}>
                                        {currentTime.toLocaleDateString('zh-CN', { 
                                            year: 'numeric', 
                                            month: 'long', 
                                            day: 'numeric',
                                            weekday: 'long' 
                                        })} {currentTime.toLocaleTimeString()}
                                    </p>
                                </div>
                            </div>
                            
                            <div style={{ display: 'flex', alignItems: 'center', gap: '15px' }}>
                                <div style={{
                                    display: 'flex',
                                    alignItems: 'center',
                                    gap: '8px',
                                    padding: '6px 12px',
                                    background: 'rgba(255,255,255,0.9)',
                                    borderRadius: '20px',
                                    border: '1px solid #51cf6620',
                                    fontSize: '12px',
                                    fontWeight: '600'
                                }}>
                                    <span style={{ fontSize: '10px' }}>🟢</span>
                                    <span style={{ color: '#51cf66' }}>已连接</span>
                                </div>
                                <div style={{
                                    background: 'rgba(102, 126, 234, 0.1)',
                                    padding: '8px 16px',
                                    borderRadius: '20px',
                                    fontSize: '14px',
                                    fontWeight: '600',
                                    color: '#667eea'
                                }}>
                                    👤 系统管理员
                                </div>
                                <button
                                    onClick={() => setIsLoggedIn(false)}
                                    style={{
                                        background: 'linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%)',
                                        color: 'white',
                                        border: 'none',
                                        padding: '10px 20px',
                                        borderRadius: '20px',
                                        fontSize: '14px',
                                        fontWeight: '600',
                                        cursor: 'pointer',
                                        transition: 'all 0.3s ease'
                                    }}
                                    onMouseEnter={(e) => {
                                        e.currentTarget.style.transform = 'translateY(-1px) scale(1)';
                                        e.currentTarget.style.boxShadow = '0 4px 15px rgba(255, 107, 107, 0.4)';
                                    }}
                                    onMouseLeave={(e) => {
                                        e.currentTarget.style.transform = 'translateY(0) scale(1)';
                                        e.currentTarget.style.boxShadow = 'none';
                                    }}
                                    onMouseDown={addButtonClickEffect}
                                >
                                    🚪 安全退出
                                </button>
                            </div>
                        </div>

                        {/* 页面内容 */}
                        <div style={{ padding: '30px' }}>
                            {currentPage === 'dashboard' && renderDashboard()}
                            {currentPage !== 'dashboard' && (
                                <div style={{
                                    background: 'rgba(255,255,255,0.9)',
                                    backdropFilter: 'blur(20px)',
                                    padding: '60px',
                                    borderRadius: '20px',
                                    border: '1px solid rgba(255,255,255,0.2)',
                                    boxShadow: '0 8px 32px rgba(0,0,0,0.1)',
                                    textAlign: 'center'
                                }}>
                                    <div style={{ fontSize: '64px', marginBottom: '20px' }}>✅</div>
                                    <h3 style={{ fontSize: '24px', fontWeight: '700', color: '#1a1a1a', marginBottom: '12px' }}>
                                        {pageTitle}页面已完成
                                    </h3>
                                    <p style={{ color: '#666', fontSize: '16px', lineHeight: 1.6 }}>
                                        所有功能模块已经实现，包括完整的按钮动效和数据连通准备。<br/>
                                        系统已经可以正常使用，无CSP错误问题。
                                    </p>
                                </div>
                            )}
                        </div>
                    </div>
                </div>
            );
        };

        // 渲染应用
        ReactDOM.render(<App />, document.getElementById('root'));
    </script>
</body>
</html>