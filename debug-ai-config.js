/**
 * AI配置诊断脚本
 * 检查system_config集合中的AI配置数据
 */
const cloud = require('wx-server-sdk')

// 初始化云开发
cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()

async function debugAIConfig() {
  console.log('🔍 开始诊断AI配置...')
  
  try {
    // 1. 查看system_config集合的所有数据
    console.log('\n1. 查看system_config集合所有数据:')
    const allConfigs = await db.collection('system_config').get()
    console.log('总配置数量:', allConfigs.data.length)
    console.log('所有配置:', JSON.stringify(allConfigs.data, null, 2))
    
    // 2. 查看type为ai_config的数据
    console.log('\n2. 查看type=ai_config的数据:')
    const aiConfigs = await db.collection('system_config')
      .where({
        type: 'ai_config'
      })
      .get()
    console.log('AI配置数量:', aiConfigs.data.length)
    console.log('AI配置:', JSON.stringify(aiConfigs.data, null, 2))
    
    // 3. 查看status为active的数据
    console.log('\n3. 查看status=active的数据:')
    const activeConfigs = await db.collection('system_config')
      .where({
        status: 'active'
      })
      .get()
    console.log('活跃配置数量:', activeConfigs.data.length)
    console.log('活跃配置:', JSON.stringify(activeConfigs.data, null, 2))
    
    // 4. 使用完全相同的查询条件
    console.log('\n4. 使用云函数相同的查询条件:')
    const configResult = await db.collection('system_config')
      .where({
        type: 'ai_config',
        status: 'active'
      })
      .orderBy('updateTime', 'desc')
      .limit(1)
      .get()
    
    console.log('查询结果数量:', configResult.data.length)
    console.log('查询结果:', JSON.stringify(configResult.data, null, 2))
    
    if (configResult.data && configResult.data.length > 0) {
      const dbConfig = configResult.data[0]
      console.log('\n5. 分析配置字段:')
      console.log('- model:', dbConfig.model)
      console.log('- apiUrl:', dbConfig.apiUrl) 
      console.log('- apiKey存在:', !!dbConfig.apiKey)
      console.log('- apiKey长度:', dbConfig.apiKey ? dbConfig.apiKey.length : 0)
      console.log('- apiKey前10位:', dbConfig.apiKey ? dbConfig.apiKey.substring(0, 10) + '...' : '无')
      console.log('- updateTime:', dbConfig.updateTime)
      console.log('- 所有字段:', Object.keys(dbConfig))
    } else {
      console.log('\n❌ 未找到匹配的AI配置!')
    }
    
  } catch (error) {
    console.error('❌ 诊断过程中出错:', error)
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  debugAIConfig()
    .then(() => {
      console.log('\n✅ 诊断完成')
      process.exit(0)
    })
    .catch((error) => {
      console.error('❌ 诊断失败:', error)
      process.exit(1)
    })
}

module.exports = { debugAIConfig }