/**
 * 现代化监控系统
 * 集成错误监控、性能分析、用户行为追踪
 * 2025年企业级标准
 */

import { z } from 'zod'

// 监控配置Schema
const MonitoringConfigSchema = z.object({
  enableErrorTracking: z.boolean().default(true),
  enablePerformanceTracking: z.boolean().default(true),
  enableUserTracking: z.boolean().default(true),
  sampleRate: z.number().min(0).max(1).default(1),
  environment: z.enum(['development', 'staging', 'production']).default('development')
})

type MonitoringConfig = z.infer<typeof MonitoringConfigSchema>

// 错误类型定义
interface ErrorInfo {
  message: string
  stack?: string
  level: 'error' | 'warning' | 'info'
  category: 'api' | 'ui' | 'logic' | 'network' | 'unknown'
  context?: Record<string, any>
  userId?: string
  timestamp: number
  page?: string
  action?: string
}

// 性能指标类型
interface PerformanceMetrics {
  type: 'page_load' | 'api_call' | 'user_action' | 'render'
  name: string
  duration: number
  timestamp: number
  success: boolean
  metadata?: Record<string, any>
}

// 用户行为事件类型
interface UserEvent {
  event: string
  properties?: Record<string, any>
  userId?: string
  timestamp: number
  page?: string
  sessionId?: string
}

class ModernMonitoring {
  private config: MonitoringConfig
  private sessionId: string
  private userId?: string
  private currentPage?: string

  constructor(config: Partial<MonitoringConfig> = {}) {
    this.config = MonitoringConfigSchema.parse(config)
    this.sessionId = this.generateSessionId()
    this.init()
  }

  private init(): void {
    // 初始化错误监控
    if (this.config.enableErrorTracking) {
      this.setupErrorTracking()
    }

    // 初始化性能监控
    if (this.config.enablePerformanceTracking) {
      this.setupPerformanceTracking()
    }

    // 初始化用户行为追踪
    if (this.config.enableUserTracking) {
      this.setupUserTracking()
    }

    console.log('🔍 Modern Monitoring initialized', {
      sessionId: this.sessionId,
      config: this.config
    })
  }

  private generateSessionId(): string {
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  private shouldSample(): boolean {
    return Math.random() < this.config.sampleRate
  }

  // 错误监控设置
  private setupErrorTracking(): void {
    // 监听全局错误
    const originalError = console.error
    console.error = (...args: any[]) => {
      this.captureError({
        message: args.join(' '),
        level: 'error',
        category: 'unknown',
        timestamp: Date.now(),
        page: this.currentPage
      })
      originalError.apply(console, args)
    }

    // 监听Promise rejection
    if (typeof process !== 'undefined' && process.on) {
      process.on('unhandledRejection', (reason: any) => {
        this.captureError({
          message: `Unhandled Promise Rejection: ${reason}`,
          level: 'error',
          category: 'logic',
          timestamp: Date.now(),
          page: this.currentPage
        })
      })
    }
  }

  // 性能监控设置
  private setupPerformanceTracking(): void {
    // 页面加载性能监控
    this.trackPagePerformance()
  }

  // 用户行为追踪设置
  private setupUserTracking(): void {
    // 页面访问追踪
    this.trackPageView()
  }

  // 设置用户ID
  setUserId(userId: string): void {
    this.userId = userId
    this.trackEvent('user_identified', { userId })
  }

  // 设置当前页面
  setCurrentPage(page: string): void {
    this.currentPage = page
    this.trackPageView(page)
  }

  // 错误捕获
  captureError(error: Partial<ErrorInfo>): void {
    if (!this.config.enableErrorTracking || !this.shouldSample()) {
      return
    }

    const errorInfo: ErrorInfo = {
      message: error.message || 'Unknown error',
      stack: error.stack,
      level: error.level || 'error',
      category: error.category || 'unknown',
      context: error.context,
      userId: this.userId,
      timestamp: Date.now(),
      page: this.currentPage,
      action: error.action
    }

    // 发送到监控服务
    this.sendToMonitoring('error', errorInfo)

    // 本地存储（离线支持）
    this.storeLocally('errors', errorInfo)
  }

  // 性能指标追踪
  trackPerformance(metrics: Partial<PerformanceMetrics>): void {
    if (!this.config.enablePerformanceTracking || !this.shouldSample()) {
      return
    }

    const performanceData: PerformanceMetrics = {
      type: metrics.type || 'user_action',
      name: metrics.name || 'unknown',
      duration: metrics.duration || 0,
      timestamp: Date.now(),
      success: metrics.success ?? true,
      metadata: {
        ...metrics.metadata,
        userId: this.userId,
        sessionId: this.sessionId,
        page: this.currentPage
      }
    }

    this.sendToMonitoring('performance', performanceData)
    this.storeLocally('performance', performanceData)
  }

  // 用户事件追踪
  trackEvent(event: string, properties?: Record<string, any>): void {
    if (!this.config.enableUserTracking || !this.shouldSample()) {
      return
    }

    const userEvent: UserEvent = {
      event,
      properties,
      userId: this.userId,
      timestamp: Date.now(),
      page: this.currentPage,
      sessionId: this.sessionId
    }

    this.sendToMonitoring('event', userEvent)
    this.storeLocally('events', userEvent)
  }

  // 页面访问追踪
  private trackPageView(page?: string): void {
    const pageName = page || this.currentPage || 'unknown'
    this.trackEvent('page_view', {
      page: pageName,
      timestamp: Date.now()
    })
  }

  // 页面性能追踪
  private trackPagePerformance(): void {
    // 模拟页面加载时间追踪
    const startTime = Date.now()
    
    setTimeout(() => {
      const loadTime = Date.now() - startTime
      this.trackPerformance({
        type: 'page_load',
        name: this.currentPage || 'unknown',
        duration: loadTime,
        success: true
      })
    }, 100)
  }

  // API调用性能追踪
  trackApiCall(apiName: string, duration: number, success: boolean, metadata?: Record<string, any>): void {
    this.trackPerformance({
      type: 'api_call',
      name: apiName,
      duration,
      success,
      metadata
    })
  }

  // 发送到监控服务
  private async sendToMonitoring(type: string, data: any): Promise<void> {
    try {
      // 在生产环境中，这里应该发送到真实的监控服务
      if (this.config.environment === 'production') {
        // 发送到腾讯云APM或其他监控服务
        await this.sendToCloudMonitoring(type, data)
      } else {
        // 开发环境只打印日志
        console.log(`📊 [${type.toUpperCase()}]`, data)
      }
    } catch (error) {
      console.warn('Failed to send monitoring data:', error)
    }
  }

  // 发送到云监控服务
  private async sendToCloudMonitoring(type: string, data: any): Promise<void> {
    try {
      await wx.cloud.callFunction({
        name: 'reportMonitoring',
        data: {
          type,
          data,
          sessionId: this.sessionId,
          timestamp: Date.now()
        }
      })
    } catch (error) {
      console.warn('Failed to send to cloud monitoring:', error)
    }
  }

  // 本地存储（离线支持）
  private storeLocally(category: string, data: any): void {
    try {
      const key = `monitoring_${category}`
      const stored = wx.getStorageSync(key) || []
      stored.push(data)
      
      // 只保留最近100条记录
      if (stored.length > 100) {
        stored.splice(0, stored.length - 100)
      }
      
      wx.setStorageSync(key, stored)
    } catch (error) {
      console.warn('Failed to store monitoring data locally:', error)
    }
  }

  // 获取监控统计
  getMonitoringStats(): Record<string, any> {
    try {
      const errors = wx.getStorageSync('monitoring_errors') || []
      const performance = wx.getStorageSync('monitoring_performance') || []
      const events = wx.getStorageSync('monitoring_events') || []

      return {
        sessionId: this.sessionId,
        userId: this.userId,
        currentPage: this.currentPage,
        stats: {
          errors: errors.length,
          performance: performance.length,
          events: events.length
        },
        recentErrors: errors.slice(-5),
        recentPerformance: performance.slice(-5),
        recentEvents: events.slice(-5)
      }
    } catch (error) {
      console.warn('Failed to get monitoring stats:', error)
      return {}
    }
  }

  // 清理本地数据
  clearLocalData(): void {
    try {
      wx.removeStorageSync('monitoring_errors')
      wx.removeStorageSync('monitoring_performance')
      wx.removeStorageSync('monitoring_events')
      console.log('📊 Monitoring local data cleared')
    } catch (error) {
      console.warn('Failed to clear monitoring data:', error)
    }
  }
}

// 创建全局监控实例
const monitoring = new ModernMonitoring({
  environment: process.env.NODE_ENV as any || 'development',
  sampleRate: process.env.NODE_ENV === 'production' ? 0.1 : 1.0
})

export { ModernMonitoring, monitoring }
export type { ErrorInfo, PerformanceMetrics, UserEvent, MonitoringConfig }
