<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据连通测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; }
        .loading { background-color: #d1ecf1; border-color: #bee5eb; }
        pre { background-color: #f8f9fa; padding: 10px; overflow-x: auto; }
        button { margin: 5px; padding: 10px 15px; cursor: pointer; }
    </style>
</head>
<body>
    <h1>评语灵感君 - 数据连通性测试</h1>
    
    <div class="section">
        <h2>云环境信息</h2>
        <p>环境ID: cloud1-4g85f8xlb8166ff1</p>
        <p>测试时间: <span id="currentTime"></span></p>
    </div>

    <div class="section">
        <h2>测试选项</h2>
        <button onclick="testCloudFunction()">测试云函数连接</button>
        <button onclick="testAdminAPI()">测试AdminAPI</button>
        <button onclick="testDataQuery()">测试数据查询</button>
        <button onclick="clearResults()">清空结果</button>
    </div>

    <div class="section">
        <h2>测试结果</h2>
        <div id="testResults">等待测试...</div>
    </div>

    <script src="https://unpkg.com/@cloudbase/js-sdk/dist/index.umd.js"></script>
    <script>
        // 显示当前时间
        document.getElementById('currentTime').textContent = new Date().toLocaleString();

        // 测试结果容器
        const resultsContainer = document.getElementById('testResults');

        function addResult(title, status, data) {
            const resultDiv = document.createElement('div');
            resultDiv.className = `section ${status}`;
            resultDiv.innerHTML = `
                <h3>${title}</h3>
                <pre>${JSON.stringify(data, null, 2)}</pre>
                <p>时间: ${new Date().toLocaleString()}</p>
            `;
            resultsContainer.appendChild(resultDiv);
        }

        function clearResults() {
            resultsContainer.innerHTML = '等待测试...';
        }

        // 初始化CloudBase
        const cloudbase = window.cloudbase;
        const app = cloudbase.init({
            env: 'cloud1-4g85f8xlb8166ff1'
        });

        async function testCloudFunction() {
            addResult('云函数连接测试', 'loading', { status: '正在测试...' });
            
            try {
                // 测试基本云函数调用
                const result = await app.callFunction({
                    name: 'adminAPI',
                    data: {
                        action: 'healthCheck',
                        source: 'admin-dashboard-test'
                    }
                });

                addResult('✅ 云函数连接测试', 'success', result);
            } catch (error) {
                addResult('❌ 云函数连接测试', 'error', {
                    message: error.message,
                    stack: error.stack
                });
            }
        }

        async function testAdminAPI() {
            addResult('AdminAPI测试', 'loading', { status: '正在测试数据查询...' });
            
            try {
                // 测试数据库连接
                const connectionTest = await app.callFunction({
                    name: 'adminAPI',
                    data: {
                        action: 'data.testConnection',
                        source: 'admin-dashboard-test'
                    }
                });

                addResult('✅ AdminAPI数据库连接', 'success', connectionTest);

                // 测试获取仪表板数据（无权限要求）
                const statsTest = await app.callFunction({
                    name: 'adminAPI',
                    data: {
                        action: 'data.getDashboardStats',
                        source: 'admin-dashboard-test'
                    }
                });

                addResult('✅ AdminAPI仪表板数据', 'success', statsTest);

            } catch (error) {
                addResult('❌ AdminAPI测试', 'error', {
                    message: error.message,
                    details: error.errMsg || error.toString()
                });
            }
        }

        async function testDataQuery() {
            addResult('直接数据查询测试', 'loading', { status: '正在查询数据库...' });
            
            try {
                const db = app.database();
                
                // 查询学生数据
                const studentsResult = await db.collection('students').get();
                addResult('📊 学生数据查询', 'success', {
                    count: studentsResult.data.length,
                    data: studentsResult.data.slice(0, 3) // 只显示前3条
                });

                // 查询评语数据
                const commentsResult = await db.collection('comments').get();
                addResult('💬 评语数据查询', 'success', {
                    count: commentsResult.data.length,
                    data: commentsResult.data.slice(0, 3) // 只显示前3条
                });

                // 查询用户数据
                const usersResult = await db.collection('users').get();
                addResult('👥 用户数据查询', 'success', {
                    count: usersResult.data.length,
                    data: usersResult.data.slice(0, 3) // 只显示前3条
                });

                // 查询班级数据
                const classesResult = await db.collection('classes').get();
                addResult('🏫 班级数据查询', 'success', {
                    count: classesResult.data.length,
                    data: classesResult.data.slice(0, 3) // 只显示前3条
                });

            } catch (error) {
                addResult('❌ 直接数据查询', 'error', {
                    message: error.message,
                    code: error.code,
                    details: error.errMsg || error.toString()
                });
            }
        }

        // 页面加载完成后自动运行测试
        window.addEventListener('load', () => {
            console.log('页面加载完成，准备进行数据连通测试');
            setTimeout(() => {
                testCloudFunction();
                setTimeout(() => testDataQuery(), 2000);
            }, 1000);
        });
    </script>
</body>
</html>