# CORS 跨域问题修复指南

## 🚨 问题描述

管理后台在访问云函数API时出现CORS跨域错误：
```
已拦截跨源请求：同源策略禁止读取位于 https://cloud1-4g85f8xlb8166ff1-1365982463.ap-shanghai.app.tcloudbase.com/adminAPI 的远程资源。（原因：CORS 预检响应的 'Access-Control-Allow-Headers'，不允许使用头 'x-request-id'）。
```

## 🔧 修复方案

### 方案1：云函数CORS配置修复（已完成）

已修复 `cloudfunctions/adminAPI/index.js` 中的CORS配置：

```javascript
// OPTIONS预检请求
'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-WX-SOURCE, X-WX-OPENID, X-Requested-With, x-request-id',

// 正常响应
'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-WX-SOURCE, X-WX-OPENID, X-Requested-With, x-request-id',
```

**部署步骤：**
1. 在微信开发者工具中打开云函数目录
2. 右键点击 `adminAPI` 云函数
3. 选择"上传并部署：云端安装依赖"
4. 等待部署完成

### 方案2：启用模拟模式（临时方案，已启用）

修改环境配置启用模拟模式，绕过CORS问题：

**开发环境** (`.env.development`):
```env
VITE_ENABLE_MOCK=true  # 启用模拟模式
```

**生产环境** (`.env.production`):
```env
VITE_ENABLE_MOCK=false  # 使用真实API
```

### 方案3：本地代理服务器

如果需要在开发环境使用真实API，可以配置Vite代理：

**vite.config.ts**:
```typescript
export default defineConfig({
  server: {
    proxy: {
      '/api': {
        target: 'https://cloud1-4g85f8xlb8166ff1-1365982463.ap-shanghai.app.tcloudbase.com',
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/api/, '/adminAPI')
      }
    }
  }
})
```

然后修改API基础URL：
```typescript
VITE_API_BASE_URL=/api
```

### 方案4：浏览器CORS扩展（仅开发用）

安装浏览器CORS扩展（如"CORS Unblock"），但**仅限开发环境使用**。

## 🎯 推荐解决流程

### 立即可用（当前状态）
✅ **模拟模式已启用** - 管理后台现在可以正常登录和使用
- 用户名：`admin`
- 密码：`admin123`

### 长期解决方案
1. **部署修复后的云函数**（推荐）
   - 上传修复后的 `adminAPI` 云函数
   - 测试真实API连接
   - 将 `VITE_ENABLE_MOCK` 改为 `false`

2. **配置代理服务器**（开发环境）
   - 适合需要调试真实API的场景

## 🧪 测试验证

### 测试模拟模式
```bash
# 确保模拟模式启用
npm run dev
# 访问 http://localhost:3000
# 使用 admin/admin123 登录
```

### 测试真实API
```bash
# 部署云函数后
# 修改 .env.development 中 VITE_ENABLE_MOCK=false
npm run dev
# 测试登录功能
```

### 测试CORS修复
在浏览器开发者工具中检查：
1. Network标签页中的OPTIONS预检请求
2. Response Headers中的CORS头信息
3. 确认 `x-request-id` 在允许的头列表中

## 📝 注意事项

1. **模拟模式限制**：
   - 数据不会真实保存到数据库
   - 无法测试真实的数据交互
   - 适合UI和功能测试

2. **云函数部署**：
   - 确保在微信开发者工具中部署
   - 检查云函数日志确认部署成功
   - 测试HTTP触发器是否正常工作

3. **环境切换**：
   - 开发环境使用模拟模式
   - 生产环境使用真实API
   - 通过环境变量控制切换

## 🔍 故障排除

如果仍有CORS问题：

1. **检查云函数部署状态**
2. **清除浏览器缓存**
3. **检查网络连接**
4. **查看云函数日志**
5. **验证环境变量配置**

## 📞 技术支持

如果问题持续存在，请提供：
- 浏览器控制台错误信息
- 网络请求详情
- 云函数日志
- 当前环境配置
