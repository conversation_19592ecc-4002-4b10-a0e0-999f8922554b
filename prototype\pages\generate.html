<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI魔法生成</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
            background: linear-gradient(135deg, #F0F2F5 0%, #E4E7ED 100%);
            min-height: 100vh;
            color: #333;
        }

        .page-container {
            padding: 20px 16px 100px;
            max-width: 375px;
            margin: 0 auto;
        }

        /* 页面头部 */
        .page-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 24px;
            padding-top: 10px;
        }

        .header-title {
            font-size: 20px;
            font-weight: 600;
            color: #333;
        }

        .header-action {
            color: #5470C6;
            font-size: 14px;
            cursor: pointer;
        }

        /* 魔法进度指示器 */
        .magic-progress {
            background: linear-gradient(135deg, #5470C6 0%, #73C0DE 100%);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 24px;
            color: white;
            text-align: center;
        }

        .progress-icon {
            font-size: 32px;
            margin-bottom: 12px;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.1); }
        }

        .progress-title {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 8px;
        }

        .progress-desc {
            font-size: 14px;
            opacity: 0.9;
            margin-bottom: 16px;
        }

        .progress-bar {
            height: 4px;
            background: rgba(255, 255, 255, 0.3);
            border-radius: 2px;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            background: white;
            border-radius: 2px;
            width: 0%;
            transition: width 0.3s ease;
        }

        /* 学生选择区域 */
        .student-section {
            background: rgba(255, 255, 255, 0.9);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 20px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.3);
        }

        .section-title {
            font-size: 16px;
            font-weight: 600;
            color: #333;
            margin-bottom: 16px;
            display: flex;
            align-items: center;
        }

        .section-icon {
            margin-right: 8px;
            color: #5470C6;
        }

        .student-search {
            position: relative;
            margin-bottom: 16px;
        }

        .search-input {
            width: 100%;
            padding: 12px 16px 12px 40px;
            border: 1px solid #E4E7ED;
            border-radius: 12px;
            font-size: 14px;
            background: white;
            outline: none;
            transition: border-color 0.3s ease;
        }

        .search-input:focus {
            border-color: #5470C6;
        }

        .search-icon {
            position: absolute;
            left: 12px;
            top: 50%;
            transform: translateY(-50%);
            color: #999;
        }

        .student-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 8px;
        }

        .student-item {
            background: #F8F9FA;
            border: 2px solid transparent;
            border-radius: 12px;
            padding: 12px 8px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .student-item.selected {
            background: #E6F4FF;
            border-color: #5470C6;
            color: #5470C6;
        }

        .student-avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background: linear-gradient(135deg, #5470C6 0%, #73C0DE 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 14px;
            font-weight: 600;
            margin: 0 auto 8px;
        }

        .student-name {
            font-size: 12px;
            font-weight: 500;
        }

        /* 评语配置 */
        .config-section {
            background: rgba(255, 255, 255, 0.9);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 20px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.3);
        }

        .config-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
        }

        .config-item {
            background: #F8F9FA;
            border: 2px solid transparent;
            border-radius: 12px;
            padding: 16px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .config-item.selected {
            background: #E6F4FF;
            border-color: #5470C6;
            color: #5470C6;
        }

        .config-icon {
            font-size: 20px;
            margin-bottom: 8px;
        }

        .config-title {
            font-size: 14px;
            font-weight: 600;
            margin-bottom: 4px;
        }

        .config-desc {
            font-size: 12px;
            opacity: 0.7;
        }

        /* 生成按钮 */
        .generate-button {
            width: 100%;
            background: linear-gradient(135deg, #5470C6 0%, #73C0DE 100%);
            border: none;
            border-radius: 16px;
            padding: 16px;
            color: white;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-bottom: 20px;
        }

        .generate-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 32px rgba(84, 112, 198, 0.4);
        }

        .generate-button:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        /* 生成结果 */
        .result-section {
            background: rgba(255, 255, 255, 0.9);
            border-radius: 16px;
            padding: 20px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.3);
            display: none;
        }

        .result-section.show {
            display: block;
            animation: slideUp 0.5s ease;
        }

        @keyframes slideUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .result-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }

        .result-score {
            background: linear-gradient(135deg, #52C41A 0%, #73D13D 100%);
            color: white;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
        }

        .result-content {
            background: #F8F9FA;
            border-radius: 12px;
            padding: 16px;
            line-height: 1.6;
            font-size: 14px;
            color: #333;
            margin-bottom: 16px;
        }

        .result-actions {
            display: flex;
            gap: 12px;
        }

        .action-button {
            flex: 1;
            padding: 12px;
            border: 1px solid #E4E7ED;
            border-radius: 12px;
            background: white;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .action-button.primary {
            background: #5470C6;
            color: white;
            border-color: #5470C6;
        }

        /* 底部导航 */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-top: 1px solid rgba(0, 0, 0, 0.05);
            padding: 8px 0 20px;
        }

        .nav-grid {
            display: grid;
            grid-template-columns: repeat(5, 1fr);
            max-width: 375px;
            margin: 0 auto;
        }

        .nav-item {
            text-align: center;
            padding: 8px;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .nav-item.active {
            color: #5470C6;
        }

        .nav-icon {
            font-size: 20px;
            margin-bottom: 4px;
        }

        .nav-label {
            font-size: 10px;
            font-weight: 500;
        }
    </style>
</head>
<body>
    <div class="page-container">
        <!-- 页面头部 -->
        <div class="page-header">
            <div class="header-title">AI魔法生成</div>
            <div class="header-action">历史记录</div>
        </div>

        <!-- 魔法进度 -->
        <div class="magic-progress">
            <div class="progress-icon">
                <i class="fas fa-magic"></i>
            </div>
            <div class="progress-title">准备施展魔法</div>
            <div class="progress-desc">选择学生和评语风格，3分钟生成专业评语</div>
            <div class="progress-bar">
                <div class="progress-fill" id="progressFill"></div>
            </div>
        </div>

        <!-- 学生选择 -->
        <div class="student-section">
            <div class="section-title">
                <i class="fas fa-user-graduate section-icon"></i>
                选择学生
            </div>
            
            <div class="student-search">
                <input type="text" class="search-input" placeholder="搜索学生姓名...">
                <i class="fas fa-search search-icon"></i>
            </div>
            
            <div class="student-grid">
                <div class="student-item selected">
                    <div class="student-avatar">李</div>
                    <div class="student-name">李小明</div>
                </div>
                <div class="student-item">
                    <div class="student-avatar">王</div>
                    <div class="student-name">王小红</div>
                </div>
                <div class="student-item">
                    <div class="student-avatar">张</div>
                    <div class="student-name">张小强</div>
                </div>
                <div class="student-item">
                    <div class="student-avatar">刘</div>
                    <div class="student-name">刘小美</div>
                </div>
                <div class="student-item">
                    <div class="student-avatar">陈</div>
                    <div class="student-name">陈小华</div>
                </div>
                <div class="student-item">
                    <div class="student-avatar">赵</div>
                    <div class="student-name">赵小龙</div>
                </div>
            </div>
        </div>

        <!-- 评语配置 -->
        <div class="config-section">
            <div class="section-title">
                <i class="fas fa-palette section-icon"></i>
                评语风格
            </div>
            
            <div class="config-grid">
                <div class="config-item selected">
                    <div class="config-icon">🎯</div>
                    <div class="config-title">全面评价</div>
                    <div class="config-desc">综合表现</div>
                </div>
                <div class="config-item">
                    <div class="config-icon">⭐</div>
                    <div class="config-title">优点突出</div>
                    <div class="config-desc">正面激励</div>
                </div>
                <div class="config-item">
                    <div class="config-icon">📈</div>
                    <div class="config-title">成长建议</div>
                    <div class="config-desc">改进方向</div>
                </div>
                <div class="config-item">
                    <div class="config-icon">🏆</div>
                    <div class="config-title">期末总结</div>
                    <div class="config-desc">学期回顾</div>
                </div>
            </div>
        </div>

        <!-- 生成按钮 -->
        <button class="generate-button" onclick="generateComment()">
            <i class="fas fa-magic"></i> 开始生成魔法评语
        </button>

        <!-- 生成结果 -->
        <div class="result-section" id="resultSection">
            <div class="result-header">
                <div class="section-title">
                    <i class="fas fa-star section-icon"></i>
                    生成结果
                </div>
                <div class="result-score">质量评分: 9.2</div>
            </div>
            
            <div class="result-content">
                李小明同学在本学期表现优异，学习态度端正，积极参与课堂讨论。该生思维活跃，善于思考，作业完成质量较高，字迹工整。在团队合作中表现出良好的协调能力，乐于帮助同学。建议在课外阅读方面继续加强，拓宽知识面，相信会有更大的进步空间。
            </div>
            
            <div class="result-actions">
                <button class="action-button">重新生成</button>
                <button class="action-button">编辑修改</button>
                <button class="action-button primary">保存使用</button>
            </div>
        </div>
    </div>

    <!-- 底部导航 -->
    <div class="bottom-nav">
        <div class="nav-grid">
            <div class="nav-item">
                <div class="nav-icon"><i class="fas fa-home"></i></div>
                <div class="nav-label">首页</div>
            </div>
            <div class="nav-item active">
                <div class="nav-icon"><i class="fas fa-magic"></i></div>
                <div class="nav-label">生成</div>
            </div>
            <div class="nav-item">
                <div class="nav-icon"><i class="fas fa-file-alt"></i></div>
                <div class="nav-label">作品</div>
            </div>
            <div class="nav-item">
                <div class="nav-icon"><i class="fas fa-chart-line"></i></div>
                <div class="nav-label">报告</div>
            </div>
            <div class="nav-item">
                <div class="nav-icon"><i class="fas fa-cog"></i></div>
                <div class="nav-label">设置</div>
            </div>
        </div>
    </div>

    <script>
        function generateComment() {
            const button = document.querySelector('.generate-button');
            const progressFill = document.getElementById('progressFill');
            const resultSection = document.getElementById('resultSection');
            
            button.disabled = true;
            button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 正在生成中...';
            
            // 模拟进度条
            let progress = 0;
            const interval = setInterval(() => {
                progress += 10;
                progressFill.style.width = progress + '%';
                
                if (progress >= 100) {
                    clearInterval(interval);
                    setTimeout(() => {
                        button.disabled = false;
                        button.innerHTML = '<i class="fas fa-magic"></i> 开始生成魔法评语';
                        resultSection.classList.add('show');
                        progressFill.style.width = '0%';
                    }, 500);
                }
            }, 200);
        }

        // 学生选择交互
        document.querySelectorAll('.student-item').forEach(item => {
            item.addEventListener('click', () => {
                document.querySelectorAll('.student-item').forEach(i => i.classList.remove('selected'));
                item.classList.add('selected');
            });
        });

        // 配置选择交互
        document.querySelectorAll('.config-item').forEach(item => {
            item.addEventListener('click', () => {
                document.querySelectorAll('.config-item').forEach(i => i.classList.remove('selected'));
                item.classList.add('selected');
            });
        });
    </script>
</body>
</html>
