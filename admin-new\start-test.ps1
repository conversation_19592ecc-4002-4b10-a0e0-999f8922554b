# AI配置页面测试环境启动脚本
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "启动AI配置页面测试环境" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

# 检查Node.js是否安装
try {
    $nodeVersion = node --version
    Write-Host "✓ Node.js版本: $nodeVersion" -ForegroundColor Green
} catch {
    Write-Host "✗ 错误: 未找到Node.js，请先安装Node.js" -ForegroundColor Red
    Read-Host "按回车键退出"
    exit 1
}

# 启动API服务器
Write-Host "🚀 正在启动本地API服务器..." -ForegroundColor Yellow
Start-Process powershell -ArgumentList "-NoExit", "-Command", "cd '$PWD'; node local-api-server.cjs"

# 等待API服务器启动
Write-Host "⏳ 等待API服务器启动..." -ForegroundColor Yellow
Start-Sleep -Seconds 3

# 启动前端开发服务器
Write-Host "🌐 正在启动前端开发服务器..." -ForegroundColor Yellow
Start-Process powershell -ArgumentList "-NoExit", "-Command", "cd '$PWD'; npm run dev"

# 等待前端服务器启动
Write-Host "⏳ 等待前端服务器启动..." -ForegroundColor Yellow
Start-Sleep -Seconds 5

Write-Host ""
Write-Host "✅ 测试环境启动完成！" -ForegroundColor Green
Write-Host ""
Write-Host "📡 API服务器: http://localhost:3000/admin" -ForegroundColor Cyan
Write-Host "🌐 前端应用: http://localhost:5173" -ForegroundColor Cyan
Write-Host ""
Write-Host "🧪 测试步骤：" -ForegroundColor Yellow
Write-Host "1. 打开浏览器访问 http://localhost:5173"
Write-Host "2. 进入AI配置页面"
Write-Host "3. 测试以下功能："
Write-Host "   - 添加AI模型"
Write-Host "   - 编辑AI模型"
Write-Host "   - 删除AI模型"
Write-Host "   - 测试AI连接"
Write-Host "   - 保存系统配置"
Write-Host ""
Write-Host "💡 提示：" -ForegroundColor Magenta
Write-Host "- 所有数据都是模拟数据，重启后会重置"
Write-Host "- 可以在API服务器窗口查看请求日志"
Write-Host "- 关闭对应的PowerShell窗口可以停止服务器"
Write-Host ""

# 尝试自动打开浏览器
try {
    Start-Sleep -Seconds 2
    Start-Process "http://localhost:5173"
    Write-Host "🌐 已自动打开浏览器" -ForegroundColor Green
} catch {
    Write-Host "⚠️ 无法自动打开浏览器，请手动访问 http://localhost:5173" -ForegroundColor Yellow
}

Write-Host ""
Read-Host "按回车键关闭此窗口"
