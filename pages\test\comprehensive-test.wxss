/* 综合测试页面样式 */

.test-container {
  padding: 20rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
}

/* 页面标题 */
.page-header {
  text-align: center;
  margin-bottom: 40rpx;
  padding: 40rpx 20rpx;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 20rpx;
  backdrop-filter: blur(10rpx);
}

.page-title {
  display: block;
  font-size: 48rpx;
  font-weight: bold;
  color: #ffffff;
  margin-bottom: 10rpx;
}

.page-subtitle {
  display: block;
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.8);
}

/* 控制面板 */
.control-panel {
  margin-bottom: 40rpx;
}

.test-button {
  width: 100%;
  height: 100rpx;
  background: linear-gradient(45deg, #4CAF50, #45a049);
  border: none;
  border-radius: 50rpx;
  color: #ffffff;
  font-size: 32rpx;
  font-weight: bold;
  box-shadow: 0 8rpx 20rpx rgba(76, 175, 80, 0.3);
  transition: all 0.3s ease;
}

.test-button.testing {
  background: linear-gradient(45deg, #ff9800, #f57c00);
  animation: pulse 2s infinite;
}

.test-button:disabled {
  opacity: 0.7;
}

@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.05); }
  100% { transform: scale(1); }
}

/* 进度区域 */
.progress-section {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
}

.progress-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.progress-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.progress-percent {
  font-size: 28rpx;
  color: #4CAF50;
  font-weight: bold;
}

.progress-bar {
  height: 12rpx;
  background: #e0e0e0;
  border-radius: 6rpx;
  overflow: hidden;
  margin-bottom: 20rpx;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #4CAF50, #8BC34A);
  border-radius: 6rpx;
  transition: width 0.3s ease;
}

.current-test {
  text-align: center;
}

.current-test-text {
  font-size: 26rpx;
  color: #666;
}

/* 统计区域 */
.stats-section {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
}

.stats-header {
  margin-bottom: 30rpx;
}

.stats-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.stats-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20rpx;
}

.stat-item {
  text-align: center;
  padding: 20rpx;
  background: #f5f5f5;
  border-radius: 12rpx;
}

.stat-item.success {
  background: linear-gradient(135deg, #4CAF50, #8BC34A);
  color: white;
}

.stat-item.fail {
  background: linear-gradient(135deg, #f44336, #e57373);
  color: white;
}

.stat-number {
  display: block;
  font-size: 36rpx;
  font-weight: bold;
  margin-bottom: 8rpx;
}

.stat-label {
  display: block;
  font-size: 24rpx;
  opacity: 0.8;
}

/* 操作按钮 */
.action-buttons {
  display: flex;
  gap: 20rpx;
  margin-bottom: 30rpx;
}

.action-button {
  flex: 1;
  height: 80rpx;
  border: none;
  border-radius: 40rpx;
  font-size: 28rpx;
  font-weight: bold;
}

.action-button.primary {
  background: linear-gradient(45deg, #2196F3, #21CBF3);
  color: white;
}

.action-button.secondary {
  background: rgba(255, 255, 255, 0.9);
  color: #333;
}

/* 日志区域 */
.log-section {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20rpx;
  padding: 30rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
}

.log-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.log-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.clear-log-button {
  background: #ff5722;
  color: white;
  border: none;
  border-radius: 20rpx;
  padding: 10rpx 20rpx;
  font-size: 24rpx;
}

.log-content {
  height: 400rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
  padding: 20rpx;
}

.log-item {
  margin-bottom: 10rpx;
  padding: 8rpx 12rpx;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 8rpx;
  border-left: 4rpx solid #4CAF50;
}

.log-text {
  font-size: 24rpx;
  color: #333;
  font-family: 'Courier New', monospace;
}

.log-empty {
  text-align: center;
  padding: 60rpx 20rpx;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
}

/* 报告弹窗 */
.report-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.report-modal.show {
  opacity: 1;
  visibility: visible;
}

.report-mask {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
}

.report-content {
  position: absolute;
  top: 10%;
  left: 5%;
  width: 90%;
  height: 80%;
  background: white;
  border-radius: 20rpx;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.report-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #eee;
}

.report-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.close-button {
  width: 60rpx;
  height: 60rpx;
  background: #f5f5f5;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 40rpx;
  color: #666;
}

.report-body {
  flex: 1;
  padding: 30rpx;
}

.report-text {
  font-size: 24rpx;
  line-height: 1.6;
  color: #333;
  font-family: 'Courier New', monospace;
  white-space: pre-wrap;
}

.report-footer {
  padding: 30rpx;
  border-top: 1rpx solid #eee;
}

.report-button {
  width: 100%;
  height: 80rpx;
  background: linear-gradient(45deg, #4CAF50, #45a049);
  color: white;
  border: none;
  border-radius: 40rpx;
  font-size: 28rpx;
  font-weight: bold;
}
