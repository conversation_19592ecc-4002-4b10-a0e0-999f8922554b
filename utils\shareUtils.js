/**
 * 分享工具模块
 * 提供统一的Excel分享、图片生成、文件分享等功能
 */

/**
 * 生成Excel文件并分享到微信
 * @param {Array} data - 要导出的数据数组
 * @param {Object} options - 导出配置
 * @param {string} options.fileName - 文件名
 * @param {Array} options.headers - 表头数组
 * @param {string} options.title - 文件标题
 * @param {Function} options.formatRow - 数据行格式化函数
 */
async function shareExcelToWeChat(data, options = {}) {
  const {
    fileName = `数据导出_${new Date().toLocaleDateString('zh-CN').replace(/\//g, '-')}.xlsx`,
    headers = [],
    title = '数据导出',
    formatRow = (item) => Object.values(item)
  } = options;

  try {
    wx.showLoading({
      title: '生成Excel文件...',
      mask: true
    });

    // 生成Excel内容（使用Tab分隔符，Excel可以直接识别）
    let excelContent = '\uFEFF'; // 添加BOM以支持中文

    // 添加标题
    excelContent += `${title}\n`;
    excelContent += `导出时间：${new Date().toLocaleString('zh-CN')}\n`;
    excelContent += `数据条数：${data.length}条\n\n`;

    // 添加表头
    if (headers.length > 0) {
      excelContent += headers.join('\t') + '\n';
      excelContent += '━'.repeat(Math.max(60, headers.length * 10)) + '\n';
    }

    // 添加数据行
    data.forEach((item, index) => {
      const row = formatRow(item, index);
      excelContent += row.map(cell => 
        String(cell || '').replace(/[\r\n\t]/g, ' ')
      ).join('\t') + '\n';
    });

    // 添加统计信息
    excelContent += '\n' + '━'.repeat(60) + '\n';
    excelContent += `总计：${data.length}条数据\n`;
    excelContent += `生成时间：${new Date().toLocaleString('zh-CN')}\n`;
    excelContent += `由 评语灵感君 v3.0 生成`;

    // 尝试分享文件到微信
    if (typeof wx.shareFileMessage === 'function') {
      // 先保存文件
      const fs = wx.getFileSystemManager();
      const filePath = `${wx.env.USER_DATA_PATH}/${fileName}`;
      
      fs.writeFileSync(filePath, excelContent, 'utf8');
      
      wx.hideLoading();
      
      // 分享文件
      wx.shareFileMessage({
        filePath: filePath,
        fileName: fileName,
        success: () => {
          wx.showModal({
            title: '📊 Excel文件分享成功',
            content: `文件"${fileName}"已发送到微信聊天中！\n\n📱 接收方操作指引：\n1. 在微信中点击文件进行下载\n2. 使用Excel或WPS打开文件\n3. 可进一步编辑和分析数据\n\n✨ 文件特点：\n• 完整的数据内容\n• 专业的格式排版\n• 支持Excel全功能编辑`,
            showCancel: false,
            confirmText: '我知道了'
          });
        },
        fail: (error) => {
          console.error('文件分享失败:', error);
          // 降级到复制内容
          fallbackToCopyContent(excelContent, fileName);
        }
      });
    } else {
      wx.hideLoading();
      // 环境不支持文件分享，使用复制内容的方式
      fallbackToCopyContent(excelContent, fileName);
    }

  } catch (error) {
    wx.hideLoading();
    console.error('Excel分享失败:', error);
    wx.showToast({
      title: '分享失败，请重试',
      icon: 'none',
      duration: 3000
    });
  }
}

/**
 * 降级方案：复制内容到剪贴板
 */
function fallbackToCopyContent(content, fileName) {
  wx.setClipboardData({
    data: content,
    success: () => {
      wx.showModal({
        title: '📋 数据已复制到剪贴板',
        content: `Excel格式的数据已复制！\n\n📁 建议文件名：${fileName}\n\n💡 使用方法：\n1. 打开Excel或WPS表格\n2. 新建工作表\n3. 直接粘贴（Ctrl+V）\n4. 保存文件\n\n✨ 数据格式说明：\n• 使用制表符分隔\n• 包含完整表头\n• 支持中文显示`,
        showCancel: false,
        confirmText: '我知道了'
      });
    },
    fail: () => {
      wx.showToast({
        title: '复制失败，请重试',
        icon: 'none'
      });
    }
  });
}

/**
 * 生成美观的分享图片 - 完全按照HTML设计实现
 * @param {Object} data - 要分享的数据
 * @param {Object} options - 图片配置
 */
async function generateBeautifulShareImage(data, options = {}) {
  const {
    canvasId = 'shareCanvas',
    title = '我的使用报告',
    subtitle = '近30天 数据分析'
  } = options;

  try {
    wx.showLoading({
      title: '生成精美分享图...',
      mask: true
    });

    // 获取系统信息
    const systemInfo = wx.getSystemInfoSync();
    const pixelRatio = systemInfo.pixelRatio || 2;

    // Canvas尺寸 - 严格按照HTML设计
    const width = 375;
    const height = 667;

    console.log('Canvas绘制参数:', { width, height, pixelRatio, title, subtitle });
    console.log('绘制数据:', data);

    const ctx = wx.createCanvasContext(canvasId);

    // 强制清除Canvas内容，避免缓存问题
    ctx.clearRect(0, 0, width, height);
    ctx.setFillStyle('#FFFFFF');
    ctx.fillRect(0, 0, width, height);

    // 1. 绘制三色渐变背景 - 完全按照CSS设计
    const gradient = ctx.createLinearGradient(0, 0, width, height);
    gradient.addColorStop(0, '#667eea');
    gradient.addColorStop(0.5, '#764ba2');
    gradient.addColorStop(1, '#f093fb');
    ctx.setFillStyle(gradient);
    ctx.fillRect(0, 0, width, height);

    // 2. 绘制装饰元素 - 严格按照HTML位置
    drawBackgroundDecorations(ctx, width, height);

    // 3. 绘制头部标题区域
    drawHeaderSection(ctx, width, title, subtitle);

    // 4. 绘制核心数据卡片 - 2x2网格
    drawStatsSection(ctx, width, data);

    // 5. 绘制成就徽章区域
    drawAchievementsSection(ctx, width, data);

    // 6. 绘制质量分析区域
    drawQualitySection(ctx, width, data);

    // 7. 绘制底部信息区域
    drawFooterSection(ctx, width, height);

    // 8. 执行绘制
    ctx.draw(false, () => {
      // 添加延迟确保绘制完成
      setTimeout(() => {
        wx.canvasToTempFilePath({
          canvasId: canvasId,
          width: width,
          height: height,
          destWidth: width * pixelRatio,
          destHeight: height * pixelRatio,
          fileType: 'png',
          quality: 1,
          success: (res) => {
          wx.hideLoading();
          
          // 预览图片
          wx.previewImage({
            urls: [res.tempFilePath],
            success: () => {
              // 保存到相册
              wx.saveImageToPhotosAlbum({
                filePath: res.tempFilePath,
                success: () => {
                  wx.showModal({
                    title: '🎨 分享图片已生成',
                    content: '精美的分享图片已保存到您的相册！\n\n📱 您可以：\n• 分享到微信朋友圈\n• 发送给微信好友\n• 分享到其他社交平台\n\n✨ 图片特点：\n• 专业设计风格\n• 高清晰度\n• 包含小程序码',
                    showCancel: false,
                    confirmText: '我知道了'
                  });
                },
                fail: (error) => {
                  wx.showModal({
                    title: '保存失败',
                    content: '图片生成成功，但保存到相册失败。\n\n可能原因：\n• 未授权访问相册\n• 存储空间不足\n\n请在设置中开启相册权限后重试。',
                    showCancel: false,
                    confirmText: '我知道了'
                  });
                }
              });
            }
          });
        },
        fail: (error) => {
          wx.hideLoading();
          console.error('图片生成失败:', error);
          wx.showToast({
            title: '图片生成失败',
            icon: 'none'
          });
        }
      });
      }, 100); // setTimeout结束
    });

  } catch (error) {
    wx.hideLoading();
    console.error('分享图片生成失败:', error);
    wx.showToast({
      title: '生成失败，请重试',
      icon: 'none'
    });
  }
}

/**
 * 绘制圆角矩形
 */
function roundRect(ctx, x, y, width, height, radius) {
  ctx.beginPath();
  ctx.moveTo(x + radius, y);
  ctx.lineTo(x + width - radius, y);
  ctx.quadraticCurveTo(x + width, y, x + width, y + radius);
  ctx.lineTo(x + width, y + height - radius);
  ctx.quadraticCurveTo(x + width, y + height, x + width - radius, y + height);
  ctx.lineTo(x + radius, y + height);
  ctx.quadraticCurveTo(x, y + height, x, y + height - radius);
  ctx.lineTo(x, y + radius);
  ctx.quadraticCurveTo(x, y, x + radius, y);
  ctx.closePath();
}

/**
 * 文本智能换行
 */
function wrapText(ctx, text, maxWidth, fontSize) {
  const lines = [];
  const chars = text.split('');
  let currentLine = '';
  
  chars.forEach(char => {
    const testLine = currentLine + char;
    const metrics = ctx.measureText(testLine);
    
    if (metrics.width > maxWidth && currentLine !== '') {
      lines.push(currentLine);
      currentLine = char;
    } else {
      currentLine = testLine;
    }
  });
  
  if (currentLine) {
    lines.push(currentLine);
  }
  
  return lines.slice(0, 10); // 最多显示10行
}

/**
 * 统一的分享功能选择器
 */
function showShareOptions(data, options = {}) {
  const shareOptions = [
    '📊 分享Excel文件',
    '🖼️ 生成分享图片',
    '📋 复制内容',
    '🔗 生成分享链接'
  ];

  wx.showActionSheet({
    itemList: shareOptions,
    success: (res) => {
      switch (res.tapIndex) {
        case 0:
          shareExcelToWeChat(data, options.excel);
          break;
        case 1:
          generateBeautifulShareImage(data, options.image);
          break;
        case 2:
          copyToClipboard(data, options.copy);
          break;
        case 3:
          generateShareLink(data, options.link);
          break;
      }
    }
  });
}

/**
 * 复制内容到剪贴板
 */
function copyToClipboard(data, options = {}) {
  const content = options.formatContent ? options.formatContent(data) : JSON.stringify(data, null, 2);
  
  wx.setClipboardData({
    data: content,
    success: () => {
      wx.showToast({
        title: '内容已复制',
        icon: 'success'
      });
    },
    fail: () => {
      wx.showToast({
        title: '复制失败',
        icon: 'none'
      });
    }
  });
}

/**
 * 生成分享链接
 */
function generateShareLink(data, options = {}) {
  const shareId = 'SHARE_' + Date.now().toString(36).toUpperCase();
  const shareContent = options.generateContent ? options.generateContent(data, shareId) : `查看我的数据分享：${shareId}`;
  
  wx.setClipboardData({
    data: shareContent,
    success: () => {
      wx.showModal({
        title: '🔗 分享链接已生成',
        content: '分享内容已复制到剪贴板！\n\n您可以将其分享到：\n• 微信好友或群聊\n• 其他社交平台\n• 教育论坛或社区',
        showCancel: false,
        confirmText: '我知道了'
      });
    }
  });
}

/**
 * 绘制背景装饰元素 - 严格按照HTML设计
 */
function drawBackgroundDecorations(ctx, width, height) {
  // 大圆圈1 - 右上角 (top: -50px; right: -50px; width: 200px; height: 200px)
  ctx.setFillStyle('rgba(255, 255, 255, 0.1)');
  ctx.beginPath();
  ctx.arc(width - 50 + 100, -50 + 100, 100, 0, 2 * Math.PI); // 圆心在 (width+50, 50)，半径100
  ctx.fill();

  // 大圆圈2 - 左下角 (bottom: -30px; left: -30px; width: 150px; height: 150px)
  ctx.setFillStyle('rgba(255, 255, 255, 0.08)');
  ctx.beginPath();
  ctx.arc(-30 + 75, height - (-30) - 75, 75, 0, 2 * Math.PI); // 圆心在 (45, height-105)，半径75
  ctx.fill();

  // 小圆点装饰 - 严格按照HTML位置
  const dots = [
    { x: 50, y: 120 },    // dot-1: top: 120px; left: 50px
    { x: width - 80, y: 200 },  // dot-2: top: 200px; right: 80px
    { x: 80, y: height - 200 }, // dot-3: bottom: 200px; left: 80px
    { x: width - 60, y: height - 120 } // dot-4: bottom: 120px; right: 60px
  ];

  ctx.setFillStyle('rgba(255, 255, 255, 0.15)');
  dots.forEach(dot => {
    ctx.beginPath();
    ctx.arc(dot.x, dot.y, 4, 0, 2 * Math.PI); // 半径4px
    ctx.fill();
  });
}

/**
 * 绘制头部标题区域 - 严格按照HTML设计
 */
function drawHeaderSection(ctx, width, title, subtitle) {
  // 内容区域从padding: 40px 30px开始，所以Y从40开始
  const contentStartY = 40;
  const headerY = contentStartY;

  // 主标题 - font-size: 28px; font-weight: 700; margin-bottom: 8px
  ctx.setFillStyle('#FFFFFF');
  ctx.setFontSize(28);
  ctx.setTextAlign('center');
  ctx.fillText(title, width / 2, headerY + 28); // 28px字体大小的基线位置

  // 副标题 - font-size: 16px; opacity: 0.9; margin-bottom: 15px
  ctx.setFillStyle('rgba(255, 255, 255, 0.9)');
  ctx.setFontSize(16);
  ctx.fillText(subtitle, width / 2, headerY + 28 + 8 + 16); // 主标题 + margin + 副标题字体大小

  // 分割线 - width: 60px; height: 2px; background: rgba(255,255,255,0.6)
  const dividerY = headerY + 28 + 8 + 16 + 15; // 副标题 + margin-bottom
  ctx.setFillStyle('rgba(255, 255, 255, 0.6)');
  ctx.fillRect(width / 2 - 30, dividerY, 60, 2);
}

/**
 * 绘制核心数据区域 - 严格按照HTML设计
 */
function drawStatsSection(ctx, width, data) {
  // 头部区域高度：40 + 28 + 8 + 16 + 15 + 2 = 109，加上margin-bottom: 30px = 139
  const sectionStartY = 139;

  // 准备数据 - 严格按照HTML顺序和样式
  const statsData = [
    {
      value: (data.totalComments || 45) + '条',
      label: '生成评语',
      color: '#FF6B6B', // primary
      class: 'primary'
    },
    {
      value: data.timeSaved || '12h',
      label: '节省时间',
      color: '#4ECDC4', // secondary
      class: 'secondary'
    },
    {
      value: (data.avgQuality || '8.8') + '分',
      label: '平均质量',
      color: '#45B7D1', // tertiary
      class: 'tertiary'
    },
    {
      value: (data.excellentRate || '85') + '%',
      label: '优秀率',
      color: '#96CEB4', // quaternary
      class: 'quaternary'
    }
  ];

  // 计算卡片布局 - grid-template-columns: 1fr 1fr; gap: 15px
  const gap = 15;
  const totalWidth = width - 60; // 减去左右padding 30px
  const cardWidth = (totalWidth - gap) / 2; // 2列布局
  const cardHeight = 80; // 根据HTML padding计算
  const startX = 30; // 左padding

  statsData.forEach((stat, index) => {
    const row = Math.floor(index / 2);
    const col = index % 2;
    const x = startX + col * (cardWidth + gap);
    const y = sectionStartY + row * (cardHeight + gap);

    // 绘制卡片背景 - background: rgba(255,255,255,0.95); border-radius: 16px
    ctx.setFillStyle('rgba(255, 255, 255, 0.95)');
    drawRoundedRect(ctx, x, y, cardWidth, cardHeight, 16);
    ctx.fill();

    // 绘制彩色顶部条 - height: 4px
    ctx.setFillStyle(stat.color);
    drawRoundedRect(ctx, x, y, cardWidth, 4, 16);
    ctx.fill();

    // 绘制数值 - font-size: 24px; font-weight: 700; color: #2c3e50
    ctx.setFillStyle('#2C3E50');
    ctx.setFontSize(24);
    ctx.setTextAlign('center');
    ctx.fillText(stat.value, x + cardWidth / 2, y + 45);

    // 绘制标签 - font-size: 12px; color: #7f8c8d
    ctx.setFillStyle('#7F8C8D');
    ctx.setFontSize(12);
    ctx.fillText(stat.label, x + cardWidth / 2, y + 65);
  });
}

/**
 * 绘制成就徽章区域 - 严格按照HTML设计
 */
function drawAchievementsSection(ctx, width, data) {
  // 计算位置：头部139 + 数据卡片区域(80*2 + 15 + 25margin) = 139 + 200 = 339
  const sectionStartY = 339;

  // 准备成就数据 - 严格按照HTML
  const achievements = [
    {
      emoji: '🚀',
      name: '效率达人',
      unlocked: (data.totalComments || 45) >= 30
    },
    {
      emoji: '⭐',
      name: '质量专家',
      unlocked: (parseFloat(data.avgQuality) || 8.8) >= 8.5
    },
    {
      emoji: '📚',
      name: '进步导师',
      unlocked: (data.totalComments || 45) >= 100
    },
    {
      emoji: '👑',
      name: 'AI专家',
      unlocked: (data.totalComments || 45) >= 200
    }
  ];

  // 绘制标题 - font-size: 18px; font-weight: 600; margin-bottom: 15px
  ctx.setFillStyle('#FFFFFF');
  ctx.setFontSize(18);
  ctx.setTextAlign('center');
  ctx.fillText('🏆 我的成就', width / 2, sectionStartY + 18);

  // 徽章网格布局 - grid-template-columns: repeat(4, 1fr); gap: 12px
  const badgeSize = 50; // width: 50px; height: 50px
  const gap = 12;
  const totalBadgeWidth = badgeSize * 4 + gap * 3;
  const startX = (width - totalBadgeWidth) / 2;
  const badgeY = sectionStartY + 18 + 15; // 标题 + margin-bottom

  achievements.forEach((achievement, index) => {
    const x = startX + index * (badgeSize + gap);
    const y = badgeY;

    // 徽章背景圆圈
    if (achievement.unlocked) {
      // 已解锁 - background: linear-gradient(135deg, #FFD700, #FFA500)
      const badgeGradient = ctx.createLinearGradient(x, y, x + badgeSize, y + badgeSize);
      badgeGradient.addColorStop(0, '#FFD700');
      badgeGradient.addColorStop(1, '#FFA500');
      ctx.setFillStyle(badgeGradient);
    } else {
      // 未解锁 - background: rgba(255,255,255,0.2)
      ctx.setFillStyle('rgba(255, 255, 255, 0.2)');
    }

    ctx.beginPath();
    ctx.arc(x + badgeSize / 2, y + badgeSize / 2, badgeSize / 2, 0, 2 * Math.PI);
    ctx.fill();

    // 徽章边框 - border: 2px solid
    ctx.setStrokeStyle(achievement.unlocked ? '#FFD700' : 'rgba(255, 255, 255, 0.3)');
    ctx.setLineWidth(2);
    ctx.stroke();

    // 徽章图标 - font-size: 24px
    ctx.setFillStyle('#FFFFFF');
    ctx.setFontSize(24);
    ctx.setTextAlign('center');
    ctx.fillText(achievement.emoji, x + badgeSize / 2, y + badgeSize / 2 + 8);

    // 徽章名称 - font-size: 10px; opacity: 0.9; margin-top: 8px
    ctx.setFillStyle('rgba(255, 255, 255, 0.9)');
    ctx.setFontSize(10);
    ctx.fillText(achievement.name, x + badgeSize / 2, y + badgeSize + 8 + 10);
  });
}

/**
 * 绘制质量分析区域 - 严格按照HTML设计
 */
function drawQualitySection(ctx, width, data) {
  // 计算位置：成就区域339 + 18 + 15 + 50 + 8 + 10 + 25margin = 465
  const sectionStartY = 465;

  // 准备质量数据
  const qualityData = [
    {
      label: '专业性',
      value: data.professionalism || 92,
      percentage: (data.professionalism || 92) + '%'
    },
    {
      label: '个性化',
      value: data.personalization || 88,
      percentage: (data.personalization || 88) + '%'
    },
    {
      label: '完整性',
      value: data.completeness || 95,
      percentage: (data.completeness || 95) + '%'
    }
  ];

  // 绘制标题 - font-size: 18px; margin-bottom: 15px
  ctx.setFillStyle('#FFFFFF');
  ctx.setFontSize(18);
  ctx.setTextAlign('center');
  ctx.fillText('📈 质量分析', width / 2, sectionStartY + 18);

  // 背景区域 - background: rgba(255,255,255,0.1); border-radius: 12px; padding: 20px
  const bgX = 30;
  const bgY = sectionStartY + 18 + 15; // 标题 + margin-bottom
  const bgWidth = width - 60; // 左右各30px margin
  const bgHeight = 80; // 根据内容计算

  ctx.setFillStyle('rgba(255, 255, 255, 0.1)');
  drawRoundedRect(ctx, bgX, bgY, bgWidth, bgHeight, 12);
  ctx.fill();

  // 绘制质量条 - margin-bottom: 12px (最后一个为0)
  qualityData.forEach((item, index) => {
    const itemY = bgY + 20 + index * (8 + 12); // padding-top + (height + margin-bottom)
    const labelWidth = 60; // width: 60px
    const valueWidth = 35; // min-width: 35px
    const barWidth = bgWidth - 40 - labelWidth - 24 - valueWidth; // 减去padding和间距
    const barHeight = 8; // height: 8px
    const barX = bgX + 20 + labelWidth + 12; // padding + label + margin

    // 标签 - font-size: 14px; width: 60px
    ctx.setFillStyle('#FFFFFF');
    ctx.setFontSize(14);
    ctx.setTextAlign('left');
    ctx.fillText(item.label, bgX + 20, itemY + 6);

    // 进度条背景 - background: rgba(255,255,255,0.2); border-radius: 4px
    ctx.setFillStyle('rgba(255, 255, 255, 0.2)');
    drawRoundedRect(ctx, barX, itemY, barWidth, barHeight, 4);
    ctx.fill();

    // 进度条 - background: linear-gradient(90deg, #4ECDC4, #44A08D)
    const progressWidth = (barWidth * item.value) / 100;
    const progressGradient = ctx.createLinearGradient(barX, itemY, barX + progressWidth, itemY);
    progressGradient.addColorStop(0, '#4ECDC4');
    progressGradient.addColorStop(1, '#44A08D');
    ctx.setFillStyle(progressGradient);
    drawRoundedRect(ctx, barX, itemY, progressWidth, barHeight, 4);
    ctx.fill();

    // 百分比 - font-size: 14px; min-width: 35px; text-align: right
    ctx.setFillStyle('#FFFFFF');
    ctx.setFontSize(14);
    ctx.setTextAlign('right');
    ctx.fillText(item.percentage, bgX + bgWidth - 20, itemY + 6);
  });
}

/**
 * 绘制底部信息区域 - 严格按照HTML设计
 */
function drawFooterSection(ctx, width, height) {
  // 底部区域使用 margin-top: auto，位于最底部
  // 计算位置：height - 40(bottom padding) - footer内容高度
  const footerHeight = 20 + 5 + 14 + 8 + 11; // 各行高度和间距
  const footerY = height - 40 - footerHeight;

  // 分割线 - border-top: 1px solid rgba(255,255,255,0.2); padding-top: 20px
  ctx.setFillStyle('rgba(255, 255, 255, 0.2)');
  ctx.fillRect(30, footerY, width - 60, 1);

  const contentY = footerY + 1 + 20; // 分割线 + padding-top

  // 应用名称 - font-size: 20px; font-weight: 700; margin-bottom: 5px
  ctx.setFillStyle('#FFFFFF');
  ctx.setFontSize(20);
  ctx.setTextAlign('center');
  ctx.fillText('评语灵感君', width / 2, contentY + 20);

  // Slogan - font-size: 14px; opacity: 0.8; margin-bottom: 8px
  ctx.setFillStyle('rgba(255, 255, 255, 0.8)');
  ctx.setFontSize(14);
  ctx.fillText('让每一句评语都充满温度', width / 2, contentY + 20 + 5 + 14);

  // 生成时间 - font-size: 11px; opacity: 0.6
  const now = new Date();
  const timeStr = `生成时间：${now.getFullYear()}/${(now.getMonth() + 1).toString().padStart(2, '0')}/${now.getDate().toString().padStart(2, '0')} ${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}:${now.getSeconds().toString().padStart(2, '0')}`;
  ctx.setFillStyle('rgba(255, 255, 255, 0.6)');
  ctx.setFontSize(11);
  ctx.fillText(timeStr, width / 2, contentY + 20 + 5 + 14 + 8 + 11);
}

/**
 * 绘制圆角矩形
 */
function drawRoundedRect(ctx, x, y, width, height, radius) {
  ctx.beginPath();
  ctx.moveTo(x + radius, y);
  ctx.lineTo(x + width - radius, y);
  ctx.quadraticCurveTo(x + width, y, x + width, y + radius);
  ctx.lineTo(x + width, y + height - radius);
  ctx.quadraticCurveTo(x + width, y + height, x + width - radius, y + height);
  ctx.lineTo(x + radius, y + height);
  ctx.quadraticCurveTo(x, y + height, x, y + height - radius);
  ctx.lineTo(x, y + radius);
  ctx.quadraticCurveTo(x, y, x + radius, y);
  ctx.closePath();
}

module.exports = {
  shareExcelToWeChat,
  generateBeautifulShareImage,
  showShareOptions,
  copyToClipboard,
  generateShareLink
};