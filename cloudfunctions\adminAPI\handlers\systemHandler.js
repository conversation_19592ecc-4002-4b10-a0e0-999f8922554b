/**
 * 系统管理模块处理器
 * 处理管理员管理、系统设置、操作日志、系统监控等系统相关操作
 */

const crypto = require('crypto')

// 密码加密
const hashPassword = (password) => {
  return crypto.createHash('sha256').update(password).digest('hex')
}

module.exports = {
  /**
   * 获取管理员列表
   */
  async getAdmins(data, db, cloud, admin) {
    const adminsResult = await db.collection('admins')
      .field({ password: false }) // 不返回密码字段
      .orderBy('createTime', 'desc')
      .get()
    
    const admins = adminsResult.data.map(adminItem => ({
      id: adminItem._id,
      username: adminItem.username,
      email: adminItem.email,
      role: adminItem.role,
      permissions: adminItem.permissions,
      status: adminItem.status,
      profile: adminItem.profile,
      createTime: adminItem.createTime,
      updateTime: adminItem.updateTime,
      lastLoginTime: adminItem.lastLoginTime,
      lastActiveTime: adminItem.lastActiveTime
    }))
    
    return { admins }
  },

  /**
   * 创建管理员
   */
  async createAdmin(data, db, cloud, admin) {
    const { username, password, email, role, permissions, profile } = data
    
    if (!username || !password) {
      throw new Error('用户名和密码不能为空')
    }
    
    // 检查权限：只有超级管理员才能创建其他管理员
    if (admin.role !== 'super_admin') {
      throw new Error('您没有权限创建管理员账户')
    }
    
    // 检查用户名是否已存在
    const existingAdmin = await db.collection('admins').where({
      username: username
    }).get()
    
    if (existingAdmin.data.length > 0) {
      throw new Error('用户名已存在')
    }
    
    // 验证角色和权限
    const validRoles = ['admin', 'super_admin']
    if (!validRoles.includes(role)) {
      throw new Error('无效的角色类型')
    }
    
    const hashedPassword = hashPassword(password)
    
    const adminData = {
      username,
      password: hashedPassword,
      email: email || `${username}@shiyutong.com`,
      role: role || 'admin',
      permissions: permissions || ['ai_manage', 'data_view'],
      status: 'active',
      profile: {
        name: profile?.name || username,
        avatar: profile?.avatar || '',
        phone: profile?.phone || '',
        department: profile?.department || '管理部门'
      },
      createTime: db.serverDate(),
      updateTime: db.serverDate(),
      createTimestamp: Date.now(),
      updateTimestamp: Date.now(),
      createdBy: admin._id,
      lastLoginTime: null,
      lastActiveTime: null
    }
    
    const result = await db.collection('admins').add({
      data: adminData
    })
    
    return {
      id: result._id,
      username: adminData.username,
      email: adminData.email,
      role: adminData.role,
      status: adminData.status,
      message: '管理员创建成功'
    }
  },

  /**
   * 更新管理员信息
   */
  async updateAdmin(data, db, cloud, admin) {
    const { id, username, email, role, permissions, status, profile, password } = data
    
    if (!id) {
      throw new Error('管理员ID不能为空')
    }
    
    // 检查权限：只有超级管理员才能修改其他管理员，或者管理员修改自己的信息
    if (admin.role !== 'super_admin' && admin._id !== id) {
      throw new Error('您没有权限修改此管理员信息')
    }
    
    // 检查目标管理员是否存在
    const targetAdminResult = await db.collection('admins').doc(id).get()
    if (targetAdminResult.data.length === 0) {
      throw new Error('管理员不存在')
    }
    
    const updateData = {
      updateTime: db.serverDate(),
      updateTimestamp: Date.now(),
      updatedBy: admin._id
    }
    
    // 只有超级管理员才能修改角色和权限
    if (admin.role === 'super_admin') {
      if (username) updateData.username = username
      if (email) updateData.email = email
      if (role) updateData.role = role
      if (permissions) updateData.permissions = permissions
      if (status) updateData.status = status
    }
    
    // 个人资料信息可以自己修改
    if (profile) updateData.profile = { ...targetAdminResult.data[0].profile, ...profile }
    
    // 密码修改
    if (password) {
      updateData.password = hashPassword(password)
    }
    
    await db.collection('admins').doc(id).update({
      data: updateData
    })
    
    return {
      message: '管理员信息更新成功'
    }
  },

  /**
   * 删除管理员
   */
  async deleteAdmin(data, db, cloud, admin) {
    const { id } = data
    
    if (!id) {
      throw new Error('管理员ID不能为空')
    }
    
    // 检查权限：只有超级管理员才能删除管理员
    if (admin.role !== 'super_admin') {
      throw new Error('您没有权限删除管理员账户')
    }
    
    // 不能删除自己
    if (admin._id === id) {
      throw new Error('不能删除自己的账户')
    }
    
    // 检查目标管理员是否存在
    const targetAdminResult = await db.collection('admins').doc(id).get()
    if (targetAdminResult.data.length === 0) {
      throw new Error('管理员不存在')
    }
    
    const targetAdmin = targetAdminResult.data[0]
    
    // 不能删除其他超级管理员
    if (targetAdmin.role === 'super_admin') {
      throw new Error('不能删除超级管理员账户')
    }
    
    await db.collection('admins').doc(id).remove()
    
    return {
      message: '管理员删除成功'
    }
  },

  /**
   * 获取操作日志
   */
  async getLogs(data, db, cloud, admin) {
    const { params = {} } = data
    const { page = 1, limit = 50, adminId, action, dateRange } = params
    
    const skip = (page - 1) * limit
    let query = db.collection('logs')
    
    // 构建查询条件
    const whereConditions = {}
    if (adminId) whereConditions.adminId = adminId
    if (action) whereConditions.action = new RegExp(action, 'i')
    
    // 日期范围查询
    if (dateRange && dateRange.length === 2) {
      whereConditions.createTime = db.command.and([
        db.command.gte(new Date(dateRange[0])),
        db.command.lte(new Date(dateRange[1]))
      ])
    }
    
    if (Object.keys(whereConditions).length > 0) {
      query = query.where(whereConditions)
    }
    
    const [countResult, dataResult] = await Promise.all([
      query.count(),
      query.skip(skip).limit(limit).orderBy('createTime', 'desc').get()
    ])
    
    const logs = dataResult.data.map(log => ({
      id: log._id,
      action: log.action,
      adminId: log.adminId,
      adminName: log.adminName,
      requestData: log.requestData,
      result: log.result,
      ip: log.ip,
      userAgent: log.userAgent,
      createTime: log.createTime,
      timestamp: log.timestamp
    }))
    
    return {
      logs,
      pagination: {
        page,
        limit,
        total: countResult.total,
        pages: Math.ceil(countResult.total / limit)
      }
    }
  },

  /**
   * 获取系统设置
   */
  async getSettings(data, db, cloud, admin) {
    const settingsResult = await db.collection('settings').get()
    
    // 如果没有设置，返回默认设置
    if (settingsResult.data.length === 0) {
      const defaultSettings = {
        site: {
          title: '智慧评语助手3.0',
          description: '基于AI的智能评语生成系统',
          version: '3.0.0'
        },
        ai: {
          defaultModel: 'doubao',
          maxTokens: 2000,
          temperature: 0.7,
          timeout: 30000
        },
        security: {
          sessionTimeout: 7 * 24 * 60 * 60 * 1000, // 7天
          maxLoginAttempts: 5,
          lockoutDuration: 15 * 60 * 1000 // 15分钟
        },
        data: {
          backupEnabled: true,
          backupInterval: 24 * 60 * 60 * 1000, // 24小时
          retentionDays: 30
        }
      }
      
      // 保存默认设置
      await db.collection('settings').add({
        data: {
          ...defaultSettings,
          createTime: db.serverDate(),
          updateTime: db.serverDate(),
          createdBy: admin._id
        }
      })
      
      return defaultSettings
    }
    
    const settings = settingsResult.data[0]
    return {
      site: settings.site,
      ai: settings.ai,
      security: settings.security,
      data: settings.data
    }
  },

  /**
   * 更新系统设置
   */
  async updateSettings(data, db, cloud, admin) {
    const { site, ai, security, dataSettings } = data
    
    // 检查权限：只有超级管理员才能修改系统设置
    if (admin.role !== 'super_admin') {
      throw new Error('您没有权限修改系统设置')
    }
    
    const settingsResult = await db.collection('settings').get()
    
    const updateData = {
      updateTime: db.serverDate(),
      updateTimestamp: Date.now(),
      updatedBy: admin._id
    }
    
    if (site) updateData.site = site
    if (ai) updateData.ai = ai
    if (security) updateData.security = security
    if (dataSettings) updateData.data = dataSettings
    
    if (settingsResult.data.length === 0) {
      // 如果没有设置记录，创建新的
      await db.collection('settings').add({
        data: {
          ...updateData,
          createTime: db.serverDate(),
          createdBy: admin._id
        }
      })
    } else {
      // 更新现有设置
      await db.collection('settings').doc(settingsResult.data[0]._id).update({
        data: updateData
      })
    }
    
    return {
      message: '系统设置更新成功'
    }
  },

  /**
   * 获取系统状态
   */
  async getSystemStatus(data, db, cloud, admin) {
    try {
      // 获取数据库统计信息
      const [
        adminCount,
        studentCount,
        commentCount,
        recordCount,
        logCount
      ] = await Promise.all([
        db.collection('admins').count(),
        db.collection('students').count(),
        db.collection('comments').count(),
        db.collection('records').count(),
        db.collection('logs').count()
      ])
      
      // 获取最近24小时的活动统计
      const yesterday = new Date(Date.now() - 24 * 60 * 60 * 1000)
      const recentActivities = await db.collection('logs').where({
        createTime: db.command.gte(yesterday)
      }).count()
      
      // 获取在线管理员数量（简化版，基于最近15分钟的活动）
      const fifteenMinutesAgo = new Date(Date.now() - 15 * 60 * 1000)
      const onlineAdmins = await db.collection('admins').where({
        lastActiveTime: db.command.gte(fifteenMinutesAgo)
      }).count()
      
      return {
        database: {
          status: 'healthy',
          collections: {
            admins: adminCount.total,
            students: studentCount.total,
            comments: commentCount.total,
            records: recordCount.total,
            logs: logCount.total
          }
        },
        performance: {
          responseTime: Math.floor(Math.random() * 100) + 50, // 模拟响应时间
          memoryUsage: Math.floor(Math.random() * 30) + 40, // 模拟内存使用率
          cpuUsage: Math.floor(Math.random() * 20) + 10 // 模拟CPU使用率
        },
        activity: {
          onlineAdmins: onlineAdmins.total,
          recentOperations: recentActivities.total,
          lastBackupTime: new Date().toISOString(), // 简化处理
          systemUptime: Math.floor(Math.random() * 100) + 50 // 模拟系统运行时间（小时）
        },
        version: {
          current: '3.0.0',
          environment: cloud.DYNAMIC_CURRENT_ENV || 'production',
          buildTime: new Date().toISOString()
        },
        checkTime: new Date().toISOString()
      }
    } catch (error) {
      throw new Error(`获取系统状态失败: ${error.message}`)
    }
  },

  /**
   * 获取系统监控数据
   */
  async getMonitoringData(data, db, cloud, admin) {
    const { timeRange = '24h' } = data
    
    // 计算时间范围
    const now = new Date()
    let startTime
    let intervalHours
    
    switch (timeRange) {
      case '1h':
        startTime = new Date(now.getTime() - 60 * 60 * 1000)
        intervalHours = 0.1 // 6分钟间隔
        break
      case '24h':
      default:
        startTime = new Date(now.getTime() - 24 * 60 * 60 * 1000)
        intervalHours = 1 // 1小时间隔
        break
    }
    
    // 获取时间段内的操作日志
    const logsResult = await db.collection('logs').where({
      createTime: db.command.gte(startTime)
    }).get()
    
    // 按时间间隔分组统计
    const intervals = Math.ceil((now - startTime) / (intervalHours * 60 * 60 * 1000))
    const monitoringData = []
    
    for (let i = 0; i < intervals; i++) {
      const intervalStart = new Date(startTime.getTime() + i * intervalHours * 60 * 60 * 1000)
      const intervalEnd = new Date(intervalStart.getTime() + intervalHours * 60 * 60 * 1000)
      
      const intervalLogs = logsResult.data.filter(log => {
        const logTime = new Date(log.createTime)
        return logTime >= intervalStart && logTime < intervalEnd
      })
      
      monitoringData.push({
        timestamp: intervalStart.toISOString(),
        operations: intervalLogs.length,
        successCount: intervalLogs.filter(log => log.result === 'success').length,
        errorCount: intervalLogs.filter(log => log.result === 'failed').length,
        // 模拟其他监控指标
        responseTime: Math.floor(Math.random() * 200) + 100,
        memoryUsage: Math.floor(Math.random() * 20) + 40,
        cpuUsage: Math.floor(Math.random() * 30) + 20
      })
    }
    
    return {
      timeRange,
      startTime: startTime.toISOString(),
      endTime: now.toISOString(),
      data: monitoringData,
      summary: {
        totalOperations: logsResult.data.length,
        successRate: logsResult.data.length > 0 
          ? ((logsResult.data.filter(log => log.result === 'success').length / logsResult.data.length) * 100).toFixed(2)
          : 0,
        avgResponseTime: monitoringData.reduce((sum, item) => sum + item.responseTime, 0) / monitoringData.length
      }
    }
  },

  /**
   * 发送系统通知
   */
  async sendNotification(data, db, cloud, admin) {
    const { title, content, type = 'info', targetAdmins } = data
    
    if (!title || !content) {
      throw new Error('通知标题和内容不能为空')
    }
    
    // 检查权限：只有超级管理员才能发送系统通知
    if (admin.role !== 'super_admin') {
      throw new Error('您没有权限发送系统通知')
    }
    
    const notificationData = {
      title,
      content,
      type, // info, warning, error, success
      targetAdmins: targetAdmins || ['all'], // 目标管理员ID数组，'all'表示所有管理员
      status: 'sent',
      createTime: db.serverDate(),
      createTimestamp: Date.now(),
      createdBy: admin._id,
      readBy: [] // 已读管理员列表
    }
    
    const result = await db.collection('notifications').add({
      data: notificationData
    })
    
    return {
      id: result._id,
      message: '系统通知发送成功',
      ...notificationData
    }
  },

  /**
   * 获取系统版本信息
   */
  async getVersionInfo(data, db, cloud, admin) {
    return {
      current: '3.0.0',
      codename: 'CloudBase Edition',
      buildDate: '2024-12-01',
      environment: cloud.DYNAMIC_CURRENT_ENV || 'production',
      features: [
        '云开发托管部署',
        'AI智能评语生成',
        '实时数据同步',
        '权限管理系统',
        '数据导入导出',
        '系统监控告警'
      ],
      changelog: [
        {
          version: '3.0.0',
          date: '2024-12-01',
          changes: [
            '迁移到云开发托管架构',
            '优化管理后台界面',
            '增强系统监控功能',
            '改进数据导入导出'
          ]
        },
        {
          version: '2.1.0',
          date: '2024-11-01',
          changes: [
            '增加AI模型管理',
            '优化评语生成算法',
            '修复已知问题'
          ]
        }
      ],
      dependencies: {
        'wx-server-sdk': 'latest',
        'node': '>=14.0.0'
      }
    }
  }
}