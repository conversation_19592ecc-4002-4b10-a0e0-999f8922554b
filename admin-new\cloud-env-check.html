<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>云开发环境检查</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }
        .container {
            background: white;
            border-radius: 12px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            margin-bottom: 20px;
        }
        h1 { color: #2c3e50; text-align: center; margin-bottom: 30px; }
        .instructions {
            background: #e8f4fd;
            border-left: 4px solid #3498db;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
        }
        .step {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 20px;
            margin: 15px 0;
        }
        .step h3 {
            color: #495057;
            margin-top: 0;
        }
        .checklist {
            list-style: none;
            padding: 0;
        }
        .checklist li {
            padding: 8px 0;
            border-bottom: 1px solid #f1f1f1;
        }
        .checklist li:before {
            content: "☐ ";
            color: #6c757d;
            font-weight: bold;
            margin-right: 8px;
        }
        .checklist li.checked:before {
            content: "✅ ";
            color: #28a745;
        }
        .url-test {
            background: #2c3e50;
            color: #ecf0f1;
            padding: 15px;
            border-radius: 6px;
            font-family: monospace;
            font-size: 12px;
            margin: 10px 0;
        }
        button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
            transition: transform 0.2s;
        }
        button:hover { transform: translateY(-2px); }
        .warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
        }
        .success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>☁️ 云开发环境检查工具</h1>
        
        <div class="instructions">
            <h3>📋 使用说明</h3>
            <p>此工具帮助你检查云开发环境配置，确保云函数能正常部署和运行。</p>
            <p><strong>请按照以下步骤逐一检查：</strong></p>
        </div>

        <div class="step">
            <h3>🔍 第一步：检查云开发控制台</h3>
            <p>在微信开发者工具中打开云开发控制台，检查以下项目：</p>
            <ul class="checklist">
                <li>云开发环境是否正常运行</li>
                <li>环境ID是否为：cloud1-4g85f8xlb8166ff1</li>
                <li>云函数列表中有哪些函数</li>
                <li>adminAPI或adminAPI_v2的状态</li>
                <li>HTTP触发器是否正确配置</li>
            </ul>
            <div class="warning">
                <strong>⚠️ 注意：</strong>如果看到adminAPI状态为"更新中"或"异常"，请删除后重新部署。
            </div>
        </div>

        <div class="step">
            <h3>🚀 第二步：重新部署云函数</h3>
            <p>建议使用全新的adminAPI_v2来避免冲突：</p>
            <ul class="checklist">
                <li>删除旧的adminAPI云函数（如果存在）</li>
                <li>右键cloudfunctions/adminAPI_v2文件夹</li>
                <li>选择"上传并部署：云端安装依赖"</li>
                <li>等待部署完成（通常需要1-2分钟）</li>
                <li>检查部署状态是否为"正常"</li>
            </ul>
        </div>

        <div class="step">
            <h3>🔗 第三步：测试云函数连通性</h3>
            <p>部署完成后，测试以下URL：</p>
            <div class="url-test">
                <div>adminAPI_v2 (推荐):</div>
                <div>https://cloud1-4g85f8xlb8166ff1.tcb.qcloud.la/admin-v2</div>
                <br>
                <div>testAPI (如果部署了):</div>
                <div>https://cloud1-4g85f8xlb8166ff1.tcb.qcloud.la/test</div>
            </div>
            <button onclick="testCloudFunction()">测试云函数连通性</button>
            <div id="testResults"></div>
        </div>

        <div class="step">
            <h3>🔧 第四步：切换管理后台API</h3>
            <p>云函数测试成功后，切换管理后台使用云函数：</p>
            <ul class="checklist">
                <li>停止本地API服务器</li>
                <li>修改.env.local中的API地址</li>
                <li>重新启动管理后台</li>
                <li>测试所有功能是否正常</li>
            </ul>
            <button onclick="switchToCloudAPI()">切换到云函数API</button>
        </div>

        <div class="step">
            <h3>📊 第五步：连接真实数据</h3>
            <p>云函数连通后，修改代码连接真实的小程序数据库：</p>
            <ul class="checklist">
                <li>修改云函数中的数据查询逻辑</li>
                <li>从模拟数据切换到真实数据库查询</li>
                <li>测试数据的准确性</li>
                <li>优化查询性能</li>
            </ul>
        </div>
    </div>

    <div class="container">
        <h2>📝 操作日志</h2>
        <div id="logContainer" style="max-height: 300px; overflow-y: auto; background: #f8f9fa; padding: 15px; border-radius: 6px; font-family: monospace; font-size: 12px;">
            等待开始操作...
        </div>
    </div>

    <script>
        function log(message, type = 'info') {
            const logContainer = document.getElementById('logContainer')
            const timestamp = new Date().toLocaleTimeString()
            const prefix = type === 'error' ? '❌' : type === 'success' ? '✅' : type === 'warning' ? '⚠️' : 'ℹ️'
            logContainer.textContent += `[${timestamp}] ${prefix} ${message}\n`
            logContainer.scrollTop = logContainer.scrollHeight
        }

        async function testCloudFunction() {
            const testResults = document.getElementById('testResults')
            testResults.innerHTML = ''
            
            log('🔍 开始测试云函数连通性...')
            
            const urls = [
                'https://cloud1-4g85f8xlb8166ff1.tcb.qcloud.la/admin-v2',
                'https://cloud1-4g85f8xlb8166ff1.tcb.qcloud.la/test',
                'https://cloud1-4g85f8xlb8166ff1.tcb.qcloud.la/admin'
            ]
            
            for (const url of urls) {
                try {
                    log(`🚀 测试 ${url}...`)
                    
                    const response = await fetch(url, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({
                            action: 'healthCheck',
                            timestamp: Date.now()
                        })
                    })
                    
                    if (response.ok) {
                        const result = await response.json()
                        log(`✅ ${url} 连通成功`)
                        
                        const successDiv = document.createElement('div')
                        successDiv.className = 'success'
                        successDiv.innerHTML = `
                            <strong>✅ ${url} 连通成功！</strong><br>
                            状态码: ${response.status}<br>
                            响应: ${JSON.stringify(result, null, 2)}
                        `
                        testResults.appendChild(successDiv)
                        
                        // 如果找到可用的云函数，提供切换按钮
                        if (result.code === 200) {
                            log(`🎉 找到可用的云函数: ${url}`)
                            break
                        }
                    } else {
                        log(`⚠️ ${url} 响应异常: ${response.status}`)
                    }
                } catch (error) {
                    log(`❌ ${url} 连接失败: ${error.message}`)
                    
                    const errorDiv = document.createElement('div')
                    errorDiv.className = 'warning'
                    errorDiv.innerHTML = `
                        <strong>❌ ${url} 连接失败</strong><br>
                        错误: ${error.message}<br>
                        建议: 检查云函数是否正确部署
                    `
                    testResults.appendChild(errorDiv)
                }
            }
            
            log('🎯 云函数连通性测试完成')
        }

        function switchToCloudAPI() {
            log('🔄 准备切换到云函数API...')
            
            const instructions = `
请按以下步骤操作：

1. 停止本地API服务器（按Ctrl+C）
2. 修改 admin-new/.env.local 文件：
   VITE_API_BASE_URL=https://cloud1-4g85f8xlb8166ff1.tcb.qcloud.la/admin-v2
3. 重新启动管理后台：npm run dev
4. 测试所有功能是否正常工作

注意：确保云函数测试成功后再进行切换！
            `
            
            const instructionDiv = document.createElement('div')
            instructionDiv.className = 'instructions'
            instructionDiv.innerHTML = `
                <h3>📋 切换步骤</h3>
                <pre style="white-space: pre-wrap; font-family: monospace; font-size: 12px;">${instructions}</pre>
            `
            document.getElementById('testResults').appendChild(instructionDiv)
            
            log('📋 切换指令已显示，请按步骤操作')
        }

        // 页面加载时的提示
        window.addEventListener('load', () => {
            log('☁️ 云开发环境检查工具已加载')
            log('💡 请按照页面上的步骤逐一检查和操作')
        })
    </script>
</body>
</html>
