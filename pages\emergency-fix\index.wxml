<!--紧急修复页面-->
<view class="container">
  <view class="header">
    <text class="title">🚨 紧急修复工具</text>
    <text class="subtitle">一键解决云开发环境问题</text>
  </view>

  <!-- 修复状态 -->
  <view class="status-card">
    <view class="status-header">
      <text class="status-title">修复状态</text>
      <view class="status-indicator {{fixResult && fixResult.success ? 'success' : (fixing ? 'warning' : 'error')}}"></view>
    </view>
    
    <view class="status-content">
      <view class="status-item">
        <text class="label">当前状态:</text>
        <text class="value">
          {{fixing ? '正在修复...' : (fixResult ? (fixResult.success ? '修复成功' : '修复失败') : '等待修复')}}
        </text>
      </view>
      
      <view class="status-item" wx:if="{{fixAttempts > 0}}">
        <text class="label">修复次数:</text>
        <text class="value">{{fixAttempts}}</text>
      </view>
      
      <view class="status-item" wx:if="{{fixResult && !fixResult.success}}">
        <text class="label">错误信息:</text>
        <text class="value error">{{fixResult.message || fixResult.error}}</text>
      </view>
    </view>
  </view>

  <!-- 操作按钮 -->
  <view class="actions">
    <button 
      class="btn primary large" 
      bindtap="startEmergencyFix" 
      loading="{{fixing}}"
      disabled="{{fixing}}"
    >
      {{fixing ? '修复中...' : '🚨 开始紧急修复'}}
    </button>
    
    <view class="action-row">
      <button class="btn secondary" bindtap="quickDiagnose">
        🔍 快速诊断
      </button>
      
      <button class="btn outline" bindtap="manualReset">
        🔄 手动重置
      </button>
    </view>
  </view>

  <!-- 修复说明 -->
  <view class="info-card">
    <view class="info-header">
      <text class="info-title">💡 修复说明</text>
    </view>
    
    <view class="info-content">
      <text class="info-text">
        此工具专门解决云开发环境连接问题，包括：
      </text>
      
      <view class="info-list">
        <text class="info-item">• env check invalid 错误</text>
        <text class="info-item">• 数据库连接失败</text>
        <text class="info-item">• 云函数调用异常</text>
        <text class="info-item">• 环境初始化问题</text>
      </view>
      
      <text class="info-text">
        修复过程会自动尝试多种策略，通常在1-2分钟内完成。
      </text>
    </view>
  </div>

  <!-- 日志区域 -->
  <view class="logs-card">
    <view class="logs-header" bindtap="toggleLogs">
      <text class="logs-title">📝 修复日志</text>
      <text class="logs-toggle">{{showLogs ? '收起' : '展开'}}</text>
    </view>
    
    <view class="logs-content" wx:if="{{showLogs}}">
      <view class="logs-list">
        <text 
          class="log-item" 
          wx:for="{{logs}}" 
          wx:key="index"
        >
          {{item}}
        </text>
      </view>
      
      <view class="logs-actions" wx:if="{{logs.length > 0}}">
        <button class="btn small outline" bindtap="copyLogs">
          复制日志
        </button>
      </view>
    </view>
  </view>

  <!-- 底部操作 -->
  <view class="footer">
    <button class="btn outline" bindtap="goHome">
      返回首页
    </button>
  </view>
</view>
