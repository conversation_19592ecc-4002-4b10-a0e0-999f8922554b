/**
 * 最终验证脚本 - 上线前的最后检查
 */

const fs = require('fs');
const path = require('path');

console.log('🔍 开始最终验证检查...');

// 检查配置文件
function validateConfigFiles() {
  console.log('\n📋 验证配置文件...');
  
  const requiredFiles = [
    'app.json',
    'project.config.json',
    'sitemap.json'
  ];
  
  let allValid = true;
  
  requiredFiles.forEach(file => {
    if (fs.existsSync(file)) {
      try {
        const content = JSON.parse(fs.readFileSync(file, 'utf8'));
        console.log(`✅ ${file}: 格式正确`);
        
        // 特殊检查
        if (file === 'app.json') {
          const testPages = content.pages.filter(page => 
            page.includes('test') || page.includes('debug')
          );
          if (testPages.length > 0) {
            console.log(`⚠️  ${file}: 仍包含测试页面 ${testPages.length} 个`);
            testPages.forEach(page => console.log(`   - ${page}`));
            allValid = false;
          }
        }
        
        if (file === 'project.config.json') {
          if (content.appid !== 'wx3de03090b8e8a734') {
            console.log(`❌ ${file}: AppID不正确`);
            allValid = false;
          }
          if (!content.packOptions || !content.packOptions.ignore) {
            console.log(`❌ ${file}: 缺少打包排除配置`);
            allValid = false;
          }
        }
        
      } catch (error) {
        console.log(`❌ ${file}: JSON格式错误 - ${error.message}`);
        allValid = false;
      }
    } else {
      console.log(`❌ ${file}: 文件不存在`);
      allValid = false;
    }
  });
  
  return allValid;
}

// 检查文件大小
function validateFileSize() {
  console.log('\n📊 验证文件大小...');
  
  const getDirectorySize = (dirPath) => {
    if (!fs.existsSync(dirPath)) return 0;
    
    let size = 0;
    const files = fs.readdirSync(dirPath);
    
    files.forEach(file => {
      const filePath = path.join(dirPath, file);
      const stats = fs.statSync(filePath);
      
      if (stats.isDirectory()) {
        size += getDirectorySize(filePath);
      } else {
        size += stats.size;
      }
    });
    
    return size;
  };
  
  // 检查主要目录
  const directories = ['pages', 'components', 'utils', 'services', 'config'];
  let totalSize = 0;
  
  directories.forEach(dir => {
    const size = getDirectorySize(dir);
    totalSize += size;
    console.log(`   ${dir}: ${(size / (1024 * 1024)).toFixed(2)} MB`);
  });
  
  // 检查主要文件
  const mainFiles = ['app.js', 'app.wxss', 'app.json'];
  mainFiles.forEach(file => {
    if (fs.existsSync(file)) {
      const size = fs.statSync(file).size;
      totalSize += size;
      console.log(`   ${file}: ${(size / 1024).toFixed(2)} KB`);
    }
  });
  
  const totalSizeInMB = (totalSize / (1024 * 1024)).toFixed(2);
  console.log(`📦 预估总大小: ${totalSizeInMB} MB`);
  
  if (totalSize > 2 * 1024 * 1024) {
    console.log('❌ 大小超过2MB限制');
    return false;
  } else {
    console.log('✅ 大小检查通过');
    return true;
  }
}

// 检查敏感信息
function validateSecurity() {
  console.log('\n🔒 验证安全性...');
  
  const sensitivePatterns = [
    { pattern: /password\s*[:=]\s*['"][^'"]+['"]/gi, name: '密码' },
    { pattern: /secret\s*[:=]\s*['"][^'"]+['"]/gi, name: '密钥' },
    { pattern: /token\s*[:=]\s*['"][^'"]+['"]/gi, name: 'Token' },
    { pattern: /console\.log\(/gi, name: '调试日志' },
    { pattern: /debugger/gi, name: '调试断点' }
  ];
  
  const scanFiles = (dir, issues = []) => {
    if (!fs.existsSync(dir)) return issues;
    
    const files = fs.readdirSync(dir);
    
    files.forEach(file => {
      const filePath = path.join(dir, file);
      const stats = fs.statSync(filePath);
      
      if (stats.isDirectory() && !file.startsWith('.') && 
          !['node_modules', 'miniprogram_npm'].includes(file)) {
        scanFiles(filePath, issues);
      } else if (stats.isFile() && /\.(js|ts|json)$/.test(file)) {
        const content = fs.readFileSync(filePath, 'utf8');
        
        sensitivePatterns.forEach(({ pattern, name }) => {
          const matches = content.match(pattern);
          if (matches) {
            issues.push({
              file: filePath,
              type: name,
              count: matches.length
            });
          }
        });
      }
    });
    
    return issues;
  };
  
  const issues = scanFiles('.');
  
  if (issues.length === 0) {
    console.log('✅ 未发现安全问题');
    return true;
  } else {
    console.log('⚠️  发现潜在安全问题:');
    issues.forEach(issue => {
      console.log(`   ${issue.file}: ${issue.type} (${issue.count}处)`);
    });
    return false;
  }
}

// 检查云函数配置
function validateCloudFunctions() {
  console.log('\n☁️ 验证云函数配置...');
  
  const cloudFunctionsDir = 'cloudfunctions';
  if (!fs.existsSync(cloudFunctionsDir)) {
    console.log('❌ 云函数目录不存在');
    return false;
  }
  
  const functions = fs.readdirSync(cloudFunctionsDir).filter(item => {
    const itemPath = path.join(cloudFunctionsDir, item);
    return fs.statSync(itemPath).isDirectory();
  });
  
  console.log(`📦 发现 ${functions.length} 个云函数:`);
  
  const criticalFunctions = [
    'login', 'getUserId', 'initDatabase', 'getStudents', 
    'callDoubaoAPI', 'adminAPI'
  ];
  
  let allCriticalExists = true;
  
  criticalFunctions.forEach(funcName => {
    if (functions.includes(funcName)) {
      console.log(`✅ ${funcName}: 存在`);
    } else {
      console.log(`❌ ${funcName}: 缺失`);
      allCriticalExists = false;
    }
  });
  
  return allCriticalExists;
}

// 生成最终验证报告
function generateFinalReport(results) {
  console.log('\n📊 生成最终验证报告...');
  
  const report = `
# 最终验证报告

## 验证时间
${new Date().toLocaleString()}

## 验证结果
- 配置文件验证: ${results.config ? '✅ 通过' : '❌ 失败'}
- 文件大小验证: ${results.size ? '✅ 通过' : '❌ 失败'}
- 安全性验证: ${results.security ? '✅ 通过' : '⚠️ 有问题'}
- 云函数验证: ${results.cloudFunctions ? '✅ 通过' : '❌ 失败'}

## 总体评估
${Object.values(results).every(r => r) ? 
  '🎉 所有检查通过，可以上线！' : 
  '⚠️ 存在问题，请修复后再上线'}

## 上线前最后确认
- [ ] 在微信开发者工具中预览正常
- [ ] 源码包大小小于2MB
- [ ] 所有测试页面已移除
- [ ] 云函数部署完成
- [ ] 数据库初始化完成

## 建议
1. 在正式发布前进行完整的功能测试
2. 准备回滚方案
3. 监控上线后的关键指标
4. 及时响应用户反馈

生成时间: ${new Date().toLocaleString()}
`;

  fs.writeFileSync('FINAL_VALIDATION_REPORT.md', report);
  console.log('✅ 最终验证报告已生成: FINAL_VALIDATION_REPORT.md');
}

// 执行所有验证
async function runFinalValidation() {
  try {
    const results = {
      config: validateConfigFiles(),
      size: validateFileSize(),
      security: validateSecurity(),
      cloudFunctions: validateCloudFunctions()
    };
    
    generateFinalReport(results);
    
    const allPassed = Object.values(results).every(r => r);
    
    if (allPassed) {
      console.log('\n🎉 最终验证通过！小程序可以上线了！');
      console.log('📝 请查看 FINAL_VALIDATION_REPORT.md 了解详细信息');
      console.log('🚀 下一步：在微信开发者工具中点击上传，提交审核');
    } else {
      console.log('\n⚠️ 验证发现问题，请修复后重新验证');
      console.log('📝 详细信息请查看 FINAL_VALIDATION_REPORT.md');
    }
    
  } catch (error) {
    console.error('❌ 验证过程中出现错误:', error.message);
    process.exit(1);
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  runFinalValidation();
}

module.exports = {
  validateConfigFiles,
  validateFileSize,
  validateSecurity,
  validateCloudFunctions,
  runFinalValidation
};
