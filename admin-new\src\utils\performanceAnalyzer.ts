import React from 'react'

// 🔥 6. 开发模式 vs 生产模式性能分析工具

export interface PerformanceReport {
  mode: 'development' | 'production' | 'preview'
  buildInfo: {
    target: string
    minified: boolean
    sourcemap: boolean
    hmr: boolean
  }
  runtimeMetrics: {
    fps: number
    memoryUsage: number
    domNodes: number
    eventListeners: number
    renderCount: number
  }
  bundleInfo: {
    totalSize: number
    chunks: Array<{
      name: string
      size: number
      type: 'js' | 'css' | 'asset'
    }>
    loadTime: number
  }
  optimization: {
    reactDevtools: boolean
    reactStrictMode: boolean
    longTasks: number
    unusedCSS: number
  }
  recommendations: string[]
}

export class PerformanceAnalyzer {
  private static instance: PerformanceAnalyzer
  private performanceObserver: PerformanceObserver | null = null
  private metrics: Map<string, number[]> = new Map()
  
  static getInstance(): PerformanceAnalyzer {
    if (!PerformanceAnalyzer.instance) {
      PerformanceAnalyzer.instance = new PerformanceAnalyzer()
    }
    return PerformanceAnalyzer.instance
  }
  
  constructor() {
    this.initializeMonitoring()
  }
  
  private initializeMonitoring() {
    if (typeof window === 'undefined') return
    
    // 🔥 监控长任务
    if ('PerformanceObserver' in window) {
      this.performanceObserver = new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          if (entry.entryType === 'longtask') {
            console.warn(`🐌 长任务检测: ${entry.duration}ms`, entry)
          }
          if (entry.entryType === 'largest-contentful-paint') {
            this.recordMetric('lcp', entry.startTime)
          }
          if (entry.entryType === 'first-input-delay') {
            this.recordMetric('fid', (entry as any).processingStart - entry.startTime)
          }
        }
      })
      
      try {
        this.performanceObserver.observe({ 
          entryTypes: ['longtask', 'largest-contentful-paint', 'first-input-delay'] 
        })
      } catch (e) {
        console.warn('Performance Observer not fully supported:', e)
      }
    }
  }
  
  private recordMetric(name: string, value: number) {
    if (!this.metrics.has(name)) {
      this.metrics.set(name, [])
    }
    this.metrics.get(name)!.push(value)
  }
  
  // 🔥 检测当前运行模式
  detectMode(): PerformanceReport['mode'] {
    if (process.env.NODE_ENV === 'development') {
      return 'development'
    }
    if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
      return 'preview' // npm run preview
    }
    return 'production'
  }
  
  // 🔥 检测构建信息
  getBuildInfo(): PerformanceReport['buildInfo'] {
    const scripts = Array.from(document.querySelectorAll('script[src]'))
    const styles = Array.from(document.querySelectorAll('link[rel="stylesheet"]'))
    
    const hasSourceMap = scripts.some(script => {
      const src = (script as HTMLScriptElement).src
      return src.includes('.map') || src.includes('sourcemap')
    })
    
    const isMinified = scripts.some(script => {
      const src = (script as HTMLScriptElement).src
      return src.includes('min.') || /\w{8,}\.js$/.test(src) // hash文件名通常表示已压缩
    })
    
    const hasHMR = !!(window as any).__vite_plugin_react_preamble_installed__
    
    return {
      target: 'es2020', // 从vite配置中获取
      minified: isMinified,
      sourcemap: hasSourceMap,
      hmr: hasHMR
    }
  }
  
  // 🔥 获取运行时指标
  getRuntimeMetrics(): PerformanceReport['runtimeMetrics'] {
    let fps = 60
    let memoryUsage = 0
    
    // 获取内存使用
    const memory = (performance as any).memory
    if (memory) {
      memoryUsage = Math.round(memory.usedJSHeapSize / 1048576) // MB
    }
    
    // 计算FPS（简化版本）
    const paintEntries = performance.getEntriesByType('paint')
    if (paintEntries.length > 0) {
      const lastPaint = paintEntries[paintEntries.length - 1]
      fps = Math.round(1000 / Math.max(16, lastPaint.duration))
    }
    
    // DOM节点数量
    const domNodes = document.querySelectorAll('*').length
    
    // 事件监听器数量（估算）
    const eventListeners = this.estimateEventListeners()
    
    // 渲染次数（从React DevTools中获取，如果可用）
    const renderCount = this.getRenderCount()
    
    return {
      fps,
      memoryUsage,
      domNodes,
      eventListeners,
      renderCount
    }
  }
  
  private estimateEventListeners(): number {
    // 简化的事件监听器估算
    let count = 0
    
    // 检查常见的事件监听器
    const elements = document.querySelectorAll('*')
    elements.forEach(el => {
      const events = ['click', 'mouseover', 'keydown', 'scroll', 'resize']
      events.forEach(event => {
        if ((el as any)[`on${event}`]) count++
      })
    })
    
    return count
  }
  
  private getRenderCount(): number {
    // 尝试从React DevTools获取渲染次数
    const reactFiber = (document.querySelector('#root') as any)?._reactInternalFiber
    if (reactFiber) {
      return reactFiber.actualDuration || 0
    }
    return 0
  }
  
  // 🔥 分析Bundle信息
  async getBundleInfo(): Promise<PerformanceReport['bundleInfo']> {
    const chunks: PerformanceReport['bundleInfo']['chunks'] = []
    let totalSize = 0
    
    // 分析JavaScript文件
    const scripts = Array.from(document.querySelectorAll('script[src]'))
    for (const script of scripts) {
      const src = (script as HTMLScriptElement).src
      if (src && !src.startsWith('data:')) {
        try {
          const response = await fetch(src, { method: 'HEAD' })
          const size = parseInt(response.headers.get('content-length') || '0')
          chunks.push({
            name: src.split('/').pop() || 'unknown',
            size,
            type: 'js'
          })
          totalSize += size
        } catch (e) {
          // 忽略跨域错误
        }
      }
    }
    
    // 分析CSS文件
    const styles = Array.from(document.querySelectorAll('link[rel="stylesheet"]'))
    for (const style of styles) {
      const href = (style as HTMLLinkElement).href
      if (href && !href.startsWith('data:')) {
        try {
          const response = await fetch(href, { method: 'HEAD' })
          const size = parseInt(response.headers.get('content-length') || '0')
          chunks.push({
            name: href.split('/').pop() || 'unknown',
            size,
            type: 'css'
          })
          totalSize += size
        } catch (e) {
          // 忽略跨域错误
        }
      }
    }
    
    // 获取页面加载时间
    const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming
    const loadTime = navigation ? navigation.loadEventEnd - navigation.fetchStart : 0
    
    return {
      totalSize,
      chunks,
      loadTime
    }
  }
  
  // 🔥 优化分析
  getOptimizationInfo(): PerformanceReport['optimization'] {
    const longTasks = this.metrics.get('longtask')?.length || 0
    
    // 检测React DevTools
    const hasReactDevtools = !!(window as any).__REACT_DEVTOOLS_GLOBAL_HOOK__
    
    // 检测React StrictMode（在开发模式下）
    const hasStrictMode = document.querySelector('[data-reactroot]') !== null
    
    // 检测未使用的CSS（简化版本）
    const unusedCSS = this.detectUnusedCSS()
    
    return {
      reactDevtools: hasReactDevtools,
      reactStrictMode: hasStrictMode,
      longTasks,
      unusedCSS
    }
  }
  
  private detectUnusedCSS(): number {
    let unusedRules = 0
    
    try {
      const styleSheets = Array.from(document.styleSheets)
      styleSheets.forEach(sheet => {
        try {
          const rules = Array.from(sheet.cssRules || [])
          rules.forEach(rule => {
            if (rule instanceof CSSStyleRule) {
              const selector = rule.selectorText
              if (selector && !document.querySelector(selector)) {
                unusedRules++
              }
            }
          })
        } catch (e) {
          // 跨域样式表可能无法访问
        }
      })
    } catch (e) {
      console.warn('Unable to analyze CSS rules:', e)
    }
    
    return unusedRules
  }
  
  // 🔥 生成性能建议
  generateRecommendations(report: Partial<PerformanceReport>): string[] {
    const recommendations: string[] = []
    
    if (report.mode === 'development') {
      recommendations.push('🚨 当前为开发模式，请在生产构建中测试性能')
      if (report.buildInfo?.hmr) {
        recommendations.push('✅ HMR已启用，开发体验良好')
      }
      if (report.buildInfo?.sourcemap) {
        recommendations.push('⚠️ 开发模式包含source map，影响加载速度')
      }
    }
    
    if (report.mode === 'production') {
      if (!report.buildInfo?.minified) {
        recommendations.push('🚨 生产代码未压缩，建议启用minification')
      }
      if (report.buildInfo?.sourcemap) {
        recommendations.push('⚠️ 生产环境包含source map，建议移除')
      }
    }
    
    if (report.runtimeMetrics?.fps && report.runtimeMetrics.fps < 30) {
      recommendations.push('🐌 帧率过低，建议优化渲染性能')
    }
    
    if (report.runtimeMetrics?.memoryUsage && report.runtimeMetrics.memoryUsage > 100) {
      recommendations.push('💾 内存使用过高，检查内存泄漏')
    }
    
    if (report.runtimeMetrics?.domNodes && report.runtimeMetrics.domNodes > 1500) {
      recommendations.push('🌳 DOM节点过多，考虑虚拟滚动')
    }
    
    if (report.optimization?.longTasks && report.optimization.longTasks > 5) {
      recommendations.push('⏰ 检测到长任务，考虑代码分割或Web Workers')
    }
    
    if (report.bundleInfo?.totalSize && report.bundleInfo.totalSize > 1024 * 1024) {
      recommendations.push('📦 Bundle体积过大，建议启用代码分割')
    }
    
    if (report.optimization?.unusedCSS && report.optimization.unusedCSS > 100) {
      recommendations.push('🎨 检测到未使用的CSS，建议启用CSS Tree Shaking')
    }
    
    return recommendations
  }
  
  // 🔥 生成完整性能报告
  async generateReport(): Promise<PerformanceReport> {
    const mode = this.detectMode()
    const buildInfo = this.getBuildInfo()
    const runtimeMetrics = this.getRuntimeMetrics()
    const bundleInfo = await this.getBundleInfo()
    const optimization = this.getOptimizationInfo()
    
    const partialReport = {
      mode,
      buildInfo,
      runtimeMetrics,
      bundleInfo,
      optimization
    }
    
    const recommendations = this.generateRecommendations(partialReport)
    
    return {
      ...partialReport,
      recommendations
    }
  }
  
  // 🔥 启动持续监控
  startMonitoring(callback: (report: PerformanceReport) => void, interval = 10000) {
    const monitor = async () => {
      const report = await this.generateReport()
      callback(report)
    }
    
    // 立即执行一次
    monitor()
    
    // 定期执行
    return setInterval(monitor, interval)
  }
  
  // 🔥 清理资源
  cleanup() {
    if (this.performanceObserver) {
      this.performanceObserver.disconnect()
      this.performanceObserver = null
    }
    this.metrics.clear()
  }
}

// 🔥 导出便捷使用的函数
export const analyzePerformance = () => {
  return PerformanceAnalyzer.getInstance().generateReport()
}

export const startPerformanceMonitoring = (
  callback: (report: PerformanceReport) => void,
  interval?: number
) => {
  return PerformanceAnalyzer.getInstance().startMonitoring(callback, interval)
}

export const getPerformanceRecommendations = async () => {
  const report = await analyzePerformance()
  return report.recommendations
}

// 🔥 React Hook for performance monitoring
export const usePerformanceMonitoring = (enabled = true) => {
  const [report, setReport] = React.useState<PerformanceReport | null>(null)
  const [loading, setLoading] = React.useState(true)
  
  React.useEffect(() => {
    if (!enabled) return
    
    let intervalId: NodeJS.Timeout
    
    const analyzer = PerformanceAnalyzer.getInstance()
    
    // 初始分析
    analyzer.generateReport().then(initialReport => {
      setReport(initialReport)
      setLoading(false)
    })
    
    // 持续监控
    intervalId = analyzer.startMonitoring((newReport) => {
      setReport(newReport)
    }, 30000) // 30秒更新一次
    
    return () => {
      if (intervalId) {
        clearInterval(intervalId)
      }
      analyzer.cleanup()
    }
  }, [enabled])
  
  return { report, loading, refresh: () => setLoading(true) }
}

export default PerformanceAnalyzer