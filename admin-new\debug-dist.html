<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>构建诊断 - 评语灵感君</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            border-left: 4px solid;
        }
        .status.success {
            background: #f0f9ff;
            border-color: #0ea5e9;
            color: #0c4a6e;
        }
        .status.error {
            background: #fef2f2;
            border-color: #ef4444;
            color: #991b1b;
        }
        .status.warning {
            background: #fffbeb;
            border-color: #f59e0b;
            color: #92400e;
        }
        .log {
            background: #1f2937;
            color: #f9fafb;
            padding: 15px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
            margin: 10px 0;
        }
        button {
            background: #3b82f6;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #2563eb;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #e5e7eb;
            border-radius: 6px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 构建文件诊断工具</h1>
        <p>这个工具将帮助诊断为什么构建的文件无法正常加载</p>

        <div class="test-section">
            <h3>📁 文件存在性检查</h3>
            <div id="file-status"></div>
            <button onclick="checkFiles()">检查文件</button>
        </div>

        <div class="test-section">
            <h3>🎨 CSS加载测试</h3>
            <div id="css-status"></div>
            <button onclick="testCSS()">测试CSS</button>
        </div>

        <div class="test-section">
            <h3>📜 JavaScript加载测试</h3>
            <div id="js-status"></div>
            <button onclick="testJS()">测试JavaScript</button>
        </div>

        <div class="test-section">
            <h3>🌐 网络请求测试</h3>
            <div id="network-status"></div>
            <button onclick="testNetwork()">测试网络</button>
        </div>

        <div class="test-section">
            <h3>📊 控制台日志</h3>
            <div class="log" id="console-log"></div>
            <button onclick="clearLog()">清空日志</button>
        </div>

        <div class="test-section">
            <h3>🔧 修复建议</h3>
            <div id="suggestions"></div>
        </div>
    </div>

    <script>
        let logContainer = document.getElementById('console-log');
        
        function log(message, type = 'info') {
            const time = new Date().toLocaleTimeString();
            const color = type === 'error' ? '#ef4444' : type === 'success' ? '#10b981' : '#6b7280';
            logContainer.innerHTML += `<div style="color: ${color}">[${time}] ${message}</div>`;
            logContainer.scrollTop = logContainer.scrollHeight;
            console.log(message);
        }

        function clearLog() {
            logContainer.innerHTML = '';
        }

        function setStatus(elementId, message, type) {
            const element = document.getElementById(elementId);
            element.innerHTML = `<div class="status ${type}">${message}</div>`;
        }

        async function checkFiles() {
            log('开始检查文件存在性...', 'info');
            
            const files = [
                './assets/index-CBWRjXXh.js',
                './assets/index-BhJlCHh7.css',
                './assets/PerformanceOptimizer-BeP-JUT4.js'
            ];

            let allExists = true;
            let results = [];

            for (const file of files) {
                try {
                    const response = await fetch(file, { method: 'HEAD' });
                    if (response.ok) {
                        results.push(`✅ ${file} - 存在 (${response.status})`);
                        log(`文件存在: ${file}`, 'success');
                    } else {
                        results.push(`❌ ${file} - 不存在 (${response.status})`);
                        log(`文件不存在: ${file} - ${response.status}`, 'error');
                        allExists = false;
                    }
                } catch (error) {
                    results.push(`❌ ${file} - 加载失败: ${error.message}`);
                    log(`文件加载失败: ${file} - ${error.message}`, 'error');
                    allExists = false;
                }
            }

            setStatus('file-status', results.join('<br>'), allExists ? 'success' : 'error');
        }

        async function testCSS() {
            log('开始测试CSS加载...', 'info');
            
            try {
                const response = await fetch('./assets/index-BhJlCHh7.css');
                if (response.ok) {
                    const cssText = await response.text();
                    log(`CSS文件大小: ${cssText.length} 字符`, 'success');
                    
                    // 检查是否包含关键样式
                    if (cssText.includes(':root') && cssText.includes('--bg-primary')) {
                        setStatus('css-status', '✅ CSS文件加载正常，包含主题变量', 'success');
                        log('CSS内容验证通过', 'success');
                    } else {
                        setStatus('css-status', '⚠️ CSS文件加载但内容可能不完整', 'warning');
                        log('CSS内容验证失败', 'error');
                    }
                } else {
                    setStatus('css-status', `❌ CSS文件加载失败: ${response.status}`, 'error');
                    log(`CSS加载失败: ${response.status}`, 'error');
                }
            } catch (error) {
                setStatus('css-status', `❌ CSS加载异常: ${error.message}`, 'error');
                log(`CSS加载异常: ${error.message}`, 'error');
            }
        }

        async function testJS() {
            log('开始测试JavaScript加载...', 'info');
            
            try {
                const response = await fetch('./assets/index-CBWRjXXh.js');
                if (response.ok) {
                    const jsText = await response.text();
                    log(`JS文件大小: ${jsText.length} 字符`, 'success');
                    
                    // 检查是否包含React相关代码
                    if (jsText.includes('React') && jsText.includes('ReactDOM')) {
                        setStatus('js-status', '✅ JavaScript文件加载正常，包含React代码', 'success');
                        log('JavaScript内容验证通过', 'success');
                        
                        // 尝试动态加载
                        try {
                            const script = document.createElement('script');
                            script.type = 'module';
                            script.src = './assets/index-CBWRjXXh.js';
                            script.onload = () => {
                                log('JavaScript模块加载成功', 'success');
                            };
                            script.onerror = (error) => {
                                log(`JavaScript模块加载失败: ${error}`, 'error');
                            };
                            document.head.appendChild(script);
                        } catch (error) {
                            log(`动态加载失败: ${error.message}`, 'error');
                        }
                    } else {
                        setStatus('js-status', '⚠️ JavaScript文件加载但内容可能不完整', 'warning');
                        log('JavaScript内容验证失败', 'error');
                    }
                } else {
                    setStatus('js-status', `❌ JavaScript文件加载失败: ${response.status}`, 'error');
                    log(`JavaScript加载失败: ${response.status}`, 'error');
                }
            } catch (error) {
                setStatus('js-status', `❌ JavaScript加载异常: ${error.message}`, 'error');
                log(`JavaScript加载异常: ${error.message}`, 'error');
            }
        }

        async function testNetwork() {
            log('开始测试网络环境...', 'info');
            
            const protocol = window.location.protocol;
            const host = window.location.host;
            const pathname = window.location.pathname;
            
            log(`协议: ${protocol}`, 'info');
            log(`主机: ${host || '本地文件'}`, 'info');
            log(`路径: ${pathname}`, 'info');
            
            let suggestions = [];
            
            if (protocol === 'file:') {
                suggestions.push('⚠️ 使用file://协议可能导致ES模块加载失败');
                suggestions.push('💡 建议使用HTTP服务器访问，如: http://localhost:8090');
                setStatus('network-status', '⚠️ 检测到file://协议，可能影响模块加载', 'warning');
            } else {
                suggestions.push('✅ 使用HTTP协议，模块加载应该正常');
                setStatus('network-status', '✅ 网络环境正常', 'success');
            }
            
            // 检查CORS
            try {
                await fetch('./assets/index-CBWRjXXh.js', { method: 'HEAD' });
                suggestions.push('✅ 资源访问正常，无CORS问题');
            } catch (error) {
                suggestions.push(`❌ 资源访问失败: ${error.message}`);
            }
            
            document.getElementById('suggestions').innerHTML = suggestions.map(s => `<div class="status ${s.startsWith('✅') ? 'success' : s.startsWith('⚠️') ? 'warning' : 'error'}">${s}</div>`).join('');
        }

        // 捕获所有错误
        window.addEventListener('error', (event) => {
            log(`全局错误: ${event.error?.message || event.message}`, 'error');
        });

        window.addEventListener('unhandledrejection', (event) => {
            log(`未处理的Promise拒绝: ${event.reason}`, 'error');
        });

        // 页面加载完成后自动运行检查
        window.addEventListener('DOMContentLoaded', () => {
            log('诊断工具加载完成', 'success');
            setTimeout(() => {
                checkFiles();
                testNetwork();
            }, 1000);
        });
    </script>
</body>
</html>
