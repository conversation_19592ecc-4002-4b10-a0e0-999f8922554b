const express = require('express')
const path = require('path')

const app = express()
const PORT = 8090

// 设置静态文件目录
app.use(express.static(path.join(__dirname, 'dist')))

// 处理所有路由，返回index.html（用于SPA路由）
app.get('*', (req, res) => {
  res.sendFile(path.join(__dirname, 'dist', 'index.html'))
})

app.listen(PORT, () => {
  console.log(`🚀 测试服务器启动成功！`)
  console.log(`📍 访问地址: http://localhost:${PORT}`)
  console.log(`📁 静态文件目录: ${path.join(__dirname, 'dist')}`)
  console.log(`\n💡 这是构建后的生产版本测试`)
  console.log(`✅ 如果此版本正常，说明构建成功`)
  console.log(`🌐 可以直接部署到微信云开发静态网站托管`)
})

// 优雅关闭
process.on('SIGTERM', () => {
  console.log('\n🛑 正在关闭测试服务器...')
  process.exit(0)
})

process.on('SIGINT', () => {
  console.log('\n🛑 正在关闭测试服务器...')
  process.exit(0)
})
