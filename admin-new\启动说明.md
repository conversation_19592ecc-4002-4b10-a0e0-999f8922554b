# 🚀 快速启动指南

## ⚡ 一键解决CSP问题

### 方法1：推荐使用（最简单）

直接双击运行：
```
QUICK_FIX_CSP.bat
```

这个脚本会：
1. 自动启动开发服务器
2. 启动Chrome无安全模式
3. 自动打开 http://localhost:8080

### 方法2：手动操作

**第1步：启动开发服务器**
```bash
npm run dev
```

**第2步：使用Chrome无安全模式打开**
```bash
chrome.exe --disable-web-security --user-data-dir="C:\temp\chrome" http://localhost:8080
```

## 📋 测试清单

登录后测试以下功能：
- ✅ 账号：`admin` 密码：`admin123`
- ✅ 全局搜索按钮（右上角搜索图标）
- ✅ 消息通知按钮（右上角铃铛图标）
- ✅ 用户菜单（点击头像）- 个人资料、账户设置
- ✅ 安全退出功能

## ❓ 常见问题

**Q: 为什么要用Chrome无安全模式？**
A: CSP是浏览器的安全策略，在开发环境下经常会阻止一些合法的代码执行（如HMR、eval等）。无安全模式可以完全绕过这些限制。

**Q: 这样安全吗？**
A: 只在开发环境使用，生产环境不会有这个问题。开发时用临时的Chrome实例，不会影响正常浏览。

**Q: 如果还是不行怎么办？**
A: 检查是否有企业防火墙、杀毒软件或其他网络策略在干扰。

---

*修复内容：全局搜索、消息通知、用户菜单、安全退出 + CSP解决方案*