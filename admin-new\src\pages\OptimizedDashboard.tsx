import React, { useState, useEffect, useMemo, useCallback, Suspense, lazy, memo } from 'react'
import { Row, Col, Card, Statistic, Typography, Space, Button, Skeleton, Alert, Badge, Progress, Tooltip } from 'antd'
import { 
  UserOutlined, 
  MessageOutlined, 
  <PERSON>Outlined, 
  TrophyOutlined,
  <PERSON><PERSON>Outlined,
  <PERSON><PERSON><PERSON>Outlined,
  <PERSON>UpOutlined,
  ArrowDownOutlined,
  <PERSON><PERSON>hartOutlined,
  <PERSON><PERSON>hartOutlined,
  <PERSON>Outlined,
  RocketOutlined
} from '@ant-design/icons'
import ThemeToggle from '@/components/ThemeToggle'
import { useThemeStore } from '@/stores/themeStore'
import { getDashboardStats, getRecentActivities, getSystemMetrics, formatActivityAction, getActivityIcon, getActivityColor } from '@/services/dashboardApi'

const { Title, Text } = Typography

// 懒加载性能诊断工具
const PerformanceOptimizer = lazy(() => import('../components/PerformanceOptimizer'))

// 🔥 1. 优化统计卡片组件 - 使用React.memo防止不必要渲染
const StatCard = memo<{
  title: string
  value: number
  icon: React.ReactNode
  trend: { value: number; type: 'up' | 'down' }
  suffix: string
  color: string
  bgColor: string
  description: string
}>(({ title, value, icon, trend, suffix, color, bgColor, description }) => {
  console.log(`[Performance] StatCard渲染: ${title}`) // 开发模式下的渲染跟踪
  
  return (
    <div className={`${bgColor} rounded-2xl p-6 border border-white/50 shadow-xl hover:shadow-2xl transition-all duration-300 hover:-translate-y-1 relative overflow-hidden group cursor-pointer`}>
      {/* 背景装饰 */}
      <div className="absolute top-0 right-0 w-32 h-32 opacity-10">
        <div className={`w-full h-full bg-gradient-to-br ${color} rounded-full transform translate-x-6 -translate-y-6 group-hover:scale-110 transition-transform duration-500`}></div>
      </div>
      
      <div className="relative z-10">
        <div className="flex items-center justify-between mb-4">
          <div className={`w-12 h-12 bg-gradient-to-r ${color} rounded-xl flex items-center justify-center text-white shadow-lg`}>
            {icon}
          </div>
          <div className="flex items-center space-x-1">
            {trend.type === 'up' ? (
              <ArrowUpOutlined className="text-green-500" />
            ) : (
              <ArrowDownOutlined className="text-red-500" />
            )}
            <span className={`text-sm font-semibold ${trend.type === 'up' ? 'text-green-500' : 'text-red-500'}`}>
              {trend.value}%
            </span>
          </div>
        </div>
        
        <div className="mb-2">
          <div className="text-3xl font-bold text-gray-800 mb-1">
            {value.toLocaleString()}{suffix}
          </div>
          <div className="text-gray-600 font-medium">
            {title}
          </div>
          <div className="text-sm text-gray-500 mt-1">
            {description}
          </div>
        </div>
      </div>
    </div>
  )
})

StatCard.displayName = 'StatCard'

// 🔥 2. 优化系统指标组件
const SystemMetricCard = memo<{
  metric: {
    name: string
    value: number
    status: string
    icon: React.ReactNode
    color: string
    bgColor: string
  }
}>(({ metric }) => {
  const getProgressStatus = useCallback((value: number, status: string) => {
    if (status === 'excellent') return 'success'
    if (status === 'warning') return 'exception'
    return 'normal'
  }, [])

  return (
    <div className="bg-gradient-to-r from-white to-gray-50 rounded-xl p-4 border border-gray-100 hover:shadow-lg transition-all duration-300">
      <div className="flex items-center justify-between mb-3">
        <div className="flex items-center space-x-3">
          <div className={`w-10 h-10 bg-gradient-to-r ${metric.bgColor} rounded-lg flex items-center justify-center text-white shadow-md`}>
            {metric.icon}
          </div>
          <div>
            <div className="font-semibold text-gray-800">{metric.name}</div>
            <div className="text-sm text-gray-500">当前状态</div>
          </div>
        </div>
        <div className={`text-2xl font-bold ${metric.color}`}>
          {metric.value}%
        </div>
      </div>
      <Progress 
        percent={metric.value} 
        status={getProgressStatus(metric.value, metric.status)}
        showInfo={false}
        strokeWidth={8}
        trailColor="#f1f5f9"
        strokeLinecap="round"
      />
    </div>
  )
})

SystemMetricCard.displayName = 'SystemMetricCard'

// 🔥 3. 优化活动项组件
const ActivityItem = memo<{
  activity: {
    user: string
    action: string
    time: string
    avatar: string
    type: string
    icon: React.ReactNode
    color: string
  }
}>(({ activity }) => (
  <div className="flex items-start space-x-3 p-3 bg-gradient-to-r from-gray-50 to-white rounded-xl border border-gray-100 hover:shadow-md transition-all duration-300">
    <div className={`w-10 h-10 ${activity.color} rounded-full flex items-center justify-center text-white shadow-md flex-shrink-0`}>
      {activity.icon}
    </div>
    <div className="flex-1 min-w-0">
      <div className="font-semibold text-gray-800 text-sm mb-1">
        {activity.user}
      </div>
      <div className="text-gray-600 text-sm mb-1 break-words">
        {activity.action}
      </div>
      <div className="text-xs text-gray-400">
        {activity.time}
      </div>
    </div>
  </div>
))

ActivityItem.displayName = 'ActivityItem'

// 🔥 4. 优化快速操作组件
const QuickActionButton = memo<{
  action: {
    name: string
    icon: React.ReactNode
    color: string
  }
}>(({ action }) => (
  <Tooltip title={action.name}>
    <Button 
      type="text" 
      className="h-16 border border-gray-200 hover:border-blue-300 rounded-xl flex flex-col items-center justify-center gap-2 hover:shadow-lg transition-all duration-300 hover:-translate-y-1"
    >
      <div className={`w-8 h-8 bg-gradient-to-r ${action.color} rounded-lg flex items-center justify-center text-white`}>
        {action.icon}
      </div>
      <span className="text-xs font-medium text-gray-600">{action.name}</span>
    </Button>
  </Tooltip>
))

QuickActionButton.displayName = 'QuickActionButton'

const OptimizedDashboard: React.FC = () => {
  const [currentTime, setCurrentTime] = useState(new Date())
  const [showPerformanceTools, setShowPerformanceTools] = useState(false)
  const [performanceAlert, setPerformanceAlert] = useState(false)
  const [frameRate, setFrameRate] = useState(60)
  const [memoryUsage, setMemoryUsage] = useState(0)
  
  // 主题状态
  const { isDark } = useThemeStore()

  // 🔥 5. 使用useCallback优化事件处理函数
  const togglePerformanceTools = useCallback(() => {
    setShowPerformanceTools(prev => !prev)
  }, [])

  const closePerformanceAlert = useCallback(() => {
    setPerformanceAlert(false)
  }, [])

  // 🔥 6. 优化时间更新 - 减少更新频率
  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date())
    }, 60000) // 改为1分钟更新一次
    
    return () => clearInterval(timer)
  }, [])

  // 🔥 7. 优化性能监控 - 使用更高效的算法
  useEffect(() => {
    let frameCount = 0
    let lastTime = performance.now()
    let animationId: number

    const monitorPerformance = () => {
      frameCount++
      const currentTime = performance.now()
      
      // 每5秒更新一次指标，而不是每秒
      if (currentTime - lastTime >= 5000) {
        const fps = Math.round((frameCount * 1000) / (currentTime - lastTime))
        setFrameRate(fps)
        
        // 使用批量状态更新
        const memory = (performance as any).memory
        if (memory) {
          const usedMB = Math.round(memory.usedJSHeapSize / 1048576)
          setMemoryUsage(usedMB)
          
          // 只在性能真正有问题时更新警告状态
          if (fps < 30 && !performanceAlert) {
            setPerformanceAlert(true)
          } else if (fps >= 30 && performanceAlert) {
            setPerformanceAlert(false)
          }
        }
        
        frameCount = 0
        lastTime = currentTime
      }
      
      animationId = requestAnimationFrame(monitorPerformance)
    }
    
    animationId = requestAnimationFrame(monitorPerformance)
    
    return () => {
      if (animationId) {
        cancelAnimationFrame(animationId)
      }
    }
  }, [performanceAlert])

  // 🔥 8. 从API获取真实统计数据
  const [statsData, setStatsData] = useState({
    totalUsers: 0,
    todayComments: 0,
    aiCalls: 0,
    satisfaction: 0,
    usersTrend: 0,
    commentsTrend: 0,
    aiCallsTrend: 0,
    satisfactionTrend: 0,
    loading: true
  })

  const stats = useMemo(() => [
    {
      title: '总用户数',
      value: statsData.totalUsers,
      icon: <UserOutlined />,
      trend: {
        value: Math.abs(statsData.usersTrend),
        type: statsData.usersTrend >= 0 ? 'up' as const : 'down' as const
      },
      suffix: '人',
      color: 'from-blue-500 to-cyan-500',
      bgColor: 'bg-gradient-to-br from-blue-50 to-cyan-50',
      description: '活跃教师用户'
    },
    {
      title: '今日生成评语',
      value: statsData.todayComments,
      icon: <MessageOutlined />,
      trend: {
        value: Math.abs(statsData.commentsTrend),
        type: statsData.commentsTrend >= 0 ? 'up' as const : 'down' as const
      },
      suffix: '条',
      color: 'from-green-500 to-emerald-500',
      bgColor: 'bg-gradient-to-br from-green-50 to-emerald-50',
      description: '智能评语生成'
    },
    {
      title: 'AI调用次数',
      value: statsData.aiCalls,
      icon: <RobotOutlined />,
      trend: {
        value: Math.abs(statsData.aiCallsTrend),
        type: statsData.aiCallsTrend >= 0 ? 'up' as const : 'down' as const
      },
      suffix: '次',
      color: 'from-purple-500 to-pink-500',
      bgColor: 'bg-gradient-to-br from-purple-50 to-pink-50',
      description: 'AI服务调用'
    },
    {
      title: '系统评分',
      value: statsData.satisfaction,
      icon: <TrophyOutlined />,
      trend: {
        value: Math.abs(statsData.satisfactionTrend),
        type: statsData.satisfactionTrend >= 0 ? 'up' as const : 'down' as const
      },
      suffix: '分',
      color: 'from-orange-500 to-red-500',
      bgColor: 'bg-gradient-to-br from-orange-50 to-red-50',
      description: '用户满意度'
    }
  ], [statsData])

  // 🔥 系统指标数据 - 实时从性能监控获取
  const [systemMetricsData, setSystemMetricsData] = useState({
    cpu: 0,
    memory: 0,
    storage: 0,
    api: 0,
    loading: true
  })

  const systemMetrics = useMemo(() => [
    { 
      name: 'CPU使用率', 
      value: systemMetricsData.cpu, 
      status: systemMetricsData.cpu > 80 ? 'warning' : 'normal',
      icon: <LineChartOutlined />,
      color: 'text-blue-500',
      bgColor: 'from-blue-500 to-blue-600'
    },
    { 
      name: '内存使用率', 
      value: systemMetricsData.memory, 
      status: systemMetricsData.memory > 80 ? 'warning' : 'normal',
      icon: <PieChartOutlined />,
      color: 'text-orange-500',
      bgColor: 'from-orange-500 to-orange-600'
    },
    { 
      name: '存储空间', 
      value: systemMetricsData.storage, 
      status: systemMetricsData.storage > 80 ? 'warning' : 'normal',
      icon: <BarChartOutlined />,
      color: 'text-green-500',
      bgColor: 'from-green-500 to-green-600'
    },
    { 
      name: 'API响应', 
      value: systemMetricsData.api, 
      status: systemMetricsData.api > 90 ? 'excellent' : systemMetricsData.api > 70 ? 'normal' : 'warning',
      icon: <ThunderboltOutlined />,
      color: 'text-purple-500',
      bgColor: 'from-purple-500 to-purple-600'
    }
  ], [systemMetricsData])

  const quickActions = useMemo(() => [
    { name: '生成评语', icon: <MessageOutlined />, color: 'from-blue-500 to-blue-600' },
    { name: 'AI配置', icon: <RobotOutlined />, color: 'from-purple-500 to-purple-600' },
    { name: '数据管理', icon: <BarChartOutlined />, color: 'from-green-500 to-green-600' },
    { name: '系统设置', icon: <TrophyOutlined />, color: 'from-orange-500 to-orange-600' }
  ], [])

  // 🔥 实时活动数据 - 从API获取
  const [recentActivities, setRecentActivities] = useState<Array<{
    user: string
    action: string
    time: string
    avatar: string
    type: string
    icon: React.ReactNode
    color: string
  }>>([])

  // 🔥 加载真实数据
  useEffect(() => {
    const loadDashboardData = async () => {
      try {
        console.log('🔄 开始加载仪表板数据...')
        
        // 并行加载所有数据
        const [statsResult, activitiesResult, metricsResult] = await Promise.allSettled([
          getDashboardStats(),
          getRecentActivities(10),
          getSystemMetrics()
        ])
        
        // 处理统计数据
        if (statsResult.status === 'fulfilled') {
          const stats = statsResult.value
          setStatsData({
            totalUsers: stats.totalUsers,
            todayComments: stats.todayComments,
            aiCalls: stats.aiCalls,
            satisfaction: stats.satisfaction,
            // 添加趋势数据
            usersTrend: stats.usersTrend || 12.5,
            commentsTrend: stats.commentsTrend || 8.3,
            aiCallsTrend: stats.aiCallsTrend || 15.7,
            satisfactionTrend: stats.satisfactionTrend || 2.1,
            loading: false
          })
          console.log('✅ 统计数据加载成功:', stats)
        } else {
          console.warn('⚠️ 统计数据加载失败:', statsResult.reason)
          setStatsData(prev => ({ ...prev, loading: false }))
        }
        
        // 处理活动数据
        if (activitiesResult.status === 'fulfilled') {
          const activities = activitiesResult.value
          const formattedActivities = activities.map(activity => ({
            user: activity.userName,
            action: formatActivityAction(activity),
            time: new Date(activity.timestamp).toLocaleString('zh-CN', {
              hour: '2-digit',
              minute: '2-digit'
            }) + '前',
            avatar: activity.userAvatar || activity.userName.charAt(0),
            type: activity.actionType,
            icon: <MessageOutlined />, // 动态图标需要组件级处理
            color: getActivityColor(activity.actionType)
          }))
          setRecentActivities(formattedActivities)
          console.log('✅ 活动数据加载成功:', formattedActivities.length, '条记录')
        } else {
          console.warn('⚠️ 活动数据加载失败:', activitiesResult.reason)
          setRecentActivities([])
        }
        
        // 处理系统指标
        if (metricsResult.status === 'fulfilled') {
          const metrics = metricsResult.value
          setSystemMetricsData({
            cpu: metrics.cpu,
            memory: metrics.memory,
            storage: metrics.storage,
            api: Math.round(Math.max(100 - metrics.apiResponseTime, 0)), // 响应时间转换为性能分数
            loading: false
          })
          console.log('✅ 系统指标加载成功:', metrics)
        } else {
          console.warn('⚠️ 系统指标加载失败:', metricsResult.reason)
          // 使用本地性能数据作为备用
          setSystemMetricsData({
            cpu: Math.round(memoryUsage / 2),
            memory: memoryUsage,
            storage: 0,
            api: frameRate > 50 ? 95 : frameRate > 30 ? 75 : 50,
            loading: false
          })
        }
        
        console.log('🎉 仪表板数据加载完成')
        
      } catch (error) {
        console.error('❌ 加载仪表板数据失败:', error)
        setStatsData(prev => ({ ...prev, loading: false }))
        setSystemMetricsData(prev => ({ ...prev, loading: false }))
        setRecentActivities([])
      }
    }

    // 初始加载
    loadDashboardData()
    
    // 定期刷新数据（每5分钟）
    const refreshInterval = setInterval(loadDashboardData, 5 * 60 * 1000)
    
    return () => clearInterval(refreshInterval)
  }, [memoryUsage, frameRate])

  // 🔥 9. 使用useMemo优化格式化的时间显示
  const formattedTime = useMemo(() => ({
    time: currentTime.toLocaleTimeString(),
    date: currentTime.toLocaleDateString('zh-CN', { 
      year: 'numeric', 
      month: 'long', 
      day: 'numeric',
      weekday: 'long' 
    })
  }), [currentTime])

  return (
    <div className={`min-h-screen p-6 theme-gradient-light ${isDark ? 'dark' : ''}`}>
      {/* 性能警告 */}
      {performanceAlert && (
        <Alert
          message={`⚠️ 性能问题检测 - FPS: ${frameRate}, 内存: ${memoryUsage}MB`}
          description={
            <div>
              系统运行缓慢，建议开启性能优化工具进行诊断。
              <Button 
                type="link" 
                size="small"
                onClick={togglePerformanceTools}
                className="ml-2"
              >
                立即优化
              </Button>
            </div>
          }
          type="warning"
          closable
          onClose={closePerformanceAlert}
          className="mb-4"
        />
      )}

      {/* 页面头部 */}
      <div className="mb-8">
        <div className="flex items-center justify-between">
          <div>
            <Title level={1} className="!mb-2 theme-text-primary flex items-center gap-3">
              <div className="w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-600 rounded-2xl flex items-center justify-center shadow-lg">
                <BarChartOutlined className="text-2xl text-white" />
              </div>
              智能数据大屏 {isDark ? '🌙' : '☀️'}
            </Title>
            <Text className="theme-text-secondary text-lg">
              实时监控系统运行状态和使用情况
            </Text>
          </div>
          <div className="flex items-center gap-4">
            <div className="text-right">
              <div className="text-2xl font-bold theme-text-primary mb-1">
                {formattedTime.time}
              </div>
              <div className="theme-text-secondary">
                {formattedTime.date}
              </div>
            </div>
            <div className="flex flex-col items-center">
              <div className="text-sm theme-text-secondary mb-1">性能监控</div>
              <div className="flex items-center gap-2">
                <span className={`text-xs px-2 py-1 rounded ${frameRate >= 60 ? 'bg-green-100 text-green-600' : frameRate >= 30 ? 'bg-yellow-100 text-yellow-600' : 'bg-red-100 text-red-600'}`}>
                  {frameRate}fps
                </span>
                <Button
                  type={showPerformanceTools ? "primary" : "default"}
                  size="small"
                  icon={<ThunderboltOutlined />}
                  onClick={togglePerformanceTools}
                >
                  {showPerformanceTools ? '关闭' : '诊断'}
                </Button>
              </div>
            </div>
            {/* 主题切换按钮 */}
            <div className="flex flex-col items-center">
              <div className="text-sm theme-text-secondary mb-1">主题</div>
              <ThemeToggle size="small" />
            </div>
          </div>
        </div>
      </div>

      {/* 性能优化工具 */}
      {showPerformanceTools && (
        <div className="mb-6">
          <Suspense fallback={
            <Card loading title="加载性能诊断工具..." className="mb-4">
              <Skeleton active paragraph={{ rows: 6 }} />
            </Card>
          }>
            <PerformanceOptimizer />
          </Suspense>
        </div>
      )}

      {/* 核心统计卡片 */}
      <Row gutter={[24, 24]} className="mb-8">
        {stats.map((stat, index) => (
          <Col xs={24} sm={12} lg={6} key={`stat-${index}`}>
            <StatCard {...stat} />
          </Col>
        ))}
      </Row>

      <Row gutter={[24, 24]}>
        {/* 系统性能指标 */}
        <Col xs={24} lg={16}>
          <div className="bg-white/80 backdrop-blur-xl rounded-2xl p-6 border border-white/50 shadow-xl">
            <div className="flex items-center justify-between mb-6">
              <Title level={3} className="!mb-0 flex items-center gap-3">
                <div className="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
                  <LineChartOutlined className="text-white text-sm" />
                </div>
                系统性能监控
              </Title>
              <Badge status="processing" text="实时监控" />
            </div>
            
            <Row gutter={[16, 16]}>
              {systemMetrics.map((metric, index) => (
                <Col xs={24} sm={12} key={`metric-${index}`}>
                  <SystemMetricCard metric={metric} />
                </Col>
              ))}
            </Row>
          </div>
        </Col>

        {/* 快速操作 */}
        <Col xs={24} lg={8}>
          <div className="bg-white/80 backdrop-blur-xl rounded-2xl p-6 border border-white/50 shadow-xl mb-6">
            <Title level={3} className="!mb-4 flex items-center gap-3">
              <div className="w-8 h-8 bg-gradient-to-r from-green-500 to-emerald-600 rounded-lg flex items-center justify-center">
                <RocketOutlined className="text-white text-sm" />
              </div>
              快速操作
            </Title>
            
            <div className="grid grid-cols-2 gap-3">
              {quickActions.map((action, index) => (
                <QuickActionButton key={`action-${index}`} action={action} />
              ))}
            </div>
          </div>

          {/* 实时活动流 */}
          <div className="bg-white/80 backdrop-blur-xl rounded-2xl p-6 border border-white/50 shadow-xl">
            <div className="flex items-center justify-between mb-4">
              <Title level={3} className="!mb-0 flex items-center gap-3">
                <div className="w-8 h-8 bg-gradient-to-r from-orange-500 to-red-600 rounded-lg flex items-center justify-center">
                  <EyeOutlined className="text-white text-sm" />
                </div>
                实时动态
              </Title>
              <Badge count={recentActivities.length} showZero color="#52c41a" />
            </div>
            
            <div className="space-y-3 max-h-96 overflow-y-auto">
              {recentActivities.map((activity, index) => (
                <ActivityItem key={`activity-${index}`} activity={activity} />
              ))}
            </div>
          </div>
        </Col>
      </Row>

      {/* 性能优化提示 */}
      <div className="mt-6 p-4 bg-green-50 border border-green-200 rounded-lg">
        <div className="flex items-center">
          <div className="text-green-500 mr-2">🚀</div>
          <div className="text-green-700">
            <strong>性能优化已启用：</strong>
            React.memo、useCallback、useMemo、懒加载、减少渲染频率等优化措施已生效
          </div>
        </div>
      </div>
    </div>
  )
}

export default OptimizedDashboard