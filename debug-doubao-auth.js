/**
 * 豆包AI认证格式诊断脚本
 * 测试不同的认证方式和API密钥格式
 */

console.log('🔍 开始诊断豆包AI认证格式...');

const API_KEY = '4d73215c-5512-418b-8749-db9514df3c75';
const API_URL = 'https://ark.cn-beijing.volces.com/api/v3/chat/completions';
const MODEL = 'doubao-seed-1-6-flash-250715';

// 测试数据
const testData = {
  model: MODEL,
  messages: [
    {
      role: 'user',
      content: '请简单回复"API测试成功"'
    }
  ],
  temperature: 0.7,
  max_tokens: 50,
  stream: false
};

console.log('📋 测试配置:', {
  apiUrl: API_URL,
  model: MODEL,
  apiKey: API_KEY.substring(0, 8) + '...',
  hasApiKey: !!API_KEY
});

// 方案1: Bearer认证 (当前使用的)
async function testBearerAuth() {
  console.log('\n🧪 测试方案1: Bearer认证');
  try {
    const response = await wx.request({
      url: API_URL,
      method: 'POST',
      header: {
        'Authorization': `Bearer ${API_KEY}`,
        'Content-Type': 'application/json'
      },
      data: testData,
      timeout: 30000
    });
    
    console.log('✅ Bearer认证响应:', response);
    return response;
  } catch (error) {
    console.log('❌ Bearer认证失败:', error);
    return null;
  }
}

// 方案2: API-Key认证
async function testApiKeyAuth() {
  console.log('\n🧪 测试方案2: API-Key认证');
  try {
    const response = await wx.request({
      url: API_URL,
      method: 'POST',
      header: {
        'API-Key': API_KEY,
        'Content-Type': 'application/json'
      },
      data: testData,
      timeout: 30000
    });
    
    console.log('✅ API-Key认证响应:', response);
    return response;
  } catch (error) {
    console.log('❌ API-Key认证失败:', error);
    return null;
  }
}

// 方案3: X-API-Key认证
async function testXApiKeyAuth() {
  console.log('\n🧪 测试方案3: X-API-Key认证');
  try {
    const response = await wx.request({
      url: API_URL,
      method: 'POST',
      header: {
        'X-API-Key': API_KEY,
        'Content-Type': 'application/json'
      },
      data: testData,
      timeout: 30000
    });
    
    console.log('✅ X-API-Key认证响应:', response);
    return response;
  } catch (error) {
    console.log('❌ X-API-Key认证失败:', error);
    return null;
  }
}

// 方案4: Authorization直接使用API-Key
async function testDirectApiKeyAuth() {
  console.log('\n🧪 测试方案4: Authorization直接API-Key');
  try {
    const response = await wx.request({
      url: API_URL,
      method: 'POST',
      header: {
        'Authorization': API_KEY,
        'Content-Type': 'application/json'
      },
      data: testData,
      timeout: 30000
    });
    
    console.log('✅ 直接API-Key认证响应:', response);
    return response;
  } catch (error) {
    console.log('❌ 直接API-Key认证失败:', error);
    return null;
  }
}

// 方案5: 测试不同的端点URL
async function testDifferentEndpoints() {
  console.log('\n🧪 测试方案5: 不同的API端点');
  
  const endpoints = [
    'https://ark.cn-beijing.volces.com/api/v3/chat/completions',
    'https://open.bigmodel.cn/api/paas/v4/chat/completions',
    'https://api.volcengine.com/v1/chat/completions'
  ];
  
  for (let endpoint of endpoints) {
    console.log(`测试端点: ${endpoint}`);
    try {
      const response = await wx.request({
        url: endpoint,
        method: 'POST',
        header: {
          'Authorization': `Bearer ${API_KEY}`,
          'Content-Type': 'application/json'
        },
        data: testData,
        timeout: 15000
      });
      
      console.log(`✅ 端点 ${endpoint} 响应:`, response);
      return { endpoint, response };
    } catch (error) {
      console.log(`❌ 端点 ${endpoint} 失败:`, error);
    }
  }
  
  return null;
}

// 执行所有测试
async function runAllTests() {
  console.log('🚀 开始执行所有认证测试...\n');
  
  const results = [];
  
  // 测试不同认证方式
  results.push(await testBearerAuth());
  results.push(await testApiKeyAuth());
  results.push(await testXApiKeyAuth());
  results.push(await testDirectApiKeyAuth());
  
  // 测试不同端点
  const endpointResult = await testDifferentEndpoints();
  if (endpointResult) {
    results.push(endpointResult);
  }
  
  console.log('\n📊 测试总结:');
  results.forEach((result, index) => {
    if (result && result.statusCode === 200) {
      console.log(`✅ 方案${index + 1} 成功`);
    } else {
      console.log(`❌ 方案${index + 1} 失败`);
    }
  });
  
  // 找到成功的方案
  const successfulResult = results.find(r => r && r.statusCode === 200);
  if (successfulResult) {
    console.log('\n🎉 找到可用的认证方式!');
    console.log('成功响应数据:', successfulResult.data);
  } else {
    console.log('\n😞 所有认证方式都失败了');
    console.log('建议:');
    console.log('1. 检查API密钥是否有效');
    console.log('2. 确认API端点URL是否正确');
    console.log('3. 检查豆包AI的官方文档确认认证格式');
  }
}

// 执行测试（在小程序开发者工具控制台中运行）
runAllTests().catch(error => {
  console.error('测试执行失败:', error);
});

console.log('📝 使用说明:');
console.log('1. 在小程序开发者工具的控制台中复制粘贴这个脚本');
console.log('2. 执行后观察哪种认证方式成功');
console.log('3. 将成功的认证方式应用到云函数中');