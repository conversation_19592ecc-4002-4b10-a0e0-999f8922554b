@echo off
echo.
echo ==========================================
echo   Chrome无CSP启动 + 开发服务器
echo ==========================================
echo.

echo 第1步：启动开发服务器...
start "Dev Server" cmd /k "cd /d \"%~dp0\" && npm run dev"

echo 等待开发服务器启动...
timeout /t 5 /nobreak >nul

echo.
echo 第2步：启动Chrome无安全模式...

REM 创建临时Chrome目录
set CHROME_TEMP=%TEMP%\chrome-no-security-%RANDOM%
mkdir "%CHROME_TEMP%" 2>nul

echo 临时Chrome目录: %CHROME_TEMP%
echo.

REM 启动Chrome with所有安全功能禁用
start "Chrome No Security" chrome.exe ^
  --disable-web-security ^
  --disable-features=VizDisplayCompositor ^
  --disable-site-isolation-trials ^
  --disable-extensions ^
  --disable-plugins ^
  --no-sandbox ^
  --disable-dev-shm-usage ^
  --allow-running-insecure-content ^
  --disable-background-timer-throttling ^
  --disable-backgrounding-occluded-windows ^
  --disable-renderer-backgrounding ^
  --disable-hang-monitor ^
  --disable-prompt-on-repost ^
  --no-first-run ^
  --no-default-browser-check ^
  --user-data-dir="%CHROME_TEMP%" ^
  "http://localhost:8080"

echo.
echo ✅ Chrome无安全模式已启动！
echo 📱 访问地址: http://localhost:8080
echo ⚠️  此Chrome实例禁用了所有安全功能
echo 💡 CSP、同源策略等都已禁用
echo.
echo 按任意键关闭...
pause >nul

REM 清理临时目录
rmdir /s /q "%CHROME_TEMP%" 2>nul