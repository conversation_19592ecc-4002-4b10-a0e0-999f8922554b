/**
 * 前端监控集成服务
 * 2025年企业级监控系统前端集成
 */

import { monitoring } from '../utils/monitoring'
import { modernStore } from '../store/modernStore'

// 监控配置类型
interface MonitoringConfig {
  enableRealTimeSync: boolean
  syncInterval: number
  maxLocalStorage: number
  enableAutoReport: boolean
}

// 监控统计类型
interface MonitoringStats {
  dashboard: {
    errors: { total: number }
    performance: { total: number; slowOperations: number; avgDuration: number }
    users: { totalEvents: number; uniqueUsers: number }
    alerts: { total: number; critical: number; pending: number }
    health: { status: string; errors: number; slowOperations: number }
  }
  timeRange: { startDate: Date; endDate: Date; days: number }
  lastUpdated: Date
}

class MonitoringService {
  private config: MonitoringConfig
  private syncTimer: number | null = null
  private isInitialized = false

  constructor() {
    this.config = {
      enableRealTimeSync: true,
      syncInterval: 30000, // 30秒
      maxLocalStorage: 1000, // 最大本地存储条数
      enableAutoReport: true
    }
  }

  /**
   * 初始化监控服务
   */
  async init(): Promise<void> {
    if (this.isInitialized) return

    try {
      // 设置监控用户ID
      const state = modernStore.getState()
      if (state.user.info?._id) {
        monitoring.setUserId(state.user.info._id)
      }

      // 启动实时同步
      if (this.config.enableRealTimeSync) {
        this.startRealTimeSync()
      }

      // 清理过期的本地监控数据
      this.cleanupLocalData()

      // 监听应用状态变化
      this.setupAppStateListeners()

      // 监听网络状态变化
      this.setupNetworkListeners()

      this.isInitialized = true
      
      monitoring.trackEvent('monitoring_service_initialized', {
        config: this.config
      })

      console.log('📊 Monitoring Service initialized')
    } catch (error) {
      monitoring.captureError({
        message: `监控服务初始化失败: ${error}`,
        category: 'logic',
        level: 'error'
      })
      throw error
    }
  }

  /**
   * 启动实时同步
   */
  private startRealTimeSync(): void {
    if (this.syncTimer) {
      clearInterval(this.syncTimer)
    }

    this.syncTimer = setInterval(() => {
      this.syncLocalDataToCloud()
    }, this.config.syncInterval)
  }

  /**
   * 停止实时同步
   */
  private stopRealTimeSync(): void {
    if (this.syncTimer) {
      clearInterval(this.syncTimer)
      this.syncTimer = null
    }
  }

  /**
   * 同步本地数据到云端
   */
  private async syncLocalDataToCloud(): Promise<void> {
    try {
      const localData = this.getLocalMonitoringData()
      
      if (localData.length === 0) return

      // 批量上报数据
      const batchSize = 10
      for (let i = 0; i < localData.length; i += batchSize) {
        const batch = localData.slice(i, i + batchSize)
        
        try {
          await Promise.all(
            batch.map(data => this.reportToCloud(data.type, data.data))
          )
          
          // 上报成功后删除本地数据
          batch.forEach(data => {
            this.removeLocalData(data.id)
          })
        } catch (error) {
          console.warn('批量上报失败:', error)
          break // 停止当前批次，下次重试
        }
      }
    } catch (error) {
      monitoring.captureError({
        message: `同步监控数据失败: ${error}`,
        category: 'api',
        level: 'warning'
      })
    }
  }

  /**
   * 上报数据到云端
   */
  private async reportToCloud(type: string, data: any): Promise<void> {
    try {
      const result = await wx.cloud.callFunction({
        name: 'reportMonitoring',
        data: {
          type,
          data,
          sessionId: monitoring.getMonitoringStats().sessionId,
          timestamp: Date.now()
        }
      })

      if (!result.result?.success) {
        throw new Error(result.result?.error || '上报失败')
      }
    } catch (error) {
      // 上报失败时保存到本地，等待下次重试
      this.saveLocalData(type, data)
      throw error
    }
  }

  /**
   * 获取监控统计数据
   */
  async getMonitoringStats(days = 7): Promise<MonitoringStats> {
    try {
      const result = await wx.cloud.callFunction({
        name: 'getMonitoringStats',
        data: {
          action: 'getDashboard',
          params: { days }
        }
      })

      if (!result.result?.success) {
        throw new Error(result.result?.error || '获取监控统计失败')
      }

      return result.result.data
    } catch (error) {
      monitoring.captureError({
        message: `获取监控统计失败: ${error}`,
        category: 'api',
        level: 'error'
      })
      throw error
    }
  }

  /**
   * 获取错误统计
   */
  async getErrorStats(days = 7, category?: string): Promise<any> {
    try {
      const result = await wx.cloud.callFunction({
        name: 'getMonitoringStats',
        data: {
          action: 'getErrorStats',
          params: { days, category }
        }
      })

      if (!result.result?.success) {
        throw new Error(result.result?.error || '获取错误统计失败')
      }

      return result.result.data
    } catch (error) {
      monitoring.captureError({
        message: `获取错误统计失败: ${error}`,
        category: 'api',
        level: 'error'
      })
      throw error
    }
  }

  /**
   * 获取性能统计
   */
  async getPerformanceStats(days = 7, type?: string): Promise<any> {
    try {
      const result = await wx.cloud.callFunction({
        name: 'getMonitoringStats',
        data: {
          action: 'getPerformanceStats',
          params: { days, type }
        }
      })

      if (!result.result?.success) {
        throw new Error(result.result?.error || '获取性能统计失败')
      }

      return result.result.data
    } catch (error) {
      monitoring.captureError({
        message: `获取性能统计失败: ${error}`,
        category: 'api',
        level: 'error'
      })
      throw error
    }
  }

  /**
   * 获取用户行为统计
   */
  async getUserBehaviorStats(days = 7): Promise<any> {
    try {
      const result = await wx.cloud.callFunction({
        name: 'getMonitoringStats',
        data: {
          action: 'getUserBehaviorStats',
          params: { days }
        }
      })

      if (!result.result?.success) {
        throw new Error(result.result?.error || '获取用户行为统计失败')
      }

      return result.result.data
    } catch (error) {
      monitoring.captureError({
        message: `获取用户行为统计失败: ${error}`,
        category: 'api',
        level: 'error'
      })
      throw error
    }
  }

  /**
   * 获取告警信息
   */
  async getAlerts(status = 'all', limit = 50): Promise<any> {
    try {
      const result = await wx.cloud.callFunction({
        name: 'getMonitoringStats',
        data: {
          action: 'getAlerts',
          params: { status, limit }
        }
      })

      if (!result.result?.success) {
        throw new Error(result.result?.error || '获取告警信息失败')
      }

      return result.result.data
    } catch (error) {
      monitoring.captureError({
        message: `获取告警信息失败: ${error}`,
        category: 'api',
        level: 'error'
      })
      throw error
    }
  }

  /**
   * 获取系统健康检查
   */
  async getHealthCheck(): Promise<any> {
    try {
      const result = await wx.cloud.callFunction({
        name: 'getMonitoringStats',
        data: {
          action: 'getHealthCheck'
        }
      })

      if (!result.result?.success) {
        throw new Error(result.result?.error || '系统健康检查失败')
      }

      return result.result.data
    } catch (error) {
      monitoring.captureError({
        message: `系统健康检查失败: ${error}`,
        category: 'api',
        level: 'error'
      })
      throw error
    }
  }

  /**
   * 设置应用状态监听器
   */
  private setupAppStateListeners(): void {
    // 监听store状态变化
    modernStore.subscribe((state, action) => {
      // 记录重要的状态变化
      if (action.type === 'SET_LOGIN_STATUS') {
        monitoring.trackEvent('user_login_status_changed', {
          isLogin: action.payload,
          timestamp: Date.now()
        })
      }

      if (action.type === 'SET_ERROR') {
        if (action.payload) {
          monitoring.captureError({
            message: action.payload,
            category: 'ui',
            level: 'error'
          })
        }
      }
    })
  }

  /**
   * 设置网络状态监听器
   */
  private setupNetworkListeners(): void {
    // 监听网络状态变化
    wx.onNetworkStatusChange((res) => {
      modernStore.dispatch({
        type: 'SET_NETWORK_STATUS',
        payload: res.isConnected ? 'online' : 'offline'
      })

      monitoring.trackEvent('network_status_changed', {
        isConnected: res.isConnected,
        networkType: res.networkType
      })

      // 网络恢复时尝试同步数据
      if (res.isConnected && this.config.enableRealTimeSync) {
        setTimeout(() => {
          this.syncLocalDataToCloud()
        }, 1000)
      }
    })
  }

  /**
   * 获取本地监控数据
   */
  private getLocalMonitoringData(): Array<{ id: string; type: string; data: any }> {
    try {
      const data = wx.getStorageSync('monitoring_pending') || []
      return data
    } catch (error) {
      console.warn('获取本地监控数据失败:', error)
      return []
    }
  }

  /**
   * 保存本地监控数据
   */
  private saveLocalData(type: string, data: any): void {
    try {
      const localData = this.getLocalMonitoringData()
      const newItem = {
        id: `${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        type,
        data,
        timestamp: Date.now()
      }

      localData.push(newItem)

      // 限制本地存储数量
      if (localData.length > this.config.maxLocalStorage) {
        localData.splice(0, localData.length - this.config.maxLocalStorage)
      }

      wx.setStorageSync('monitoring_pending', localData)
    } catch (error) {
      console.warn('保存本地监控数据失败:', error)
    }
  }

  /**
   * 删除本地监控数据
   */
  private removeLocalData(id: string): void {
    try {
      const localData = this.getLocalMonitoringData()
      const filteredData = localData.filter(item => item.id !== id)
      wx.setStorageSync('monitoring_pending', filteredData)
    } catch (error) {
      console.warn('删除本地监控数据失败:', error)
    }
  }

  /**
   * 清理本地数据
   */
  private cleanupLocalData(): void {
    try {
      const localData = this.getLocalMonitoringData()
      const oneDayAgo = Date.now() - 24 * 60 * 60 * 1000

      const validData = localData.filter(item => item.timestamp > oneDayAgo)
      
      if (validData.length !== localData.length) {
        wx.setStorageSync('monitoring_pending', validData)
        console.log(`🧹 清理了${localData.length - validData.length}条过期监控数据`)
      }
    } catch (error) {
      console.warn('清理本地监控数据失败:', error)
    }
  }

  /**
   * 更新配置
   */
  updateConfig(newConfig: Partial<MonitoringConfig>): void {
    this.config = { ...this.config, ...newConfig }
    
    // 重新启动同步
    if (this.config.enableRealTimeSync) {
      this.startRealTimeSync()
    } else {
      this.stopRealTimeSync()
    }

    monitoring.trackEvent('monitoring_config_updated', {
      config: this.config
    })
  }

  /**
   * 获取监控服务状态
   */
  getStatus(): {
    isInitialized: boolean
    config: MonitoringConfig
    localDataCount: number
    lastSyncTime: number | null
  } {
    return {
      isInitialized: this.isInitialized,
      config: this.config,
      localDataCount: this.getLocalMonitoringData().length,
      lastSyncTime: null // TODO: 实现最后同步时间记录
    }
  }

  /**
   * 销毁监控服务
   */
  destroy(): void {
    this.stopRealTimeSync()
    this.isInitialized = false
    
    monitoring.trackEvent('monitoring_service_destroyed')
    console.log('📊 Monitoring Service destroyed')
  }
}

// 创建全局监控服务实例
const monitoringService = new MonitoringService()

export { MonitoringService, monitoringService }
export type { MonitoringConfig, MonitoringStats }
