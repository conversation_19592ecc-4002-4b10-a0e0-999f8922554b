<!--环境修复页面-->
<view class="container">
  <view class="header">
    <text class="title">🔧 环境诊断与修复</text>
    <text class="subtitle">解决云开发环境配置问题</text>
  </view>

  <!-- 检测状态 -->
  <view class="status-card">
    <view class="status-header">
      <text class="status-title">环境检测状态</text>
      <view class="status-indicator {{result && result.success ? 'success' : 'error'}}"></view>
    </view>
    
    <view class="status-content">
      <view class="status-item">
        <text class="label">当前环境ID:</text>
        <text class="value">{{currentEnvId}}</text>
      </view>
      
      <view class="status-item" wx:if="{{errorInfo}}">
        <text class="label">错误信息:</text>
        <text class="value error">{{errorInfo}}</text>
      </view>
      
      <view class="status-item">
        <text class="label">检测时间:</text>
        <text class="value">{{checking ? '检测中...' : '刚刚'}}</text>
      </view>
    </view>
  </view>

  <!-- 操作按钮 -->
  <view class="actions">
    <button 
      class="btn primary" 
      bindtap="checkEnvironment" 
      loading="{{checking}}"
      disabled="{{checking}}"
    >
      {{checking ? '检测中...' : '重新检测'}}
    </button>
    
    <button 
      class="btn secondary" 
      bindtap="fixEnvironment"
      wx:if="{{result && !result.success}}"
    >
      自动修复
    </button>
    
    <button 
      class="btn outline" 
      bindtap="manualSetEnv"
    >
      手动设置环境ID
    </button>
  </view>

  <!-- 修复建议 -->
  <view class="suggestions" wx:if="{{suggestions.length > 0}}">
    <view class="suggestions-header">
      <text class="suggestions-title">💡 修复建议</text>
    </view>
    
    <view class="suggestions-list">
      <view 
        class="suggestion-item" 
        wx:for="{{suggestions}}" 
        wx:key="index"
      >
        <text class="suggestion-text">{{item}}</text>
      </view>
    </view>
  </view>

  <!-- 常见问题 -->
  <view class="faq">
    <view class="faq-header">
      <text class="faq-title">❓ 常见问题</text>
    </view>
    
    <view class="faq-list">
      <view class="faq-item">
        <text class="faq-question">Q: 为什么会出现环境ID验证失败？</text>
        <text class="faq-answer">A: 通常是因为配置的环境ID与实际的云开发环境不匹配，请检查微信开发者工具中的环境设置。</text>
      </view>
      
      <view class="faq-item">
        <text class="faq-question">Q: 如何查看正确的环境ID？</text>
        <text class="faq-answer">A: 在微信开发者工具中点击"云开发"按钮，在云开发控制台中可以看到当前环境的ID。</text>
      </view>
      
      <view class="faq-item">
        <text class="faq-question">Q: 修复后还是无法正常使用怎么办？</text>
        <text class="faq-answer">A: 可以尝试重启微信开发者工具，或者检查网络连接是否正常。</text>
      </view>
    </view>
  </view>

  <!-- 底部操作 -->
  <view class="footer">
    <button class="btn outline small" bindtap="showDetails">查看详细信息</button>
    <button class="btn primary small" bindtap="goHome">返回首页</button>
  </view>
</view>
