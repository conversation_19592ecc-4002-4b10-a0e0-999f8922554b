/**
 * Loading骨架屏组件
 * 提供优雅的加载状态和骨架屏效果
 */

import React from 'react'
import { Skeleton } from 'antd'

/**
 * 卡片骨架屏
 */
export const CardSkeleton: React.FC<{ count?: number }> = ({ count = 1 }) => {
  return (
    <div className="space-y-4">
      {Array.from({ length: count }, (_, i) => (
        <div key={i} className="bg-white rounded-lg p-6 shadow-sm">
          <Skeleton active paragraph={{ rows: 3 }} />
        </div>
      ))}
    </div>
  )
}

/**
 * 图表骨架屏
 */
export const ChartSkeleton: React.FC = () => {
  return (
    <div className="bg-white rounded-lg p-6 shadow-sm">
      <div className="flex items-center justify-between mb-4">
        <Skeleton.Input style={{ width: 200, height: 24 }} active />
        <Skeleton.Button style={{ width: 100, height: 32 }} active />
      </div>
      <Skeleton.Avatar style={{ width: '100%', height: 300 }} active shape="square" />
    </div>
  )
}

/**
 * 列表骨架屏
 */
export const ListSkeleton: React.FC<{ rows?: number }> = ({ rows = 3 }) => {
  return (
    <div className="space-y-4">
      {Array.from({ length: rows }, (_, i) => (
        <div key={i} className="bg-white rounded-lg p-4 shadow-sm">
          <div className="flex items-start space-x-4">
            <Skeleton.Avatar size={48} active />
            <div className="flex-1 space-y-2">
              <Skeleton.Input style={{ width: '60%', height: 20 }} active />
              <Skeleton.Input style={{ width: '40%', height: 16 }} active />
              <Skeleton.Input style={{ width: '80%', height: 16 }} active />
            </div>
          </div>
        </div>
      ))}
    </div>
  )
}

/**
 * 表格骨架屏
 */
export const TableSkeleton: React.FC<{ rows?: number; cols?: number }> = ({ 
  rows = 5, 
  cols = 4 
}) => {
  return (
    <div className="bg-white rounded-lg shadow-sm overflow-hidden">
      {/* 表头 */}
      <div className="grid grid-cols-${cols} gap-4 p-4 bg-gray-50 border-b">
        {Array.from({ length: cols }, (_, i) => (
          <Skeleton key={i} width={100} height={20} active />
        ))}
      </div>
      
      {/* 表体 */}
      {Array.from({ length: rows }, (_, rowIndex) => (
        <div key={rowIndex} className="grid grid-cols-${cols} gap-4 p-4 border-b last:border-b-0">
          {Array.from({ length: cols }, (_, colIndex) => (
            <Skeleton
              key={`${rowIndex}-${colIndex}`}
              width={colIndex === 0 ? '80%' : '100%'}
              height={16}
              active
            />
          ))}
        </div>
      ))}
    </div>
  )
}

/**
 * 表单骨架屏
 */
export const FormSkeleton: React.FC = () => {
  return (
    <div className="bg-white rounded-lg p-6 shadow-sm space-y-6">
      {Array.from({ length: 4 }, (_, i) => (
        <div key={i} className="space-y-2">
          <Skeleton.Input style={{ width: 80, height: 20 }} active />
          <Skeleton.Input style={{ width: '100%', height: 40 }} active />
        </div>
      ))}
      <Skeleton.Button style={{ width: 120, height: 40 }} active />
    </div>
  )
}

/**
 * 统计卡片骨架屏
 */
export const StatsCardSkeleton: React.FC = () => {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      {Array.from({ length: 4 }, (_, i) => (
        <div key={i} className="bg-white rounded-lg p-6 shadow-sm">
          <div className="flex items-center justify-between">
            <div className="space-y-2">
              <Skeleton.Input style={{ width: 60, height: 12 }} active />
              <Skeleton.Input style={{ width: 100, height: 32 }} active />
              <Skeleton.Input style={{ width: 80, height: 12 }} active />
            </div>
            <Skeleton.Avatar size={48} active shape="circle" />
          </div>
        </div>
      ))}
    </div>
  )
}

/**
 * 动画骨架屏
 */
export const ShimmerLoading: React.FC<{
  children?: React.ReactNode
  isLoading: boolean
  style?: 'card' | 'list' | 'chart' | 'table'
  rows?: number
}> = ({ children, isLoading, style = 'card', rows = 3 }) => {
  if (!isLoading) return <>{children}</>

  const SkeletonComponent = {
    card: CardSkeleton,
    list: ListSkeleton,
    chart: ChartSkeleton,
    table: TableSkeleton,
  }[style]

  return <SkeletonComponent rows={rows} />
}

/**
 * 内容加载包装器
 */
export const ContentLoader: React.FC<{
  isLoading: boolean
  error?: string
  children: React.ReactNode
  loadingStyle?: 'spinner' | 'skeleton' | 'dots'
  skeletonType?: 'card' | 'list' | 'chart' | 'table'
  retryAction?: () => void
}> = ({
  isLoading,
  error,
  children,
  loadingStyle = 'skeleton',
  skeletonType = 'card',
  retryAction
}) => {
  if (isLoading) {
    return loadingStyle === 'spinner' ? (
      <div className="flex items-center justify-center min-h-[200px]">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-purple-600"></div>
      </div>
    ) : loadingStyle === 'dots' ? (
      <div className="flex items-center justify-center min-h-[200px]">
        <div className="flex space-x-2">
          <div className="w-2 h-2 bg-purple-600 rounded-full animate-bounce"></div>
          <div className="w-2 h-2 bg-purple-600 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
          <div className="w-2 h-2 bg-purple-600 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
        </div>
      </div>
    ) : (
      <ShimmerLoading isLoading={true} style={skeletonType} />
    )
  }

  if (error) {
    return (
      <div className="flex flex-col items-center justify-center min-h-[200px] text-center">
        <div className="text-red-500 mb-4">{error}</div>
        {retryAction && (
          <button
            onClick={retryAction}
            className="px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors"
          >
            重试
          </button>
        )}
      </div>
    )
  }

  return <>{children}</>
}

/**
 * 延迟加载显示组件
 */
export const DelayLoader: React.FC<{
  delay?: number
  children: React.ReactNode
}> = ({ 
  delay = 300,
  children 
}) => {
  const [show, setShow] = React.useState(false)

  React.useEffect(() => {
    const timer = setTimeout(() => setShow(true), delay)
    return () => clearTimeout(timer)
  }, [delay])

  return show ? <>{children}</> : null
}

// 骨架屏预设样式
export const SkeletonPresets = {
  dashboard: {
    stats: StatsCardSkeleton,
    charts: ChartSkeleton,
    recentActivity: ListSkeleton,
  },
  users: {
    list: () => <TableSkeleton rows={8} cols={4} />,
    chart: ChartSkeleton,
  },
  settings: {
    form: FormSkeleton,
    table: TableSkeleton,
  },
}