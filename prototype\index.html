<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI智能评语助手 - 高保真原型</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .prototype-container {
            display: flex;
            flex-wrap: wrap;
            gap: 40px;
            padding: 40px;
            justify-content: center;
            align-items: flex-start;
        }

        .phone-frame {
            width: 375px;
            height: 812px;
            background: #000;
            border-radius: 40px;
            padding: 8px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.3);
            position: relative;
            overflow: hidden;
        }

        .phone-screen {
            width: 100%;
            height: 100%;
            border-radius: 32px;
            overflow: hidden;
            position: relative;
            background: #fff;
        }

        .status-bar {
            height: 44px;
            background: linear-gradient(135deg, #5470C6 0%, #73C0DE 100%);
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 20px;
            color: white;
            font-size: 14px;
            font-weight: 600;
        }

        .status-left {
            display: flex;
            align-items: center;
            gap: 4px;
        }

        .status-right {
            display: flex;
            align-items: center;
            gap: 6px;
        }

        .phone-label {
            position: absolute;
            bottom: -50px;
            left: 50%;
            transform: translateX(-50%);
            background: rgba(255,255,255,0.9);
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 14px;
            font-weight: 600;
            color: #333;
            backdrop-filter: blur(10px);
        }

        iframe {
            width: 100%;
            height: calc(100% - 44px);
            border: none;
            background: #F0F2F5;
        }

        .prototype-header {
            width: 100%;
            text-align: center;
            margin-bottom: 40px;
        }

        .prototype-title {
            font-size: 48px;
            font-weight: 700;
            color: white;
            margin-bottom: 16px;
            text-shadow: 0 4px 20px rgba(0,0,0,0.3);
        }

        .prototype-subtitle {
            font-size: 20px;
            color: rgba(255,255,255,0.8);
            font-weight: 400;
        }

        .prototype-info {
            background: rgba(255,255,255,0.1);
            backdrop-filter: blur(20px);
            border-radius: 20px;
            padding: 30px;
            margin: 40px auto;
            max-width: 800px;
            color: white;
            border: 1px solid rgba(255,255,255,0.2);
        }

        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }

        .info-item {
            text-align: center;
        }

        .info-icon {
            font-size: 32px;
            margin-bottom: 12px;
            opacity: 0.8;
        }

        .info-title {
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 8px;
        }

        .info-desc {
            font-size: 14px;
            opacity: 0.7;
            line-height: 1.4;
        }

        @media (max-width: 768px) {
            .prototype-container {
                padding: 20px;
                gap: 30px;
            }
            
            .phone-frame {
                width: 320px;
                height: 693px;
            }
            
            .prototype-title {
                font-size: 36px;
            }
        }
    </style>
</head>
<body>
    <div class="prototype-header">
        <h1 class="prototype-title">AI智能评语助手</h1>
        <p class="prototype-subtitle">高保真交互原型 · iPhone 15 Pro 设计规范</p>
        
        <div class="prototype-info">
            <h3 style="margin-bottom: 20px; font-size: 24px;">🎯 产品核心价值</h3>
            <p style="font-size: 18px; line-height: 1.6; margin-bottom: 20px;">
                3分钟生成专业评语，让每位教师都能写出高质量学生评价
            </p>
            
            <div class="info-grid">
                <div class="info-item">
                    <div class="info-icon">⚡</div>
                    <div class="info-title">效率提升</div>
                    <div class="info-desc">节省80%评语写作时间</div>
                </div>
                <div class="info-item">
                    <div class="info-icon">🎯</div>
                    <div class="info-title">质量保证</div>
                    <div class="info-desc">AI专业度评分>8分</div>
                </div>
                <div class="info-item">
                    <div class="info-icon">🏆</div>
                    <div class="info-title">个性化</div>
                    <div class="info-desc">千人千面的评语内容</div>
                </div>
                <div class="info-item">
                    <div class="info-icon">📊</div>
                    <div class="info-title">成长追踪</div>
                    <div class="info-desc">专业能力持续提升</div>
                </div>
            </div>
        </div>
    </div>

    <div class="prototype-container">
        <!-- 智能首页 -->
        <div class="phone-frame">
            <div class="phone-screen">
                <div class="status-bar">
                    <div class="status-left">
                        <span>9:41</span>
                    </div>
                    <div class="status-right">
                        <i class="fas fa-signal"></i>
                        <i class="fas fa-wifi"></i>
                        <i class="fas fa-battery-three-quarters"></i>
                    </div>
                </div>
                <iframe src="pages/home.html"></iframe>
            </div>
            <div class="phone-label">🏠 智能首页</div>
        </div>

        <!-- AI魔法生成 -->
        <div class="phone-frame">
            <div class="phone-screen">
                <div class="status-bar">
                    <div class="status-left">
                        <span>9:41</span>
                    </div>
                    <div class="status-right">
                        <i class="fas fa-signal"></i>
                        <i class="fas fa-wifi"></i>
                        <i class="fas fa-battery-three-quarters"></i>
                    </div>
                </div>
                <iframe src="pages/generate.html"></iframe>
            </div>
            <div class="phone-label">🤖 AI魔法生成</div>
        </div>

        <!-- 我的作品 -->
        <div class="phone-frame">
            <div class="phone-screen">
                <div class="status-bar">
                    <div class="status-left">
                        <span>9:41</span>
                    </div>
                    <div class="status-right">
                        <i class="fas fa-signal"></i>
                        <i class="fas fa-wifi"></i>
                        <i class="fas fa-battery-three-quarters"></i>
                    </div>
                </div>
                <iframe src="pages/works.html"></iframe>
            </div>
            <div class="phone-label">📝 我的作品</div>
        </div>

        <!-- 成长报告 -->
        <div class="phone-frame">
            <div class="phone-screen">
                <div class="status-bar">
                    <div class="status-left">
                        <span>9:41</span>
                    </div>
                    <div class="status-right">
                        <i class="fas fa-signal"></i>
                        <i class="fas fa-wifi"></i>
                        <i class="fas fa-battery-three-quarters"></i>
                    </div>
                </div>
                <iframe src="pages/growth.html"></iframe>
            </div>
            <div class="phone-label">📊 成长报告</div>
        </div>

        <!-- 智能设置 -->
        <div class="phone-frame">
            <div class="phone-screen">
                <div class="status-bar">
                    <div class="status-left">
                        <span>9:41</span>
                    </div>
                    <div class="status-right">
                        <i class="fas fa-signal"></i>
                        <i class="fas fa-wifi"></i>
                        <i class="fas fa-battery-three-quarters"></i>
                    </div>
                </div>
                <iframe src="pages/settings.html"></iframe>
            </div>
            <div class="phone-label">⚙️ 智能设置</div>
        </div>
    </div>
</body>
</html>
