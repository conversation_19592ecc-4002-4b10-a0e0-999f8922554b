/**
 * 评语管理页面
 * 提供评语的查看、编辑、删除、导出等功能
 */
const app = getApp();
const { extractSurname } = require('../../../utils/globalUtils');
const ExcelShareUtils = require('../../../utils/excelShareUtils');

// 获取云服务实例的辅助函数
function getCloudService() {
  const cloudService = app.globalData.cloudService;
  if (!cloudService) {
    throw new Error('云服务未初始化，请稍后重试');
  }
  return cloudService;
}

Page({
  /**
   * 页面的初始数据
   */
  data: {
    // 评语列表
    commentList: [],

    // 搜索
    searchKeyword: '',

    // 页面状态
    loading: true,
    refreshing: false,

    // 分页
    pageSize: 20,
    currentPage: 1,
    hasMore: true,

    // 统计信息
    statistics: {
      totalComments: 0,
      todayComments: 0,
      avgLength: 0,
      styleDistribution: {}
    },

    // UI状态
    showFilterSheet: false,
    showActionSheet: false,
    selectedComment: null,

    // 操作面板选项
    actionSheetActions: [
      { name: '查看详情', value: 'view' },
      { name: '编辑评语', value: 'edit' },
      { name: '复制内容', value: 'copy' },
      { name: '删除评语', value: 'delete', color: '#FF5247' }
    ]
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    this.initPage();
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    this.refreshData();
  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {
    this.refreshData();
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {
    this.loadMoreComments();
  },

  /**
   * 初始化页面
   */
  async initPage() {
    try {
      await this.loadCommentList();
    } catch (error) {
      console.error('页面初始化失败:', error);
      wx.showToast({
        title: '加载失败',
        icon: 'none'
      });
    }
  },

  /**
   * 刷新数据
   */
  async refreshData() {
    this.setData({
      refreshing: true,
      currentPage: 1,
      hasMore: true
    });

    try {
      await this.loadCommentList(true);
      await this.loadStatistics();
      wx.stopPullDownRefresh();
    } catch (error) {
      console.error('刷新数据失败:', error);
    } finally {
      this.setData({ refreshing: false });
    }
  },

  /**
   * 加载班级列表
   */
  async loadClassList() {
    try {
      const cloudService = getCloudService();
      const result = await cloudService.getClassList();
      if (result.success) {
        const classList = [
          { id: '', name: '全部班级' },
          ...result.data.map(item => ({
            id: item._id,
            name: item.className
          }))
        ];
        this.setData({ classList });
      }
    } catch (error) {
      console.error('加载班级列表失败:', error);
    }
  },

  /**
   * 加载学生列表
   */
  async loadStudentList() {
    try {
      const cloudService = getCloudService();
      const result = await cloudService.getStudentList();
      if (result.success) {
        const studentList = [
          { id: '', name: '全部学生' },
          ...result.data.map(item => ({
            id: item._id,
            name: item.name,
            className: item.className
          }))
        ];
        this.setData({ studentList });
      }
    } catch (error) {
      console.error('加载学生列表失败:', error);
    }
  },

  /**
   * 加载评语列表
   */
  async loadCommentList(refresh = false) {
    if (refresh) {
      this.setData({
        commentList: [],
        currentPage: 1,
        hasMore: true
      });
    }

    if (!this.data.hasMore && !refresh) {
      return;
    }

    this.setData({ loading: true });

    try {
      // 优先从云数据库获取评语数据
      let cloudComments = [];
      try {
        const cloudService = getCloudService();
        const result = await cloudService.getCommentList({
          page: this.data.currentPage,
          limit: this.data.pageSize,
          keyword: this.data.searchKeyword,
          filter: this.data.filterOptions
        });
        
        if (result.success) {
          cloudComments = result.data || [];
          console.log('从云数据库加载的评语数量:', cloudComments.length);
        } else {
          console.warn('云数据库加载评语失败:', result.error);
        }
      } catch (error) {
        console.error('云数据库查询失败，使用本地数据:', error);
      }

      // 如果云数据库没有数据，回退到本地存储（兼容性）
      let allComments = cloudComments;
      if (cloudComments.length === 0) {
        const savedComments = wx.getStorageSync('savedComments') || [];
        const recentComments = wx.getStorageSync('recentComments') || [];
        allComments = [...savedComments, ...recentComments];
        console.log('回退到本地存储，评语数量:', allComments.length);
      }
      
      // 去重（基于ID）
      const uniqueComments = allComments.filter((comment, index, arr) => 
        arr.findIndex(c => (c._id || c.id) === (comment._id || comment.id)) === index
      );
      
      if (uniqueComments.length > 0) {
        const newComments = uniqueComments.map(comment => ({
          ...comment,
          id: comment._id || comment.id,
          createTimeText: this.formatTime(comment.createTime || comment.createdAt || Date.now()),
          styleText: this.getStyleText(comment.style),
          contentPreview: this.getContentPreview(comment.content),
          surname: extractSurname(comment.studentName)
        }));

        const commentList = refresh ? newComments : [...this.data.commentList, ...newComments];

        this.setData({
          commentList,
          currentPage: this.data.currentPage + 1,
          hasMore: cloudComments.length > 0 ? cloudComments.length === this.data.pageSize : false,
          loading: false
        });
      } else {
        // 没有本地数据时显示空状态
        this.setData({
          commentList: [],
          loading: false,
          hasMore: false
        });
      }
      
    } catch (error) {
      console.error('加载评语列表失败:', error);
      this.setData({
        commentList: [],
        loading: false
      });
      wx.showToast({
        title: '加载失败',
        icon: 'none'
      });
    }
  },

  /**
   * 加载统计信息
   */
  async loadStatistics() {
    try {
      // 从本地存储获取评语数据进行统计
      const savedComments = wx.getStorageSync('savedComments') || [];
      const recentComments = wx.getStorageSync('recentComments') || [];
      
      // 合并并去重
      const allComments = [...savedComments, ...recentComments];
      const uniqueComments = allComments.filter((comment, index, arr) => 
        arr.findIndex(c => c.id === comment.id) === index
      );
      
      if (uniqueComments.length > 0) {
        // 计算今日评语
        const today = new Date();
        today.setHours(0, 0, 0, 0);
        const todayTimestamp = today.getTime();

        const todayComments = uniqueComments.filter(comment => {
          const commentTime = comment.createTime || comment.createdAt || Date.now();
          return commentTime >= todayTimestamp;
        });

        // 计算平均长度
        const totalLength = uniqueComments.reduce((sum, comment) => sum + (comment.content?.length || 0), 0);
        const avgLength = totalLength > 0 ? (totalLength / uniqueComments.length).toFixed(1) : 0;

        // 统计风格分布
        const styleDistribution = {
          warm: uniqueComments.filter(c => c.style === 'warm').length,
          encouraging: uniqueComments.filter(c => c.style === 'encouraging').length,
          formal: uniqueComments.filter(c => c.style === 'formal').length,
          detailed: uniqueComments.filter(c => c.style === 'detailed').length
        };

        this.setData({
          statistics: {
            totalComments: uniqueComments.length,
            todayComments: todayComments.length,
            avgLength: parseFloat(avgLength),
            styleDistribution
          }
        });
      } else {
        // 没有评语数据时显示0
        this.setData({
          statistics: {
            totalComments: 0,
            todayComments: 0,
            avgLength: 0,
            styleDistribution: {
              warm: 0,
              encouraging: 0,
              formal: 0,
              detailed: 0
            }
          }
        });
      }
    } catch (error) {
      console.error('加载统计信息失败:', error);
      this.setData({
        statistics: {
          totalComments: 0,
          todayComments: 0,
          avgLength: 0,
          styleDistribution: {
            warm: 0,
            encouraging: 0,
            formal: 0,
            detailed: 0
          }
        }
      });
    }
  },

  /**
   * 加载更多评语
   */
  loadMoreComments() {
    if (!this.data.loading && this.data.hasMore) {
      this.loadCommentList();
    }
  },

  /**
   * 显示筛选面板
   */
  showFilter() {
    this.setData({ showFilterSheet: true });
  },

  /**
   * 隐藏筛选面板
   */
  hideFilter() {
    this.setData({ showFilterSheet: false });
  },

  /**
   * 搜索功能
   */
  onSearchInput(e) {
    const keyword = e.detail.value;
    this.setData({ searchKeyword: keyword });

    // 防抖搜索
    if (this.searchTimer) {
      clearTimeout(this.searchTimer);
    }

    this.searchTimer = setTimeout(() => {
      this.performSearch();
    }, 500);
  },

  /**
   * 执行搜索
   */
  async performSearch() {
    const { searchKeyword } = this.data;

    // 更新筛选条件
    this.setData({
      'filterOptions.keyword': searchKeyword
    });

    // 重新加载数据
    await this.loadCommentList(true);
  },

  /**
   * 清空搜索
   */
  clearSearch() {
    this.setData({
      searchKeyword: '',
      'filterOptions.keyword': ''
    });
    this.loadCommentList(true);
  },

  /**
   * 显示筛选弹窗
   */
  showFilterPopup() {
    this.setData({ showFilterPopup: true });
  },

  /**
   * 隐藏筛选弹窗
   */
  hideFilterPopup() {
    this.setData({ showFilterPopup: false });
  },

  /**
   * 筛选条件变化
   */
  onFilterChange(e) {
    const { field } = e.currentTarget.dataset;
    const { value } = e.detail;

    this.setData({
      [`filterOptions.${field}`]: value
    });
  },

  /**
   * 应用筛选
   */
  async applyFilter() {
    this.hideFilterPopup();
    await this.loadCommentList(true);
  },

  /**
   * 重置筛选
   */
  resetFilter() {
    this.setData({
      filterOptions: {
        studentId: '',
        classId: '',
        style: '',
        dateRange: '',
        keyword: ''
      },
      searchKeyword: ''
    });
    this.loadCommentList(true);
  },

  /**
   * 应用筛选
   */
  applyFilter() {
    this.hideFilter();
    this.refreshData();
  },

  /**
   * 重置筛选
   */
  resetFilter() {
    this.setData({
      filterOptions: {
        studentId: '',
        classId: '',
        style: '',
        dateRange: ''
      }
    });
    this.applyFilter();
  },

  /**
   * 评语项点击
   */
  onCommentTap(e) {
    const { comment } = e.currentTarget.dataset;
    this.setData({
      selectedComment: comment,
      showActionSheet: true
    });
  },

  /**
   * 隐藏操作面板
   */
  hideActionSheet() {
    this.setData({
      showActionSheet: false,
      selectedComment: null
    });
  },

  /**
   * 操作选择
   */
  onActionSelect(e) {
    const { value } = e.detail;

    switch (value) {
      case 'view':
        this.viewComment();
        break;
      case 'edit':
        this.editComment();
        break;
      case 'copy':
        this.copyComment();
        break;
      case 'delete':
        this.deleteComment();
        break;
    }
  },

  /**
   * 查看评语详情
   */
  viewComment() {
    const comment = this.data.selectedComment;
    if (comment) {
      wx.navigateTo({
        url: `/pages/comment/detail/detail?id=${comment._id || comment.id}`
      });
    }
    this.hideActionSheet();
  },

  /**
   * 编辑评语
   */
  editComment(e) {
    // 从事件参数获取评语数据
    const comment = e?.currentTarget?.dataset?.comment || this.data.selectedComment;
    if (comment) {
      wx.navigateTo({
        url: `/pages/comment/edit/edit?id=${comment._id || comment.id}`
      });
    } else {
      wx.showToast({
        title: '获取评语信息失败',
        icon: 'none'
      });
    }
    this.hideActionSheet();
  },

  /**
   * 复制评语内容
   */
  copyComment(e) {
    // 从事件参数获取评语数据
    const comment = e?.currentTarget?.dataset?.comment || this.data.selectedComment;
    if (!comment) {
      wx.showToast({
        title: '获取评语信息失败',
        icon: 'none'
      });
      return;
    }

    wx.setClipboardData({
      data: comment.content,
      success: () => {
        wx.showToast({
          title: '已复制到剪贴板',
          icon: 'success'
        });
      },
      fail: () => {
        wx.showToast({
          title: '复制失败',
          icon: 'none'
        });
      }
    });
    this.hideActionSheet();
  },

  /**
   * 删除评语
   */
  deleteComment(e) {
    // 从事件参数获取评语数据
    const comment = e?.currentTarget?.dataset?.comment || this.data.selectedComment;
    if (!comment) {
      wx.showToast({
        title: '获取评语信息失败',
        icon: 'none'
      });
      return;
    }

    wx.showModal({
      title: '确认删除',
      content: `确定要删除${comment.studentName}的评语吗？`,
      success: async (res) => {
        if (res.confirm) {
          wx.showLoading({
            title: '删除中...',
            mask: true
          });

          try {
            // 尝试云端删除
            let cloudDeleteSuccess = false;
            try {
              const cloudService = getCloudService();
              const commentId = comment._id || comment.id;
              console.log('尝试删除评语ID:', commentId);
              
              if (cloudService && commentId) {
                const result = await cloudService.deleteComment(commentId);
                console.log('云端删除结果:', result);
                cloudDeleteSuccess = result.success;
                if (!result.success) {
                  console.warn('云端删除失败:', result.error);
                }
              }
            } catch (cloudError) {
              console.error('云端删除出错:', cloudError);
            }

            // 无论云端删除是否成功，都从本地移除（用户优先）
            const newCommentList = this.data.commentList.filter(item => 
              (item._id || item.id) !== (comment._id || comment.id)
            );
            
            // 更新统计信息
            const newStatistics = {
              ...this.data.statistics,
              totalComments: newCommentList.length
            };
            
            this.setData({
              commentList: newCommentList,
              statistics: newStatistics
            });

            wx.hideLoading();
            
            if (cloudDeleteSuccess) {
              wx.showToast({
                title: '删除成功',
                icon: 'success'
              });
            } else {
              wx.showToast({
                title: '已从列表移除',
                icon: 'success'
              });
            }
            
          } catch (error) {
            console.error('删除评语失败:', error);
            wx.hideLoading();
            wx.showToast({
              title: '删除失败',
              icon: 'none'
            });
          }
        }
      }
    });

    // 如果是从操作面板调用的，隐藏面板
    if (this.data.showActionSheet) {
      this.hideActionSheet();
    }
  },

  /**
   * 重新生成评语
   */
  regenerateComment(e) {
    // 从事件参数获取评语数据
    const comment = e?.currentTarget?.dataset?.comment || this.data.selectedComment;
    if (!comment) {
      wx.showToast({
        title: '获取评语信息失败',
        icon: 'none'
      });
      return;
    }

    wx.showModal({
      title: '重新生成评语',
      content: `确定要重新生成${comment.studentName}的评语吗？\n\n原评语内容将被覆盖，此操作不可撤销。`,
      confirmText: '重新生成',
      cancelText: '取消',
      success: async (res) => {
        if (res.confirm) {
          wx.showLoading({
            title: '重新生成中...',
            mask: true
          });

          try {
            // 构建跳转参数，包含学生信息和重新生成标识（不使用URLSearchParams）
            const paramStr = [
              `mode=regenerate`,
              `commentId=${comment._id || comment.id}`,
              `studentId=${comment.studentId}`,
              `studentName=${encodeURIComponent(comment.studentName)}`,
              `className=${encodeURIComponent(comment.className || '')}`,
              `originalStyle=${comment.style || 'warm'}`,
              `originalLength=${comment.length || 'medium'}`
            ].join('&');

            wx.hideLoading();
            wx.navigateTo({
              url: `/pages/comment/generate/generate?${paramStr}`
            });
          } catch (error) {
            console.error('重新生成评语失败:', error);
            wx.hideLoading();
            wx.showToast({
              title: '操作失败',
              icon: 'none'
            });
          }
        }
      }
    });

    // 如果是从操作面板调用的，隐藏面板
    if (this.data.showActionSheet) {
      this.hideActionSheet();
    }
  },

  /**
   * 新增评语
   */
  addComment() {
    wx.navigateTo({
      url: '/pages/comment/generate/generate'
    });
  },

  /**
   * 跳转到AI生成评语页面
   */
  goToGenerate() {
    wx.navigateTo({
      url: '/pages/comment/generate/generate'
    });
  },

  /**
   * 跳转到评语详情页面
   */
  goToCommentDetail(e) {
    const comment = e?.currentTarget?.dataset?.comment;
    if (comment) {
      wx.navigateTo({
        url: `/pages/comment/detail/detail?id=${comment._id || comment.id}`
      });
    } else {
      wx.showToast({
        title: '获取评语信息失败',
        icon: 'none'
      });
    }
  },

  /**
   * 清空所有评语
   */
  clearAllComments() {
    if (this.data.commentList.length === 0) {
      wx.showToast({
        title: '暂无评语可清空',
        icon: 'none'
      });
      return;
    }

    wx.showModal({
      title: '⚠️ 危险操作',
      content: `确定要清空所有评语吗？\n\n这将删除全部 ${this.data.commentList.length} 条评语，\n清空后无法恢复！`,
      confirmText: '确认清空',
      confirmColor: '#e74c3c',
      cancelText: '取消',
      success: async (res) => {
        if (res.confirm) {
          try {
            wx.showLoading({
              title: '清空中...',
              mask: true
            });

            const cloudService = getCloudService();
            const result = await cloudService.clearAllComments();
            
            wx.hideLoading();
            
            if (result.success) {
              wx.showToast({
                title: `清空成功，删除了${result.deletedCount}条评语`,
                icon: 'success',
                duration: 2000
              });
              // 清空本地数据并刷新页面
              this.setData({
                commentList: [],
                statistics: {
                  totalComments: 0,
                  todayComments: 0,
                  avgLength: 0,
                  styleDistribution: {}
                }
              });
            } else {
              throw new Error(result.error || '清空失败');
            }
          } catch (error) {
            wx.hideLoading();
            console.error('清空评语失败:', error);
            wx.showModal({
              title: '清空失败',
              content: error.message || '网络错误，请重试',
              showCancel: false
            });
          }
        }
      }
    });
  },

  /**
   * 导出评语 - 使用新的分享功能
   */
  exportComments() {
    if (this.data.commentList.length === 0) {
      wx.showToast({
        title: '暂无评语可导出',
        icon: 'none'
      });
      return;
    }

    const { showShareOptions } = require('../../../utils/shareUtils');
    
    const commentsToExport = this.data.commentList;
    const options = {
      excel: {
        fileName: `评语数据表_${new Date().toLocaleDateString('zh-CN').replace(/\//g, '-')}.xlsx`,
        headers: ['序号', '学生姓名', '班级', '评语内容', '评语风格', '评语长度', '创建时间', '字数统计'],
        title: '评语灵感君 - 评语数据表',
        formatRow: (comment, index) => [
          index + 1,
          comment.studentName || '',
          comment.className || '',
          (comment.content || '').replace(/[\r\n]/g, ' '),
          this.getStyleText(comment.style),
          comment.length || '标准',
          this.formatExcelTime(comment.createTime),
          comment.content ? comment.content.length : 0
        ]
      },
      image: {
        canvasId: 'shareCanvas',
        title: '评语灵感君',
        subtitle: `评语数据表（${commentsToExport.length}条评语）`,
        theme: 'blue'
      },
      copy: {
        formatContent: (data) => {
          let content = `【评语数据导出】\n导出时间：${new Date().toLocaleString('zh-CN')}\n评语总数：${data.length}条\n\n`;
          data.forEach((comment, index) => {
            content += `${index + 1}. ${comment.studentName}（${comment.className}）\n${comment.content}\n\n`;
          });
          content += `—————————————\n由评语灵感君生成`;
          return content;
        }
      },
      link: {
        generateContent: (data, shareId) => {
          return `📊 【评语灵感君】评语数据分享\n\n📝 评语总数：${data.length}条\n📅 导出时间：${new Date().toLocaleDateString('zh-CN')}\n\n✨ 查看完整数据：${shareId}\n\n🎯 特色功能：\n• AI智能评语生成\n• 多种评语风格\n• 专业数据导出\n• 成长记录跟踪\n\n💡 使用评语灵感君，让每句评语都充满温度！`;
        }
      }
    };

    showShareOptions(commentsToExport, options);
  },

  /**
   * 导出为Excel表格 - 使用统一分享工具
   */
  async exportToExcel() {
    if (this.data.commentList.length === 0) {
      wx.showModal({
        title: '提示',
        content: '暂无评语数据，建议先生成一些评语再导出。\n\n点击"确定"前往生成评语页面。',
        success: (res) => {
          if (res.confirm) {
            wx.navigateTo({
              url: '/pages/comment/generate/generate'
            });
          }
        }
      });
      return;
    }

    // 使用统一的Excel分享工具
    return ExcelShareUtils.shareComments(this.data.commentList);
  },

  /**
   * 原版导出为Excel表格（备用）
   */
  exportToExcelOriginal() {
    if (this.data.commentList.length === 0) {
      wx.showModal({
        title: '提示',
        content: '暂无评语数据，建议先生成一些评语再导出。\n\n点击"确定"前往生成评语页面。',
        success: (res) => {
          if (res.confirm) {
            wx.navigateTo({
              url: '/pages/comment/generate/generate'
            });
          }
        }
      });
      return;
    }

    wx.showLoading({
      title: '生成Excel中...',
      mask: true
    });

    try {
      // 引入xlsx库 - 需要先安装
      const XLSX = require('xlsx-js-style'); // 如果没有安装，使用备用方案
      
      // 准备Excel数据
      const excelData = this.prepareExcelData();
      
      // 创建工作簿
      const wb = XLSX.utils.book_new();
      const ws = XLSX.utils.aoa_to_sheet(excelData);
      
      // 设置列宽
      ws['!cols'] = [
        { width: 8 },   // 序号
        { width: 12 },  // 学生姓名
        { width: 12 },  // 班级
        { width: 12 },  // 生成时间
        { width: 10 },  // 评语长度
        { width: 12 },  // 评语风格
        { width: 50 }   // 评语内容
      ];
      
      // 添加工作表到工作簿
      XLSX.utils.book_append_sheet(wb, ws, '评语导出');
      
      // 生成文件
      const excelBuffer = XLSX.write(wb, { type: 'buffer', bookType: 'xlsx' });
      
      // 保存文件
      const fs = wx.getFileSystemManager();
      const now = new Date();
      const timestamp = now.toISOString().slice(0, 10);
      const fileName = `评语导出_${timestamp}.xlsx`;
      const filePath = `${wx.env.USER_DATA_PATH}/${fileName}`;
      
      fs.writeFile({
        filePath: filePath,
        data: excelBuffer,
        success: () => {
          wx.hideLoading();
          // 提供分享选项
          wx.showModal({
            title: '导出成功',
            content: `Excel文件已生成\n文件名：${fileName}\n\n是否立即分享到微信？`,
            confirmText: '分享到微信',
            cancelText: '稍后处理',
            confirmText: '分享文件',
            cancelText: '稍后处理',
            success: (res) => {
              if (res.confirm) {
                this.shareFileToWeChat(filePath, fileName);
              }
            }
          });
        },
        fail: (error) => {
          console.error('Excel文件写入失败:', error);
          wx.hideLoading();
          // 如果Excel导出失败，fallback到CSV格式
          this.exportToCSV();
        }
      });
      
    } catch (error) {
      console.error('Excel导出失败:', error);
      wx.hideLoading();
      // 如果xlsx库不可用，使用CSV格式导出
      this.exportToCSV();
    }
  },

  /**
   * 导出为CSV格式（Excel备用方案）
   */
  exportToCSV() {
    wx.showLoading({
      title: '生成CSV中...',
      mask: true
    });

    try {
      // 生成CSV内容
      const csvContent = this.generateCSVContent();
      
      // 保存文件
      const fs = wx.getFileSystemManager();
      const now = new Date();
      const timestamp = now.toISOString().slice(0, 10);
      const fileName = `评语导出_${timestamp}.csv`;
      const filePath = `${wx.env.USER_DATA_PATH}/${fileName}`;
      
      fs.writeFile({
        filePath: filePath,
        data: csvContent,
        encoding: 'utf8',
        success: () => {
          wx.hideLoading();
          // 提供分享选项
          wx.showModal({
            title: '导出成功',
            content: `CSV文件已生成，可用Excel打开\n文件名：${fileName}\n\n是否立即分享到微信？`,
            confirmText: '分享到微信',
            cancelText: '稍后处理',
            confirmText: '分享文件',
            cancelText: '稍后处理',
            success: (res) => {
              if (res.confirm) {
                this.shareFileToWeChat(filePath, fileName);
              }
            }
          });
        },
        fail: (error) => {
          console.error('CSV文件写入失败:', error);
          wx.hideLoading();
          wx.showToast({
            title: '导出失败，请重试',
            icon: 'none'
          });
        }
      });
    } catch (error) {
      console.error('CSV导出失败:', error);
      wx.hideLoading();
      wx.showToast({
        title: '导出失败',
        icon: 'none'
      });
    }
  },

  /**
   * 准备Excel数据
   */
  prepareExcelData() {
    const userInfo = wx.getStorageSync('userInfo') || {};
    const teacherName = userInfo.name || '老师';
    
    // 表头
    const headers = ['序号', '学生姓名', '班级', '生成时间', '评语长度', '评语风格', '评语内容'];
    
    // 数据行
    const dataRows = this.data.commentList.map((comment, index) => [
      index + 1,
      comment.studentName || '未知',
      comment.className || '',
      this.formatExcelTime(comment.createTime),
      comment.length || '标准',
      comment.styleText || '鼓励性',
      comment.content || ''
    ]);
    
    // 组合数据
    return [
      [`评语导出表 - ${teacherName}`, '', '', '', '', '', ''],
      [`导出时间：${new Date().toLocaleString('zh-CN')}`, '', '', '', '', '', ''],
      ['', '', '', '', '', '', ''], // 空行
      headers,
      ...dataRows
    ];
  },

  /**
   * 生成CSV内容
   */
  generateCSVContent() {
    const userInfo = wx.getStorageSync('userInfo') || {};
    const teacherName = userInfo.name || '老师';
    
    // CSV内容
    let csvContent = '\uFEFF'; // BOM头，确保中文正确显示
    csvContent += `评语导出表 - ${teacherName}\n`;
    csvContent += `导出时间：${new Date().toLocaleString('zh-CN')}\n\n`;
    
    // 表头
    csvContent += '序号,学生姓名,班级,生成时间,评语长度,评语风格,评语内容\n';
    
    // 数据行
    this.data.commentList.forEach((comment, index) => {
      const row = [
        index + 1,
        this.escapeCSV(comment.studentName || '未知'),
        this.escapeCSV(comment.className || ''),
        this.formatExcelTime(comment.createTime),
        this.escapeCSV(comment.length || '标准'),
        this.escapeCSV(comment.styleText || '鼓励性'),
        this.escapeCSV(comment.content || '')
      ];
      csvContent += row.join(',') + '\n';
    });
    
    return csvContent;
  },

  /**
   * CSV字段转义
   */
  escapeCSV(field) {
    if (typeof field !== 'string') {
      field = String(field);
    }
    // 如果包含逗号、引号或换行符，需要用引号包围并转义内部引号
    if (field.includes(',') || field.includes('"') || field.includes('\n')) {
      return '"' + field.replace(/"/g, '""') + '"';
    }
    return field;
  },

  /**
   * 格式化Excel时间
   */
  formatExcelTime(timestamp) {
    if (!timestamp) return '';
    const date = new Date(timestamp);
    return date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    });
  },

  /**
   * 导出为文件
   */
  exportToFile() {
    wx.showLoading({
      title: '生成文件中...',
      mask: true
    });

    try {
      // 生成导出内容
      const content = this.generateExportContent();
      
      // 获取当前时间戳
      const now = new Date();
      const timestamp = now.toISOString().slice(0, 10);
      
      // 生成文件名
      const fileName = `评语导出_${timestamp}.txt`;
      
      // 调用文件系统API创建文件
      const fs = wx.getFileSystemManager();
      const filePath = `${wx.env.USER_DATA_PATH}/${fileName}`;
      
      fs.writeFile({
        filePath: filePath,
        data: content,
        encoding: 'utf8',
        success: () => {
          wx.hideLoading();
          // 提供分享选项
          wx.showModal({
            title: '导出成功',
            content: `文本文件已生成\n文件名：${fileName}\n\n是否立即分享到微信？`,
            confirmText: '分享到微信',
            cancelText: '稍后处理',
            confirmText: '分享文件',
            cancelText: '稍后处理',
            success: (res) => {
              if (res.confirm) {
                this.shareFileToWeChat(filePath, fileName);
              }
            }
          });
        },
        fail: (error) => {
          console.error('文件写入失败:', error);
          wx.hideLoading();
          this.copyAllComments(); // 如果文件导出失败，fallback到复制
        }
      });
    } catch (error) {
      console.error('导出失败:', error);
      wx.hideLoading();
      wx.showToast({
        title: '导出失败',
        icon: 'none'
      });
    }
  },

  /**
   * 分享文件到微信
   */
  shareFileToWeChat(filePath, fileName) {
    // 检查分享功能是否可用
    if (typeof wx.shareFileMessage !== 'function') {
      wx.showModal({
        title: '功能不可用',
        content: '当前环境不支持文件分享功能\n请在真机微信中使用\n\n替代方案：\n• 可以复制评语内容到剪贴板\n• 然后粘贴到其他应用中使用',
        confirmText: '复制内容',
        cancelText: '知道了',
        success: (res) => {
          if (res.confirm) {
            this.copyAllComments();
          }
        }
      });
      return;
    }

    // 显示分享指引
    wx.showModal({
      title: '分享文件到微信',
      content: `文件：${fileName}\n\n操作步骤：\n1. 选择微信聊天发送文件\n2. 建议发送到"文件传输助手"\n3. 在微信中可以下载和查看\n4. 也可以转发给其他联系人\n\n点击"开始分享"继续`,
      confirmText: '开始分享',
      cancelText: '取消',
      success: (res) => {
        if (res.confirm) {
          this.performFileShare(filePath, fileName);
        }
      }
    });
  },

  /**
   * 执行文件分享
   */
  performFileShare(filePath, fileName) {
    wx.shareFileMessage({
      filePath: filePath,
      fileName: fileName,
      success: () => {
        wx.showModal({
          title: '分享成功',
          content: `文件已发送到微信聊天\n\n文件信息：\n• 文件名：${fileName}\n• 内容：评语导出数据\n• 格式：可用Excel等软件打开\n\n您可以在微信中下载和查看文件`,
          showCancel: false,
          confirmText: '知道了'
        });
      },
      fail: (err) => {
        console.error('文件分享失败:', err);
        wx.showModal({
          title: '分享失败',
          content: `无法分享文件到微信\n\n可能原因：\n• 微信版本过低\n• 系统权限限制\n• 文件路径异常\n\n替代方案：\n• 复制评语内容到剪贴板\n• 手动保存到其他应用`,
          confirmText: '复制内容',
          cancelText: '重新尝试',
          success: (res) => {
            if (res.confirm) {
              this.copyAllComments();
            } else {
              // 用户选择重新尝试
              this.shareFileToWeChat(filePath, fileName);
            }
          }
        });
      }
    });
  },

  /**
   * 复制所有评语
   */
  copyAllComments() {
    try {
      const content = this.generateExportContent();
      
      wx.setClipboardData({
        data: content,
        success: () => {
          wx.showToast({
            title: '已复制到剪贴板',
            icon: 'success'
          });
        },
        fail: () => {
          wx.showToast({
            title: '复制失败',
            icon: 'none'
          });
        }
      });
    } catch (error) {
      console.error('复制失败:', error);
      wx.showToast({
        title: '复制失败',
        icon: 'none'
      });
    }
  },

  /**
   * 生成导出内容
   */
  generateExportContent() {
    const userInfo = wx.getStorageSync('userInfo') || {};
    const teacherName = userInfo.name || '老师';
    const now = new Date();
    const timestamp = now.toLocaleString('zh-CN');
    
    let content = `=== 评语导出 ===\n`;
    content += `导出时间：${timestamp}\n`;
    content += `导出教师：${teacherName}\n`;
    content += `评语总数：${this.data.commentList.length}条\n\n`;
    content += `${'='.repeat(50)}\n\n`;
    
    this.data.commentList.forEach((comment, index) => {
      content += `【评语 ${index + 1}】\n`;
      content += `学生：${comment.studentName || '未知'}\n`;
      content += `生成时间：${this.formatTime(comment.createTime)}\n`;
      content += `评语长度：${comment.length || '标准'}\n`;
      content += `评语风格：${comment.style || '鼓励性'}\n`;
      content += `内容：\n${comment.content}\n`;
      content += `${'-'.repeat(30)}\n\n`;
    });
    
    content += `导出完成 - 共${this.data.commentList.length}条评语\n`;
    content += `生成工具：评语灵感君`;
    
    return content;
  },

  /**
   * 格式化时间
   */
  formatTime(timestamp) {
    const date = new Date(timestamp);
    const now = new Date();
    const diff = now - date;

    if (diff < 60000) { // 1分钟内
      return '刚刚';
    } else if (diff < 3600000) { // 1小时内
      return `${Math.floor(diff / 60000)}分钟前`;
    } else if (diff < 86400000) { // 1天内
      return `${Math.floor(diff / 3600000)}小时前`;
    } else {
      return date.toLocaleDateString();
    }
  },

  /**
   * 获取风格文本
   */
  getStyleText(style) {
    const styleMap = {
      formal: '正式严谨',
      warm: '温和亲切',
      encouraging: '激励向上',
      detailed: '详细具体'
    };
    return styleMap[style] || '未知风格';
  },

  /**
   * 获取内容预览
   */
  getContentPreview(content) {
    if (!content) return '';
    return content.length > 50 ? content.substring(0, 50) + '...' : content;
  },


  /**
   * 长按标题显示调试菜单
   */
  onTitleLongPress() {
    wx.showActionSheet({
      itemList: ['清空所有数据', '查看数据库状态'],
      success: (res) => {
        switch (res.tapIndex) {
          case 0:
            this.clearAllData();
            break;
          case 1:
            this.checkDatabaseStatus();
            break;
        }
      }
    });
  },

  /**
   * 清空所有数据（调试用）
   */
  async clearAllData() {
    wx.showModal({
      title: '危险操作',
      content: '确定要删除所有评语数据吗？此操作不可撤销！',
      confirmText: '确定删除',
      cancelText: '取消',
      success: async (res) => {
        if (res.confirm) {
          try {
            wx.showLoading({ title: '删除中...' });
            const cloudService = getCloudService();
            // 这里需要实现批量删除功能
            wx.hideLoading();
            wx.showToast({ title: '删除成功', icon: 'success' });
            this.refreshData();
          } catch (error) {
            wx.hideLoading();
            wx.showToast({ title: '删除失败', icon: 'none' });
          }
        }
      }
    });
  },

  /**
   * 查看数据库状态（调试用）
   */
  async checkDatabaseStatus() {
    try {
      const cloudService = getCloudService();
      const userId = await cloudService.getCurrentUserId();
      wx.showModal({
        title: '数据库状态',
        content: `当前用户ID: ${userId}\n评语总数: ${this.data.commentList.length}`,
        showCancel: false
      });
    } catch (error) {
      wx.showToast({
        title: '查询失败',
        icon: 'none'
      });
    }
  },

});