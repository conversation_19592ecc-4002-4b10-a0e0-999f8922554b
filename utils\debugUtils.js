/**
 * 调试工具函数
 * 用于开发和测试时快速解决数据同步问题
 */

const debugUtils = {
  /**
   * 清除数据清空标记
   */
  clearStudentsClearedFlag() {
    wx.removeStorageSync('studentsJustCleared');
    wx.removeStorageSync('clearTimestamp');
    wx.removeStorageSync('deletedStudents');
    console.log('🔧 [调试] 已清除所有数据清空标记');
    
    // 清除统一数据管理器的缓存
    const { unifiedDataManager } = require('./unifiedDataManager');
    if (unifiedDataManager.dataCache) {
      unifiedDataManager.dataCache.clear();
      console.log('🔧 [调试] 已清除统一数据管理器缓存');
    }
    
    wx.showToast({
      title: '调试标记已清除',
      icon: 'success'
    });
  },

  /**
   * 强制刷新所有学生数据
   */
  async forceRefreshStudents() {
    try {
      const { getStudents } = require('./unifiedDataManager');
      const result = await getStudents({
        pageId: 'debug-refresh',
        forceRefresh: true
      });
      
      console.log('🔧 [调试] 强制刷新学生数据结果:', result);
      
      wx.showModal({
        title: '调试信息',
        content: `数据刷新${result.success ? '成功' : '失败'}\n学生数量: ${result.data?.length || 0}\n数据源: ${result.source}`,
        showCancel: false
      });
      
      return result;
    } catch (error) {
      console.error('🔧 [调试] 强制刷新失败:', error);
      wx.showToast({
        title: '刷新失败',
        icon: 'error'
      });
    }
  },

  /**
   * 检查数据状态
   */
  checkDataStatus() {
    const status = {
      studentsJustCleared: wx.getStorageSync('studentsJustCleared'),
      clearTimestamp: wx.getStorageSync('clearTimestamp'),
      deletedStudents: wx.getStorageSync('deletedStudents'),
      studentList: wx.getStorageSync('studentList'),
    };

    console.log('🔧 [调试] 当前数据状态:', status);
    
    // 显示状态信息
    const statusText = Object.entries(status)
      .map(([key, value]) => `${key}: ${value ? (typeof value === 'object' ? JSON.stringify(value).slice(0, 50) : value) : '无'}`)
      .join('\n');

    wx.showModal({
      title: '数据状态检查',
      content: statusText,
      showCancel: false
    });

    return status;
  },

  /**
   * 获取统一数据管理器状态
   */
  getUnifiedDataManagerStatus() {
    const { getDataStatus } = require('./unifiedDataManager');
    const status = getDataStatus();
    
    console.log('🔧 [调试] 统一数据管理器状态:', status);
    
    wx.showModal({
      title: '数据管理器状态',
      content: `初始化: ${status.isInitialized}\n缓存键: ${status.cacheKeys.join(', ')}\n加载状态: ${status.loadingStates.join(', ')}\n清空状态: ${JSON.stringify(status.clearStatus)}`,
      showCancel: false
    });

    return status;
  },

  /**
   * 完整重置（紧急修复）
   */
  emergencyReset() {
    wx.showModal({
      title: '🚨 紧急重置',
      content: '这将清除所有本地数据并重新初始化，确定继续吗？',
      success: (res) => {
        if (res.confirm) {
          // 清除所有相关存储
          const keysToRemove = [
            'studentsJustCleared',
            'clearTimestamp', 
            'deletedStudents',
            'studentList',
            'classList',
            'dataEventLogs'
          ];

          keysToRemove.forEach(key => {
            wx.removeStorageSync(key);
          });

          // 清除缓存
          const { unifiedDataManager } = require('./unifiedDataManager');
          if (unifiedDataManager.dataCache) {
            unifiedDataManager.dataCache.clear();
          }

          console.log('🔧 [调试] 紧急重置完成');
          
          wx.showModal({
            title: '重置完成',
            content: '所有数据已重置，建议重启小程序',
            showCancel: false
          });
        }
      }
    });
  }
};

// 在开发环境下注册到全局
if (typeof global !== 'undefined') {
  global.debugUtils = debugUtils;
}

// 在小程序中注册到 wx 对象
if (typeof wx !== 'undefined') {
  wx.debugUtils = debugUtils;
}

module.exports = debugUtils;