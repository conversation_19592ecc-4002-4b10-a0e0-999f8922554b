const fs = require('fs')
const path = require('path')

const readEnvFile = (filePath) => {
  try {
    console.log(`\n=== Reading ${path.basename(filePath)} ===`)
    const content = fs.readFileSync(filePath, 'utf8')
    console.log('Raw content:')
    console.log(content.substring(0, 200) + '...')
    
    const env = {}
    const lines = content.split('\n')
    
    lines.forEach((line, index) => {
      const trimmed = line.trim()
      console.log(`Line ${index + 1}: "${trimmed}"`)
      
      if (trimmed && !trimmed.startsWith('#') && trimmed.includes('=')) {
        const equalIndex = trimmed.indexOf('=')
        const key = trimmed.substring(0, equalIndex).trim()
        const value = trimmed.substring(equalIndex + 1).trim()
        
        console.log(`  -> Key: "${key}", Value: "${value}"`)
        
        if (key) {
          env[key] = value
        }
      }
    })
    
    console.log('Final parsed env:', env)
    return env
  } catch (error) {
    console.error('Error:', error.message)
    return null
  }
}

// 测试读取文件
const files = ['.env.development', '.env.production']

files.forEach(file => {
  const filePath = path.join(__dirname, file)
  readEnvFile(filePath)
})