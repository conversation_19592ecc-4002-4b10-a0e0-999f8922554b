<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>设置页面功能测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            padding: 30px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 2px solid #e5e7eb;
            border-radius: 8px;
            background: #fafafa;
        }
        .test-section.success {
            border-color: #10b981;
            background: #ecfdf5;
        }
        .test-section.error {
            border-color: #ef4444;
            background: #fef2f2;
        }
        .test-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin: 10px 0;
            padding: 10px;
            background: white;
            border-radius: 6px;
            border: 1px solid #e5e7eb;
        }
        .switch {
            position: relative;
            width: 50px;
            height: 24px;
            background: #ccc;
            border-radius: 12px;
            cursor: pointer;
            transition: background 0.3s;
        }
        .switch.active {
            background: #1890ff;
        }
        .switch::after {
            content: '';
            position: absolute;
            width: 20px;
            height: 20px;
            background: white;
            border-radius: 50%;
            top: 2px;
            left: 2px;
            transition: transform 0.3s;
        }
        .switch.active::after {
            transform: translateX(26px);
        }
        button {
            background: #1890ff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 6px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #40a9ff;
        }
        button.danger {
            background: #ff4d4f;
        }
        button.danger:hover {
            background: #ff7875;
        }
        .log {
            background: #f0f0f0;
            padding: 10px;
            border-radius: 6px;
            font-family: monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
            margin-top: 10px;
        }
        .status {
            display: inline-block;
            padding: 2px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
        }
        .status.success {
            background: #d1fae5;
            color: #065f46;
        }
        .status.error {
            background: #fee2e2;
            color: #991b1b;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 设置页面功能测试</h1>
        
        <div class="test-section">
            <h3>🔐 问题1：密码修改测试</h3>
            <p>测试密码修改功能是否正常工作</p>
            <div class="test-item">
                <span>当前密码验证：</span>
                <button onclick="testPasswordChange()">测试密码修改</button>
            </div>
            <div id="passwordResult" class="log"></div>
        </div>

        <div class="test-section">
            <h3>⚙️ 问题2：系统配置测试</h3>
            <p>测试最大用户数和会话超时设置</p>
            <div class="test-item">
                <span>最大用户数：</span>
                <input type="number" id="maxUsers" value="1000" min="1" max="10000">
                <button onclick="updateMaxUsers()">更新</button>
            </div>
            <div class="test-item">
                <span>会话超时(分钟)：</span>
                <input type="number" id="sessionTimeout" value="30" min="5" max="120">
                <button onclick="updateSessionTimeout()">更新</button>
            </div>
            <div id="systemResult" class="log"></div>
        </div>

        <div class="test-section">
            <h3>🎛️ 问题3：功能开关测试</h3>
            <p>测试功能开关是否能持久化保存</p>
            <div class="test-item">
                <span>数据自动刷新：</span>
                <div class="switch" id="autoRefreshSwitch" onclick="toggleSetting('autoRefresh')"></div>
                <span class="status" id="autoRefreshStatus">关闭</span>
            </div>
            <div class="test-item">
                <span>暗色主题：</span>
                <div class="switch" id="darkModeSwitch" onclick="toggleSetting('darkMode')"></div>
                <span class="status" id="darkModeStatus">关闭</span>
            </div>
            <div class="test-item">
                <span>调试模式：</span>
                <div class="switch" id="debugModeSwitch" onclick="toggleSetting('debugMode')"></div>
                <span class="status" id="debugModeStatus">关闭</span>
            </div>
            <div class="test-item">
                <span>数据缓存：</span>
                <div class="switch" id="enableCacheSwitch" onclick="toggleSetting('enableCache')"></div>
                <span class="status" id="enableCacheStatus">开启</span>
            </div>
            <button onclick="testPersistence()">测试持久化</button>
            <div id="switchResult" class="log"></div>
        </div>

        <div class="test-section">
            <h3>🔔 问题4：通知设置测试</h3>
            <p>测试通知开关是否能正常工作</p>
            <div class="test-item">
                <span>浏览器通知：</span>
                <div class="switch" id="browserNotificationSwitch" onclick="toggleNotification('browserEnabled')"></div>
                <span class="status" id="browserNotificationStatus">开启</span>
            </div>
            <div class="test-item">
                <span>页面提醒：</span>
                <div class="switch" id="pageAlertSwitch" onclick="toggleNotification('pageAlert')"></div>
                <span class="status" id="pageAlertStatus">开启</span>
            </div>
            <div class="test-item">
                <span>声音提醒：</span>
                <div class="switch" id="soundAlertSwitch" onclick="toggleNotification('soundAlert')"></div>
                <span class="status" id="soundAlertStatus">关闭</span>
            </div>
            <button onclick="testNotifications()">测试通知</button>
            <div id="notificationResult" class="log"></div>
        </div>

        <div class="test-section">
            <h3>📊 存储状态检查</h3>
            <button onclick="checkStorage()">检查本地存储</button>
            <button class="danger" onclick="clearStorage()">清除所有设置</button>
            <div id="storageResult" class="log"></div>
        </div>
    </div>

    <script>
        // 初始化设置
        let systemSettings = JSON.parse(localStorage.getItem('systemSettings') || '{"maxUsers":1000,"sessionTimeout":30,"autoRefresh":false,"darkMode":false,"debugMode":false,"enableCache":true}');
        let notificationSettings = JSON.parse(localStorage.getItem('notificationSettings') || '{"browserEnabled":true,"pageAlert":true,"soundAlert":false}');

        // 日志函数
        function log(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] ${message}\n`;
            element.textContent += logEntry;
            element.scrollTop = element.scrollHeight;
        }

        // 测试1：密码修改
        function testPasswordChange() {
            const currentPassword = 'admin123';
            const newPassword = 'newPassword123';
            
            log('passwordResult', '开始测试密码修改...');
            
            // 模拟密码验证
            const storedPassword = localStorage.getItem('adminPassword') || 'admin123';
            log('passwordResult', `当前存储的密码: ${storedPassword}`);
            
            if (currentPassword === storedPassword) {
                localStorage.setItem('adminPassword', newPassword);
                log('passwordResult', '✅ 密码修改成功！', 'success');
                log('passwordResult', `新密码已保存: ${newPassword}`);
            } else {
                log('passwordResult', '❌ 当前密码错误', 'error');
            }
        }

        // 测试2：系统配置
        function updateMaxUsers() {
            const value = document.getElementById('maxUsers').value;
            systemSettings.maxUsers = parseInt(value);
            localStorage.setItem('systemSettings', JSON.stringify(systemSettings));
            log('systemResult', `✅ 最大用户数已更新为: ${value}`);
        }

        function updateSessionTimeout() {
            const value = document.getElementById('sessionTimeout').value;
            systemSettings.sessionTimeout = parseInt(value);
            localStorage.setItem('systemSettings', JSON.stringify(systemSettings));
            log('systemResult', `✅ 会话超时已更新为: ${value}分钟`);
        }

        // 测试3：功能开关
        function toggleSetting(setting) {
            systemSettings[setting] = !systemSettings[setting];
            localStorage.setItem('systemSettings', JSON.stringify(systemSettings));
            
            const switchEl = document.getElementById(setting + 'Switch');
            const statusEl = document.getElementById(setting + 'Status');
            
            switchEl.classList.toggle('active', systemSettings[setting]);
            statusEl.textContent = systemSettings[setting] ? '开启' : '关闭';
            statusEl.className = 'status ' + (systemSettings[setting] ? 'success' : 'error');
            
            log('switchResult', `✅ ${setting} 已${systemSettings[setting] ? '开启' : '关闭'}`);
            
            // 特殊处理
            if (setting === 'darkMode') {
                document.body.style.background = systemSettings[setting] ? '#1f2937' : '#f5f5f5';
                document.body.style.color = systemSettings[setting] ? 'white' : 'black';
            }
        }

        // 测试4：通知设置
        function toggleNotification(setting) {
            notificationSettings[setting] = !notificationSettings[setting];
            localStorage.setItem('notificationSettings', JSON.stringify(notificationSettings));
            
            const switchEl = document.getElementById(setting.replace('Enabled', 'Notification') + 'Switch');
            const statusEl = document.getElementById(setting.replace('Enabled', 'Notification') + 'Status');
            
            switchEl.classList.toggle('active', notificationSettings[setting]);
            statusEl.textContent = notificationSettings[setting] ? '开启' : '关闭';
            statusEl.className = 'status ' + (notificationSettings[setting] ? 'success' : 'error');
            
            log('notificationResult', `✅ ${setting} 已${notificationSettings[setting] ? '开启' : '关闭'}`);
        }

        // 测试持久化
        function testPersistence() {
            log('switchResult', '测试设置持久化...');
            const saved = JSON.parse(localStorage.getItem('systemSettings') || '{}');
            log('switchResult', `保存的设置: ${JSON.stringify(saved, null, 2)}`);
            
            // 模拟页面刷新
            setTimeout(() => {
                const reloaded = JSON.parse(localStorage.getItem('systemSettings') || '{}');
                log('switchResult', '✅ 设置在"刷新"后仍然保持');
                log('switchResult', `重新加载的设置: ${JSON.stringify(reloaded, null, 2)}`);
            }, 1000);
        }

        // 测试通知
        function testNotifications() {
            log('notificationResult', '测试通知功能...');
            
            if (notificationSettings.browserEnabled && 'Notification' in window) {
                Notification.requestPermission().then(permission => {
                    if (permission === 'granted') {
                        new Notification('测试通知', {
                            body: '浏览器通知功能正常！',
                            icon: '/favicon.ico'
                        });
                        log('notificationResult', '✅ 浏览器通知发送成功');
                    } else {
                        log('notificationResult', '❌ 浏览器通知权限被拒绝');
                    }
                });
            }
            
            if (notificationSettings.soundAlert) {
                const audio = new Audio('data:audio/wav;base64,UklGRnoGAABXQVZFZm10IBAAAAABAAEAQB8AAEAfAAABAAgAZGF0YQoGAACBhYqFbF1fdJivrJBhNjVgodDbq2EcBj+a2/LDciUFLIHO8tiJNwgZaLvt559NEAxQp+PwtmMcBjiR1/LMeSwFJHfH8N2QQAoUXrTp66hVFApGn+DyvmwhBSuBzvLZiTYIG2m98OScTgwOUarm7blmGgU7k9n1unEiBC13yO/eizEIHWq+8+OWT');
                audio.volume = 0.3;
                audio.play().catch(() => {});
                log('notificationResult', '✅ 声音提醒播放成功');
            }
        }

        // 检查存储
        function checkStorage() {
            log('storageResult', '检查本地存储状态...');
            log('storageResult', `系统设置: ${JSON.stringify(systemSettings, null, 2)}`);
            log('storageResult', `通知设置: ${JSON.stringify(notificationSettings, null, 2)}`);
            log('storageResult', `管理员密码: ${localStorage.getItem('adminPassword') || 'admin123'}`);
            log('storageResult', `存储项总数: ${Object.keys(localStorage).length}`);
        }

        // 清除存储
        function clearStorage() {
            if (confirm('确定要清除所有设置吗？')) {
                localStorage.clear();
                log('storageResult', '✅ 所有设置已清除');
                location.reload();
            }
        }

        // 初始化界面
        function initUI() {
            // 初始化开关状态
            Object.keys(systemSettings).forEach(key => {
                const switchEl = document.getElementById(key + 'Switch');
                const statusEl = document.getElementById(key + 'Status');
                if (switchEl && statusEl) {
                    switchEl.classList.toggle('active', systemSettings[key]);
                    statusEl.textContent = systemSettings[key] ? '开启' : '关闭';
                    statusEl.className = 'status ' + (systemSettings[key] ? 'success' : 'error');
                }
            });
            
            Object.keys(notificationSettings).forEach(key => {
                const switchEl = document.getElementById(key.replace('Enabled', 'Notification') + 'Switch');
                const statusEl = document.getElementById(key.replace('Enabled', 'Notification') + 'Status');
                if (switchEl && statusEl) {
                    switchEl.classList.toggle('active', notificationSettings[key]);
                    statusEl.textContent = notificationSettings[key] ? '开启' : '关闭';
                    statusEl.className = 'status ' + (notificationSettings[key] ? 'success' : 'error');
                }
            });
            
            // 更新输入框值
            document.getElementById('maxUsers').value = systemSettings.maxUsers;
            document.getElementById('sessionTimeout').value = systemSettings.sessionTimeout;
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', initUI);
    </script>
</body>
</html>
