<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智能设置</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
            background: linear-gradient(135deg, #F0F2F5 0%, #E4E7ED 100%);
            min-height: 100vh;
            color: #333;
        }

        .page-container {
            padding: 20px 16px 100px;
            max-width: 375px;
            margin: 0 auto;
        }

        /* 页面头部 */
        .page-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 24px;
            padding-top: 10px;
        }

        .header-title {
            font-size: 20px;
            font-weight: 600;
            color: #333;
        }

        .header-action {
            color: #5470C6;
            font-size: 14px;
            cursor: pointer;
        }

        /* 用户信息卡片 */
        .user-card {
            background: linear-gradient(135deg, #5470C6 0%, #73C0DE 100%);
            border-radius: 20px;
            padding: 24px;
            margin-bottom: 24px;
            color: white;
            position: relative;
            overflow: hidden;
        }

        .user-card::before {
            content: '';
            position: absolute;
            top: -50%;
            right: -50%;
            width: 200px;
            height: 200px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 50%;
        }

        .user-info {
            display: flex;
            align-items: center;
            position: relative;
            z-index: 1;
        }

        .user-avatar {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.2);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            margin-right: 16px;
        }

        .user-details {
            flex: 1;
        }

        .user-name {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 4px;
        }

        .user-level {
            font-size: 12px;
            opacity: 0.9;
            background: rgba(255, 255, 255, 0.2);
            padding: 2px 8px;
            border-radius: 12px;
            display: inline-block;
        }

        /* 设置分组 */
        .settings-group {
            background: rgba(255, 255, 255, 0.9);
            border-radius: 16px;
            margin-bottom: 20px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.3);
            overflow: hidden;
        }

        .group-title {
            font-size: 14px;
            font-weight: 600;
            color: #666;
            padding: 16px 20px 8px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .setting-item {
            display: flex;
            align-items: center;
            padding: 16px 20px;
            border-bottom: 1px solid rgba(0, 0, 0, 0.05);
            cursor: pointer;
            transition: background-color 0.3s ease;
        }

        .setting-item:last-child {
            border-bottom: none;
        }

        .setting-item:hover {
            background: rgba(84, 112, 198, 0.05);
        }

        .setting-icon {
            width: 32px;
            height: 32px;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 12px;
            font-size: 16px;
            color: white;
        }

        .setting-icon.ai { background: linear-gradient(135deg, #5470C6 0%, #73C0DE 100%); }
        .setting-icon.data { background: linear-gradient(135deg, #52C41A 0%, #73D13D 100%); }
        .setting-icon.security { background: linear-gradient(135deg, #FA8C16 0%, #FFC53D 100%); }
        .setting-icon.help { background: linear-gradient(135deg, #722ED1 0%, #B37FEB 100%); }
        .setting-icon.about { background: linear-gradient(135deg, #13C2C2 0%, #36CFC9 100%); }

        .setting-content {
            flex: 1;
        }

        .setting-title {
            font-size: 16px;
            font-weight: 500;
            color: #333;
            margin-bottom: 2px;
        }

        .setting-desc {
            font-size: 12px;
            color: #666;
            line-height: 1.3;
        }

        .setting-action {
            color: #999;
            font-size: 14px;
        }

        /* 开关按钮 */
        .toggle-switch {
            position: relative;
            width: 44px;
            height: 24px;
            background: #E4E7ED;
            border-radius: 12px;
            cursor: pointer;
            transition: background-color 0.3s ease;
        }

        .toggle-switch.active {
            background: #5470C6;
        }

        .toggle-switch::before {
            content: '';
            position: absolute;
            top: 2px;
            left: 2px;
            width: 20px;
            height: 20px;
            background: white;
            border-radius: 50%;
            transition: transform 0.3s ease;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .toggle-switch.active::before {
            transform: translateX(20px);
        }

        /* 版本信息 */
        .version-info {
            text-align: center;
            padding: 20px;
            color: #999;
            font-size: 12px;
        }

        .version-number {
            font-weight: 600;
            margin-bottom: 4px;
        }

        .version-desc {
            line-height: 1.4;
        }

        /* 底部导航 */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-top: 1px solid rgba(0, 0, 0, 0.05);
            padding: 8px 0 20px;
        }

        .nav-grid {
            display: grid;
            grid-template-columns: repeat(5, 1fr);
            max-width: 375px;
            margin: 0 auto;
        }

        .nav-item {
            text-align: center;
            padding: 8px;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .nav-item.active {
            color: #5470C6;
        }

        .nav-icon {
            font-size: 20px;
            margin-bottom: 4px;
        }

        .nav-label {
            font-size: 10px;
            font-weight: 500;
        }

        /* 弹窗样式 */
        .modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.5);
            display: none;
            align-items: center;
            justify-content: center;
            z-index: 1000;
        }

        .modal.show {
            display: flex;
        }

        .modal-content {
            background: white;
            border-radius: 16px;
            padding: 24px;
            margin: 20px;
            max-width: 300px;
            width: 100%;
        }

        .modal-title {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 12px;
            text-align: center;
        }

        .modal-desc {
            font-size: 14px;
            color: #666;
            line-height: 1.5;
            text-align: center;
            margin-bottom: 20px;
        }

        .modal-actions {
            display: flex;
            gap: 12px;
        }

        .modal-btn {
            flex: 1;
            padding: 12px;
            border: 1px solid #E4E7ED;
            border-radius: 8px;
            background: white;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .modal-btn.primary {
            background: #5470C6;
            color: white;
            border-color: #5470C6;
        }
    </style>
</head>
<body>
    <div class="page-container">
        <!-- 页面头部 -->
        <div class="page-header">
            <div class="header-title">智能设置</div>
            <div class="header-action">帮助</div>
        </div>

        <!-- 用户信息 -->
        <div class="user-card">
            <div class="user-info">
                <div class="user-avatar">
                    <i class="fas fa-user"></i>
                </div>
                <div class="user-details">
                    <div class="user-name">张老师</div>
                    <div class="user-level">专业版用户</div>
                </div>
            </div>
        </div>

        <!-- AI配置 -->
        <div class="settings-group">
            <div class="group-title">AI配置</div>
            
            <div class="setting-item">
                <div class="setting-icon ai">
                    <i class="fas fa-robot"></i>
                </div>
                <div class="setting-content">
                    <div class="setting-title">评语风格偏好</div>
                    <div class="setting-desc">设置默认的评语生成风格</div>
                </div>
                <div class="setting-action">
                    <i class="fas fa-chevron-right"></i>
                </div>
            </div>
            
            <div class="setting-item">
                <div class="setting-icon ai">
                    <i class="fas fa-palette"></i>
                </div>
                <div class="setting-content">
                    <div class="setting-title">个性化模板</div>
                    <div class="setting-desc">自定义评语模板和关键词</div>
                </div>
                <div class="setting-action">
                    <i class="fas fa-chevron-right"></i>
                </div>
            </div>
            
            <div class="setting-item">
                <div class="setting-icon ai">
                    <i class="fas fa-brain"></i>
                </div>
                <div class="setting-content">
                    <div class="setting-title">智能建议</div>
                    <div class="setting-desc">开启AI智能优化建议</div>
                </div>
                <div class="toggle-switch active" onclick="toggleSwitch(this)"></div>
            </div>
        </div>

        <!-- 数据管理 -->
        <div class="settings-group">
            <div class="group-title">数据管理</div>
            
            <div class="setting-item">
                <div class="setting-icon data">
                    <i class="fas fa-cloud-upload-alt"></i>
                </div>
                <div class="setting-content">
                    <div class="setting-title">云端同步</div>
                    <div class="setting-desc">自动同步数据到云端</div>
                </div>
                <div class="toggle-switch active" onclick="toggleSwitch(this)"></div>
            </div>
            
            <div class="setting-item">
                <div class="setting-icon data">
                    <i class="fas fa-download"></i>
                </div>
                <div class="setting-content">
                    <div class="setting-title">导出数据</div>
                    <div class="setting-desc">导出所有评语和统计数据</div>
                </div>
                <div class="setting-action">
                    <i class="fas fa-chevron-right"></i>
                </div>
            </div>
            
            <div class="setting-item">
                <div class="setting-icon data">
                    <i class="fas fa-trash-alt"></i>
                </div>
                <div class="setting-content">
                    <div class="setting-title">清理缓存</div>
                    <div class="setting-desc">清理本地缓存数据</div>
                </div>
                <div class="setting-action">
                    <i class="fas fa-chevron-right"></i>
                </div>
            </div>
        </div>

        <!-- 隐私安全 -->
        <div class="settings-group">
            <div class="group-title">隐私安全</div>
            
            <div class="setting-item">
                <div class="setting-icon security">
                    <i class="fas fa-shield-alt"></i>
                </div>
                <div class="setting-content">
                    <div class="setting-title">数据加密</div>
                    <div class="setting-desc">启用本地数据加密保护</div>
                </div>
                <div class="toggle-switch active" onclick="toggleSwitch(this)"></div>
            </div>
            
            <div class="setting-item">
                <div class="setting-icon security">
                    <i class="fas fa-user-secret"></i>
                </div>
                <div class="setting-content">
                    <div class="setting-title">隐私政策</div>
                    <div class="setting-desc">查看隐私保护政策</div>
                </div>
                <div class="setting-action">
                    <i class="fas fa-chevron-right"></i>
                </div>
            </div>
        </div>

        <!-- 帮助支持 -->
        <div class="settings-group">
            <div class="group-title">帮助支持</div>
            
            <div class="setting-item">
                <div class="setting-icon help">
                    <i class="fas fa-question-circle"></i>
                </div>
                <div class="setting-content">
                    <div class="setting-title">使用指南</div>
                    <div class="setting-desc">查看详细使用教程</div>
                </div>
                <div class="setting-action">
                    <i class="fas fa-chevron-right"></i>
                </div>
            </div>
            
            <div class="setting-item">
                <div class="setting-icon help">
                    <i class="fas fa-comments"></i>
                </div>
                <div class="setting-content">
                    <div class="setting-title">意见反馈</div>
                    <div class="setting-desc">提交问题和建议</div>
                </div>
                <div class="setting-action">
                    <i class="fas fa-chevron-right"></i>
                </div>
            </div>
            
            <div class="setting-item">
                <div class="setting-icon about">
                    <i class="fas fa-info-circle"></i>
                </div>
                <div class="setting-content">
                    <div class="setting-title">关于我们</div>
                    <div class="setting-desc">应用信息和版本说明</div>
                </div>
                <div class="setting-action">
                    <i class="fas fa-chevron-right"></i>
                </div>
            </div>
        </div>

        <!-- 版本信息 -->
        <div class="version-info">
            <div class="version-number">AI智能评语助手 v3.0.0</div>
            <div class="version-desc">让每一句评语都充满温度</div>
        </div>
    </div>

    <!-- 底部导航 -->
    <div class="bottom-nav">
        <div class="nav-grid">
            <div class="nav-item">
                <div class="nav-icon"><i class="fas fa-home"></i></div>
                <div class="nav-label">首页</div>
            </div>
            <div class="nav-item">
                <div class="nav-icon"><i class="fas fa-magic"></i></div>
                <div class="nav-label">生成</div>
            </div>
            <div class="nav-item">
                <div class="nav-icon"><i class="fas fa-file-alt"></i></div>
                <div class="nav-label">作品</div>
            </div>
            <div class="nav-item">
                <div class="nav-icon"><i class="fas fa-chart-line"></i></div>
                <div class="nav-label">报告</div>
            </div>
            <div class="nav-item active">
                <div class="nav-icon"><i class="fas fa-cog"></i></div>
                <div class="nav-label">设置</div>
            </div>
        </div>
    </div>

    <!-- 确认弹窗 -->
    <div class="modal" id="confirmModal">
        <div class="modal-content">
            <div class="modal-title">确认操作</div>
            <div class="modal-desc">此操作将清理所有本地缓存数据，确定要继续吗？</div>
            <div class="modal-actions">
                <button class="modal-btn" onclick="closeModal()">取消</button>
                <button class="modal-btn primary" onclick="confirmAction()">确定</button>
            </div>
        </div>
    </div>

    <script>
        function toggleSwitch(element) {
            element.classList.toggle('active');
        }

        function showModal() {
            document.getElementById('confirmModal').classList.add('show');
        }

        function closeModal() {
            document.getElementById('confirmModal').classList.remove('show');
        }

        function confirmAction() {
            // 执行确认操作
            closeModal();
            // 这里可以添加实际的操作逻辑
        }

        // 设置项点击事件
        document.querySelectorAll('.setting-item').forEach(item => {
            item.addEventListener('click', function() {
                const title = this.querySelector('.setting-title').textContent;
                
                if (title === '清理缓存') {
                    showModal();
                } else {
                    // 其他设置项的处理逻辑
                    console.log('点击了:', title);
                }
            });
        });
    </script>
</body>
</html>
