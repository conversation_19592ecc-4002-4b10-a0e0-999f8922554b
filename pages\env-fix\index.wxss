/* 环境修复页面样式 */

.container {
  padding: 20rpx;
  background: #f5f7fa;
  min-height: 100vh;
}

.header {
  text-align: center;
  margin-bottom: 40rpx;
}

.title {
  display: block;
  font-size: 48rpx;
  font-weight: bold;
  color: #2c3e50;
  margin-bottom: 10rpx;
}

.subtitle {
  display: block;
  font-size: 28rpx;
  color: #7f8c8d;
}

.status-card {
  background: white;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.1);
}

.status-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
  padding-bottom: 20rpx;
  border-bottom: 1rpx solid #eee;
}

.status-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #2c3e50;
}

.status-indicator {
  width: 20rpx;
  height: 20rpx;
  border-radius: 50%;
  background: #e74c3c;
}

.status-indicator.success {
  background: #27ae60;
}

.status-item {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 15rpx;
}

.status-item:last-child {
  margin-bottom: 0;
}

.label {
  font-size: 28rpx;
  color: #7f8c8d;
  flex-shrink: 0;
  margin-right: 20rpx;
}

.value {
  font-size: 28rpx;
  color: #2c3e50;
  text-align: right;
  flex: 1;
  word-break: break-all;
}

.value.error {
  color: #e74c3c;
}

.actions {
  margin-bottom: 30rpx;
}

.btn {
  width: 100%;
  height: 88rpx;
  border-radius: 44rpx;
  font-size: 32rpx;
  margin-bottom: 20rpx;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
}

.btn.primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.btn.secondary {
  background: linear-gradient(135deg, #74b9ff, #0984e3);
  color: white;
}

.btn.outline {
  background: white;
  color: #667eea;
  border: 2rpx solid #667eea;
}

.btn.small {
  height: 60rpx;
  font-size: 28rpx;
  width: 48%;
  margin-bottom: 0;
}

.suggestions {
  background: white;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.1);
}

.suggestions-header {
  margin-bottom: 20rpx;
  padding-bottom: 20rpx;
  border-bottom: 1rpx solid #eee;
}

.suggestions-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #2c3e50;
}

.suggestion-item {
  margin-bottom: 15rpx;
  padding: 15rpx;
  background: #f8f9fa;
  border-radius: 10rpx;
  border-left: 4rpx solid #3498db;
}

.suggestion-item:last-child {
  margin-bottom: 0;
}

.suggestion-text {
  font-size: 28rpx;
  color: #2c3e50;
  line-height: 1.5;
}

.faq {
  background: white;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.1);
}

.faq-header {
  margin-bottom: 20rpx;
  padding-bottom: 20rpx;
  border-bottom: 1rpx solid #eee;
}

.faq-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #2c3e50;
}

.faq-item {
  margin-bottom: 25rpx;
}

.faq-item:last-child {
  margin-bottom: 0;
}

.faq-question {
  display: block;
  font-size: 28rpx;
  font-weight: bold;
  color: #2c3e50;
  margin-bottom: 10rpx;
}

.faq-answer {
  display: block;
  font-size: 26rpx;
  color: #7f8c8d;
  line-height: 1.5;
  padding-left: 20rpx;
}

.footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 20rpx;
}
