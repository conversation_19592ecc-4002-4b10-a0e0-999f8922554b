/**
 * 测试提示词模板功能
 * 用于调试云函数调用和数据格式问题
 */

import { getPromptTemplates } from '../services/authApi'
import cloudFunctionService from '../services/cloudFunctionService'

export const testPromptTemplatesFunction = async () => {
  console.log('🧪 开始测试提示词模板功能...')
  
  try {
    // 直接调用云函数服务
    console.log('🔍 1. 直接调用 cloudFunctionService...')
    const directResult = await cloudFunctionService.callFunction('managePromptTemplates', {
      action: 'getAll'
    })
    console.log('📥 直接调用结果:', directResult)
    
    // 通过 authApi 调用
    console.log('🔍 2. 通过 authApi 调用...')
    const apiResult = await getPromptTemplates()
    console.log('📥 authApi 调用结果:', apiResult)
    
    // 分析数据结构
    console.log('🔍 3. 数据结构分析...')
    if (directResult?.data) {
      console.log('📊 directResult.data 类型:', typeof directResult.data)
      console.log('📊 directResult.data 内容:', directResult.data)
      
      if (typeof directResult.data === 'object') {
        console.log('📊 directResult.data.success:', directResult.data.success)
        console.log('📊 directResult.data.data:', directResult.data.data)
        console.log('📊 directResult.data.count:', directResult.data.count)
      }
    }
    
    return {
      directResult,
      apiResult,
      success: true
    }
  } catch (error) {
    console.error('❌ 测试失败:', error)
    return {
      error: error.message,
      success: false
    }
  }
}

// 在浏览器控制台中调用的便捷函数
(window as any).testPromptTemplates = testPromptTemplatesFunction