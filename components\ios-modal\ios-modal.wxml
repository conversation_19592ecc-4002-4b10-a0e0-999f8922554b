<!--
  iOS风格弹窗组件 - 高级简约设计
-->
<view class="ios-modal-overlay {{show ? 'show' : ''}}" bindtap="onOverlayTap" wx:if="{{show}}">
  <view class="ios-modal-container" catchtap="stopPropagation">
    <!-- 弹窗头部 -->
    <view class="ios-modal-header" wx:if="{{title || icon}}">
      <view class="ios-modal-icon" wx:if="{{icon}}">
        <text class="icon-emoji">{{icon}}</text>
      </view>
      <view class="ios-modal-title" wx:if="{{title}}">{{title}}</view>
    </view>

    <!-- 弹窗内容 -->
    <view class="ios-modal-content">
      <!-- 描述文字 -->
      <view class="ios-modal-description" wx:if="{{description}}">
        {{description}}
      </view>

      <!-- 列表内容 -->
      <view class="ios-modal-list" wx:if="{{items && items.length > 0}}">
        <view class="ios-modal-list-item {{item.checked ? 'checked' : ''}}" 
              wx:for="{{items}}" 
              wx:key="id"
              data-index="{{index}}"
              bindtap="onItemTap">
          <view class="list-item-icon">
            <text class="item-emoji">{{item.icon}}</text>
          </view>
          <view class="list-item-content">
            <view class="list-item-title">{{item.title}}</view>
            <view class="list-item-subtitle" wx:if="{{item.subtitle}}">{{item.subtitle}}</view>
          </view>
          <view class="list-item-check" wx:if="{{showCheckmarks}}">
            <view class="check-circle {{item.checked ? 'checked' : ''}}">
              <text class="check-icon" wx:if="{{item.checked}}">✓</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 特性说明 -->
      <view class="ios-modal-features" wx:if="{{features && features.length > 0}}">
        <view class="feature-item" wx:for="{{features}}" wx:key="index">
          <view class="feature-dot"></view>
          <text class="feature-text">{{item}}</text>
        </view>
      </view>
    </view>

    <!-- 弹窗按钮 -->
    <view class="ios-modal-actions">
      <view class="ios-modal-button cancel" 
            wx:if="{{showCancel}}"
            bindtap="onCancel">
        <text class="button-text">{{cancelText || '取消'}}</text>
      </view>
      <view class="ios-modal-button confirm" 
            bindtap="onConfirm">
        <text class="button-text">{{confirmText || '确定'}}</text>
      </view>
    </view>
  </view>
</view>
