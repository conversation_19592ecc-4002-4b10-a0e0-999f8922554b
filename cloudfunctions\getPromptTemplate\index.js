/**
 * 获取提示词模板云函数
 * 专门为小程序端提供模板获取服务，无需管理员权限
 */

const cloud = require('wx-server-sdk');

// 初始化云开发环境
cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV  
});

const db = cloud.database();
const _ = db.command;

/**
 * 云函数入口函数
 */
exports.main = async (event, context) => {
  const { type, version } = event;
  
  try {
    console.log(`[getPromptTemplate] 请求获取模板: type=${type}, version=${version}`);
    
    if (!type) {
      throw new Error('模板类型不能为空');
    }
    
    // 查询模板数据
    let query = db.collection('prompt_templates').where({
      type: type,
      enabled: true
    });
    
    // 如果指定了版本，则查询特定版本
    if (version) {
      query = query.where({
        version: version
      });
    }
    
    // 获取最新版本的模板
    const templatesResult = await query.orderBy('version', 'desc').limit(1).get();
    
    if (templatesResult.data.length === 0) {
      console.log(`[getPromptTemplate] 未找到类型为${type}的模板，使用默认模板`);
      
      // 如果没有找到模板，返回默认模板
      const defaultTemplate = getDefaultTemplate(type);
      return {
        success: true,
        data: defaultTemplate,
        source: 'default'
      };
    }
    
    const template = templatesResult.data[0];
    console.log(`[getPromptTemplate] 找到模板: ${template.name}, 版本: ${template.version}`);
    
    // 转换为统一格式
    const formattedTemplate = {
      id: template._id,
      name: template.name,
      type: template.type,
      content: template.content,
      variables: template.variables || [],
      metadata: template.metadata || {},
      version: template.version || 1,
      enabled: template.enabled !== false,
      source: 'database',
      cachedAt: Date.now(),
      cacheVersion: 2
    };
    
    // 更新使用统计
    try {
      await db.collection('prompt_templates').doc(template._id).update({
        data: {
          'metadata.usageCount': _.inc(1),
          'metadata.lastUsedAt': new Date().toISOString()
        }
      });
    } catch (updateError) {
      console.warn('[getPromptTemplate] 更新使用统计失败:', updateError);
      // 不影响主流程
    }
    
    return {
      success: true,
      data: formattedTemplate,
      source: 'database'
    };
    
  } catch (error) {
    console.error('[getPromptTemplate] 获取模板失败:', error);
    
    // 发生错误时返回默认模板作为兜底
    try {
      const defaultTemplate = getDefaultTemplate(type || 'warm');
      return {
        success: true,
        data: defaultTemplate,
        source: 'fallback',
        error: error.message
      };
    } catch (fallbackError) {
      return {
        success: false,
        error: error.message || '获取模板失败',
        fallbackError: fallbackError.message
      };
    }
  }
};

/**
 * 获取默认模板（兜底方案）- 使用新管理后台的提示词模板
 */
function getDefaultTemplate(type) {
  const defaultTemplates = {
    'formal': {
      id: 'formal_default',
      name: '正式规范型',
      type: 'formal',
      content: `你是一位拥有15年教龄、经验丰富、充满爱心且善于观察的班主任。你的任务是根据提供的学生日常表现素材，为学生撰写一份全面、客观、个性化、充满关怀的学期综合评语。

学生姓名：
<student_name>
{{学生姓名}}
</student_name>

学生本学期的日常表现素材如下：
<performance_material>
{{行为记录}}
</performance_material>

请严格遵循以下要求撰写评语：
1. **评语结构**：采用"总体评价 + 优点详述 + 待改进点与期望 + 结尾祝福"的结构。
2. **内容要求**：
   * **优点详述部分**：必须从素材中提炼2-3个最突出的优点，并引用具体事例来支撑，让表扬不空洞。
   * **待改进点部分**：如果素材中有相关记录，请用委婉、鼓励的语气指出，并提出具体、可行的建议。如果没有，则可以写一些普遍性的鼓励和期望。
3. **语气风格**：语言要真诚、平实、温暖，既要体现班主任老师的专业性，又要充满人文关怀，让学生和家长感受到被尊重和关爱。称呼以学生名字（不带姓，如小华同学）开头，文字风格要符合高中阶段学生的年龄阶段。
4. **个性化**：评语必须尊重、保留且紧密结合提供的素材，体现出每个学生的独特性，严禁使用千篇一律的套话，严禁捏造素材中不存在的内容。
5. **日常行为记录缺失处理要求**：当表现素材为空，则只输出"无行为依据，无法生评语！"

请直接生成完整的评语内容，100-150字之间。`,
      variables: [
        { name: 'studentName', type: 'string', required: true, description: '学生姓名' },
        { name: 'performanceMaterial', type: 'text', required: true, description: '学生表现材料' }
      ],
      metadata: {
        author: 'system',
        description: '正式严谨的评语模板',
        tags: ['formal', 'professional', 'default'],
        usageCount: 0
      },
      version: 1,
      enabled: true,
      source: 'hardcoded',
      cachedAt: Date.now(),
      cacheVersion: 2
    },
    
    'warm': {
      id: 'warm_default',
      name: '温和亲切型',
      type: 'warm',
      content: `你是一位特别有亲和力、善于用温暖话语鼓励学生的班主任。你的目光总能捕捉到每个孩子身上独特的闪光点。你的任务是根据提供的学生日常表现素材，为学生撰写一份充满善意与温情的学期综合评语。

学生姓名：
<student_name>
{{学生姓名}}
</student_name>

学生本学期的日常表现素材如下：
<performance_material>
{{行为记录}}
</performance_material>

请严格遵循以下要求撰写评语：
1. **评语结构**：采用"如沐春风的总体印象 + 令人欣喜的闪光点 + 温馨提示与小期待 + 美好的祝愿"的结构。
2. **内容要求**：
   * **闪光点部分**：从素材中提炼2-3个最暖心的优点，并结合具体的小事来描述，让表扬听起来格外真诚、亲切。
   * **温馨提示部分**：如果素材中有相关记录，请用"如果……就更好了"的句式，像聊天一样温柔地提出小建议。如果没有，则可以写一些对未来生活的美好期盼。
3. **语气风格**：语言要像邻家大姐姐/大哥哥一样，充满生活气息和真挚的情感。多用微笑、拥抱等词语，营造亲密的氛围。称呼以学生可爱的昵称或名字（不带姓，如小宇同学）开头。
4. **个性化**：评语必须紧密围绕素材，发掘学生独特的可爱之处，严禁使用生硬的套话，让学生感觉这份评语是专门为他/她写的"悄悄话"。
5. **日常行为记录缺失处理要求**：当表现素材为空，则只输出"老师还不太了解你，无法写出对你的心里话！"

请直接生成完整的评语内容，100-150字之间。`,
      variables: [
        { name: 'studentName', type: 'string', required: true, description: '学生姓名' },
        { name: 'performanceMaterial', type: 'text', required: true, description: '学生表现材料' }
      ],
      metadata: {
        author: 'system',
        description: '温暖亲切的评语模板',
        tags: ['warm', 'caring', 'default'],
        usageCount: 0
      },
      version: 1,
      enabled: true,
      source: 'hardcoded',
      cachedAt: Date.now(),
      cacheVersion: 2
    },
    
    'encouraging': {
      id: 'encouraging_default',
      name: '鼓励激励型',
      type: 'encouraging',
      content: `你是一位充满激情与活力的"教练型"班主任，善于发现并点燃学生的潜能。你的任务是根据提供的学生日常表现素材，为学生撰写一份能激发其斗志、明确其方向的学期综合评语。

学生姓名：
<student_name>
{{学生姓名}}
</student_name>

学生本学期的日常表现素材如下：
<performance_material>
{{行为记录}}
</performance_material>

请严格遵循以下要求撰写评语：
1. **评语结构**：采用"潜力与亮点的总体判断 + 蓄力起跳的优势分析 + 迎接挑战的成长阶梯 + 充满力量的期许"的结构。
2. **内容要求**：
   * **优势分析部分**：必须从素材中提炼2-3个核心优势，将其定义为未来成功的基石，并用有力的事例证明其价值。
   * **成长阶梯部分**：将待改进点包装成"下一个要攻克的关卡"或"一项值得挑战的任务"，用"相信你只要……""期待你突破……"等句式，给出清晰的行动指令。
3. **语气风格**：语言要坚定、有力、富有感染力，多用积极动词和带有希望的词汇（如"未来"、"潜能"、"突破"）。称呼以学生全名或名字（如张伟同学/张伟）开头，营造一种郑重而充满信任的氛围。
4. **个性化**：评语必须聚焦于学生独特的潜质，精准地从素材中提炼出其最可能获得突破的领域，让学生感到自己被寄予厚望。严禁空泛的鼓励。
5. **日常行为记录缺失处理要求**：当表现素材为空，则只输出"缺乏具体依据，无法进行潜力评估！"

请直接生成完整的评语内容，100-150字之间。`,
      variables: [
        { name: 'studentName', type: 'string', required: true, description: '学生姓名' },
        { name: 'performanceMaterial', type: 'text', required: true, description: '学生表现材料' }
      ],
      metadata: {
        author: 'system',
        description: '激励性评语模板',
        tags: ['encouraging', 'motivational', 'default'],
        usageCount: 0
      },
      version: 1,
      enabled: true,
      source: 'hardcoded',
      cachedAt: Date.now(),
      cacheVersion: 2
    },
    
    'detailed': {
      id: 'detailed_default',
      name: '详细具体型',
      type: 'detailed',
      content: `你是一位观察力极其敏锐、逻辑清晰、注重事实依据的"分析师型"班主任。你的任务是根据提供的学生日常表现素材，为学生撰写一份数据详实、论证严谨、指导性强的学期综合评语。

学生姓名：
<student_name>
{{学生姓名}}
</student_name>

学生本学期的日常表现素材如下：
<performance_material>
{{行为记录}}
</performance_material>

请严格遵循以下要求撰写评语：
1. **评语结构**：采用"基于事实的行为总结 + 数据支撑的优点剖析 + 具体问题的现象与建议 + 指向明确的未来规划"的结构。
2. **内容要求**：
   * **优点剖析部分**：必须从素材中提炼2-3个可量化的优点。例如，不仅仅说"乐于助人"，而是要说明"本学期累计帮助同学解决问题5次以上"，让评价有据可查。
   * **问题与建议部分**：必须明确指出素材中反映的具体问题现象，并提供1-2条具有高度可操作性的改进步骤。例如，针对"作业拖延"，建议"尝试使用番茄工作法，每学习25分钟休息5分钟，来提高专注度"。
3. **语气风格**：语言要客观、冷静、精准，如同在进行一次专业的复盘。避免过多的情感化描述，用事实和数据说话，体现专业性和指导性。称呼以学生全名（如李华同学）开头。
4. **个性化**：评语必须严格基于素材中的时间、地点、事件、数据等具体信息，进行逻辑严密的分析和归纳，杜绝任何形式的模糊评价和主观臆断。
5. **日常行为记录缺失处理要求**：当表现素材为空，则只输出"无行为数据记录，无法生成分析报告！"

请直接生成完整的评语内容，100-150字之间。`,
      variables: [
        { name: 'studentName', type: 'string', required: true, description: '学生姓名' },
        { name: 'performanceMaterial', type: 'text', required: true, description: '学生表现材料' }
      ],
      metadata: {
        author: 'system',
        description: '详细全面的分析型评语模板',
        tags: ['detailed', 'comprehensive', 'default'],
        usageCount: 0
      },
      version: 1,
      enabled: true,
      source: 'hardcoded',
      cachedAt: Date.now(),
      cacheVersion: 2
    },
    
    'comprehensive': {
      id: 'comprehensive_default',
      name: '综合发展型',
      type: 'comprehensive',
      content: `你是一位经验丰富、眼光长远的资深班主任，善于从多个维度全面评价学生的发展。你的任务是根据提供的学生日常表现素材，为学生撰写一份全面而平衡的学期综合评语。

学生姓名：
<student_name>
{{学生姓名}}
</student_name>

学生本学期的日常表现素材如下：
<performance_material>
{{行为记录}}
</performance_material>

请严格遵循以下要求撰写评语：
1. **评语结构**：采用"综合表现概述 + 多维度优势展示 + 均衡发展建议 + 全面成长期许"的结构。
2. **内容要求**：
   * **多维度展示部分**：从学习态度、品德表现、能力发展、社交能力等多个角度分析学生优势。
   * **均衡建议部分**：针对需要加强的方面，提出具体而可行的发展建议，注重全面发展。
3. **语气风格**：语言要稳重、全面、具有前瞻性，既有鼓励也有指导，体现教育者的专业素养。
4. **个性化**：基于素材全面分析学生的成长轨迹，给出个性化的发展规划。
5. **日常行为记录缺失处理要求**：当表现素材为空，则只输出"需要更多观察时间，期待看到你的全面发展！"

请直接生成完整的评语内容，100-150字之间。`,
      variables: [
        { name: 'studentName', type: 'string', required: true, description: '学生姓名' },
        { name: 'performanceMaterial', type: 'text', required: true, description: '学生表现材料' }
      ],
      metadata: {
        author: 'system',
        description: '全面发展的综合型评语模板',
        tags: ['comprehensive', 'balanced', 'default'],
        usageCount: 0
      },
      version: 1,
      enabled: true,
      source: 'hardcoded',
      cachedAt: Date.now(),
      cacheVersion: 2
    },
    
    'gentle': {
      id: 'gentle_default',
      name: '温和亲切型',
      type: 'gentle',
      content: `你是一位特别有亲和力、善于用温暖话语鼓励学生的班主任。你的目光总能捕捉到每个孩子身上独特的闪光点。你的任务是根据提供的学生日常表现素材，为学生撰写一份充满善意与温情的期综合评语。

学生姓名：
<student_name>
{{学生姓名}}
</student_name>

学生本学期的日常表现素材如下：
<performance_material>
{{行为记录}}
</performance_material>

请严格遵循以下要求撰写评语：
1. **评语结构**：采用"如沐春风的总体印象 + 令人欣喜的闪光点 + 温馨提示与小期待 + 美好的祝愿"的结构。
2. **内容要求**：
   * **闪光点部分**：从素材中提炼2-3个最暖心的优点，并结合具体的小事来描述，让表扬听起来格外真诚、亲切。
   * **温馨提示部分**：如果素材中有相关记录，请用"如果……就更好了"的句式，像聊天一样温柔地提出小建议。如果没有，则可以写一些对未来生活的美好期盼。
3. **语气风格**：语言要像邻家大姐姐/大哥哥一样，充满生活气息和真挚的情感。多用微笑、拥抱等词语，营造亲密的氛围。称呼以学生可爱的昵称或名字（不带姓，如小宇同学）开头。
4. **个性化**：评语必须紧密围绕素材，发掘学生独特的可爱之处，严禁使用生硬的套话，让学生感觉这份评语是专门为他/她写的"悄悄话"。
5. **日常行为记录缺失处理要求**：当表现素材为空，则只输出"老师还不太了解你，无法写出对你的心里话！"

请直接生成完整的评语内容，100-150字之间。`,
      variables: [
        { name: 'studentName', type: 'string', required: true, description: '学生姓名' },
        { name: 'performanceMaterial', type: 'text', required: true, description: '学生表现材料' }
      ],
      metadata: {
        author: 'system',
        description: '充满关爱、如沐春风般温暖的评语',
        tags: ['gentle', 'warm', 'default'],
        usageCount: 0
      },
      version: 1,
      enabled: true,
      source: 'hardcoded',
      cachedAt: Date.now(),
      cacheVersion: 2
    }
  };
  
  return defaultTemplates[type] || defaultTemplates['warm'];
}