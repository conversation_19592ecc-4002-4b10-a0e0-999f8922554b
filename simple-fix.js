/**
 * 简化修复脚本 - 直接在小程序控制台运行
 */

console.log('🔧 开始简化修复...');

// 直接添加配置到正确的集合
const config = {
  type: 'ai_config',
  provider: 'doubao',
  apiKey: '4d73215c-5512-418b-8749-db9514df3c75',
  model: 'doubao-seed-1-6-flash-250715',
  apiUrl: 'https://ark.cn-beijing.volces.com/api/v3/chat/completions',
  temperature: 0.7,
  maxTokens: 300,
  status: 'active',
  createTime: new Date(),
  updateTime: new Date()
};

// 执行修复
wx.cloud.database().collection('system_config')
  .where({ type: 'ai_config' })
  .remove()
  .then(() => {
    console.log('✅ 清理旧配置成功');
    return wx.cloud.database().collection('system_config').add({ data: config });
  })
  .then((result) => {
    console.log('✅ 添加新配置成功:', result);
    // 立即测试
    return wx.cloud.callFunction({
      name: 'doubaoAI',
      data: {
        prompt: '测试',
        temperature: 0.1,
        max_tokens: 10
      }
    });
  })
  .then((testResult) => {
    console.log('🧪 测试结果:', testResult);
    if (testResult.result && !testResult.result.isFallback) {
      console.log('🎉 成功！现在应该可以正常使用AI功能了');
    } else {
      console.log('⚠️ 仍在使用备用方案，请检查云函数部署');
    }
  })
  .catch((error) => {
    console.error('❌ 修复失败:', error);
  });