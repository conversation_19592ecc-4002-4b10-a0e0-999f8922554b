/**
 * 数据桥接服务 - 已简化为directDataService的代理
 * 架构优化：减少复杂度，统一使用dataService
 * 此文件保留仅为兼容性，建议直接使用dataService
 */

import { dataService } from './index'

interface CloudFunctionResponse<T = any> {
  code: number
  message: string
  data: T
  timestamp: string
}

interface UserData {
  id: string
  openid: string
  wechatName: string
  avatar?: string
  lastActivity: string
  activityCount: number
  totalTokens: number
  totalComments: number
}

interface CommentData {
  id: string
  studentName: string
  className: string
  content: string
  teacherName: string
  createTime: string
  aiModel: string
  tokenUsed: number
  status: string
}

interface StudentData {
  id: string
  name: string
  studentId: string
  className: string
  gender: string
  phone?: string
  parentName?: string
  createTime: string
}

class DataBridgeService {
  private listeners: Map<string, Array<(data: any) => void>> = new Map()
  private lastSyncTime = 0
  private isConnected = true // 直接标记为已连接

  constructor() {
    console.log('🔌 数据桥接服务初始化 - 重定向到统一dataService')
    console.log('⚠️ 建议直接使用dataService，此服务将在未来版本中移除')
  }

  /**
   * 初始化连接 - 已简化
   */
  private async initDirectConnection(): Promise<void> {
    console.log('✅ DataBridge服务已简化，直接使用dataService')
    this.isConnected = true
  }

  // 已移除复杂的桥接逻辑，直接使用dataService

  /**
   * 设置消息监听器
   */
  private setupMessageListener(): void {
    if (typeof window !== 'undefined') {
      window.addEventListener('message', (event) => {
        this.handleBridgeMessage(event.data)
      })
    }
  }

  /**
   * 处理来自小程序桥接页面的消息
   */
  private handleBridgeMessage(message: any): void {
    if (!message || typeof message !== 'object') return

    console.log('📨 收到桥接消息:', message)

    switch (message.type) {
      case 'bridge_ready':
        this.isConnected = true
        console.log('✅ 小程序桥接已就绪')
        this.notifyListeners('connection_ready', message.data)
        break

      case 'bridge_error':
        this.isConnected = false
        console.error('❌ 小程序桥接错误:', message.data)
        this.notifyListeners('connection_error', message.data)
        break

      case 'action_result':
        this.handleActionResult(message)
        break

      case 'data_sync':
        this.handleDataSync(message.data)
        break

      case 'full_sync':
        this.handleFullSync(message.data)
        break
    }
  }

  /**
   * 启动数据同步 - 已简化
   */
  async startSync(): Promise<void> {
    console.log('🚀 DataBridge数据同步 - 重定向到dataService')
    this.isConnected = true
  }

  /**
   * 连接到小程序桥接页面
   */
  private async connectToBridge(): Promise<void> {
    try {
      console.log('🔗 尝试连接小程序桥接页面...')
      
      // 方案1: 尝试打开小程序桥接页面（如果支持）
      // 注意: 这需要用户手动打开小程序并导航到桥接页面
      
      // 方案2: 使用云函数直接调用方案作为备选
      await this.fallbackToCloudFunction()
      
    } catch (error) {
      console.error('❌ 连接小程序桥接失败:', error)
      throw error
    }
  }

  /**
   * 备选方案：直接调用云函数
   */
  private async fallbackToCloudFunction(): Promise<void> {
    console.log('🔄 使用云函数直接调用作为备选方案')
    this.isConnected = true
  }

  /**
   * 停止数据同步
   */
  stopSync(): void {
    console.log('⏹️ 停止数据同步服务')
    if (this.pollingTimer) {
      clearInterval(this.pollingTimer)
      this.pollingTimer = null
    }
  }

  /**
   * 同步所有数据
   */
  private async syncAllData(): Promise<void> {
    try {
      console.log('🔄 开始同步所有数据...')
      
      // 并行获取所有数据
      const [dashboardStats, recentActivities, usersList, commentsList] = await Promise.all([
        this.getDashboardStats(),
        this.getRecentActivities(),
        this.getUsersList(),
        this.getCommentsList()
      ])

      // 通知监听器数据更新
      this.notifyListeners('dashboard_stats', dashboardStats)
      this.notifyListeners('recent_activities', recentActivities)
      this.notifyListeners('users_list', usersList)
      this.notifyListeners('comments_list', commentsList)
      this.notifyListeners('sync_complete', {
        timestamp: Date.now(),
        lastSyncTime: this.lastSyncTime
      })

      this.lastSyncTime = Date.now()
      console.log('✅ 数据同步完成')

    } catch (error) {
      console.error('❌ 数据同步失败:', error)
      this.notifyListeners('sync_error', error)
    }
  }

  /**
   * 获取仪表板统计数据 - 重定向到dataService
   */
  async getDashboardStats(): Promise<any> {
    console.log('🔄 DataBridge重定向: getDashboardStats → dataService')
    return await dataService.getDashboardStats()
  }

  /**
   * 获取最新的桥接数据
   */
  private getLatestBridgeData(type: string): any {
    try {
      const keys = Object.keys(localStorage)
        .filter(key => key.startsWith(`admin_bridge_${type}_`))
        .sort()
        .reverse() // 最新的在前面
      
      if (keys.length > 0) {
        const data = localStorage.getItem(keys[0])
        return data ? JSON.parse(data) : null
      }
      
      return null
    } catch (error) {
      console.error('获取桥接数据失败:', error)
      return null
    }
  }

  /**
   * 获取最近活动记录 - 重定向到dataService
   */
  async getRecentActivities(limit = 10): Promise<any[]> {
    console.log('🔄 DataBridge重定向: getRecentActivities → dataService')
    return await dataService.getRecentActivities(limit)
  }

  /**
   * 获取用户列表 - 重定向到dataService
   */
  async getUsersList(): Promise<UserData[]> {
    console.log('🔄 DataBridge重定向: getUsersList → dataService')
    // dataService暂时没有这个方法，返回空数组
    return []
  }

  /**
   * 获取评语列表 - 重定向到dataService
   */
  async getCommentsList(limit = 50): Promise<CommentData[]> {
    console.log('🔄 DataBridge重定向: getCommentsList → dataService')
    // 使用dataService的getComments方法
    const result = await dataService.getComments({ limit })
    return result.list || []
  }

  /**
   * 测试数据库连接 - 重定向到dataService
   */
  async testConnection(): Promise<any> {
    console.log('🔄 DataBridge重定向: testConnection → dataService')
    return await dataService.testConnection()
  }

  /**
   * 获取系统性能指标 - 重定向到dataService
   */
  async getSystemMetrics(): Promise<any> {
    console.log('🔄 DataBridge重定向: getSystemMetrics → dataService')
    return await dataService.getSystemMetrics()
  }

  /**
   * 已废弃 - 不再使用HTTP调用adminAPI
   * 统一使用dataQuery云函数通过Web SDK调用
   */
  private async callCloudFunction(action: string, data: any = {}): Promise<CloudFunctionResponse> {
    console.log(`⚠️ 警告: callCloudFunction已废弃，请使用dataQuery云函数`)
    throw new Error('此方法已废弃，请使用directDataService')
  }

  /**
   * 订阅数据更新
   */
  on(event: string, callback: (data: any) => void): void {
    if (!this.listeners.has(event)) {
      this.listeners.set(event, [])
    }
    this.listeners.get(event)!.push(callback)
  }

  /**
   * 取消订阅
   */
  off(event: string, callback: (data: any) => void): void {
    const callbacks = this.listeners.get(event)
    if (callbacks) {
      const index = callbacks.indexOf(callback)
      if (index > -1) {
        callbacks.splice(index, 1)
      }
    }
  }

  /**
   * 通知监听器
   */
  private notifyListeners(event: string, data: any): void {
    const callbacks = this.listeners.get(event)
    if (callbacks) {
      callbacks.forEach(callback => {
        try {
          callback(data)
        } catch (error) {
          console.error('监听器回调错误:', error)
        }
      })
    }
  }

  /**
   * 生成请求ID
   */
  private generateRequestId(): string {
    return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  /**
   * 手动触发数据同步
   */
  async forceSyncData(): Promise<void> {
    console.log('🔄 手动触发数据同步')
    await this.directDataService.forceSyncData()
    this.lastSyncTime = Date.now()
  }

  /**
   * 获取同步状态
   */
  getSyncStatus(): {
    isRunning: boolean
    lastSyncTime: number
    pollingInterval: number
  } {
    return this.directDataService.getSyncStatus()
  }
}

// 单例实例
let dataBridgeServiceInstance: DataBridgeService | null = null

/**
 * 获取数据桥接服务实例
 */
export const getDataBridgeService = (): DataBridgeService => {
  if (!dataBridgeServiceInstance) {
    dataBridgeServiceInstance = new DataBridgeService()
  }
  return dataBridgeServiceInstance
}

export default DataBridgeService
export type { UserData, CommentData, StudentData, CloudFunctionResponse }