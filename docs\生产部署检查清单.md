# 🚀 评语灵感君生产部署检查清单

## ⚠️ 部署前必读

**此清单必须100%完成才能进行生产部署！**

## 📋 部署前检查清单

### 🔧 环境配置（必须）

- [ ] **云开发环境配置**
  - [ ] 生产环境ID已正确配置：`cloud1-4g85f8xlb8166ff1`
  - [ ] 开发环境和生产环境已分离
  - [ ] 环境切换逻辑已测试

- [ ] **环境变量配置**
  - [ ] 复制`.env.example`为`.env`并填入真实值
  - [ ] `DOUBAO_API_KEY`已配置且有效
  - [ ] `MINIPROGRAM_APP_ID`和`MINIPROGRAM_APP_SECRET`已配置
  - [ ] `CLOUD_ENV_ID`已配置为生产环境ID
  - [ ] 监控相关环境变量已配置（SENTRY_DSN_PROD等）

### ☁️ 云函数配置（必须）

- [ ] **核心云函数部署**
  - [ ] `login` - 用户登录
  - [ ] `getUserId` - 获取用户ID
  - [ ] `getStudents` - 获取学生列表
  - [ ] `callDoubaoAPI` - AI服务调用
  - [ ] `generateComment` - 评语生成
  - [ ] `adminAPI` - 管理后台API

- [ ] **云函数环境变量**
  - [ ] 每个云函数的环境变量已正确配置
  - [ ] API密钥已在云函数环境中设置
  - [ ] 超时时间已合理配置

### 🗄️ 数据库配置（必须）

- [ ] **数据库初始化**
  - [ ] 运行`initDatabase`云函数
  - [ ] 所有必需的集合已创建
  - [ ] 数据库索引已创建
  - [ ] 数据库权限已正确配置

- [ ] **数据安全**
  - [ ] 生产数据和测试数据已分离
  - [ ] 数据备份策略已制定
  - [ ] 数据恢复流程已测试

### 🤖 AI服务配置（必须）

- [ ] **豆包AI配置**
  - [ ] API密钥有效且有足够额度
  - [ ] API调用限制已了解
  - [ ] 错误处理和重试机制已测试
  - [ ] 降级策略已实现

### 🔒 安全配置（必须）

- [ ] **访问控制**
  - [ ] 用户认证机制已完善
  - [ ] 数据访问权限已正确配置
  - [ ] 输入验证已实现
  - [ ] SQL注入防护已实现

- [ ] **数据加密**
  - [ ] 敏感数据已加密存储
  - [ ] 传输过程已使用HTTPS
  - [ ] 加密密钥已安全管理

### 📊 监控配置（推荐）

- [ ] **错误监控**
  - [ ] Sentry已配置
  - [ ] 错误告警已设置
  - [ ] 错误日志已配置

- [ ] **性能监控**
  - [ ] 响应时间监控已启用
  - [ ] 资源使用监控已启用
  - [ ] 用户行为分析已配置

### 🧪 测试验证（必须）

- [ ] **功能测试**
  - [ ] 用户登录流程测试
  - [ ] 学生管理功能测试
  - [ ] AI评语生成测试
  - [ ] 数据同步测试

- [ ] **性能测试**
  - [ ] 并发用户测试
  - [ ] 大数据量测试
  - [ ] 网络异常测试

- [ ] **安全测试**
  - [ ] 权限控制测试
  - [ ] 输入验证测试
  - [ ] 数据泄露测试

### 🚀 部署流程（必须）

- [ ] **部署准备**
  - [ ] 运行`node scripts/pre-deploy-check.js`
  - [ ] 所有检查项都通过
  - [ ] 代码已提交到版本控制

- [ ] **部署执行**
  - [ ] 使用正确的部署脚本
  - [ ] 部署过程有日志记录
  - [ ] 部署后进行健康检查

- [ ] **部署验证**
  - [ ] 所有核心功能正常
  - [ ] 数据库连接正常
  - [ ] AI服务调用正常
  - [ ] 监控系统正常

### 🔄 回滚准备（必须）

- [ ] **回滚策略**
  - [ ] 回滚脚本已准备
  - [ ] 数据备份已完成
  - [ ] 回滚流程已测试

## 🚨 部署后立即检查

### 立即验证（5分钟内）
- [ ] 小程序能正常打开
- [ ] 用户能正常登录
- [ ] 核心功能能正常使用
- [ ] 没有明显错误

### 短期监控（1小时内）
- [ ] 监控系统显示正常
- [ ] 没有大量错误日志
- [ ] 响应时间在正常范围
- [ ] 用户反馈正常

### 持续监控（24小时内）
- [ ] 系统稳定运行
- [ ] 性能指标正常
- [ ] 用户使用正常
- [ ] 没有数据异常

## ⚡ 紧急联系方式

- **技术负责人**: [填入联系方式]
- **运维负责人**: [填入联系方式]
- **产品负责人**: [填入联系方式]

## 📞 应急处理

如果部署后出现问题：

1. **立即回滚**：使用准备好的回滚脚本
2. **通知相关人员**：按照联系方式通知
3. **记录问题**：详细记录问题现象和处理过程
4. **分析原因**：部署后分析问题原因并改进

---

**⚠️ 重要提醒：只有当所有检查项都完成时，才能进行生产部署！**
