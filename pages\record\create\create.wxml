<!--
  快速记录 - 莫兰迪简约设计
-->
<view class="morandi-page">
  <!-- 页面标题 -->
  <view class="page-header">
    <view class="page-title">快速记录</view>
    <view class="page-subtitle">记录学生的精彩瞬间</view>
  </view>

  <!-- 学生选择卡片 -->
  <view class="function-card">
    <view class="card-header">
      <view class="card-icon-wrapper">
        <view class="card-icon student-icon">
          <text class="icon-text">👤</text>
        </view>
      </view>
      <view class="card-info">
        <text class="card-title">选择学生</text>
      </view>
    </view>

    <!-- 已选择学生 -->
    <view wx:if="{{selectedStudent}}" class="selected-student-card" bindtap="showStudentPicker">
      <view class="student-avatar-wrapper">
        <view class="student-avatar">
          <image
            wx:if="{{selectedStudent.avatar}}"
            src="{{selectedStudent.avatar}}"
            class="avatar-image"
            mode="aspectFill"
          />
          <view wx:else class="avatar-placeholder">
            <text class="avatar-text">{{selectedStudent.surname || selectedStudent.name.charAt(0)}}</text>
          </view>
        </view>
      </view>

      <view class="student-info">
        <view class="student-name">{{selectedStudent.name}}</view>
        <view class="student-class">{{selectedStudent.className}}</view>
      </view>

      <view wx:if="{{!isEditMode}}" class="change-btn" catchtap="showStudentPicker">
        <text class="change-text">更换</text>
      </view>
    </view>

    <!-- 学生选择器 -->
    <view wx:else class="student-selector" bindtap="showStudentPicker">
      <view class="selector-content">
        <text class="selector-text">点击选择学生</text>
        <text class="selector-hint">选择要记录的学生</text>
      </view>
      <view class="selector-arrow">→</view>
    </view>
  </view>

  <!-- 核心记录内容卡片 - 最重要的区域 -->
  <view class="record-card">
    <!-- 简化标题 -->
    <view class="record-title">
      <text class="title-icon">📝</text>
      <text class="title-text">记录学生表现</text>
    </view>

    <!-- 输入区域 -->
    <view class="input-area">
      <textarea
        class="content-input"
        placeholder="请详细记录学生的具体行为表现，如：上课时主动举手回答问题，回答准确且声音洪亮，展现出良好的学习态度和积极性..."
        value="{{formData.recordContent}}"
        bindinput="onRecordContentChange"
        maxlength="500"
        auto-height="{{true}}"
        show-confirm-bar="{{false}}"
      />

      <!-- 底部工具栏 -->
      <view class="input-toolbar">
        <view class="char-count">{{formData.recordContent.length}}/500</view>
        <view class="upload-btn" bindtap="chooseImage">
          <text class="upload-icon">📷</text>
          <text class="upload-label">添加图片</text>
        </view>
      </view>

      <!-- 表现评分滑杆 -->
      <view class="score-section">
        <view class="score-header">
          <text class="score-label">表现评分</text>
          <view class="score-value-display">
            <text class="score-number">{{formData.score}}</text>
            <text class="score-text">分</text>
          </view>
        </view>
        <view class="score-slider-area">
          <slider
            value="{{formData.score}}"
            min="0"
            max="10"
            step="0.5"
            block-size="20"
            activeColor="#5470C6"
            backgroundColor="rgba(84, 112, 198, 0.12)"
            bindchange="onScoreChange"
            bindchanging="onScoreChange"
            class="custom-slider"
          />
          <view class="score-marks">
            <text class="mark-label">较差</text>
            <text class="mark-label">一般</text>
            <text class="mark-label">优秀</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 图片展示 -->
    <view class="images-section" wx:if="{{images.length > 0}}">
      <view class="images-list">
        <view
          wx:for="{{images}}"
          wx:key="index"
          class="image-preview"
          data-index="{{index}}"
          bindtap="previewImage"
        >
          <image src="{{item}}" class="preview-img" mode="aspectFill" />
          <view class="remove-btn" data-index="{{index}}" catchtap="deleteImage">
            <text class="remove-icon">×</text>
          </view>
        </view>
      </view>
    </view>

  </view>

  <!-- 保存按钮 - 紧跟容器底部 -->
  <view class="submit-btn {{submitting ? 'loading' : ''}}" bindtap="onSubmit">
    <view class="submit-content">
      <van-loading size="20px" color="white" wx:if="{{submitting}}" />
      <text class="submit-text">{{submitting ? '保存中...' : '保存记录'}}</text>
    </view>
  </view>



  <!-- 学生选择底部弹窗 -->
  <view class="bottom-popup-mask {{showStudentPopup ? 'show' : ''}}" bindtap="hideStudentPicker" wx:if="{{showStudentPopup}}">
    <view class="bottom-popup {{showStudentPopup ? 'show' : ''}}" catchtap="preventClose">
      <!-- 拖拽指示器 -->
      <view class="drag-indicator"></view>

      <!-- 弹窗标题 -->
      <view class="popup-title-bar">
        <text class="popup-title">选择学生</text>
        <view class="close-icon" bindtap="hideStudentPicker">
          <text>×</text>
        </view>
      </view>

      <!-- 搜索栏 -->
      <view class="search-container">
        <view class="search-box">
          <input
            class="search-input"
            placeholder="输入学生姓名或班级"
            value="{{studentSearchKeyword}}"
            bindinput="onStudentSearchChange"
            focus="{{false}}"
          />
          <view class="search-clear {{studentSearchKeyword ? 'show' : ''}}" bindtap="onStudentSearchClear">
            <text class="clear-icon">×</text>
          </view>
        </view>
        <view class="search-tip" wx:if="{{studentSearchKeyword}}">
          <text class="tip-text">找到 {{filteredStudents.length}} 个匹配的学生</text>
        </view>
      </view>

      <!-- 学生列表 -->
      <scroll-view class="student-scroll-list" scroll-y="{{true}}">
        <view
          class="student-row {{item.isSelected ? 'selected' : ''}}"
          wx:for="{{filteredStudents}}"
          wx:key="id"
          data-student="{{item}}"
          bindtap="selectStudent"
        >
          <view class="student-info">
            <view class="student-avatar">{{item.surname || item.name.charAt(0)}}</view>
            <view class="student-details">
              <text class="student-name">{{item.name}}</text>
              <text class="student-class">{{item.className}}</text>
            </view>
          </view>
          <view class="selection-indicator {{item.isSelected ? 'active' : ''}}">
            <text class="check-mark">✓</text>
          </view>
        </view>

        <!-- 空状态 -->
        <view class="empty-state" wx:if="{{filteredStudents.length === 0}}">
          <view class="empty-icon">{{studentSearchKeyword ? '🔍' : '👥'}}</view>
          <text class="empty-text">{{studentSearchKeyword ? '没有找到匹配的学生' : '暂无学生数据'}}</text>
          <text class="empty-hint" wx:if="{{studentSearchKeyword}}">请尝试输入其他关键词</text>
          <text class="empty-hint" wx:if="{{!studentSearchKeyword}}">请先在学生管理中添加学生</text>
        </view>
      </scroll-view>
    </view>
  </view>

  <!-- 保存成功弹窗 -->
  <view class="save-success-mask {{showSaveSuccessDialog ? 'show' : ''}}" wx:if="{{showSaveSuccessDialog}}" bindtap="hideSaveSuccessDialog">
    <view class="save-success-dialog {{showSaveSuccessDialog ? 'show' : ''}}" catchtap="preventClose">
      <!-- 成功图标 -->
      <view class="success-icon-wrapper">
        <view class="success-icon">✓</view>
      </view>

      <!-- 标题和描述 -->
      <view class="dialog-content">
        <view class="dialog-title">记录已保存</view>
        <view class="dialog-desc">
          已成功保存 <text class="student-name">{{savedRecordData.studentName}}</text> 的行为记录
        </view>
      </view>

      <!-- 操作按钮 -->
      <view class="dialog-actions">
        <view class="action-row">
          <button class="action-btn secondary" bindtap="onReEditRecord">
            <text class="btn-icon">✏️</text>
            <text class="btn-text">重新编辑</text>
          </button>
          <button class="action-btn primary" bindtap="onViewStudentDetail">
            <text class="btn-icon">👤</text>
            <text class="btn-text">查看详情</text>
          </button>
        </view>
        <view class="action-row">
          <button class="action-btn continue" bindtap="onContinueRecord">
            <text class="btn-icon">➕</text>
            <text class="btn-text">继续记录该学生</text>
          </button>
        </view>
        <view class="action-row">
          <button class="action-btn new-record" bindtap="onRecordOtherStudent">
            <text class="btn-icon">👥</text>
            <text class="btn-text">记录其他学生</text>
          </button>
        </view>
      </view>
    </view>
  </view>
</view>