<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>纯HTML测试 - 评语灵感君</title>
    <style>
        body {
            margin: 0;
            font-family: -apple-system, BlinkMacSystemFont, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .login-box {
            background: white;
            padding: 40px;
            border-radius: 10px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            width: 400px;
            text-align: center;
        }
        .title {
            color: #333;
            margin-bottom: 30px;
            font-size: 24px;
            font-weight: bold;
        }
        .form-group {
            margin-bottom: 20px;
            text-align: left;
        }
        label {
            display: block;
            margin-bottom: 5px;
            color: #666;
            font-weight: 500;
        }
        input {
            width: 100%;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
            box-sizing: border-box;
        }
        .login-btn {
            width: 100%;
            padding: 12px;
            background: #667eea;
            color: white;
            border: none;
            border-radius: 5px;
            font-size: 16px;
            cursor: pointer;
            margin-top: 10px;
            transition: background 0.3s;
        }
        .login-btn:hover {
            background: #5a6fd8;
        }
        .test-info {
            margin-top: 20px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 5px;
            color: #666;
            font-size: 14px;
        }
        .success {
            background: #d4edda;
            color: #155724;
            padding: 15px;
            border-radius: 5px;
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <div class="login-box">
        <h1 class="title">🤖 评语灵感君管理后台</h1>
        
        <form id="loginForm">
            <div class="form-group">
                <label for="username">用户名</label>
                <input type="text" id="username" name="username" required>
            </div>
            
            <div class="form-group">
                <label for="password">密码</label>
                <input type="password" id="password" name="password" required>
            </div>
            
            <button type="submit" class="login-btn">登录</button>
        </form>
        
        <div class="test-info">
            <strong>测试账号：</strong>admin<br>
            <strong>测试密码：</strong>admin123
        </div>
        
        <div id="result" style="display: none;"></div>
    </div>

    <script>
        console.log('🚀 纯HTML版本加载完成');
        
        document.getElementById('loginForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            const resultDiv = document.getElementById('result');
            
            console.log('登录尝试:', { username, password });
            
            if (username === 'admin' && password === 'admin123') {
                resultDiv.innerHTML = '<div class="success">✅ 登录成功！如果这个版本正常工作，说明问题在React/Vite配置中。</div>';
                resultDiv.style.display = 'block';
                console.log('✅ 登录验证成功');
            } else {
                resultDiv.innerHTML = '<div style="background: #f8d7da; color: #721c24; padding: 15px; border-radius: 5px;">❌ 用户名或密码错误</div>';
                resultDiv.style.display = 'block';
                console.log('❌ 登录验证失败');
            }
        });
        
        // 每秒输出一次状态，确认页面没有异常刷新
        let counter = 0;
        setInterval(() => {
            counter++;
            console.log(`⏰ 页面运行正常 ${counter}秒`);
        }, 1000);
    </script>
</body>
</html>