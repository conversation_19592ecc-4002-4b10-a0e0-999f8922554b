/* 成长报告页面 - 严格按照原型图设计 */

.page-container {
  padding: 40rpx 32rpx 200rpx;
  max-width: 750rpx;
  margin: 0 auto;
  background: linear-gradient(135deg, #F0F2F5 0%, #E4E7ED 100%);
  min-height: 100vh;
}

/* 页面头部 */
.page-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 48rpx;
  padding-top: 20rpx;
}

.header-title {
  font-size: 40rpx;
  font-weight: 600;
  color: #333;
}

.header-action {
  color: #5470C6;
  font-size: 28rpx;
  cursor: pointer;
}

/* 成长概览卡片 */
.growth-overview {
  background: linear-gradient(135deg, #5470C6 0%, #73C0DE 100%);
  border-radius: 40rpx;
  padding: 48rpx;
  margin-bottom: 48rpx;
  color: white;
  position: relative;
  overflow: hidden;
}

.growth-overview::before {
  content: '';
  position: absolute;
  top: -50%;
  right: -50%;
  width: 400rpx;
  height: 400rpx;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
}

.overview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 40rpx;
  position: relative;
  z-index: 1;
}

.overview-title {
  font-size: 36rpx;
  font-weight: 600;
}

.overview-period {
  font-size: 24rpx;
  opacity: 0.8;
  background: rgba(255, 255, 255, 0.2);
  padding: 8rpx 24rpx;
  border-radius: 24rpx;
}

.overview-stats {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 32rpx;
  position: relative;
  z-index: 1;
}

.overview-stat {
  text-align: center;
}

.stat-value {
  font-size: 56rpx;
  font-weight: 700;
  display: block;
  margin-bottom: 8rpx;
}

.stat-label {
  font-size: 24rpx;
  opacity: 0.9;
}

.stat-change {
  font-size: 22rpx;
  margin-top: 8rpx;
  opacity: 0.8;
}

.stat-change.negative {
  color: #ff6b6b;
}

/* 效率统计 */
.efficiency-section {
  background: rgba(255, 255, 255, 0.9);
  border-radius: 32rpx;
  padding: 40rpx;
  margin-bottom: 40rpx;
  backdrop-filter: blur(20rpx);
  border: 1rpx solid rgba(255, 255, 255, 0.3);
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 32rpx;
  display: flex;
  align-items: center;
}

.section-icon {
  margin-right: 16rpx;
  color: #5470C6;
}


.efficiency-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 32rpx;
}

.efficiency-item {
  text-align: center;
  padding: 32rpx;
  background: #F8F9FA;
  border-radius: 24rpx;
}

.efficiency-icon {
  font-size: 48rpx;
  margin-bottom: 16rpx;
  color: #5470C6;
}

.efficiency-number {
  font-size: 40rpx;
  font-weight: 700;
  color: #333;
  margin-bottom: 8rpx;
}

.efficiency-label {
  font-size: 24rpx;
  color: #666;
}

/* 质量分析 */
.quality-section {
  background: rgba(255, 255, 255, 0.9);
  border-radius: 32rpx;
  padding: 40rpx;
  margin-bottom: 40rpx;
  backdrop-filter: blur(20rpx);
  border: 1rpx solid rgba(255, 255, 255, 0.3);
}

.quality-chart {
  background: #F8F9FA;
  border-radius: 24rpx;
  padding: 32rpx;
  margin-bottom: 32rpx;
  text-align: center;
}

.chart-placeholder {
  height: 240rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #666;
  font-size: 28rpx;
  border: 4rpx dashed #E4E7ED;
  border-radius: 16rpx;
}

/* 图表容器 */
.chart-container {
  width: 100%;
}

.chart-header {
  margin-bottom: 24rpx;
  text-align: center;
}

.chart-title {
  display: block;
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 8rpx;
}

.chart-subtitle {
  display: block;
  font-size: 24rpx;
  color: #666;
}

.chart-content {
  display: flex;
  height: 240rpx;
  position: relative;
}

.chart-y-axis {
  width: 60rpx;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: flex-end;
  padding-right: 16rpx;
}

.y-label {
  font-size: 20rpx;
  color: #999;
  line-height: 1;
}

.chart-area {
  flex: 1;
  position: relative;
  background: #FAFAFA;
  border-radius: 8rpx;
}

.chart-grid {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.grid-line {
  height: 1rpx;
  background: #E8E8E8;
  width: 100%;
}

/* 柱状图样式 */
.chart-bars {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 100%;
  display: flex;
  align-items: flex-end;
  padding: 16rpx 8rpx 40rpx 8rpx;
}

.chart-bar-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  height: 100%;
  position: relative;
}

.chart-bar {
  width: 24rpx;
  min-height: 4rpx;
  border-radius: 4rpx 4rpx 0 0;
  margin-bottom: 8rpx;
  transition: all 0.3s ease;
}

.bar-value {
  font-size: 20rpx;
  color: #333;
  font-weight: 600;
  margin-bottom: 4rpx;
}

.bar-label {
  position: absolute;
  bottom: -32rpx;
  font-size: 18rpx;
  color: #666;
  transform: rotate(-45deg);
  transform-origin: center;
  white-space: nowrap;
}

/* 折线图样式 */
.chart-line-container {
  position: absolute;
  top: 16rpx;
  left: 8rpx;
  right: 8rpx;
  bottom: 40rpx;
}

.chart-line-path {
  position: relative;
  width: 100%;
  height: 100%;
}

.chart-point {
  position: absolute;
  transform: translate(-50%, 50%);
}

.point-dot {
  width: 12rpx;
  height: 12rpx;
  background: #5470C6;
  border-radius: 50%;
  border: 3rpx solid #fff;
  box-shadow: 0 2rpx 8rpx rgba(84, 112, 198, 0.3);
  cursor: pointer;
  transition: all 0.3s ease;
}

.point-dot:hover {
  transform: scale(1.2);
}

/* 质量趋势折线图样式 */
.quality-point {
  width: 16rpx;
  height: 16rpx;
  border: 4rpx solid #fff;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.2);
}

/* 每日记录统计样式 */
.daily-records-section {
  background: rgba(255, 255, 255, 0.9);
  border-radius: 32rpx;
  padding: 40rpx;
  margin-bottom: 40rpx;
  backdrop-filter: blur(20rpx);
  border: 1rpx solid rgba(255, 255, 255, 0.3);
}

.daily-chart-container {
  margin-top: 32rpx;
}

.daily-bar {
  background: linear-gradient(to top, #5470C6, #91CC75);
  border-radius: 8rpx 8rpx 0 0;
  border: 1rpx solid rgba(84, 112, 198, 0.3);
  box-shadow: 0 2rpx 8rpx rgba(84, 112, 198, 0.2);
}

.point-tooltip {
  position: absolute;
  bottom: 24rpx;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 8rpx 12rpx;
  border-radius: 8rpx;
  font-size: 20rpx;
  white-space: nowrap;
  opacity: 0;
  pointer-events: none;
  transition: opacity 0.3s ease;
  z-index: 10;
}

.point-tooltip text {
  display: block;
  line-height: 1.4;
}

.chart-point:hover .point-tooltip {
  opacity: 1;
}

.chart-x-labels {
  position: absolute;
  bottom: 0;
  left: 8rpx;
  right: 8rpx;
  height: 32rpx;
  display: flex;
  align-items: center;
}

.x-label {
  position: absolute;
  font-size: 18rpx;
  color: #666;
  transform: translateX(-50%);
  white-space: nowrap;
}

.quality-metrics {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 24rpx;
}

.quality-metric {
  text-align: center;
  padding: 24rpx;
  background: #F8F9FA;
  border-radius: 16rpx;
}

.metric-score {
  font-size: 36rpx;
  font-weight: 700;
  color: #52C41A;
  margin-bottom: 8rpx;
}

.metric-label {
  font-size: 22rpx;
  color: #666;
}

/* 成就展示 */
.achievement-section {
  background: rgba(255, 255, 255, 0.9);
  border-radius: 32rpx;
  padding: 40rpx;
  margin-bottom: 40rpx;
  backdrop-filter: blur(20rpx);
  border: 1rpx solid rgba(255, 255, 255, 0.3);
}

.achievement-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 24rpx;
}

.achievement-item {
  background: linear-gradient(135deg, #FFD700 0%, #FFA500 100%);
  border-radius: 24rpx;
  padding: 32rpx;
  text-align: center;
  color: white;
  position: relative;
  overflow: hidden;
  cursor: pointer;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.achievement-item:hover {
  transform: translateY(-4rpx);
  box-shadow: 0 8rpx 24rpx rgba(255, 215, 0, 0.4);
}

.achievement-item.unlocked {
  background: linear-gradient(135deg, #FFD700 0%, #FFA500 100%);
  color: white;
  box-shadow: 0 8rpx 24rpx rgba(255, 215, 0, 0.3);
  transform: scale(1.02);
}

.achievement-item.locked {
  background: linear-gradient(135deg, #E5E5E5 0%, #CCCCCC 100%);
  color: #999;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.achievement-icon {
  font-size: 56rpx;
  margin-bottom: 16rpx;
}

.achievement-title {
  font-size: 28rpx;
  font-weight: 600;
  margin-bottom: 8rpx;
}

.achievement-desc {
  font-size: 24rpx;
  opacity: 0.9;
}

.achievement-progress {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 6rpx;
  background: rgba(255, 255, 255, 0.3);
}

.progress-fill {
  height: 100%;
  background: rgba(255, 255, 255, 0.8);
  transition: width 0.3s ease;
}

/* 趋势图表 */
.trend-section {
  background: rgba(255, 255, 255, 0.9);
  border-radius: 32rpx;
  padding: 40rpx;
  backdrop-filter: blur(20rpx);
  border: 1rpx solid rgba(255, 255, 255, 0.3);
}

.trend-chart {
  background: #F8F9FA;
  border-radius: 24rpx;
  padding: 32rpx;
  height: 300rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #666;
  font-size: 28rpx;
  border: 4rpx dashed #E4E7ED;
}

/* 徽章状态样式 */
.achievement-status {
  margin-top: 8rpx;
  font-size: 24rpx;
}

.status-unlocked {
  color: #52C41A;
  font-weight: 600;
}

.status-progress {
  color: rgba(255, 255, 255, 0.8);
}

.achievement-item.locked .status-progress {
  color: #999;
}

.achievement-item.locked .achievement-progress {
  background: rgba(0, 0, 0, 0.1);
}

.achievement-item.locked .progress-fill {
  background: #ccc;
}

/* 徽章解锁动画 */
@keyframes achievementUnlock {
  0% {
    transform: scale(1);
    box-shadow: 0 0 0 rgba(255, 215, 0, 0.7);
  }
  50% {
    transform: scale(1.05);
    box-shadow: 0 0 20rpx rgba(255, 215, 0, 0.7);
  }
  100% {
    transform: scale(1);
    box-shadow: 0 0 0 rgba(255, 215, 0, 0);
  }
}

.achievement-item.newly-unlocked {
  animation: achievementUnlock 1s ease-in-out;
}

/* 空状态样式 */
.achievement-empty {
  grid-column: 1 / -1;
  text-align: center;
  padding: 60rpx 40rpx;
  color: #999;
}

.empty-icon {
  font-size: 64rpx;
  margin-bottom: 16rpx;
}

.empty-text {
  font-size: 28rpx;
}