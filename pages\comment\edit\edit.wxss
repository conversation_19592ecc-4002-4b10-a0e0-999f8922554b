/* 评语编辑页面样式 */

/* 防止页面水平滚动 */
page {
  overflow-x: hidden;
  width: 100%;
}
.edit-page {
  height: 100vh;
  width: 100vw;
  background-color: #f5f5f5;
  display: flex;
  flex-direction: column;
  overflow-x: hidden;
  position: relative;
}

.content-container {
  flex: 1;
  padding: 0 24rpx;
  width: 100%;
  box-sizing: border-box;
  overflow-x: hidden;
}

/* 学生信息区域 */
.student-info-section {
  margin-bottom: 24rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 16rpx;
  padding: 24rpx 0 8rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.student-card {
  background: white;
  border-radius: 16rpx;
  padding: 24rpx;
  display: flex;
  align-items: center;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
}

.student-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  background: linear-gradient(135deg, #4080FF, #6366F1);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20rpx;
}

.avatar-text {
  font-size: 32rpx;
  font-weight: 600;
  color: white;
}

.student-details {
  flex: 1;
}

.student-name {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 8rpx;
}

.student-class {
  font-size: 26rpx;
  color: #666;
}

/* 评语内容区域 */
.content-section {
  margin-bottom: 24rpx;
}

.word-count {
  font-size: 24rpx;
  color: #999;
  font-weight: normal;
}

.word-count.error {
  color: #FF5247;
}

.content-textarea {
  width: 100%;
  min-height: 300rpx;
  background: white;
  border-radius: 16rpx;
  padding: 24rpx;
  font-size: 28rpx;
  line-height: 1.6;
  color: #333;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
  border: 2rpx solid transparent;
  transition: border-color 0.3s ease;
  box-sizing: border-box;
  word-wrap: break-word;
  overflow-wrap: break-word;
}

.content-textarea:focus {
  border-color: #4080FF;
}

/* 设置区域 */
.settings-section {
  margin-bottom: 24rpx;
}

.setting-item {
  background: white;
  border-radius: 16rpx;
  padding: 24rpx;
  margin-bottom: 16rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
}

.setting-label {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 16rpx;
}

.picker-display {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
  border: 1rpx solid #e9ecef;
}

.picker-text {
  font-size: 28rpx;
  color: #333;
}

/* 历史记录区域 */
.history-section {
  margin-bottom: 24rpx;
}

.history-item {
  background: white;
  border-radius: 16rpx;
  padding: 24rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
}

.history-info {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.history-action {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

.history-time {
  font-size: 24rpx;
  color: #999;
}

/* 底部间距 */
.bottom-spacing {
  height: 120rpx; /* 增加底部间距，确保按钮完全显示 */
}

/* 底部操作栏 */
.bottom-actions {
  position: relative;
  margin: 32rpx 0; /* 调整外边距 */
  margin-bottom: 40rpx;
  padding: 0 24rpx; /* 使用内边距控制水平空间 */
  display: flex;
  gap: 16rpx;
  width: 100%; /* 确保全宽 */
  box-sizing: border-box; /* 包含内边距在内的盒模型 */
}

.action-btn {
  flex: 1;
  height: 88rpx !important;
  border-radius: 16rpx !important;
  font-size: 28rpx !important;
  font-weight: 600 !important;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1) !important;
  max-width: none !important; /* 移除最大宽度限制 */
  min-width: 0 !important; /* 允许按钮收缩 */
}

.cancel-btn {
  background: #f8f9fa !important;
  color: #666 !important;
  border: 1rpx solid #e9ecef !important;
}

.regenerate-btn {
  background: #52c41a !important;
  color: white !important;
  border: none !important;
}

.save-btn {
  background: #4080FF !important;
  color: white !important;
}

/* 加载状态居中 */
.van-loading {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

/* 响应式适配 */
@media (max-width: 750rpx) {
  .content-container {
    padding: 0 16rpx;
  }

  .bottom-actions {
    margin: 24rpx 0; /* 调整小屏幕的外边距 */
    margin-bottom: 32rpx; /* 确保小屏幕也有足够的底部空间 */
    padding: 0 16rpx; /* 使用内边距 */
    gap: 12rpx;
  }

  .action-btn {
    height: 80rpx !important;
    font-size: 26rpx !important;
  }

  .bottom-spacing {
    height: 100rpx; /* 小屏幕适当减少间距 */
  }
}