/**
 * 评语灵感君小程序应用入口
 * 基于微信云开发
 */
const { storeUtils } = require('./store/index');
const stateManager = require('./utils/stateManager');
const errorHandler = require('./utils/errorHandler');
const globalUtils = require('./utils/globalUtils');
const { databaseConnectionManager } = require('./utils/databaseConnectionManager');
const { realTimeSyncManager } = require('./utils/realTimeSyncManager');
const { systemMonitor } = require('./utils/systemMonitor');
const { getCurrentCloudConfig, initCloudDevelopment } = require('./config/cloudConfig');
// cloudService将在云开发初始化完成后动态加载

App({
  /**
   * 全局数据
   */
  globalData: {
    userInfo: null,
    userInfoUpdateTime: 0, // 用户信息更新时间戳
    token: null,
    isLogin: false,
    cloudConfig: getCurrentCloudConfig(), // 统一云开发配置
    cloudEnv: getCurrentCloudConfig().env, // 云开发环境ID
    db: null, // 云数据库实例
    stateManager: stateManager, // 全局状态管理器
    errorHandler: errorHandler, // 全局错误处理器
    globalUtils: globalUtils, // 全局工具函数
    databaseConnectionManager: databaseConnectionManager, // 数据库连接管理器
    realTimeSyncManager: realTimeSyncManager, // 实时同步管理器
    systemMonitor: systemMonitor, // 系统监控器
    cloudService: null, // 云服务实例
    degradedMode: false // 降级模式标识
  },

  /**
   * 应用启动时触发
   */
  async onLaunch(options) {
    console.log('评语灵感君小程序启动', options);

    // 紧急修复模式：如果检测到环境问题，立即修复
    await this.emergencyFixIfNeeded();

    // 初始化云开发
    await this.initCloud();

    // 初始化应用
    this.initApp();
  },

  /**
   * 紧急修复检测
   */
  async emergencyFixIfNeeded() {
    try {
      console.log('[App] 检测是否需要紧急修复...');

      // 快速测试云开发是否正常
      if (wx.cloud) {
        try {
          wx.cloud.init({ env: 'cloud1-4g85f8xlb8166ff1' });
          const db = wx.cloud.database();
          await db.collection('users').limit(1).get();
          console.log('[App] 云开发环境正常，无需修复');
          return;
        } catch (error) {
          console.log('[App] 检测到云开发问题，启动紧急修复:', error.message);
        }
      }

      // 启动紧急修复
      const { emergencyFix } = require('./utils/emergencyFix');
      const fixResult = await emergencyFix.emergencyFix();

      if (fixResult.success) {
        console.log('[App] 紧急修复成功');
        wx.showToast({
          title: '环境修复成功',
          icon: 'success'
        });
      } else {
        console.error('[App] 紧急修复失败:', fixResult.message);
        this.globalData.degradedMode = true;
      }

    } catch (error) {
      console.error('[App] 紧急修复过程异常:', error);
      this.globalData.degradedMode = true;
    }
  },

  /**
   * 应用显示时触发
   */
  onShow(options) {
    console.log('评语灵感君小程序显示', options);

    // 执行生产环境数据清理
    this.performProductionCleanup();
  },

  /**
   * 应用隐藏时触发
   */
  onHide() {
    console.log('评语灵感君小程序隐藏');
  },

  /**
   * 执行生产环境数据清理
   */
  performProductionCleanup() {
    try {
      const { performFullCleanup } = require('./utils/dataCleanup');
      const { performProductionCheck } = require('./utils/productionDataValidator');

      // 执行数据清理（静默模式，不显示toast）
      performFullCleanup(null, { showToast: false });

      // 执行生产环境检查
      const checkResult = performProductionCheck();

      if (!checkResult.isClean) {
        console.warn('⚠️ 生产环境检查发现问题:', checkResult.issues);
      }

      console.log('✅ 生产环境数据清理完成');
    } catch (error) {
      console.error('❌ 生产环境数据清理失败:', error);
    }
  },

  /**
   * 应用发生错误时触发
   */
  onError(error) {
    console.error('评语灵感君小程序错误:', error);

    // 特殊处理常见错误
    if (error && error.message) {
      if (error.message.includes('__route__ is not defined')) {
        console.warn('路由变量未定义错误，可能是页面生命周期问题');
        return;
      }
      if (error.message.includes('is not defined')) {
        console.warn('变量未定义错误:', error.message);
        return;
      }
      if (error.message.includes('module') && error.message.includes('is not defined')) {
        console.warn('模块加载错误，启用降级模式:', error.message);
        this.globalData.degradedMode = true;
        return;
      }
    }

    // 使用全局错误处理器
    if (this.globalData.errorHandler) {
      this.globalData.errorHandler.handleError(error);
    }
  },

  /**
   * 页面不存在时触发
   */
  onPageNotFound(res) {
    console.log('页面不存在:', res);
    
    // 跳转到首页
    wx.switchTab({
      url: '/pages/index/index'
    });
  },

  /**
   * 初始化云开发 - 增强版，解决环境验证问题
   */
  async initCloud() {
    if (!wx.cloud) {
      console.error('请使用 2.2.3 或以上的基础库以使用云能力');
      return;
    }

    try {
      console.log('[App] 开始云开发初始化...');

      // 强制清除可能的缓存
      if (wx.cloud._initialized) {
        console.log('[App] 检测到云开发已初始化，重置状态...');
        delete wx.cloud._initialized;
      }

      // 使用强制配置初始化
      const cloudConfig = {
        env: 'cloud1-4g85f8xlb8166ff1', // 强制使用正确的环境ID
        traceUser: true,
        timeout: 60000
      };

      console.log('[App] 使用配置初始化云开发:', cloudConfig);

      // 多次尝试初始化
      let initSuccess = false;
      for (let attempt = 1; attempt <= 3; attempt++) {
        try {
          console.log(`[App] 第${attempt}次尝试初始化云开发...`);

          wx.cloud.init(cloudConfig);

          // 测试连接
          const db = wx.cloud.database();
          await db.collection('users').limit(1).get();

          console.log(`[App] 第${attempt}次初始化成功！`);
          initSuccess = true;
          break;

        } catch (error) {
          console.error(`[App] 第${attempt}次初始化失败:`, error);
          if (attempt < 3) {
            console.log('[App] 等待2秒后重试...');
            await new Promise(resolve => setTimeout(resolve, 2000));
          }
        }
      }

      if (!initSuccess) {
        throw new Error('多次尝试初始化云开发均失败');
      }

      // 获取云数据库实例
      this.globalData.db = wx.cloud.database();
      this.globalData.cloudEnv = cloudConfig.env;

      console.log('[App] 云开发初始化完成，环境:', this.globalData.cloudEnv);

      // 云开发初始化完成后，再初始化cloudService
      await this.initCloudService();

    } catch (error) {
      console.error('[App] 云开发初始化失败:', error);

      // 降级处理
      this.handleCloudInitFailure(error);
    }
  },

  /**
   * 初始化云服务 - 增强版
   */
  async initCloudService() {
    try {
      console.log('[App] 开始初始化云服务...');
      
      // 初始化数据库连接管理器
      console.log('[App] 初始化数据库连接管理器...');
      // 数据库连接管理器已在导入时自动初始化
      
      // 初始化云服务
      const { cloudService } = require('./services/cloudService');
      await cloudService.init();
      this.globalData.cloudService = cloudService;
      console.log('[App] 云服务初始化完成');
      
      // 初始化实时同步管理器
      console.log('[App] 实时同步管理器已自动初始化');
      
      // 测试连接状态
      await this.testSystemConnections();
      
      // 启动系统监控
      console.log('[App] 启动系统监控...');
      // 系统监控已在导入时自动初始化并启动
      
    } catch (error) {
      console.error('[App] 云服务初始化失败:', error);
      
      // 初始化失败时的降级处理
      this.handleInitializationFailure(error);
    }
  },

  /**
   * 处理云开发初始化失败
   */
  handleCloudInitFailure(error) {
    console.error('[App] 云开发初始化失败，启用降级模式:', error);

    this.globalData.degradedMode = true;

    // 显示用户友好的错误提示
    wx.showModal({
      title: '云开发连接异常',
      content: '检测到云开发环境连接问题，部分功能可能受限。是否尝试修复？',
      confirmText: '去修复',
      cancelText: '稍后再说',
      success: (res) => {
        if (res.confirm) {
          // 跳转到环境修复页面
          wx.navigateTo({
            url: '/pages/env-fix/index'
          }).catch(() => {
            // 如果页面不存在，显示修复指南
            wx.showModal({
              title: '修复指南',
              content: '请检查微信开发者工具中的云开发环境设置，确认环境ID是否正确。',
              showCancel: false
            });
          });
        }
      }
    });
  },

  /**
   * 获取系统状态报告
   */
  getSystemStatusReport() {
    try {
      return this.globalData.systemMonitor.getStatusReport();
    } catch (error) {
      console.error('[App] 获取系统状态报告失败:', error);
      return null;
    }
  },

  /**
   * 手动触发系统检查
   */
  async triggerSystemCheck() {
    try {
      console.log('[App] 手动触发系统检查');
      return await this.globalData.systemMonitor.triggerManualCheck();
    } catch (error) {
      console.error('[App] 手动系统检查失败:', error);
      return null;
    }
  },

  /**
   * 测试系统连接状态
   */
  async testSystemConnections() {
    try {
      console.log('[App] 开始测试系统连接状态...');
      
      // 测试数据库连接
      const dbStats = this.globalData.databaseConnectionManager.getConnectionStats();
      console.log('[App] 数据库连接状态:', dbStats);
      
      // 测试云服务连接
      if (this.globalData.cloudService) {
        await this.globalData.cloudService.testConnection();
        console.log('[App] 云服务连接测试通过');
      }
      
      console.log('[App] 系统连接状态测试完成');
      
    } catch (error) {
      console.warn('[App] 系统连接测试失败:', error);
      // 连接测试失败不影响应用启动
    }
  },

  /**
   * 处理初始化失败
   */
  handleInitializationFailure(error) {
    console.error('[App] 处理初始化失败:', error);
    
    // 记录错误到本地
    try {
      const errorLog = {
        timestamp: new Date().toISOString(),
        error: error.message || error.toString(),
        type: 'initialization_failure'
      };
      
      let errorLogs = wx.getStorageSync('error_logs') || [];
      errorLogs.push(errorLog);
      
      // 只保留最近50条错误日志
      if (errorLogs.length > 50) {
        errorLogs = errorLogs.slice(-50);
      }
      
      wx.setStorageSync('error_logs', errorLogs);
    } catch (logError) {
      console.error('[App] 记录错误日志失败:', logError);
    }
    
    // 设置降级模式标识
    this.globalData.degradedMode = true;
    
    // 显示用户友好的错误提示
    setTimeout(() => {
      wx.showToast({
        title: '服务初始化中，部分功能可能受限',
        icon: 'none',
        duration: 3000
      });
    }, 1000);
  },


  /**
   * 初始化应用
   */
  async initApp() {
    try {
      // 检查更新
      this.checkUpdate();

      // 获取系统信息
      this.getSystemInfo();

      // 初始化用户信息
      await this.initUserInfo();

    } catch (error) {
      console.error('应用初始化失败:', error);
    }
  },

  /**
   * 检查小程序更新
   */
  checkUpdate() {
    if (wx.canIUse('getUpdateManager')) {
      const updateManager = wx.getUpdateManager();
      
      updateManager.onCheckForUpdate((res) => {
        if (res.hasUpdate) {
          console.log('发现新版本');
        }
      });
      
      updateManager.onUpdateReady(() => {
        wx.showModal({
          title: '更新提示',
          content: '新版本已经准备好，是否重启应用？',
          success: (res) => {
            if (res.confirm) {
              updateManager.applyUpdate();
            }
          }
        });
      });
      
      updateManager.onUpdateFailed(() => {
        console.error('新版本下载失败');
      });
    }
  },

  /**
   * 获取系统信息
   */
  getSystemInfo() {
    try {
      const systemInfo = {
        ...wx.getDeviceInfo(),
        ...wx.getWindowInfo(),
        ...wx.getAppBaseInfo()
      };
      this.globalData.systemInfo = systemInfo;
      
      console.log('系统信息:', systemInfo);
    } catch (error) {
      console.error('获取系统信息失败:', error);
    }
  },

  /**
   * 初始化用户信息
   */
  async initUserInfo() {
    try {
      // 检查登录状态（简化版本，直接从本地存储获取）
      const userInfo = wx.getStorageSync('userInfo');
      if (userInfo) {
        storeUtils.setUserInfo(userInfo);
        this.globalData.isLogin = true;
      }
    } catch (error) {
      console.error('初始化用户信息失败:', error);
    }
  },

  /**
   * 验证token有效性
   */
  async validateToken(token) {
    try {
      // 简化版本：直接从本地存储获取用户信息，避免云函数依赖
      const userInfo = wx.getStorageSync('userInfo');
      if (userInfo && token) {
        console.log('Token验证通过（本地模式）');
        return userInfo;
      }
      console.log('Token验证失败：无有效用户信息');
      return null;
    } catch (error) {
      console.error('验证token失败:', error);
      return null;
    }
  },

  /**
   * 获取用户信息
   */
  getUserInfo(callback) {
    const userInfo = storeUtils.getUserInfo();
    if (userInfo) {
      callback && callback(userInfo);
    } else {
      // 尝试从本地存储获取
      const localUserInfo = wx.getStorageSync('userInfo');
      if (localUserInfo) {
        storeUtils.setUserInfo(localUserInfo);
        callback && callback(localUserInfo);
      } else {
        callback && callback(null);
      }
    }
  },

  /**
   * 用户登录
   */
  async login() {
    try {
      // 获取微信登录code
      const loginRes = await new Promise((resolve, reject) => {
        wx.login({
          success: resolve,
          fail: reject
        });
      });

      if (loginRes.code) {
        // 调用云函数进行登录
        const result = await cloudService.login(loginRes.code);
        if (result.success) {
          const { userInfo, token } = result.data;

          // 保存token和用户信息
          wx.setStorageSync('token', token);
          wx.setStorageSync('userInfo', userInfo);
          storeUtils.setUserInfo(userInfo);

          return userInfo;
        } else {
          throw new Error(result.error || '登录失败');
        }
      } else {
        throw new Error('获取微信登录code失败');
      }
    } catch (error) {
      console.error('登录失败:', error);
      throw error;
    }
  },

  /**
   * 用户退出登录
   */
  logout() {
    // 清除本地存储
    wx.removeStorageSync('token');
    wx.removeStorageSync('userInfo');

    // 清除全局状态
    storeUtils.clear();

    // 跳转到首页
    wx.switchTab({
      url: '/pages/index/index'
    });
  },


});
