import express from 'express';
import path from 'path';
import fs from 'fs';
import { createProxyMiddleware } from 'http-proxy-middleware';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const app = express();
const PORT = 3000;

console.log('🚀 启动纯Express开发服务器（完全无CSP限制）...');

// 完全禁用所有安全头中间件
app.use((req, res, next) => {
  // 移除所有安全相关的响应头
  res.removeHeader('X-Powered-By');
  res.removeHeader('Content-Security-Policy');
  res.removeHeader('X-Content-Security-Policy'); 
  res.removeHeader('X-WebKit-CSP');
  res.removeHeader('X-Frame-Options');
  res.removeHeader('X-XSS-Protection');
  res.removeHeader('X-Content-Type-Options');
  res.removeHeader('Referrer-Policy');
  res.removeHeader('Permissions-Policy');
  
  // 设置完全开放的CORS头
  res.header('Access-Control-Allow-Origin', '*');
  res.header('Access-Control-Allow-Methods', '*');
  res.header('Access-Control-Allow-Headers', '*');
  res.header('Access-Control-Allow-Credentials', 'true');
  
  // 确保没有CSP限制
  res.header('Content-Security-Policy', '');
  res.header('X-Content-Security-Policy', '');
  res.header('X-WebKit-CSP', '');
  
  next();
});

// 处理预检请求
app.options('*', (req, res) => {
  res.status(200).end();
});

// 直接服务index.html文件
app.get('/', (req, res) => {
  const indexPath = path.join(__dirname, 'index.html');
  if (fs.existsSync(indexPath)) {
    res.sendFile(indexPath);
  } else {
    res.status(404).send('index.html not found');
  }
});

// 静态文件服务
app.use('/src', express.static(path.join(__dirname, 'src')));
app.use('/public', express.static(path.join(__dirname, 'public')));
app.use('/node_modules', express.static(path.join(__dirname, 'node_modules')));

// API代理（如果需要）
app.use('/api', createProxyMiddleware({
  target: 'http://localhost:8080',
  changeOrigin: true,
  onError: (err, req, res) => {
    console.log('API代理错误:', err.message);
    res.status(500).json({ error: 'API服务暂不可用' });
  }
}));

// SPA回退
app.get('*', (req, res) => {
  res.redirect('/');
});

app.listen(PORT, () => {
  console.log(`🎯 Express服务器已启动！`);
  console.log(`📱 访问地址: http://localhost:${PORT}`);
  console.log(`✅ 完全无CSP限制`);
  console.log(`🔓 所有eval功能可用`);
  console.log(`💡 这是专门解决CSP问题的版本`);
  console.log('');
  console.log('🛑 按 Ctrl+C 停止服务器');
});