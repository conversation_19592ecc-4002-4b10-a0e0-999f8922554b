<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智慧评语助手 - 管理后台</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f5f7fa;
            color: #333;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px 0;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .header-content {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .logo {
            font-size: 24px;
            font-weight: bold;
        }
        
        .header-info {
            display: flex;
            align-items: center;
            gap: 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .stat-card {
            background: white;
            border-radius: 12px;
            padding: 25px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            transition: transform 0.2s;
        }
        
        .stat-card:hover {
            transform: translateY(-5px);
        }
        
        .stat-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }
        
        .stat-title {
            color: #666;
            font-size: 14px;
            font-weight: 500;
        }
        
        .stat-icon {
            width: 40px;
            height: 40px;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
        }
        
        .stat-number {
            font-size: 32px;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 5px;
        }
        
        .stat-change {
            font-size: 12px;
            color: #27ae60;
        }
        
        .content-grid {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .card {
            background: white;
            border-radius: 12px;
            padding: 25px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
        
        .card-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 1px solid #eee;
        }
        
        .card-title {
            font-size: 18px;
            font-weight: 600;
            color: #2c3e50;
        }
        
        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            transition: transform 0.2s;
        }
        
        .btn:hover {
            transform: translateY(-1px);
        }
        
        .activity-item {
            display: flex;
            align-items: center;
            padding: 12px 0;
            border-bottom: 1px solid #f0f0f0;
        }
        
        .activity-item:last-child {
            border-bottom: none;
        }
        
        .activity-icon {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: linear-gradient(135deg, #74b9ff, #0984e3);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            margin-right: 12px;
            font-size: 14px;
        }
        
        .activity-content {
            flex: 1;
        }
        
        .activity-text {
            font-size: 14px;
            color: #333;
            margin-bottom: 2px;
        }
        
        .activity-time {
            font-size: 12px;
            color: #666;
        }
        
        .chart-container {
            height: 300px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: #f8f9fa;
            border-radius: 8px;
            color: #666;
        }
        
        .status-indicator {
            display: inline-block;
            width: 8px;
            height: 8px;
            border-radius: 50%;
            margin-right: 8px;
        }
        
        .status-online { background: #27ae60; }
        .status-warning { background: #f39c12; }
        .status-error { background: #e74c3c; }
        
        .footer {
            text-align: center;
            padding: 20px;
            color: #666;
            border-top: 1px solid #eee;
            margin-top: 40px;
        }
        
        .refresh-btn {
            position: fixed;
            bottom: 30px;
            right: 30px;
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            cursor: pointer;
            font-size: 20px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
            transition: transform 0.2s;
        }
        
        .refresh-btn:hover {
            transform: scale(1.1);
        }
        
        @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }
        
        .spinning {
            animation: spin 1s linear infinite;
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="header-content">
            <div class="logo">
                🎓 智慧评语助手 - 管理后台
            </div>
            <div class="header-info">
                <div>
                    <span class="status-indicator status-warning"></span>
                    <span id="connectionStatus">连接状态检测中...</span>
                </div>
                <div>环境: cloud1-4g85f8xlb8166ff1-dev</div>
                <div id="lastUpdate">最后更新: --</div>
            </div>
        </div>
    </div>

    <div class="container">
        <!-- 统计卡片 -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-header">
                    <div class="stat-title">活跃用户数</div>
                    <div class="stat-icon" style="background: linear-gradient(135deg, #74b9ff, #0984e3);">
                        👥
                    </div>
                </div>
                <div class="stat-number" id="totalUsers">--</div>
                <div class="stat-change">📈 较昨日 +12%</div>
            </div>
            
            <div class="stat-card">
                <div class="stat-header">
                    <div class="stat-title">今日评语数</div>
                    <div class="stat-icon" style="background: linear-gradient(135deg, #00b894, #00cec9);">
                        📝
                    </div>
                </div>
                <div class="stat-number" id="todayComments">--</div>
                <div class="stat-change">📈 较昨日 +8%</div>
            </div>
            
            <div class="stat-card">
                <div class="stat-header">
                    <div class="stat-title">AI调用次数</div>
                    <div class="stat-icon" style="background: linear-gradient(135deg, #fd79a8, #e84393);">
                        🤖
                    </div>
                </div>
                <div class="stat-number" id="aiCalls">--</div>
                <div class="stat-change">📈 较昨日 +25%</div>
            </div>
            
            <div class="stat-card">
                <div class="stat-header">
                    <div class="stat-title">用户满意度</div>
                    <div class="stat-icon" style="background: linear-gradient(135deg, #fdcb6e, #e17055);">
                        ⭐
                    </div>
                </div>
                <div class="stat-number" id="satisfaction">--</div>
                <div class="stat-change">📈 较昨日 +2%</div>
            </div>
        </div>

        <!-- 内容区域 -->
        <div class="content-grid">
            <!-- 最近活动 -->
            <div class="card">
                <div class="card-header">
                    <div class="card-title">📊 最近活动</div>
                    <button class="btn" onclick="refreshActivities()">刷新</button>
                </div>
                <div id="activitiesList">
                    <div style="text-align: center; color: #666; padding: 20px;">
                        正在加载活动数据...
                    </div>
                </div>
            </div>
            
            <!-- 系统状态 -->
            <div class="card">
                <div class="card-header">
                    <div class="card-title">🔧 系统状态</div>
                    <button class="btn" onclick="checkSystemStatus()">检测</button>
                </div>
                <div id="systemStatus">
                    <div class="activity-item">
                        <div class="activity-icon">🔗</div>
                        <div class="activity-content">
                            <div class="activity-text">API服务器</div>
                            <div class="activity-time">
                                <span class="status-indicator status-online"></span>运行正常
                            </div>
                        </div>
                    </div>
                    <div class="activity-item">
                        <div class="activity-icon">💾</div>
                        <div class="activity-content">
                            <div class="activity-text">数据库连接</div>
                            <div class="activity-time">
                                <span class="status-indicator status-warning"></span>需要云函数
                            </div>
                        </div>
                    </div>
                    <div class="activity-item">
                        <div class="activity-icon">🤖</div>
                        <div class="activity-content">
                            <div class="activity-text">AI服务</div>
                            <div class="activity-time">
                                <span class="status-indicator status-online"></span>正常运行
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 数据图表 -->
        <div class="card">
            <div class="card-header">
                <div class="card-title">📈 数据趋势</div>
                <button class="btn" onclick="refreshCharts()">刷新图表</button>
            </div>
            <div class="chart-container">
                📊 数据图表功能开发中...
                <br>
                <small>将显示用户增长、评语生成趋势、AI使用情况等</small>
            </div>
        </div>
    </div>

    <div class="footer">
        <p>智慧评语助手管理后台 v3.0 | 基于你的真实数据库集合构建</p>
        <p>支持的集合: users, teachers, students, classes, comments, records, ai_configs, ai_usage, ai_generation_logs, ai_error_logs, admins, system_config, settings, logs, notifications, teams, tags, prompt_templates, files</p>
    </div>

    <button class="refresh-btn" onclick="refreshAllData()" title="刷新所有数据">
        🔄
    </button>

    <script>
        const API_URL = 'http://localhost:3000/admin'
        let isRefreshing = false
        
        // 调用API
        async function callAPI(action, data = {}) {
            try {
                const response = await fetch(API_URL, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        action,
                        ...data,
                        timestamp: Date.now(),
                        requestId: `admin_${Date.now()}`
                    })
                })
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`)
                }
                
                const result = await response.json()
                
                if (result.code === 200) {
                    return { success: true, data: result.data }
                } else {
                    return { success: false, error: result.message }
                }
                
            } catch (error) {
                return { success: false, error: error.message }
            }
        }
        
        // 更新连接状态
        function updateConnectionStatus(status, message) {
            const statusEl = document.getElementById('connectionStatus')
            const indicator = statusEl.previousElementSibling
            
            if (status === 'online') {
                indicator.className = 'status-indicator status-online'
                statusEl.textContent = message || '连接正常'
            } else if (status === 'warning') {
                indicator.className = 'status-indicator status-warning'
                statusEl.textContent = message || '部分功能受限'
            } else {
                indicator.className = 'status-indicator status-error'
                statusEl.textContent = message || '连接异常'
            }
        }
        
        // 加载仪表板数据
        async function loadDashboardData() {
            try {
                const result = await callAPI('data.getDashboardStats')
                
                if (result.success) {
                    const data = result.data
                    document.getElementById('totalUsers').textContent = data.totalUsers || 0
                    document.getElementById('todayComments').textContent = data.todayComments || 0
                    document.getElementById('aiCalls').textContent = data.aiCalls || 0
                    document.getElementById('satisfaction').textContent = data.satisfaction || '0.0'
                    
                    document.getElementById('lastUpdate').textContent = 
                        `最后更新: ${new Date(data.lastUpdated).toLocaleTimeString()}`
                    
                    updateConnectionStatus('warning', '数据服务正常，建议部署云函数获取真实数据')
                } else {
                    updateConnectionStatus('error', '数据加载失败')
                }
            } catch (error) {
                console.error('加载仪表板数据失败:', error)
                updateConnectionStatus('error', '连接失败')
            }
        }
        
        // 加载最近活动
        async function loadRecentActivities() {
            try {
                const result = await callAPI('data.getRecentActivities', { limit: 8 })
                
                const activitiesEl = document.getElementById('activitiesList')
                
                if (result.success && result.data.length > 0) {
                    activitiesEl.innerHTML = result.data.map(activity => `
                        <div class="activity-item">
                            <div class="activity-icon">📝</div>
                            <div class="activity-content">
                                <div class="activity-text">${activity.action}</div>
                                <div class="activity-time">${new Date(activity.timestamp).toLocaleString()}</div>
                            </div>
                        </div>
                    `).join('')
                } else {
                    activitiesEl.innerHTML = `
                        <div style="text-align: center; color: #666; padding: 20px;">
                            暂无活动记录<br>
                            <small>部署云函数后可查看真实活动数据</small>
                        </div>
                    `
                }
            } catch (error) {
                console.error('加载活动数据失败:', error)
            }
        }
        
        // 刷新所有数据
        async function refreshAllData() {
            if (isRefreshing) return
            
            isRefreshing = true
            const refreshBtn = document.querySelector('.refresh-btn')
            refreshBtn.classList.add('spinning')
            
            try {
                await Promise.all([
                    loadDashboardData(),
                    loadRecentActivities()
                ])
            } finally {
                isRefreshing = false
                refreshBtn.classList.remove('spinning')
            }
        }
        
        // 刷新活动数据
        function refreshActivities() {
            loadRecentActivities()
        }
        
        // 检查系统状态
        async function checkSystemStatus() {
            try {
                const result = await callAPI('database.testConnection')
                
                if (result.success) {
                    updateConnectionStatus('warning', '服务正常，建议部署云函数')
                } else {
                    updateConnectionStatus('warning', '需要部署云函数获取真实数据')
                }
            } catch (error) {
                updateConnectionStatus('error', '连接失败')
            }
        }
        
        // 刷新图表
        function refreshCharts() {
            alert('图表功能开发中，将在后续版本中提供完整的数据可视化功能')
        }
        
        // 页面加载时初始化
        window.addEventListener('load', () => {
            console.log('🎓 智慧评语助手管理后台已加载')
            refreshAllData()
            
            // 每30秒自动刷新数据
            setInterval(refreshAllData, 30000)
        })
    </script>
</body>
</html>
