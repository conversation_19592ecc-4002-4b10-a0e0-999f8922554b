/**
 * 简化版本地API服务器
 * 先恢复基本功能，然后再逐步添加真实数据连接
 */

const http = require('http')
const url = require('url')

const PORT = 3000

// 配置选项
const USE_REAL_DATA = true // 设置为true使用真实数据，false使用模拟数据
const USE_CLOUD_FUNCTION = true // 设置为true通过云函数查询数据

// 云函数调用服务
async function callCloudFunction(functionName, data) {
  try {
    // 这里模拟云函数调用，实际应该使用wx.cloud.callFunction
    // 由于在Node.js环境中，我们直接调用本地的云函数逻辑

    console.log(`🔗 模拟调用云函数: ${functionName}`, data)

    // 暂时返回模拟响应，表示云函数调用成功
    return {
      success: true,
      message: '云函数调用成功（模拟）',
      data: null
    }
  } catch (error) {
    console.error(`❌ 云函数调用失败: ${functionName}`, error)
    return {
      success: false,
      message: error.message,
      data: null
    }
  }
}

// 动态加载数据库服务
let databaseService = null
if (USE_REAL_DATA) {
  try {
    // 使用真实数据服务，连接用户的真实数据库集合
    databaseService = require('./real-data-service.cjs')
    console.log('✅ 已加载真实数据服务（连接真实数据库集合）')
  } catch (error) {
    console.error('❌ 加载真实数据服务失败，将使用模拟数据:', error.message)
  }
}

// 模拟数据
const mockData = {
  dashboardStats: {
    totalUsers: 156,
    todayComments: 23,
    aiCalls: 89,
    satisfaction: 4.8,
    lastUpdated: new Date().toISOString()
  },
  recentActivities: [
    {
      id: '1',
      userId: 'user001',
      userName: '张老师',
      action: '生成评语',
      actionType: 'comment_generate',
      timestamp: new Date(Date.now() - 5 * 60 * 1000).toISOString(),
      metadata: { studentName: '小明', subject: '数学' }
    },
    {
      id: '2',
      userId: 'user002',
      userName: '李老师',
      action: '登录系统',
      actionType: 'user_login',
      timestamp: new Date(Date.now() - 10 * 60 * 1000).toISOString(),
      metadata: {}
    }
  ],
  students: [
    { id: '1', name: '小明', class: '三年级一班', teacher: '张老师', commentsCount: 5, lastUpdate: new Date().toISOString(), status: 'active' },
    { id: '2', name: '小红', class: '三年级二班', teacher: '李老师', commentsCount: 3, lastUpdate: new Date().toISOString(), status: 'active' },
    { id: '3', name: '小刚', class: '三年级三班', teacher: '王老师', commentsCount: 2, lastUpdate: new Date().toISOString(), status: 'active' }
  ],
  aiModels: [
    {
      id: 'doubao',
      name: '豆包',
      provider: 'bytedance',
      status: 'active',
      config: {
        model: 'doubao-pro-4k',
        temperature: 0.7,
        maxTokens: 2000
      }
    }
  ]
}

// 处理API请求
async function handleApiRequest(req, res, body) {
  try {
    const data = JSON.parse(body)
    const { action, requestId } = data
    
    console.log(`📥 收到请求: ${action} (${requestId})`)
    
    let responseData = null
    
    switch (action) {
      case 'healthCheck':
        responseData = {
          status: 'healthy',
          timestamp: new Date().toISOString(),
          version: '3.0.0-simple',
          source: data.source || 'unknown',
          message: '简化版API服务器运行正常'
        }
        break
        
      case 'data.getDashboardStats':
        if (USE_REAL_DATA && databaseService) {
          try {
            responseData = await databaseService.getDashboardStats()
            console.log('✅ 使用真实仪表板数据')
          } catch (error) {
            console.error('❌ 获取真实数据失败，使用模拟数据:', error)
            responseData = mockData.dashboardStats
          }
        } else {
          responseData = mockData.dashboardStats
        }
        break
        
      case 'data.getRecentActivities':
        if (USE_REAL_DATA && databaseService) {
          try {
            responseData = await databaseService.getRecentActivities(data.limit || 10)
            console.log('✅ 使用真实活动数据')
          } catch (error) {
            console.error('❌ 获取真实活动数据失败，使用模拟数据:', error)
            responseData = mockData.recentActivities.slice(0, data.limit || 10)
          }
        } else {
          responseData = mockData.recentActivities.slice(0, data.limit || 10)
        }
        break
        
      case 'data.getStudents':
        if (USE_REAL_DATA && databaseService) {
          try {
            responseData = await databaseService.getStudents(data.params || {})
            console.log('✅ 使用真实学生数据')
          } catch (error) {
            console.error('❌ 获取真实学生数据失败，使用模拟数据:', error)
            responseData = {
              list: mockData.students,
              total: mockData.students.length,
              page: data.params?.page || 1,
              limit: data.params?.limit || 10
            }
          }
        } else {
          responseData = {
            list: mockData.students,
            total: mockData.students.length,
            page: data.params?.page || 1,
            limit: data.params?.limit || 10
          }
        }
        break
        
      case 'data.getRecords':
        if (USE_REAL_DATA && databaseService) {
          try {
            responseData = await databaseService.getComments(data.params || {})
            console.log('✅ 使用真实评语数据')
          } catch (error) {
            console.error('❌ 获取真实评语数据失败，使用模拟数据:', error)
            responseData = {
              list: [
                {
                  id: '1',
                  studentId: '1',
                  studentName: '小明',
                  teacherId: 'teacher1',
                  teacherName: '张老师',
                  content: '该学生表现优秀，积极参与课堂活动。',
                  aiModel: 'doubao',
                  tokensUsed: 150,
                  createTime: new Date().toISOString(),
                  subject: '数学'
                }
              ],
              total: 1,
              page: data.params?.page || 1,
              limit: data.params?.limit || 10
            }
          }
        } else {
          responseData = {
            list: [
              {
                id: '1',
                studentId: '1',
                studentName: '小明',
                teacherId: 'teacher1',
                teacherName: '张老师',
                content: '该学生表现优秀，积极参与课堂活动。',
                aiModel: 'doubao',
                tokensUsed: 150,
                createTime: new Date().toISOString(),
                subject: '数学'
              }
            ],
            total: 1,
            page: data.params?.page || 1,
            limit: data.params?.limit || 10
          }
        }
        break
        
      case 'ai.getModels':
        responseData = mockData.aiModels
        break
        
      case 'database.testConnection':
        if (USE_REAL_DATA && databaseService) {
          try {
            responseData = await databaseService.testConnection()
            console.log('✅ 使用真实数据库连接测试')
          } catch (error) {
            console.error('❌ 真实数据库连接测试失败:', error)
            responseData = {
              success: false,
              message: `真实数据库连接失败: ${error.message}`,
              collections: [],
              sampleData: null
            }
          }
        } else {
          responseData = {
            success: true,
            message: '简化版API服务器连接正常（模拟数据模式）',
            collections: ['模拟数据'],
            sampleData: mockData.dashboardStats
          }
        }
        break
        
      default:
        throw new Error(`未知的action: ${action}`)
    }
    
    const response = {
      code: 200,
      message: 'success',
      data: responseData,
      timestamp: Date.now()
    }
    
    const responseBody = JSON.stringify(response)
    const responseSize = Buffer.byteLength(responseBody, 'utf8')
    
    res.writeHead(200, {
      'Content-Type': 'application/json',
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-WX-SOURCE, X-Requested-With'
    })
    
    res.end(responseBody)
    
    console.log(`✅ 响应: ${action} - ${responseSize} bytes`)
    
  } catch (error) {
    console.error('❌ 处理请求失败:', error)
    
    const errorResponse = {
      code: 500,
      message: error.message,
      data: null,
      timestamp: Date.now()
    }
    
    res.writeHead(500, {
      'Content-Type': 'application/json',
      'Access-Control-Allow-Origin': '*'
    })
    
    res.end(JSON.stringify(errorResponse))
  }
}

// 创建HTTP服务器
const server = http.createServer((req, res) => {
  const parsedUrl = url.parse(req.url, true)
  
  // 处理CORS预检请求
  if (req.method === 'OPTIONS') {
    res.writeHead(200, {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-WX-SOURCE, X-Requested-With',
      'Access-Control-Max-Age': '86400'
    })
    res.end()
    return
  }
  
  // 处理API请求
  if (req.method === 'POST' && parsedUrl.pathname === '/admin') {
    let body = ''
    
    req.on('data', chunk => {
      body += chunk.toString()
    })
    
    req.on('end', async () => {
      await handleApiRequest(req, res, body)
    })
  } else {
    // 返回404
    res.writeHead(404, {
      'Content-Type': 'application/json',
      'Access-Control-Allow-Origin': '*'
    })
    res.end(JSON.stringify({
      code: 404,
      message: 'Not Found',
      data: null
    }))
  }
})

// 启动服务器
server.listen(PORT, () => {
  console.log('🚀 简化版API服务器启动成功!')
  console.log(`📡 服务地址: http://localhost:${PORT}/admin`)
  console.log(`🔗 测试地址: http://localhost:${PORT}/admin`)
  console.log(`📋 支持的actions:`)
  console.log(`   - healthCheck`)
  console.log(`   - data.getDashboardStats`)
  console.log(`   - data.getRecentActivities`)
  console.log(`   - data.getStudents`)
  console.log(`   - data.getRecords`)
  console.log(`   - ai.getModels`)
  console.log(`   - database.testConnection`)
  console.log(`\n💡 在浏览器测试页面中可以测试连通性`)
  console.log(`\n按 Ctrl+C 停止服务器`)
})

// 优雅关闭
process.on('SIGINT', () => {
  console.log('\n🛑 正在关闭服务器...')
  server.close(() => {
    console.log('✅ 服务器已关闭')
    process.exit(0)
  })
})
