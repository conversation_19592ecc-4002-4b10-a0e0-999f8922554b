import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'
import path from 'path'

export default defineConfig({
  
  plugins: [
    react({
      // 完全禁用所有可能导致问题的功能
      babel: false,
      fastRefresh: false
    })
  ],
  
  // 路径别名
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src'),
      '@/components': path.resolve(__dirname, './src/components'),
      '@/utils': path.resolve(__dirname, './src/utils'),
      '@/hooks': path.resolve(__dirname, './src/hooks'),
      '@/services': path.resolve(__dirname, './src/services')
    }
  },

  // 开发服务器配置 - 完全禁用HMR
  server: {
    port: 8083,
    open: false,
    host: true,
    strictPort: false,
    // 添加宽松的CSP头部
    headers: {
      'Content-Security-Policy': "default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob: *; script-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob: *; style-src 'self' 'unsafe-inline' data: blob: *; img-src 'self' data: blob: *; font-src 'self' data: blob: *; connect-src 'self' ws: wss: data: blob: *; worker-src 'self' blob: *; frame-src 'self' *; object-src 'self' data: blob:;"
    },
    hmr: false // 完全禁用HMR
  },

  // 构建配置
  build: {
    outDir: 'dist',
    sourcemap: false,
    minify: false // 开发环境不压缩
  },

  // esbuild配置
  esbuild: {
    target: 'es2020',
    format: 'esm'
  },

  // 优化依赖
  optimizeDeps: {
    include: ['react', 'react-dom', 'antd'],
    esbuildOptions: {
      target: 'es2020'
    }
  }
})
