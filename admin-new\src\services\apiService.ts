/**
 * API服务
 * 统一处理所有后端API调用的服务层
 * 集成认证token、错误处理、重试机制等
 */

import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios'
import { authService } from './authService'
import { SecureStorageService } from './secureStorageService'

/**
 * API响应统一接口
 */
export interface APIResponse<T = any> {
  success: boolean
  data: T
  message: string
  code: number
  timestamp: string
}

/**
 * 分页响应接口
 */
export interface PaginatedResponse<T> {
  items: T[]
  total: number
  page: number
  pageSize: number
  totalPages: number
}

/**
 * API错误类
 */
export class APIError extends Error {
  public response?: AxiosResponse
  public statusCode: number
  public code: string

  constructor(message: string, statusCode: number, code: string, response?: AxiosResponse) {
    super(message)
    this.name = 'APIError'
    this.statusCode = statusCode
    this.code = code
    this.response = response
  }
}

/**
 * API服务类
 */
export class APIService {
  private static instance: APIService
  private api: AxiosInstance
  private isRefreshing = false
  private failedQueue: Array<{ resolve: (value: any) => void; reject: (reason: any) => void }> = []

  private constructor() {
    this.api = axios.create({
      baseURL: import.meta.env.VITE_API_BASE_URL || '/api',
      timeout: 30000,
      headers: {
        'Content-Type': 'application/json',
      },
    })

    this.setupInterceptors()
  }

  /**
   * 获取单例实例
   */
  static getInstance(): APIService {
    if (!APIService.instance) {
      APIService.instance = new APIService()
    }
    return APIService.instance
  }

  /**
   * 设置请求和响应拦截器
   */
  private setupInterceptors(): void {
    // 请求拦截器
    this.api.interceptors.request.use(
      async (config) => {
        // 添加认证token
        const token = authService.getToken()
        if (token) {
          config.headers.Authorization = `Bearer ${token}`
        }

        // 添加请求ID用于跟织
        config.headers['X-Request-ID'] = generateRequestId()

        return config
      },
      (error) => Promise.reject(error)
    )

    // 响应拦截器
    this.api.interceptors.response.use(
      (response) => {
        // 标准化响应格式
        if (response.data && typeof response.data === 'object' && !response.data.success) {
          response.data = normalizeResponse(response.data, true)
        }
        return response
      },
      (error) => {
        return this.handleResponseError(error)
      }
    )
  }

  /**
   * 处理响应错误
   */
  private async handleResponseError(error: any): Promise<any> {
    const originalRequest = error.config

    if (error.response?.status === 401 && !originalRequest._retry) {
      if (this.isRefreshing) {
        return new Promise((resolve, reject) => {
          this.failedQueue.push({ resolve, reject })
        }).then((token) => {
          originalRequest.headers.Authorization = `Bearer ${token}`
          return axios(originalRequest)
        })
      }

      originalRequest._retry = true
      this.isRefreshing = true

      try {
        const newToken = await this.refreshToken()
        this.processQueue(null, newToken)
        originalRequest.headers.Authorization = `Bearer ${newToken}`
        return axios(originalRequest)
      } catch (refreshError) {
        this.processQueue(refreshError, null)
        await authService.logout()
        window.location.href = '/login'
        return Promise.reject(refreshError)
      } finally {
        this.isRefreshing = false
      }
    }

    // 其他错误处理
    const normalizedError = this.normalizeError(error)
    return Promise.reject(normalizedError)
  }

  /**
   * 处理失败的请求队列
   */
  private processQueue(error: any, token: string | null = null): void {
    this.failedQueue.forEach((prom) => {
      if (error) {
        prom.reject(error)
      } else {
        prom.resolve(token)
      }
    })
    this.failedQueue = []
  }

  /**
   * 刷新token
   */
  private async refreshToken(): Promise<string> {
    const newToken = await authService.refreshAccessToken()
    if (!newToken) {
      throw new APIError('Token refresh failed', 401, 'TOKEN_REFRESH_FAILED')
    }
    return newToken
  }

  /**
   * 标准化错误信息
   */
  private normalizeError(error: any): APIError {
    if (error.response) {
      const { data, status } = error.response
      return new APIError(
        data.message || `请求失败 (${status})`,
        status,
        data.code || 'REQUEST_ERROR',
        error.response
      )
    } else if (error.request) {
      return new APIError('网络连接失败', 0, 'NETWORK_ERROR')
    } else {
      return new APIError('请求配置错误', 0, 'CONFIG_ERROR')
    }
  }

  /**
   * GET请求
   */
  async get<T>(url: string, config?: AxiosRequestConfig): Promise<T> {
    const response = await this.api.get(url, config)
    return response.data?.data || response.data
  }

  /**
   * POST请求
   */
  async post<T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> {
    const response = await this.api.post(url, data, config)
    return response.data?.data || response.data
  }

  /**
   * PUT请求
   */
  async put<T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> {
    const response = await this.api.put(url, data, config)
    return response.data?.data || response.data
  }

  /**
   * DELETE请求
   */
  async delete<T>(url: string, config?: AxiosRequestConfig): Promise<T> {
    const response = await this.api.delete(url, config)
    return response.data?.data || response.data
  }

  /**
   * 分页GET请求
   */
  async getPaginated<T>(url: string, params?: any): Promise<PaginatedResponse<T>> {
    const response = await this.api.get(url, { params })
    return response.data?.data || response.data
  }

  /**
   * 文件上传
   */
  async uploadFile<T>(
    url: string,
    file: File,
    onProgress?: (progress: number) => void
  ): Promise<T> {
    const formData = new FormData()
    formData.append('file', file)

    const response = await this.api.post(url, formData, {
      headers: { 'Content-Type': 'multipart/form-data' },
      onUploadProgress: (progressEvent) => {
        if (onProgress && progressEvent.total) {
          const progress = Math.round((progressEvent.loaded * 100) / progressEvent.total)
          onProgress(progress)
        }
      },
    })

    return response.data?.data || response.data
  }

  /**
   * 带重试的请求
   */
  async retryRequest<T>(
    requestFn: () => Promise<T>,
    retries = 3,
    delay = 1000
  ): Promise<T> {
    try {
      return await requestFn()
    } catch (error) {
      if (retries > 0 && this.shouldRetry(error)) {
        await new Promise((resolve) => setTimeout(resolve, delay))
        return this.retryRequest(requestFn, retries - 1, delay * 2)
      }
      throw error
    }
  }

  /**
   * 检查是否应该重试请求
   */
  private shouldRetry(error: any): boolean {
    return (
      !error.response?.status ||
      [408, 429, 500, 502, 503, 504].includes(error.response.status)
    )
  }
}

/**
 * 数据统计API
 */
export class AnalyticsAPI {
  private api = APIService.getInstance()

  /**
   * 获取系统总览数据
   */
  async getSystemOverview() {
    return this.api.get('/analytics/overview')
  }

  /**
   * 获取用户统计
   */
  async getUserStats() {
    return this.api.get('/analytics/users')
  }

  /**
   * 获取AI使用统计
   */
  async getAIUsageStats(params?: { startDate?: string; endDate?: string }) {
    return this.api.get('/analytics/ai-usage', { params })
  }

  /**
   * 获取token使用统计
   */
  async getTokenUsageStats(params?: { period?: string }) {
    return this.api.get('/analytics/token-usage', { params })
  }

  /**
   * 获取实时数据
   */
  async getRealTimeData() {
    return this.api.get('/analytics/realtime')
  }
}

/**
 * 用户管理API
 */
export class UsersAPI {
  private api = APIService.getInstance()

  /**
   * 获取用户列表
   */
  async getUsers(params?: { page?: number; pageSize?: number; keyword?: string }) {
    return this.api.getPaginated('/users', params)
  }

  /**
   * 获取单个用户信息
   */
  async getUser(id: string) {
    return this.api.get(`/users/${id}`)
  }

  /**
   * 更新用户信息
   */
  async updateUser(id: string, data: Partial<any>) {
    return this.api.put(`/users/${id}`, data)
  }

  /**
   * 删除用户
   */
  async deleteUser(id: string) {
    return this.api.delete(`/users/${id}`)
  }

  /**
   * 重置用户密码
   */
  async resetPassword(id: string) {
    return this.api.post(`/users/${id}/reset-password`)
  }

  /**
   * 更新用户使用限额
   */
  async updateUserQuota(id: string, quota: number) {
    return this.api.put(`/users/${id}/quota`, { quota })
  }

  /**
   * 批量导入用户
   */
  async importUsers(file: File, onProgress?: (progress: number) => void) {
    return this.api.uploadFile('/users/import', file, onProgress)
  }

  /**
   * 导出用户数据
   */
  async exportUsers(params?: any) {
    const response = await this.api.get('/users/export', { params })
    return response
  }
}

/**
 * AI配置API
 */
export class AIConfigAPI {
  private api = APIService.getInstance()

  /**
   * 获取AI提供商配置
   */
  async getAIProviders() {
    return this.api.get('/ai-config/providers')
  }

  /**
   * 测试AI连接
   */
  async testConnection(provider: string, config: any) {
    return this.api.post(`/ai-config/${provider}/test`, config)
  }

  /**
   * 保存AI配置
   */
  async saveConfig(provider: string, config: any) {
    return this.api.post(`/ai-config/${provider}`, config)
  }

  /**
   * 获取AI配置
   */
  async getConfig(provider: string) {
    return this.api.get(`/ai-config/${provider}`)
  }

  /**
   * 获取AI参数模板
   */
  async getAIParameters() {
    return this.api.get('/ai-config/parameters')
  }

  /**
   * 验证API密钥
   */
  async validateAPIKey(provider: string, key: string) {
    return this.api.post('/ai-config/validate-key', { provider, key })
  }
}

/**
 * 系统设置API
 */
export class SystemAPI {
  private api = APIService.getInstance()

  /**
   * 获取系统配置
   */
  async getSystemConfig() {
    return this.api.get('/system/config')
  }

  /**
   * 更新系统配置
   */
  async updateSystemConfig(config: any) {
    return this.api.put('/system/config', config)
  }

  /**
   * 获取系统日志
   */
  async getSystemLogs(params?: { page?: number; pageSize?: number; level?: string }) {
    return this.api.getPaginated('/system/logs', params)
  }

  /**
   * 备份配置
   */
  async backupConfig() {
    return this.api.post('/system/backup')
  }

  /**
   * 恢复配置
   */
  async restoreConfig(file: File) {
    return this.api.uploadFile('/system/restore', file)
  }

  /**
   * 导出系统数据
   */
  async exportSystemData() {
    return this.api.get('/system/export')
  }
}

/**
 * hook提供器
 */
export class APIProvider {
  private static instance: APIProvider
  
  public analytics = new AnalyticsAPI()
  public users = new UsersAPI()
  public aiConfig = new AIConfigAPI()
  public system = new SystemAPI()

  static getInstance(): APIProvider {
    if (!APIProvider.instance) {
      APIProvider.instance = new APIProvider()
    }
    return APIProvider.instance
  }
}

/**
 * 工具函数
 */
function normalizeResponse(data: any, isSuccess: boolean): APIResponse {
  return {
    success: isSuccess,
    data: data.data || null,
    message: data.message || data.error || '操作成功',
    code: data.code || (isSuccess ? 0 : 500),
    timestamp: new Date().toISOString(),
  }
}

function generateRequestId(): string {
  return `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
}

// 导出API实例和工具
export const apiService = APIService.getInstance()
export const apiProvider = APIProvider.getInstance()

export default apiService