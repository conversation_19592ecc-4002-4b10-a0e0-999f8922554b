@echo off
echo.
echo ==========================================
echo 🔥 终极CSP解决方案 - 绕过浏览器限制
echo ==========================================
echo.

echo CSP问题无法通过配置解决，直接绕过浏览器安全检查！
echo.

echo 第1步：启动普通开发服务器...
start "开发服务器" cmd /k "npm run dev"

echo 等待服务器启动...
timeout /t 3 /nobreak >nul

echo.
echo 第2步：使用Chrome无安全模式访问...

REM 杀死现有Chrome进程
taskkill /f /im chrome.exe >nul 2>&1

REM 等待进程完全结束
timeout /t 2 /nobreak >nul

REM 创建临时目录
set TEMP_DIR=%TEMP%\chrome-no-security-%RANDOM%
mkdir "%TEMP_DIR%" >nul 2>&1

echo 启动Chrome（完全禁用所有安全检查）...
echo.

start "Chrome 无安全模式" chrome.exe ^
  --disable-web-security ^
  --disable-features=VizDisplayCompositor ^
  --disable-site-isolation-trials ^
  --disable-extensions ^
  --no-sandbox ^
  --allow-running-insecure-content ^
  --disable-background-timer-throttling ^
  --disable-backgrounding-occluded-windows ^
  --disable-renderer-backgrounding ^
  --user-data-dir="%TEMP_DIR%" ^
  --ignore-certificate-errors ^
  --ignore-ssl-errors ^
  --ignore-certificate-errors-spki-list ^
  --disable-dev-shm-usage ^
  "http://localhost:8080"

echo.
echo ✅ Chrome已启动（所有安全功能已禁用）
echo 📱 自动打开: http://localhost:8080
echo 🔐 登录账号: admin / admin123
echo.
echo ⚠️  这个Chrome实例已禁用所有安全策略
echo 💡 现在应该可以正常使用所有功能了
echo.
echo 如果还不行，请检查：
echo   1. 是否有杀毒软件在干扰
echo   2. 是否有企业网络策略
echo   3. 是否有浏览器扩展残留
echo.
pause