/**
 * 环境配置管理
 * 2025年现代化环境配置系统
 */

// 环境类型定义
export type Environment = 'development' | 'staging' | 'production'

// 配置接口定义
interface EnvironmentConfig {
  // 基础配置
  name: string
  displayName: string
  debug: boolean
  
  // 云开发配置
  cloudEnv: string
  cloudRegion: string
  
  // API配置
  apiBaseUrl: string
  apiTimeout: number
  
  // 监控配置
  monitoring: {
    enableErrorTracking: boolean
    enablePerformanceTracking: boolean
    enableUserTracking: boolean
    sampleRate: number
    sentryDsn?: string
    mtaAppId?: string
  }
  
  // AI服务配置
  ai: {
    provider: 'doubao' | 'openai' | 'qianfan'
    apiUrl: string
    model: string
    temperature: number
    maxTokens: number
    enableCache: boolean
    cacheExpiry: number
  }
  
  // 存储配置
  storage: {
    enablePersistence: boolean
    maxLocalStorage: number
    enableCloudSync: boolean
    syncInterval: number
  }
  
  // 安全配置
  security: {
    enableEncryption: boolean
    enableInputValidation: boolean
    enableRateLimit: boolean
    maxRequestsPerMinute: number
  }
  
  // 功能开关
  features: {
    enableBatchComment: boolean
    enableAdvancedAnalytics: boolean
    enableExport: boolean
    enableNotifications: boolean
    enableOfflineMode: boolean
  }
  
  // 性能配置
  performance: {
    enableLazyLoading: boolean
    enableVirtualList: boolean
    enableImageOptimization: boolean
    maxConcurrentRequests: number
  }
  
  // 日志配置
  logging: {
    level: 'debug' | 'info' | 'warn' | 'error'
    enableConsole: boolean
    enableRemote: boolean
    maxLocalLogs: number
  }
}

// 开发环境配置
const developmentConfig: EnvironmentConfig = {
  name: 'development',
  displayName: '开发环境',
  debug: true,
  
  cloudEnv: 'cloud1-4g85f8xlb8166ff1-dev',
  cloudRegion: 'ap-shanghai',
  
  apiBaseUrl: 'https://dev-api.pingyulingganjun.com',
  apiTimeout: 10000,
  
  monitoring: {
    enableErrorTracking: true,
    enablePerformanceTracking: true,
    enableUserTracking: true,
    sampleRate: 1.0,
    sentryDsn: process.env.SENTRY_DSN_DEV,
    mtaAppId: process.env.MTA_APP_ID_DEV
  },
  
  ai: {
    provider: 'doubao',
    apiUrl: 'https://ark.cn-beijing.volces.com/api/v3',
    model: 'doubao-lite-4k',
    temperature: 0.7,
    maxTokens: 300,
    enableCache: true,
    cacheExpiry: 3600000 // 1小时
  },
  
  storage: {
    enablePersistence: true,
    maxLocalStorage: 1000,
    enableCloudSync: true,
    syncInterval: 30000 // 30秒
  },
  
  security: {
    enableEncryption: false, // 开发环境关闭加密
    enableInputValidation: true,
    enableRateLimit: false,
    maxRequestsPerMinute: 1000
  },
  
  features: {
    enableBatchComment: true,
    enableAdvancedAnalytics: true,
    enableExport: true,
    enableNotifications: true,
    enableOfflineMode: true
  },
  
  performance: {
    enableLazyLoading: true,
    enableVirtualList: true,
    enableImageOptimization: false, // 开发环境关闭图片优化
    maxConcurrentRequests: 10
  },
  
  logging: {
    level: 'debug',
    enableConsole: true,
    enableRemote: true,
    maxLocalLogs: 1000
  }
}

// 预发布环境配置
const stagingConfig: EnvironmentConfig = {
  name: 'staging',
  displayName: '预发布环境',
  debug: false,
  
  cloudEnv: 'cloud1-4g85f8xlb8166ff1-staging',
  cloudRegion: 'ap-shanghai',
  
  apiBaseUrl: 'https://staging-api.pingyulingganjun.com',
  apiTimeout: 8000,
  
  monitoring: {
    enableErrorTracking: true,
    enablePerformanceTracking: true,
    enableUserTracking: true,
    sampleRate: 0.5,
    sentryDsn: process.env.SENTRY_DSN_STAGING,
    mtaAppId: process.env.MTA_APP_ID_STAGING
  },
  
  ai: {
    provider: 'doubao',
    apiUrl: 'https://ark.cn-beijing.volces.com/api/v3',
    model: 'doubao-pro-4k',
    temperature: 0.7,
    maxTokens: 300,
    enableCache: true,
    cacheExpiry: 7200000 // 2小时
  },
  
  storage: {
    enablePersistence: true,
    maxLocalStorage: 500,
    enableCloudSync: true,
    syncInterval: 60000 // 1分钟
  },
  
  security: {
    enableEncryption: true,
    enableInputValidation: true,
    enableRateLimit: true,
    maxRequestsPerMinute: 500
  },
  
  features: {
    enableBatchComment: true,
    enableAdvancedAnalytics: true,
    enableExport: true,
    enableNotifications: true,
    enableOfflineMode: true
  },
  
  performance: {
    enableLazyLoading: true,
    enableVirtualList: true,
    enableImageOptimization: true,
    maxConcurrentRequests: 8
  },
  
  logging: {
    level: 'info',
    enableConsole: false,
    enableRemote: true,
    maxLocalLogs: 500
  }
}

// 生产环境配置
const productionConfig: EnvironmentConfig = {
  name: 'production',
  displayName: '生产环境',
  debug: false,
  
  cloudEnv: 'cloud1-4g85f8xlb8166ff1',
  cloudRegion: 'ap-shanghai',
  
  apiBaseUrl: 'https://api.pingyulingganjun.com',
  apiTimeout: 5000,
  
  monitoring: {
    enableErrorTracking: true,
    enablePerformanceTracking: true,
    enableUserTracking: true,
    sampleRate: 0.1, // 生产环境降低采样率
    sentryDsn: process.env.SENTRY_DSN_PROD,
    mtaAppId: process.env.MTA_APP_ID_PROD
  },
  
  ai: {
    provider: 'doubao',
    apiUrl: 'https://ark.cn-beijing.volces.com/api/v3',
    model: 'doubao-pro-4k',
    temperature: 0.7,
    maxTokens: 300,
    enableCache: true,
    cacheExpiry: 86400000 // 24小时
  },
  
  storage: {
    enablePersistence: true,
    maxLocalStorage: 200,
    enableCloudSync: true,
    syncInterval: 300000 // 5分钟
  },
  
  security: {
    enableEncryption: true,
    enableInputValidation: true,
    enableRateLimit: true,
    maxRequestsPerMinute: 200
  },
  
  features: {
    enableBatchComment: true,
    enableAdvancedAnalytics: false, // 生产环境关闭高级分析
    enableExport: true,
    enableNotifications: true,
    enableOfflineMode: false // 生产环境关闭离线模式
  },
  
  performance: {
    enableLazyLoading: true,
    enableVirtualList: true,
    enableImageOptimization: true,
    maxConcurrentRequests: 5
  },
  
  logging: {
    level: 'error',
    enableConsole: false,
    enableRemote: true,
    maxLocalLogs: 100
  }
}

// 环境配置映射
const environments: Record<Environment, EnvironmentConfig> = {
  development: developmentConfig,
  staging: stagingConfig,
  production: productionConfig
}

// 获取当前环境
function getCurrentEnvironment(): Environment {
  // 从环境变量获取
  const envFromProcess = process.env.NODE_ENV as Environment
  if (envFromProcess && environments[envFromProcess]) {
    return envFromProcess
  }
  
  // 从小程序云开发环境ID推断
  try {
    const app = getApp<any>()
    if (app?.globalData?.cloudEnv) {
      const cloudEnv = app.globalData.cloudEnv
      if (cloudEnv.includes('-dev')) return 'development'
      if (cloudEnv.includes('-staging')) return 'staging'
      return 'production'
    }
  } catch (error) {
    console.warn('无法从应用获取环境信息:', error)
  }
  
  // 默认开发环境
  return 'development'
}

// 获取当前配置
function getCurrentConfig(): EnvironmentConfig {
  const env = getCurrentEnvironment()
  return environments[env]
}

// 配置验证
function validateConfig(config: EnvironmentConfig): boolean {
  try {
    // 必需字段检查
    if (!config.name || !config.cloudEnv || !config.apiBaseUrl) {
      console.error('配置验证失败：缺少必需字段')
      return false
    }
    
    // 云环境ID格式检查
    if (!/^cloud1-[a-z0-9]+(-\w+)?$/.test(config.cloudEnv)) {
      console.error('配置验证失败：云环境ID格式不正确')
      return false
    }
    
    // API URL格式检查
    if (!/^https?:\/\/.+/.test(config.apiBaseUrl)) {
      console.error('配置验证失败：API URL格式不正确')
      return false
    }
    
    // 采样率范围检查
    if (config.monitoring.sampleRate < 0 || config.monitoring.sampleRate > 1) {
      console.error('配置验证失败：监控采样率必须在0-1之间')
      return false
    }
    
    return true
  } catch (error) {
    console.error('配置验证异常:', error)
    return false
  }
}

// 配置热更新
function updateConfig(updates: Partial<EnvironmentConfig>): boolean {
  try {
    const currentEnv = getCurrentEnvironment()
    const currentConfig = environments[currentEnv]
    const newConfig = { ...currentConfig, ...updates }
    
    if (!validateConfig(newConfig)) {
      console.error('配置更新失败：新配置验证不通过')
      return false
    }
    
    environments[currentEnv] = newConfig
    
    // 触发配置更新事件
    wx.triggerEvent?.('configUpdated', { 
      environment: currentEnv, 
      config: newConfig 
    })
    
    console.log('✅ 配置更新成功:', currentEnv)
    return true
  } catch (error) {
    console.error('配置更新失败:', error)
    return false
  }
}

// 获取功能开关状态
function isFeatureEnabled(feature: keyof EnvironmentConfig['features']): boolean {
  const config = getCurrentConfig()
  return config.features[feature] ?? false
}

// 获取配置值
function getConfigValue<K extends keyof EnvironmentConfig>(key: K): EnvironmentConfig[K] {
  const config = getCurrentConfig()
  return config[key]
}

// 导出
export {
  type EnvironmentConfig,
  environments,
  getCurrentEnvironment,
  getCurrentConfig,
  validateConfig,
  updateConfig,
  isFeatureEnabled,
  getConfigValue
}

// 默认导出当前配置
export default getCurrentConfig()
