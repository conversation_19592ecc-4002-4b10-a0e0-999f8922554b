/**
 * 班级数据分析页面
 * 展示班级的详细数据统计和可视化图表
 */
const { cloudService } = require('../../../services/cloudService');

Page({
  /**
   * 页面的初始数据
   */
  data: {
    classId: null,
    classInfo: {},
    loading: true,
    
    // 统计数据
    statistics: {
      studentCount: 0,
      totalRecords: 0,
      avgScore: 0,
      positiveRate: 0,
      negativeRate: 0,
      neutralRate: 0,
      academicRate: 0
    },

    // 图表数据
    chartData: {
      behaviorTypeChart: [],
      scoreDistribution: [],
      weeklyTrend: [],
      topStudents: []
    },

    // 时间范围
    timeRange: 'week', // week, month, term
    timeRangeOptions: [
      { label: '本周', value: 'week' },
      { label: '本月', value: 'month' },
      { label: '本学期', value: 'term' }
    ],

    // 详细记录
    recentRecords: [],
    
    // 学生表现排行
    studentRanking: []
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    const { id } = options;
    if (id) {
      this.setData({ classId: id });
      this.initPage();
    } else {
      wx.showToast({
        title: '班级ID缺失',
        icon: 'none'
      });
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
    }
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {
    // 页面渲染完成后可以进行DOM操作
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    if (this.data.classId) {
      this.loadAnalyticsData();
    }
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {
    
  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {
    
  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {
    this.loadAnalyticsData().then(() => {
      wx.stopPullDownRefresh();
    });
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {
    
  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {
    return {
      title: `${this.data.classInfo.name || '班级'}数据分析报告`,
      path: `/pages/analytics/class/class?id=${this.data.classId}`
    };
  },

  /**
   * 初始化页面
   */
  async initPage() {
    try {
      await this.loadClassInfo();
      await this.loadAnalyticsData();
    } catch (error) {
      console.error('初始化页面失败:', error);
      wx.showToast({
        title: '加载失败',
        icon: 'none'
      });
    } finally {
      this.setData({ loading: false });
    }
  },

  /**
   * 加载班级信息
   */
  async loadClassInfo() {
    try {
      const result = await cloudService.getClassList();
      if (!result.success) {
        throw new Error('获取班级列表失败');
      }

      const classInfo = result.data.find(cls => cls._id === this.data.classId);

      if (classInfo) {
        this.setData({
          classInfo: {
            id: classInfo._id,
            name: classInfo.className,
            grade: classInfo.grade,
            subject: classInfo.subject,
            description: classInfo.description
          }
        });

        // 设置页面标题
        wx.setNavigationBarTitle({
          title: `${classInfo.className} - 数据分析`
        });
      } else {
        throw new Error('班级不存在');
      }
    } catch (error) {
      console.error('加载班级信息失败:', error);
      throw error;
    }
  },

  /**
   * 加载分析数据
   */
  async loadAnalyticsData() {
    try {
      this.setData({ loading: true });

      // 并行加载学生和记录数据
      const [studentResult, recordResult] = await Promise.all([
        cloudService.getStudentList(),
        cloudService.getRecordList({ pageSize: 1000 })
      ]);

      const students = studentResult.success ? studentResult.data : [];
      const records = recordResult.success ? recordResult.data : [];

      // 筛选当前班级的数据
      const classStudents = students.filter(student => student.classId === this.data.classId);
      const classRecords = records.filter(record => record.classId === this.data.classId);

      // 根据时间范围筛选记录
      const filteredRecords = this.filterRecordsByTimeRange(classRecords);

      // 计算统计数据
      const statistics = this.calculateStatistics(classStudents, filteredRecords);

      // 生成图表数据
      const chartData = this.generateChartData(classStudents, filteredRecords);

      // 获取最近记录
      const recentRecords = this.getRecentRecords(filteredRecords, classStudents);

      // 计算学生排行
      const studentRanking = this.calculateStudentRanking(classStudents, filteredRecords);

      this.setData({
        statistics,
        chartData,
        recentRecords,
        studentRanking
      });

    } catch (error) {
      console.error('加载分析数据失败:', error);
      wx.showToast({
        title: '加载数据失败',
        icon: 'none'
      });
    } finally {
      this.setData({ loading: false });
    }
  },

  /**
   * 根据时间范围筛选记录
   */
  filterRecordsByTimeRange(records) {
    const now = new Date();
    let startDate;

    switch (this.data.timeRange) {
      case 'week':
        startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
        break;
      case 'month':
        startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
        break;
      case 'term':
        // 假设学期为3个月
        startDate = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000);
        break;
      default:
        startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
    }

    return records.filter(record => {
      const recordDate = new Date(record.createTime);
      return recordDate >= startDate;
    });
  },

  /**
   * 计算统计数据
   */
  calculateStatistics(students, records) {
    const studentCount = students.length;
    const totalRecords = records.length;

    if (totalRecords === 0) {
      return {
        studentCount,
        totalRecords: 0,
        avgScore: 0,
        positiveRate: 0,
        negativeRate: 0,
        neutralRate: 0,
        academicRate: 0
      };
    }

    // 计算平均分
    const totalScore = records.reduce((sum, record) => sum + (record.score || 0), 0);
    const avgScore = Math.round((totalScore / totalRecords) * 10) / 10;

    // 计算行为类型比例
    const behaviorCounts = {
      positive: 0,
      negative: 0,
      neutral: 0,
      academic: 0
    };

    records.forEach(record => {
      behaviorCounts[record.behaviorType] = (behaviorCounts[record.behaviorType] || 0) + 1;
    });

    return {
      studentCount,
      totalRecords,
      avgScore,
      positiveRate: Math.round((behaviorCounts.positive / totalRecords) * 100),
      negativeRate: Math.round((behaviorCounts.negative / totalRecords) * 100),
      neutralRate: Math.round((behaviorCounts.neutral / totalRecords) * 100),
      academicRate: Math.round((behaviorCounts.academic / totalRecords) * 100)
    };
  },

  /**
   * 生成图表数据
   */
  generateChartData(students, records) {
    // 行为类型分布
    const behaviorTypeChart = this.generateBehaviorTypeChart(records);
    
    // 分数分布
    const scoreDistribution = this.generateScoreDistribution(records);
    
    // 周趋势
    const weeklyTrend = this.generateWeeklyTrend(records);
    
    // 优秀学生
    const topStudents = this.generateTopStudents(students, records);

    return {
      behaviorTypeChart,
      scoreDistribution,
      weeklyTrend,
      topStudents
    };
  },

  /**
   * 生成行为类型图表数据
   */
  generateBehaviorTypeChart(records) {
    const behaviorCounts = {
      positive: 0,
      negative: 0,
      neutral: 0,
      academic: 0
    };

    records.forEach(record => {
      behaviorCounts[record.behaviorType] = (behaviorCounts[record.behaviorType] || 0) + 1;
    });

    return [
      { name: '积极行为', value: behaviorCounts.positive, color: '#52C873' },
      { name: '消极行为', value: behaviorCounts.negative, color: '#FF5247' },
      { name: '中性行为', value: behaviorCounts.neutral, color: '#FFB800' },
      { name: '学习表现', value: behaviorCounts.academic, color: '#4080FF' }
    ];
  },

  /**
   * 生成分数分布数据
   */
  generateScoreDistribution(records) {
    const scoreRanges = {
      '0-2': 0,
      '3-4': 0,
      '5-6': 0,
      '7-8': 0,
      '9-10': 0
    };

    records.forEach(record => {
      const score = record.score || 0;
      if (score <= 2) scoreRanges['0-2']++;
      else if (score <= 4) scoreRanges['3-4']++;
      else if (score <= 6) scoreRanges['5-6']++;
      else if (score <= 8) scoreRanges['7-8']++;
      else scoreRanges['9-10']++;
    });

    return Object.keys(scoreRanges).map(range => ({
      name: range + '分',
      value: scoreRanges[range]
    }));
  },

  /**
   * 生成周趋势数据
   */
  generateWeeklyTrend(records) {
    const days = ['周一', '周二', '周三', '周四', '周五', '周六', '周日'];
    const dayCounts = new Array(7).fill(0);

    records.forEach(record => {
      const date = new Date(record.createTime);
      const dayOfWeek = date.getDay();
      const adjustedDay = dayOfWeek === 0 ? 6 : dayOfWeek - 1; // 调整为周一开始
      dayCounts[adjustedDay]++;
    });

    // 找到最大值用于计算百分比
    const maxValue = Math.max(...dayCounts);

    return days.map((day, index) => ({
      name: day,
      value: dayCounts[index],
      heightPercent: maxValue > 0 ? Math.round((dayCounts[index] / maxValue) * 100) : 0
    }));
  },

  /**
   * 生成优秀学生数据
   */
  generateTopStudents(students, records) {
    const studentScores = {};

    // 计算每个学生的平均分
    records.forEach(record => {
      if (!studentScores[record.studentId]) {
        studentScores[record.studentId] = { totalScore: 0, count: 0 };
      }
      studentScores[record.studentId].totalScore += (record.score || 0);
      studentScores[record.studentId].count++;
    });

    // 计算平均分并排序
    const studentAvgScores = Object.keys(studentScores).map(studentId => {
      const student = students.find(s => s._id === studentId);
      const avgScore = studentScores[studentId].totalScore / studentScores[studentId].count;
      return {
        id: studentId,
        name: student ? student.name : '未知学生',
        avgScore: Math.round(avgScore * 10) / 10,
        recordCount: studentScores[studentId].count
      };
    }).sort((a, b) => b.avgScore - a.avgScore);

    return studentAvgScores.slice(0, 10); // 返回前10名
  },

  /**
   * 获取最近记录
   */
  getRecentRecords(records, students) {
    return records
      .sort((a, b) => new Date(b.createTime) - new Date(a.createTime))
      .slice(0, 20)
      .map(record => {
        const student = students.find(s => s._id === record.studentId);
        return {
          ...record,
          studentName: student ? student.name : '未知学生',
          timeText: this.formatTime(record.createTime)
        };
      });
  },

  /**
   * 计算学生排行
   */
  calculateStudentRanking(students, records) {
    const studentStats = {};

    // 初始化学生统计
    students.forEach(student => {
      studentStats[student._id] = {
        id: student._id,
        name: student.name,
        totalRecords: 0,
        totalScore: 0,
        avgScore: 0,
        positiveCount: 0,
        negativeCount: 0
      };
    });

    // 统计记录数据
    records.forEach(record => {
      if (studentStats[record.studentId]) {
        const stats = studentStats[record.studentId];
        stats.totalRecords++;
        stats.totalScore += (record.score || 0);
        
        if (record.behaviorType === 'positive' || record.behaviorType === 'academic') {
          stats.positiveCount++;
        } else if (record.behaviorType === 'negative') {
          stats.negativeCount++;
        }
      }
    });

    // 计算平均分并排序
    return Object.values(studentStats)
      .map(stats => ({
        ...stats,
        avgScore: stats.totalRecords > 0 ? Math.round((stats.totalScore / stats.totalRecords) * 10) / 10 : 0
      }))
      .sort((a, b) => b.avgScore - a.avgScore);
  },

  /**
   * 格式化时间
   */
  formatTime(timestamp) {
    const date = new Date(timestamp);
    const now = new Date();
    const diff = now - date;
    
    if (diff < 60000) { // 1分钟内
      return '刚刚';
    } else if (diff < 3600000) { // 1小时内
      return `${Math.floor(diff / 60000)}分钟前`;
    } else if (diff < 86400000) { // 1天内
      return `${Math.floor(diff / 3600000)}小时前`;
    } else {
      return date.toLocaleDateString();
    }
  },

  /**
   * 切换时间范围
   */
  onTimeRangeChange(e) {
    const { value } = e.detail;
    this.setData({ timeRange: value });
    this.loadAnalyticsData();
  },



  /**
   * 导出分析报告
   */
  exportReport() {
    wx.showLoading({
      title: '生成报告中...',
      mask: true
    });

    try {
      const reportContent = this.generateAnalysisReport();
      
      wx.setClipboardData({
        data: reportContent,
        success: () => {
          wx.hideLoading();
          wx.showModal({
            title: '导出完成',
            content: '分析报告已复制到剪贴板，请粘贴到文档中保存。',
            showCancel: false
          });
        },
        fail: () => {
          wx.hideLoading();
          wx.showToast({
            title: '导出失败',
            icon: 'none'
          });
        }
      });
    } catch (error) {
      wx.hideLoading();
      wx.showToast({
        title: '生成报告失败',
        icon: 'none'
      });
    }
  },

  /**
   * 生成分析报告
   */
  generateAnalysisReport() {
    const { classInfo, statistics, timeRange } = this.data;
    const timeRangeText = this.data.timeRangeOptions.find(opt => opt.value === timeRange)?.label || '本周';
    
    let report = `${classInfo.name} 数据分析报告\n`;
    report += `生成时间：${new Date().toLocaleString()}\n`;
    report += `分析期间：${timeRangeText}\n\n`;
    
    report += `=== 基础统计 ===\n`;
    report += `班级学生数：${statistics.studentCount}人\n`;
    report += `记录总数：${statistics.totalRecords}条\n`;
    report += `平均分：${statistics.avgScore}分\n\n`;
    
    report += `=== 行为分布 ===\n`;
    report += `积极行为：${statistics.positiveRate}%\n`;
    report += `消极行为：${statistics.negativeRate}%\n`;
    report += `中性行为：${statistics.neutralRate}%\n`;
    report += `学习表现：${statistics.academicRate}%\n\n`;
    
    report += `=== 学生排行榜 ===\n`;
    this.data.studentRanking.slice(0, 10).forEach((student, index) => {
      report += `${index + 1}. ${student.name} - 平均分：${student.avgScore}分 (记录数：${student.totalRecords})\n`;
    });
    
    return report;
  }
});