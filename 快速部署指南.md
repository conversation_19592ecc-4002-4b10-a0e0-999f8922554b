# ⚡ 评语灵感君快速部署指南

## 🎯 5分钟快速上线

### 第一步：获取项目 (1分钟)
```bash
# 克隆项目
git clone https://github.com/chanwarmsun/wechat3.0.git
cd wechat3.0

# 安装依赖
npm install
```

### 第二步：配置环境 (2分钟)
```bash
# 复制环境配置
cp .env.example .env
```

编辑 `.env` 文件，填入以下**必需配置**：
```env
# 微信小程序配置（必填）
MINIPROGRAM_APP_ID=wx3de03090b8e8a734
MINIPROGRAM_APP_SECRET=你的小程序密钥

# 云开发配置（必填）
CLOUD_ENV_ID=cloud1-4g85f8xlb8166ff1

# 豆包AI配置（必填）
DOUBAO_API_KEY=你的豆包API密钥
```

### 第三步：部署上线 (2分钟)
```bash
# 运行部署检查
node scripts/pre-deploy-check.js

# 如果检查通过，执行部署
node scripts/production-deploy.js
```

## 🔧 详细配置步骤

### 1. 微信小程序配置
1. 登录 [微信公众平台](https://mp.weixin.qq.com)
2. 进入"开发" → "开发管理" → "开发设置"
3. 复制 AppID 和 AppSecret

### 2. 云开发环境配置
1. 打开微信开发者工具
2. 导入项目（选择项目根目录）
3. 点击"云开发"按钮开通服务
4. 记录云环境ID

### 3. 豆包AI配置
1. 访问 [豆包开放平台](https://www.volcengine.com/product/doubao)
2. 注册账号并创建应用
3. 获取API密钥

### 4. 云函数部署
在微信开发者工具中，右键以下云函数并选择"上传并部署"：
- `login` - 用户登录
- `getUserId` - 获取用户ID  
- `getStudents` - 获取学生列表
- `callDoubaoAPI` - AI服务调用
- `generateComment` - 评语生成
- `adminAPI` - 管理后台API

### 5. 管理后台部署
```bash
cd admin-v2
npm install
npm run build

# 将build目录上传到云开发静态托管
```

## ✅ 部署验证清单

### 基础功能测试
- [ ] 小程序正常打开
- [ ] 用户登录功能正常
- [ ] 学生管理功能正常
- [ ] AI评语生成功能正常
- [ ] 管理后台访问正常

### 性能检查
- [ ] 页面加载时间 < 3秒
- [ ] AI生成时间 < 3分钟
- [ ] 数据同步正常
- [ ] 错误处理正常

## 🚨 常见问题快速解决

### Q1: 环境变量检查失败
```bash
# 检查.env文件是否存在
ls -la .env

# 检查环境变量是否正确
node -e "require('dotenv').config(); console.log(process.env.MINIPROGRAM_APP_ID)"
```

### Q2: 云函数部署失败
```bash
# 检查云函数代码
# 在微信开发者工具中查看云函数日志
# 确保依赖包正确安装
```

### Q3: AI服务调用失败
```bash
# 测试API连接
node -e "
const axios = require('axios');
axios.post('https://ark.cn-beijing.volces.com/api/v3/chat/completions', {
  model: 'doubao-pro-4k',
  messages: [{role: 'user', content: 'test'}]
}, {
  headers: {
    'Authorization': 'Bearer ' + process.env.DOUBAO_API_KEY,
    'Content-Type': 'application/json'
  }
}).then(res => console.log('API连接正常')).catch(err => console.log('API连接失败:', err.message));
"
```

### Q4: 管理后台无法访问
```bash
# 检查构建是否成功
cd admin-v2
npm run build

# 检查静态托管是否开通
# 在云开发控制台中查看静态网站托管状态
```

## 🎉 部署成功！

恭喜！你的评语灵感君小程序已经成功部署上线！

### 下一步操作：
1. **配置AI模型**：登录管理后台配置豆包AI参数
2. **创建提示词模板**：设置四种评语风格的模板
3. **邀请用户测试**：邀请几位教师进行真实使用
4. **监控系统状态**：关注用户使用数据和系统性能

### 管理后台访问：
- 地址：`https://你的云环境ID.tcb.qcloud.la`
- 默认账号：admin
- 默认密码：admin123（首次登录后请修改）

### 小程序体验：
- 在微信中搜索"评语灵感君"
- 或扫描小程序码体验

## 📞 技术支持

如果遇到问题，可以：
- 查看 [详细部署指南](docs/部署指南.md)
- 提交 [GitHub Issue](https://github.com/chanwarmsun/wechat3.0/issues)
- 查看 [技术亮点文档](docs/技术亮点.md)

---

🚀 **让AI赋能教育，3分钟生成专业评语！**
