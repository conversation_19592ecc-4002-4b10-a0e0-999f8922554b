/* 班级创建页面样式 */
.class-create-page {
  background-color: #f5f5f5;
  min-height: 100vh;
  padding-bottom: 120rpx;
}

/* 表单容器 */
.form-container {
  padding: 24rpx;
}

.form-section {
  background: white;
  border-radius: 20rpx;
  padding: 32rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.06);
}

.form-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 32rpx;
  text-align: center;
}

.form-item {
  margin-bottom: 32rpx;
}

.form-item:last-child {
  margin-bottom: 0;
}

.form-label {
  font-size: 30rpx;
  color: #333;
  font-weight: 500;
  margin-bottom: 16rpx;
}

.form-input {
  font-size: 28rpx !important;
  padding: 24rpx !important;
  border-radius: 16rpx !important;
  background: #f8f9fa !important;
  border: 2rpx solid transparent !important;
}

.form-input:focus {
  border-color: #4080FF !important;
  background: white !important;
}

.form-textarea {
  min-height: 120rpx !important;
  font-size: 28rpx !important;
  padding: 24rpx !important;
  border-radius: 16rpx !important;
  background: #f8f9fa !important;
  border: 2rpx solid transparent !important;
}

.form-textarea:focus {
  border-color: #4080FF !important;
  background: white !important;
}

/* 底部操作栏 */
.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  display: flex;
  gap: 24rpx;
  padding: 24rpx;
  background: white;
  border-top: 1rpx solid #f0f0f0;
  box-shadow: 0 -4rpx 20rpx rgba(0, 0, 0, 0.06);
}

.cancel-btn {
  flex: 1;
}

.cancel-btn .van-button {
  height: 88rpx !important;
  font-size: 30rpx !important;
  border-radius: 44rpx !important;
  border: 2rpx solid #e0e0e0 !important;
  color: #666 !important;
}

.submit-btn {
  flex: 2;
}

.submit-btn .van-button {
  height: 88rpx !important;
  font-size: 30rpx !important;
  font-weight: 600 !important;
  border-radius: 44rpx !important;
  box-shadow: 0 4rpx 16rpx rgba(64, 128, 255, 0.3) !important;
}