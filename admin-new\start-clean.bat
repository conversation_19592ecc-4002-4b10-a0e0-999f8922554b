@echo off
echo.
echo 🚀 启动评语灵感君管理后台（清洁版）
echo ========================================
echo.

REM 检查Node.js
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 错误：未找到Node.js，请先安装Node.js 18+
    pause
    exit /b 1
)

REM 检查依赖
if not exist "node_modules" (
    echo 📦 首次运行，正在安装依赖...
    npm install
    if %errorlevel% neq 0 (
        echo ❌ 依赖安装失败，请检查网络连接
        pause
        exit /b 1
    )
)

REM 清理缓存
echo 🧹 清理Vite缓存...
if exist "node_modules\.vite" rmdir /s /q "node_modules\.vite"

echo.
echo ✅ 配置已优化，CSP问题已解决
echo 📍 访问地址: http://localhost:8080
echo 🔄 支持热更新和自动刷新
echo.
echo 正在启动开发服务器...
echo.

REM 启动开发服务器
npm run dev

pause