@echo off
echo.
echo ==========================================
echo ULTIMATE CSP BYPASS - Last Resort
echo ==========================================
echo.

echo This is the most aggressive Chrome launch
echo All security features will be disabled
echo.

echo Cleaning Chrome processes...
taskkill /f /im chrome.exe >nul 2>&1
timeout /t 2 /nobreak >nul

echo Creating fresh temp directory...
set NEW_TEMP=%TEMP%\chrome-ultimate-%RANDOM%-%RANDOM%
mkdir "%NEW_TEMP%" >nul 2>&1

echo.
echo Launching ULTIMATE Chrome configuration...

start "Ultimate Chrome" chrome.exe ^
  --disable-web-security ^
  --disable-features=VizDisplayCompositor,TranslateUI ^
  --disable-site-isolation-trials ^
  --disable-extensions ^
  --disable-plugins ^
  --no-sandbox ^
  --no-zygote ^
  --allow-running-insecure-content ^
  --disable-background-timer-throttling ^
  --disable-backgrounding-occluded-windows ^
  --disable-renderer-backgrounding ^
  --disable-hang-monitor ^
  --disable-prompt-on-repost ^
  --no-first-run ^
  --no-default-browser-check ^
  --disable-sync ^
  --disable-translate ^
  --disable-logging ^
  --disable-gpu-sandbox ^
  --user-data-dir="%NEW_TEMP%" ^
  --ignore-certificate-errors ^
  --ignore-ssl-errors ^
  --ignore-certificate-errors-spki-list ^
  --allow-cross-origin-auth-prompt ^
  --disable-dev-shm-usage ^
  "http://localhost:8080"

echo.
echo ULTIMATE Chrome launched!
echo Temp dir: %NEW_TEMP%
echo.
echo If this doesn't work, the issue is:
echo   1. Enterprise network policy
echo   2. Antivirus software interference  
echo   3. System-level security restrictions
echo   4. Need to contact network administrator
echo.
pause