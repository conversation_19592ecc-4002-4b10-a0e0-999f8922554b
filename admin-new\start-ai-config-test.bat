@echo off
chcp 65001 >nul
echo ========================================
echo AI Config Test Environment Startup
echo ========================================

echo.
echo Starting local API server...
start "API-Server" cmd /k "cd /d %~dp0 && node local-api-server.cjs"

echo.
echo Waiting for API server to start...
timeout /t 3 /nobreak >nul

echo.
echo Starting frontend dev server...
start "Frontend-Server" cmd /k "cd /d %~dp0 && npm run dev"

echo.
echo Waiting for frontend server to start...
timeout /t 5 /nobreak >nul

echo.
echo Test environment started successfully!
echo.
echo API Server: http://localhost:3000/admin
echo Frontend App: http://localhost:5173
echo.
echo Test Steps:
echo 1. Open browser and visit http://localhost:5173
echo 2. Navigate to AI Config page
echo 3. Test the following functions:
echo    - Add AI model
echo    - Edit AI model
echo    - Delete AI model
echo    - Test AI connection
echo    - Save system config
echo.
echo Tips:
echo - All data is simulated, will reset after restart
echo - Check API server window for request logs
echo - Press Ctrl+C to stop corresponding server
echo.
echo Press any key to close this window...
pause >nul
