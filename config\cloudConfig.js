/**
 * 云开发统一配置文件
 * 确保所有服务使用相同的云开发环境配置
 */

// 加载环境变量
const { envLoader } = require('./envLoader');

// 生产环境云开发配置
const PRODUCTION_CLOUD_CONFIG = {
  env: envLoader.get('CLOUD_ENV_ID', 'cloud1-4g85f8xlb8166ff1'),
  appid: envLoader.get('MINIPROGRAM_APP_ID', 'wx3de03090b8e8a734'),
  region: envLoader.get('CLOUD_REGION', 'ap-shanghai'),
  traceUser: true,
  timeout: 60000
};

// 开发环境云开发配置
const DEVELOPMENT_CLOUD_CONFIG = {
  env: envLoader.get('CLOUD_ENV_ID_DEV', 'cloud1-4g85f8xlb8166ff1-dev'),
  appid: envLoader.get('MINIPROGRAM_APP_ID', 'wx3de03090b8e8a734'),
  region: envLoader.get('CLOUD_REGION', 'ap-shanghai'),
  traceUser: true,
  timeout: 60000
};

// 获取当前环境配置
function getCurrentCloudConfig() {
  // 检查环境变量或小程序版本来确定环境
  const isDevelopment = typeof wx !== 'undefined' && wx.getSystemInfoSync &&
    (wx.getSystemInfoSync().platform === 'devtools' ||
     wx.getAccountInfoSync().miniProgram.envVersion === 'develop');

  const isStaging = typeof wx !== 'undefined' && wx.getAccountInfoSync &&
    wx.getAccountInfoSync().miniProgram.envVersion === 'trial';

  // 根据环境返回对应配置
  if (isDevelopment) {
    console.log('🔧 使用开发环境云配置');
    return DEVELOPMENT_CLOUD_CONFIG;
  } else if (isStaging) {
    console.log('🧪 使用预发布环境云配置');
    return PRODUCTION_CLOUD_CONFIG; // 预发布使用生产配置
  } else {
    console.log('🚀 使用生产环境云配置');
    return PRODUCTION_CLOUD_CONFIG;
  }
}

// 云函数配置
const CLOUD_FUNCTIONS = {
  // 用户相关
  login: { timeout: 10000 },
  getUserId: { timeout: 5000 },
  
  // 学生管理
  getStudents: { timeout: 10000 },
  addStudent: { timeout: 10000 },
  updateStudent: { timeout: 10000 },
  deleteStudent: { timeout: 10000 },
  
  // AI相关
  callDoubaoAPI: { timeout: 60000 },
  doubaoAI: { timeout: 60000 },
  generateComment: { timeout: 30000 },
  generateEvaluation: { timeout: 30000 },
  
  // 数据管理
  initDatabase: { timeout: 30000 },
  clearAllData: { timeout: 30000 },
  
  // 统计分析
  getStatistics: { timeout: 15000 },
  getGrowthStats: { timeout: 15000 },
  getMonitoringStats: { timeout: 10000 },
  
  // 管理后台
  adminAPI: { timeout: 30000 },
  webAPI: { timeout: 30000 }
};

// 数据库集合配置
const DATABASE_COLLECTIONS = {
  // 用户相关
  users: 'users',
  admins: 'admins',
  
  // 教学相关
  classes: 'classes',
  students: 'students',
  records: 'records',
  comments: 'comments',
  
  // AI相关
  ai_configs: 'ai_configs',
  ai_usage: 'ai_usage',
  ai_generation_logs: 'ai_generation_logs',
  ai_error_logs: 'ai_error_logs',
  
  // 系统相关
  settings: 'settings',
  system_config: 'system_config',
  logs: 'logs',
  files: 'files',
  notifications: 'notifications'
};

// 初始化云开发
function initCloudDevelopment() {
  if (typeof wx === 'undefined' || !wx.cloud) {
    console.error('微信云开发环境未找到');
    return false;
  }
  
  try {
    const config = getCurrentCloudConfig();
    wx.cloud.init(config);
    console.log('☁️ 云开发初始化成功:', config.env);
    return true;
  } catch (error) {
    console.error('☁️ 云开发初始化失败:', error);
    return false;
  }
}

// 获取云数据库实例
function getCloudDatabase() {
  if (typeof wx === 'undefined' || !wx.cloud) {
    throw new Error('微信云开发环境未找到');
  }
  
  return wx.cloud.database();
}

// 调用云函数的统一方法
function callCloudFunction(name, data = {}) {
  return new Promise((resolve, reject) => {
    const config = CLOUD_FUNCTIONS[name] || { timeout: 10000 };
    
    wx.cloud.callFunction({
      name,
      data,
      ...config,
      success: (res) => {
        console.log(`☁️ 云函数 ${name} 调用成功:`, res.result);
        resolve(res.result);
      },
      fail: (error) => {
        console.error(`☁️ 云函数 ${name} 调用失败:`, error);
        reject(error);
      }
    });
  });
}

// 导出配置和方法
module.exports = {
  // 配置
  PRODUCTION_CLOUD_CONFIG,
  DEVELOPMENT_CLOUD_CONFIG,
  CLOUD_FUNCTIONS,
  DATABASE_COLLECTIONS,
  
  // 方法
  getCurrentCloudConfig,
  initCloudDevelopment,
  getCloudDatabase,
  callCloudFunction
};

// 如果在小程序环境中，自动初始化
if (typeof wx !== 'undefined' && wx.cloud) {
  initCloudDevelopment();
}
