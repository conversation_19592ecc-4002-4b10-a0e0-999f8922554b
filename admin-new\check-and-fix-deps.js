/**
 * 检查和修复依赖包
 * 解决模块加载失败问题
 */

const fs = require('fs')
const path = require('path')
const { execSync } = require('child_process')

console.log('🔍 检查管理后台依赖和配置...')

// 检查必要文件
const checkFiles = [
  'package.json',
  'vite.config.ts',
  'tsconfig.json',
  'src/main.tsx',
  'src/App.tsx'
]

const missingFiles = checkFiles.filter(file => !fs.existsSync(file))
if (missingFiles.length > 0) {
  console.error('❌ 缺失文件:', missingFiles)
  process.exit(1)
}

console.log('✅ 所有必要文件存在')

// 检查package.json的依赖
const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'))
const requiredDeps = [
  'react',
  'react-dom', 
  '@vitejs/plugin-react',
  'vite',
  'typescript'
]
const missingDeps = []

requiredDeps.forEach(dep => {
  if (!packageJson.dependencies[dep] && !packageJson.devDependencies[dep]) {
    missingDeps.push(dep)
  }
})

if (missingDeps.length > 0) {
  console.log('⚠️  发现缺失依赖:', missingDeps)
  console.log('🔄 正在安装缺失依赖...')
  
  try {
    execSync('npm install', { stdio: 'inherit' })
    console.log('✅ 依赖安装完成')
  } catch (error) {
    console.error('❌ 依赖安装失败:', error.message)
    process.exit(1)
  }
}

// 检查TypeScript配置
const tsconfigContent = JSON.parse(fs.readFileSync('tsconfig.json', 'utf8'))
const expectedSettings = {
  "jsx": "react-jsx",
  "target": "ES2020",
  "module": "ESNext",
  "lib": ["ES2020", "DOM", "DOM.Iterable"]
}

Object.entries(expectedSettings).forEach(([key, value]) => {
  if (!tsconfigContent.compilerOptions[key]) {
    console.log(`⚠️  tsconfig.json缺少: ${key}`)
  })
})

// 检查main.tsx文件
const mainTsxPath = 'src/main.tsx'
if (fs.existsSync(mainTsxPath)) {
  const mainContent = fs.readFileSync(mainTsxPath, 'utf8')
  console.log('📄 main.tsx内容检查完成')
  
  if (!mainContent.includes('createRoot')) {
    console.log('⚠️  main.tsx建议使用createRoot API')
  }
}

console.log('\n🎯 修复步骤建议:')
console.log('1. 运行: npm install')
console.log('2. 运行: npm run dev')
console.log('3. 如果仍然失败，运行: node check-and-fix-deps.js')
console.log('\n✨ 检查完成！')