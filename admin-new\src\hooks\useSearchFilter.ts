/**
 * 高级搜索和筛选Hook
 * 提供智能搜索、多重筛选、排序和数据转换功能
 */

import { useState, useMemo, useCallback, useEffect } from 'react'

export interface SearchFilterConfig<T> {
  initialData: T[]
  searchKeys?: (keyof T)[]
  filters?: Record<string, (value: any, item: T) => boolean>
  sortOptions?: {
    key: keyof T
    label: string
    reverse?: boolean
  }[]
  groupBy?: (item: T) => string
  pageSize?: number
  debounceTime?: number
}

export interface SearchFilters {
  search?: string
  [key: string]: any
}

export interface UseSearchFilterReturn<T> {
  items: T[]
  filteredItems: T[]
  paginatedItems: T[]
  groupedItems: Record<string, T[]>
  searchFilters: SearchFilters
  setSearchFilters: (filters: SearchFilters) => void
  sortedBy: keyof T | null
  setSortedBy: (key: keyof T | null) => void
  sortOrder: 'asc' | 'desc'
  setSortOrder: (order: 'asc' | 'desc') => void
  currentPage: number
  setCurrentPage: (page: number) => void
  totalPages: number
  totalItems: number
  loading: boolean
  setLoading: (loading: boolean) => void
  resetFilters: () => void
  search: (query: string) => void
  applyFilter: (key: string, value: any) => void
  removeFilter: (key: string) => void
  clearSearch: () => void
}

/**
 * 高级搜索和筛选Hook
 */
export function useSearchFilter<T extends Record<string, any>>(
  config: SearchFilterConfig<T>
): UseSearchFilterReturn<T> {
  const {
    initialData,
    searchKeys = [],
    filters = {},
    sortOptions = [],
    groupBy,
    pageSize = 20,
    debounceTime = 300,
  } = config

  const [items, setItems] = useState<T[]>(initialData)
  const [searchFilters, setSearchFilters] = useState<SearchFilters>({})
  const [debouncedSearch, setDebouncedSearch] = useState('')
  const [sortedBy, setSortedBy] = useState<keyof T | null>(null)
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('asc')
  const [currentPage, setCurrentPage] = useState(1)
  const [loading, setLoading] = useState(false)

  // 实时更新数据
  useEffect(() => {
    setItems(initialData)
  }, [initialData])

  // 防抖搜索
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearch(searchFilters.search || '')
    }, debounceTime)

    return () => clearTimeout(timer)
  }, [searchFilters.search, debounceTime])

  // 计算总计
  const totalItems = items.length
  const totalPages = Math.ceil(totalItems / pageSize)

  // 多条件筛选和搜索
  const filteredItems = useMemo(() => {
    let result = [...items]

    // 搜索功能
    if (debouncedSearch && searchKeys.length > 0) {
      const searchLower = debouncedSearch.toLowerCase()
      result = result.filter(item =>
        searchKeys.some(key => {
          const value = item[key]
          return String(value).toLowerCase().includes(searchLower)
        })
      )
    }

    // 应用筛选器
    Object.entries(searchFilters).forEach(([key, value]) => {
      if (key === 'search' || value === undefined || value === null) return

      const filterFn = filters[key]
      if (filterFn) {
        result = result.filter(item => filterFn(value, item))
      } else {
        // 默认精确匹配
        result = result.filter(item => item[key] === value)
      }
    })

    return result
  }, [items, debouncedSearch, searchKeys, searchFilters, filters])

  // 排序
  const sortedItems = useMemo(() => {
    if (!sortedBy) return filteredItems

    return [...filteredItems].sort((a, b) => {
      const aValue = a[sortedBy]
      const bValue = b[sortedBy]

      // 处理中文拼音排序
      if (typeof aValue === 'string' && typeof bValue === 'string') {
        const comparison = aValue.localeCompare(bValue, 'zh-CN')
        return sortOrder === 'asc' ? comparison : -comparison
      }

      // 数字排序
      if (typeof aValue === 'number' && typeof bValue === 'number') {
        return sortOrder === 'asc' ? aValue - bValue : bValue - aValue
      }

      // 日期排序
      if (aValue instanceof Date && bValue instanceof Date) {
        const comparison = aValue.getTime() - bValue.getTime()
        return sortOrder === 'asc' ? comparison : -comparison
      }

      // 通用排序
      const aStr = String(aValue)
      const bStr = String(bValue)
      const comparison = aStr.localeCompare(bStr)
      return sortOrder === 'asc' ? comparison : -comparison
    })
  }, [filteredItems, sortedBy, sortOrder])

  // 分组
  const groupedItems = useMemo(() => {
    if (!groupBy) return {}

    return sortedItems.reduce((groups, item) => {
      const groupKey = groupBy(item)
      if (!groups[groupKey]) groups[groupKey] = []
      groups[groupKey].push(item)
      return groups
    }, {} as Record<string, T[]>)
  }, [sortedItems, groupBy])

  // 分页
  const paginatedItems = useMemo(() => {
    const startIndex = (currentPage - 1) * pageSize
    return sortedItems.slice(startIndex, startIndex + pageSize)
  }, [sortedItems, currentPage, pageSize])

  // 操作函数
  const setSearchFiltersInternal = useCallback((filters: SearchFilters) => {
    setSearchFilters(filters)
    setCurrentPage(1) // 重置到第一页
  }, [])

  const search = useCallback((query: string) => {
    setSearchFilters(prev => ({ ...prev, search: query }))
    setCurrentPage(1)
  }, [])

  const applyFilter = useCallback((key: string, value: any) => {
    setSearchFilters(prev => ({ ...prev, [key]: value }))
    setCurrentPage(1)
  }, [])

  const removeFilter = useCallback((key: string) => {
    setSearchFilters(prev => {
      const newFilters = { ...prev }
      delete newFilters[key]
      return newFilters
    })
    setCurrentPage(1)
  }, [])

  const clearSearch = useCallback(() => {
    setSearchFilters(prev => {
      const newFilters = { ...prev }
      delete newFilters.search
      return newFilters
    })
  }, [])

  const resetFilters = useCallback(() => {
    setSearchFilters({})
    setCurrentPage(1)
    setSortedBy(null)
  }, [])

  return {
    items,
    filteredItems: sortedItems,
    paginatedItems,
    groupedItems,
    searchFilters,
    setSearchFilters: setSearchFiltersInternal,
    sortedBy,
    setSortedBy,
    sortOrder,
    setSortOrder,
    currentPage,
    setCurrentPage,
    totalPages,
    totalItems,
    loading,
    setLoading,
    resetFilters,
    search,
    applyFilter,
    removeFilter,
    clearSearch,
  }
}

/**
 * 本地存储的搜索hook
 */
export function useLocalStorageSearchFilter<T>(
  key: string,
  config: SearchFilterConfig<T>
): UseSearchFilterReturn<T> {
  const [searchConfig, setSearchConfig] = useState(config)

  // 从localStorage加载配置
  useEffect(() => {
    const saved = localStorage.getItem(`search-filters:${key}`)
    if (saved) {
      try {
        const parsed = JSON.parse(saved)
        setSearchConfig(prev => ({
          ...prev,
          searchFilters: { ...prev.searchFilters, ...parsed.filters },
          sortedBy: parsed.sortedBy || null,
          sortOrder: parsed.sortOrder || 'asc',
        }))
      } catch (e) {
        console.warn('Failed to load search filters from storage')
      }
    }
  }, [key])

  // 保存配置到localStorage
  const handleFiltersChange = useCallback((filters: SearchFilters) => {
    localStorage.setItem(`search-filters:${key}`, JSON.stringify({
      filters,
      timestamp: new Date().toISOString(),
    }))
    setSearchConfig(prev => ({ ...prev, searchFilters: filters }))
  }, [key])

  const searchFilter = useSearchFilter({
    ...searchConfig,
    setSearchFilters: handleFiltersChange,
  })

  return searchFilter
}

/**
 * 服务端搜索hook (模拟)
 */
export function useServerSearch<T>(
  fetchFn: (params: { search?: string; filters?: any; page?: number; sort?: any }) => Promise<{
    items: T[]
    total: number
    hasMore: boolean
  }>
) {
  const [items, setItems] = useState<T[]>([])
  const [loading, setLoading] = useState(false)
  const [total, setTotal] = useState(0)
  const [hasMore, setHasMore] = useState(true)
  const [error, setError] = useState<string | null>(null)

  const search = useCallback(async (params = {}) => {
    setLoading(true)
    setError(null)
    
    try {
      const result = await fetchFn({
        search: params.search,
        filters: params.filters,
        page: params.page || 1,
        sort: params.sort,
      })
      
      setItems(result.items)
      setTotal(result.total)
      setHasMore(result.hasMore)
    } catch (err) {
      setError(err instanceof Error ? err.message : '搜索失败')
    } finally {
      setLoading(false)
    }
  }, [fetchFn])

  const loadMore = useCallback(() => {
    if (!loading && hasMore) {
      // 实现分页加载
    }
  }, [loading, hasMore])

  return {
    items,
    loading,
    total,
    hasMore,
    error,
    search,
    loadMore,
  }
}

export default useSearchFilter