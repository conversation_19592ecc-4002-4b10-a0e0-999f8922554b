/* 学生详情页样式 */
.container {
  background-color: #f5f5f5;
  min-height: 100vh;
  padding-bottom: 40rpx;
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 60vh;
}

/* 学生信息卡片 */
.student-info-card {
  margin: 30rpx;
  background: white;
  border-radius: 20rpx;
  padding: 40rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.info-header {
  display: flex;
  align-items: center;
}

.avatar-section {
  margin-right: 30rpx;
}

.student-avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 60rpx;
  background-color: #f0f0f0;
}

.student-avatar image {
  width: 100%;
  height: 100%;
  border-radius: 60rpx;
}

.student-avatar-placeholder {
  width: 120rpx;
  height: 120rpx;
  border-radius: 60rpx;
  background-color: #5470C6;
  display: flex;
  align-items: center;
  justify-content: center;
}

.student-avatar-placeholder text {
  color: white;
  font-size: 48rpx;
  font-weight: bold;
}

.basic-info {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.student-name {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 8rpx;
}

.student-class {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 8rpx;
}

.student-number {
  font-size: 24rpx;
  color: #999;
}

.action-btn {
  padding: 20rpx;
}

.action-icon {
  font-size: 40rpx;
  color: #007AFF;
}

/* 统计信息 */
.statistics-section {
  margin: 30rpx;
  background: white;
  border-radius: 20rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.stats-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 30rpx;
}

.stats-grid {
  display: flex;
  justify-content: space-around;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
}

.stat-number {
  font-size: 48rpx;
  font-weight: bold;
  color: #007AFF;
  margin-bottom: 8rpx;
}

.stat-item.positive .stat-number {
  color: #34C759;
}

.stat-item.negative .stat-number {
  color: #FF3B30;
}

.stat-label {
  font-size: 24rpx;
  color: #666;
}

/* 筛选器 */
.filter-section {
  margin: 30rpx;
  background: white;
  border-radius: 20rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
}

.filter-label {
  font-size: 28rpx;
  color: #333;
  margin-right: 20rpx;
}

.filter-picker {
  display: flex;
  align-items: center;
  padding: 16rpx 24rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 12rpx;
  background: #f8f8f8;
}

.picker-arrow {
  margin-left: 16rpx;
  color: #999;
  font-size: 20rpx;
}

/* 记录列表 */
.records-section {
  margin: 30rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  padding: 0 20rpx;
}

.empty-state {
  background: white;
  border-radius: 20rpx;
  padding: 80rpx 40rpx;
  text-align: center;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.empty-text {
  display: block;
  font-size: 32rpx;
  color: #999;
  margin-bottom: 16rpx;
}

.empty-hint {
  font-size: 24rpx;
  color: #ccc;
}

.record-list {
  background: white;
  border-radius: 20rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.record-item {
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
  position: relative;
}

.record-item:last-child {
  border-bottom: none;
}

.record-item.positive {
  border-left: 8rpx solid #34C759;
}

.record-item.negative {
  border-left: 8rpx solid #FF3B30;
}

.record-item.academic {
  border-left: 8rpx solid #007AFF;
}

.record-item.social {
  border-left: 8rpx solid #FF9500;
}

.record-item.creative {
  border-left: 8rpx solid #AF52DE;
}

.record-header {
  display: flex;
  align-items: center;
}

.record-type-icon {
  margin-right: 24rpx;
  font-size: 40rpx;
}

.record-content {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.record-action-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 8rpx;
}

.record-action {
  font-size: 30rpx;
  color: #333;
  font-weight: 500;
  flex: 1;
}

.image-indicator {
  display: flex;
  align-items: center;
  background: #E8F4FD;
  border-radius: 12rpx;
  padding: 4rpx 8rpx;
  margin-left: 12rpx;
}

.image-icon {
  font-size: 20rpx;
  margin-right: 4rpx;
}

.image-count {
  font-size: 20rpx;
  color: #5470C6;
  font-weight: 500;
}

.record-time {
  font-size: 24rpx;
  color: #999;
}

.record-description {
  margin-top: 16rpx;
  padding-left: 64rpx;
  font-size: 26rpx;
  color: #666;
  line-height: 1.5;
}

/* 底部操作按钮 */
.bottom-actions {
  margin: 40rpx;
  margin-top: 60rpx;
  display: flex;
  gap: 20rpx;
  padding-bottom: env(safe-area-inset-bottom);
}

.action-button {
  flex: 1;
  height: 88rpx;
  border-radius: 44rpx;
  font-size: 32rpx;
  font-weight: 500;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.action-button.primary {
  background: linear-gradient(135deg, #5470C6 0%, #73C0DE 100%);
  color: white;
  box-shadow: 0 8rpx 24rpx rgba(84, 112, 198, 0.3);
}

.action-button.primary:active {
  transform: translateY(2rpx);
  box-shadow: 0 4rpx 12rpx rgba(84, 112, 198, 0.3);
}

.action-button.secondary {
  background: white;
  color: #5470C6;
  border: 2rpx solid #5470C6;
  box-shadow: 0 4rpx 16rpx rgba(84, 112, 198, 0.1);
}

.action-button.secondary:active {
  transform: translateY(2rpx);
  background: rgba(84, 112, 198, 0.05);
  box-shadow: 0 2rpx 8rpx rgba(84, 112, 198, 0.1);
}

/* 操作菜单 */
.action-sheet-mask {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: flex-end;
  z-index: 999;
}

.action-sheet {
  width: 100%;
  background: white;
  border-radius: 20rpx 20rpx 0 0;
  padding-bottom: env(safe-area-inset-bottom);
}

.action-sheet-header {
  padding: 40rpx;
  text-align: center;
  border-bottom: 1rpx solid #f0f0f0;
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.action-options {
  padding: 0;
}

.action-option {
  padding: 40rpx;
  text-align: center;
  border-bottom: 1rpx solid #f0f0f0;
  font-size: 30rpx;
  color: #007AFF;
}

.action-option:last-child {
  border-bottom: none;
}

.action-cancel {
  padding: 40rpx;
  text-align: center;
  border-top: 20rpx solid #f5f5f5;
  font-size: 30rpx;
  color: #FF3B30;
}