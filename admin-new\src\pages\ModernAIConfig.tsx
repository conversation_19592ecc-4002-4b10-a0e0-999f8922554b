import React, { useState } from 'react'
import { 
  Card, 
  Form, 
  Input, 
  Select, 
  Button, 
  Space, 
  Typography, 
  Tabs, 
  InputNumber,
  Switch,
  message,
  Table,
  Tag,
  Descriptions
} from 'antd'
import { PlusOutlined, EditOutlined, DeleteOutlined, PlayCircleOutlined, EyeOutlined } from '@ant-design/icons'
import InlineDrawer from '../components/InlineDrawer'

const { Title, Text, Paragraph } = Typography
const { TextArea } = Input
const { TabPane } = Tabs

const ModernAIConfig: React.FC = () => {
  const [form] = Form.useForm()
  const [loading, setLoading] = useState(false)
  const [currentAI, setCurrentAI] = useState<any>(null)
  const [currentTemplate, setCurrentTemplate] = useState<any>(null)
  const [drawerMode, setDrawerMode] = useState<'model' | 'template' | 'test'>('model')

  const aiModels = [
    {
      id: '1',
      name: '豆包模型',
      provider: 'bytedance',
      model: 'doubao-pro-32k',
      status: 'active',
      apiKey: 'bce-v3-xxx',
      usage: 1234,
      cost: 45.67,
      lastUsed: '2024-01-15 14:30',
      responseTime: '1.2s',
      quality: '优秀'
    },
    {
      id: '2', 
      name: 'GPT-4',
      provider: 'openai',
      model: 'gpt-4-turbo',
      status: 'inactive',
      apiKey: 'sk-xxx',
      usage: 0,
      cost: 0,
      lastUsed: '-',
      responseTime: '-',
      quality: '待测试'
    }
  ]

  const promptTemplates = [
    {
      id: '1',
      name: '积极鼓励型评语',
      type: 'positive',
      preview: '你在课堂上总是积极举手发言，数学解题思路越来越清晰...',
      usage: 1256,
      parameters: ['学生基本信息', '具体表现', '家长期望'],
      example: '你在课堂上总是积极举手发言，数学解题思路越来越清晰，老师为你的进步感到骄傲！继续保持这份好奇心和努力，相信你会在语文阅读上也能取得同样的进步，加油！'
    },
    {
      id: '2',
      name: '客观建议型评语',
      type: 'objective',
      preview: '本学期你在数学方面表现稳定，课堂练习正确率达到85%...',
      usage: 894,
      parameters: ['学生成绩数据', '课堂表现记录', '作业完成情况'],
      example: '本学期你在数学方面表现稳定，课堂练习正确率达到85%，特别是在几何图形认知方面有明显优势。建议在语文阅读理解方面加强练习，可以尝试每天阅读20分钟课外书，并做好读书笔记。'
    }
  ]

  const onFinish = async (values: any) => {
    setLoading(true)
    try {
      console.log('AI配置:', values)
      message.success('AI配置保存成功！')
    } catch (error) {
      message.error('保存失败，请重试')
    } finally {
      setLoading(false)
    }
  }

  const modelColumns = [
    {
      title: '模型状态',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => (
        <Tag color={status === 'active' ? 'success' : 'default'}>
          {status === 'active' ? '活跃' : '未启用'}
        </Tag>
      )
    },
    {
      title: '模型名称',
      dataIndex: 'name',
      key: 'name',
      render: (name: string, record: any) => (
        <Space direction="vertical" size={0}>
          <Text strong>{name}</Text>
          <Text type="secondary" style={{ fontSize: 12 }}>{record.model}</Text>
        </Space>
      )
    },
    {
      title: '使用情况',
      key: 'usage',
      render: (_, record: any) => (
        <Space direction="vertical" size={0}>
          <Text>使用：{record.usage}次</Text>
          <Text type="secondary" style={{ fontSize: 12 }}>费用：¥{record.cost.toFixed(2)}</Text>
        </Space>
      )
    },
    {
      title: '性能',
      key: 'performance',
      render: (_, record: any) => (
        <Space direction="vertical" size={0}>
          <Text>{record.responseTime}</Text>
          <Tag color="green" style={{ fontSize: 10, padding: '0 4px' }}>{record.quality}</Tag>
        </Space>
      )
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => (
        <Space>
          <Button 
            type="primary" 
            size="small" 
            icon={<EditOutlined />}
            onClick={() => {
              setCurrentAI(record)
              setDrawerMode('model')
            }}
          >
            配置
          </Button>
          <Button 
            size="small" 
            icon={<PlayCircleOutlined />}
            onClick={() => {
              setCurrentAI(record)
              setDrawerMode('test')
            }}
          >
            测试
          </Button>
        </Space>
      )
    }
  ]

  return (
    <div className="modern-ai-config">
      <Space direction="vertical" size="large" style={{ width: '100%' }}>
        <div className="config-header">
          <Title level={2} style={{ marginBottom: 8 }}>
            AI智能配置中心 ⚡
          </Title>
          <Text type="secondary">
            管理AI模型、提示词模板和系统参数，优化评语生成效果
          </Text>
        </div>

        <Tabs defaultActiveKey="models" className="config-tabs">
          <TabPane tab="🤖 AI模型配置" key="models">
            <Card className="config-card">
              <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 16 }}>
                <div>
                  <Text strong>AI模型管理</Text>
                  <Text type="secondary" style={{ marginLeft: 8, fontSize: 12 }}>
                    当前支持 {aiModels.length} 个模型
                  </Text>
                </div>
                <Button 
                  type="primary" 
                  icon={<PlusOutlined />}
                  onClick={() => {
                    setCurrentAI(null)
                    setDrawerMode('model')
                  }}
                >
                  新增模型
                </Button>
              </div>
              
              <Table 
                columns={modelColumns} 
                dataSource={aiModels}
                rowKey="id"
                pagination={false}
                className="modern-table"
              />
            </Card>
          </TabPane>

          <TabPane tab="🎯 提示词模板" key="templates">
            <Card className="config-card">
              <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 16 }}>
                <div>
                  <Text strong>提示词模板库</Text>
                  <Text type="secondary" style={{ marginLeft: 8, fontSize: 12 }}>
                    {promptTemplates.length} 个标准模板
                  </Text>
                </div>
                <Button 
                  type="primary" 
                  icon={<PlusOutlined />}
                  onClick={() => {
                    setCurrentTemplate(null)
                    setDrawerMode('template')
                  }}
                >
                  创建模板
                </Button>
              </div>

              <Table 
                columns={[
                  {
                    title: '模板信息',
                    key: 'info',
                    render: (_, record) => (
                      <Space direction="vertical" size={0}>
                        <Text strong>{record.name}</Text>
                        <Text type="secondary" style={{ fontSize: 12 }}>{record.preview}</Text>
                      </Space>
                    )
                  },
                  {
                    title: '使用情况',
                    dataIndex: 'usage',
                    key: 'usage',
                    render: (usage: number) => (
                      <Space>
                        <Text>{usage}次使用</Text>
                        <Tag color="blue">活跃</Tag>
                      </Space>
                    )
                  },
                  {
                    title: '参数',
                    key: 'params',
                    render: (_, record) => (
                      <div>
                        {record.parameters.map((p: string) => (
                          <Tag key={p} size="small" style={{ margin: 2 }}>{p}</Tag>
                        ))}
                      </div>
                    )
                  },
                  {
                    title: '操作',
                    key: 'action',
                    render: (_, record) => (
                      <Space>
                        <Button 
                          type="primary" 
                          size="small" 
                          icon={<EyeOutlined />}
                          onClick={() => {
                            setCurrentTemplate(record)
                            setDrawerMode('template')
                          }}
                        >
                          查看
                        </Button>
                      </Space>
                    )
                  }
                ]} 
                dataSource={promptTemplates}
                rowKey="id"
                pagination={false}
              />
            </Card>
          </TabPane>

          <TabPane tab="⚙️ 系统参数" key="settings">
            <Card className="config-card" title="AI生成参数调优">
              <Form
                form={form}
                layout="vertical"
                onFinish={onFinish}
                initialValues={{
                  temperature: 0.7,
                  maxTokens: 2000,
                  topP: 0.9,
                  frequencyPenalty: 0,
                  presencePenalty: 0
                }}
              >
                <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))', gap: 24 }}>
                  <Form.Item
                    label="温度参数"
                    name="temperature"
                    tooltip="值越高，生成结果越随机。评语建议0.6-0.8"
                    extra="推荐值: 0.7"
                  >
                    <InputNumber min={0} max={2} step={0.1} style={{ width: '100%' }} suffix="随机度" />
                  </Form.Item>

                  <Form.Item
                    label="最大长度"
                    name="maxTokens"
                    tooltip="控制生成评语的长度"
                    extra="推荐: 120-150字"
                  >
                    <InputNumber min={100} max={400} style={{ width: '100%' }} suffix="字符" />
                  </Form.Item>

                  <Form.Item
                    label="多样性控制"
                    name="topP"
                    tooltip="控制词汇使用广度，0.9适合教育场景"
                  >
                    <InputNumber min={0} max={1} step={0.1} style={{ width: '100%' }} />
                  </Form.Item>

                  <Form.Item
                    label="重复度控制"
                    name="frequencyPenalty"
                    tooltip="降低重复短语出现的概率"
                  >
                    <InputNumber min={-2} max={2} step={0.1} style={{ width: '100%' }} />
                  </Form.Item>
                </div>

                <Form.Item style={{ marginTop: 32, textAlign: 'right' }}>
                  <Space>
                    <Button onClick={() => form.resetFields()}>
                      重置
                    </Button>
                    <Button type="primary" htmlType="submit" loading={loading}>
                      保存配置
                    </Button>
                  </Space>
                </Form.Item>
              </Form>
            </Card>
          </TabPane>
        </Tabs>
      </Space>

      {/* 抽屉式配置面板 */}
      <InlineDrawer
        visible={currentAI !== null || currentTemplate !== null || drawerMode === 'test'}
        onClose={() => {
          setCurrentAI(null)
          setCurrentTemplate(null)
        }}
        title={drawerMode === 'model' ? 'AI模型配置' : 
               drawerMode === 'template' ? '提示词模板' : 
               'AI测试中心'}
        position="right"
        width="500px"
      >
        {drawerMode === 'test' && (
          <div>
            <Title level={4} style={{ marginBottom: 16 }}>模型性能测试</Title>
            <Card title="测试评语生成" size="small">
              <Form layout="vertical">
                <Form.Item label="测试学生信息">
                  <TextArea 
                    rows={3}
                    placeholder="例如：小明，3年级，数学优秀，语文需要加强..."
                    defaultValue="小王，4年级学生，数学成绩稳定在90分以上，阅读理解能力有待提升，家长希望加强写作训练。"
                  />
                </Form.Item>
                <Form.Item label="期望评语风格">
                  <Select defaultValue="positive">
                    <Select.Option value="positive">积极鼓励型</Select.Option>
                    <Select.Option value="objective">客观建议型</Select.Option>
                  </Select>
                </Form.Item>
                <Form.Item>
                  <Button type="primary" block icon={<PlayCircleOutlined />}>
                    开始测试
                  </Button>
                </Form.Item>
              </Form>
            </Card>
          </div>
        )}
      </InlineDrawer>

      <style>{`
        .modern-ai-config {
          background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
          min-height: 100vh;
          padding: 24px;
        }

        .config-header {
          padding: 24px 0;
          text-align: center;
          border-radius: 12px;
          background: white;
          margin-bottom: 24px;
          box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .config-card {
          border-radius: 12px;
          box-shadow: 0 4px 12px rgba(0,0,0,0.1);
          margin-bottom: 24px;
        }

        .config-tabs .ant-tabs-nav {
          background: white;
          border-radius: 12px 12px 0 0;
          margin: 0;
          padding: 8px;
        }

        .modern-table .ant-table-thead > tr > th {
          background: #fafafa;
          font-weight: 600;
        }

        @media (max-width: 768px) {
          .modern-ai-config {
            padding: 12px;
          }
        }
      `}</style>
    </div>
  )
}

export default ModernAIConfig