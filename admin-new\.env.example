# 应用配置
VITE_APP_TITLE=评语灵感君管理后台
VITE_APP_VERSION=2.0.0
VITE_APP_DESCRIPTION=现代化AI评语生成系统管理平台

# API配置
VITE_API_BASE_URL=https://your-env-id.ap-beijing.app.tcloudbase.com/adminAPI
VITE_UPLOAD_BASE_URL=https://your-env-id.tcb.qcloud.la

# 微信云开发配置
VITE_WECHAT_CLOUD_ENV_ID=your-env-id
VITE_WECHAT_CLOUD_REGION=ap-beijing

# 功能开关
VITE_ENABLE_MOCK=false
VITE_ENABLE_DEVTOOLS=true
VITE_ENABLE_PWA=true
VITE_ENABLE_ERROR_REPORTING=true

# 监控配置
VITE_SENTRY_DSN=https://your-sentry-dsn
VITE_ANALYTICS_ID=your-analytics-id

# 第三方服务配置
VITE_AI_PROVIDERS=doubao,openai,wenxin,tongyi
VITE_MAX_UPLOAD_SIZE=10485760
VITE_SUPPORTED_FILE_TYPES=.jpg,.jpeg,.png,.pdf,.xlsx,.csv

# 缓存配置
VITE_CACHE_TTL=300000
VITE_REQUEST_TIMEOUT=30000
VITE_MAX_RETRIES=3

# 安全配置
VITE_ENABLE_CSRF=true
VITE_SESSION_TIMEOUT=3600000
VITE_TOKEN_REFRESH_THRESHOLD=300000

# 性能配置
VITE_ENABLE_COMPRESSION=true
VITE_BUNDLE_ANALYZER=false
VITE_SOURCE_MAP=false

# 管理员认证配置
VITE_ADMIN_USERNAME=admin
VITE_ADMIN_PASSWORD_HASH=your_secure_password_hash_here

# JWT配置
VITE_JWT_SECRET_KEY=your_jwt_secret_key_32_chars_min_length
VITE_JWT_EXPIRES_IN=24h
VITE_TOKEN_REFRESH_THRESHOLD=300000

# 加密配置
VITE_ENCRYPTION_KEY=your_32_character_encryption_key_here

# 云函数URL配置
VITE_AUTH_API_URL=https://your-cloud-function-url/auth
VITE_USERS_API_URL=https://your-cloud-function-url/users

# 开发配置
VITE_DEV_SERVER_PORT=3000
VITE_DEV_SERVER_HOST=localhost
VITE_HMR_PORT=3001