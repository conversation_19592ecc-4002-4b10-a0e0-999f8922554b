import React from 'react'

const SimpleApp: React.FC = () => {
  return (
    <div style={{ 
      padding: '20px', 
      background: '#f0f0f0', 
      minHeight: '100vh',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center'
    }}>
      <div style={{
        background: 'white',
        padding: '40px',
        borderRadius: '8px',
        boxShadow: '0 4px 12px rgba(0,0,0,0.1)',
        textAlign: 'center'
      }}>
        <h1 style={{ color: '#1890ff', marginBottom: '20px' }}>
          🎉 管理后台启动成功！
        </h1>
        <p style={{ color: '#666', fontSize: '16px' }}>
          如果你能看到这个页面，说明基础配置没问题
        </p>
        <button 
          style={{
            background: '#1890ff',
            color: 'white',
            border: 'none',
            padding: '10px 20px',
            borderRadius: '4px',
            cursor: 'pointer',
            marginTop: '20px'
          }}
          onClick={() => alert('按钮点击正常！')}
        >
          测试按钮
        </button>
      </div>
    </div>
  )
}

export default SimpleApp