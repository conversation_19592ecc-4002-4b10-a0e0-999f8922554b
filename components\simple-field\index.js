Component({
  properties: {
    value: {
      type: String,
      value: ''
    },
    placeholder: {
      type: String,
      value: ''
    },
    label: {
      type: String,
      value: ''
    },
    type: {
      type: String,
      value: 'text'
    },
    disabled: {
      type: Boolean,
      value: false
    },
    required: {
      type: Boolean,
      value: false
    }
  },
  
  methods: {
    onInput(e) {
      this.setData({
        value: e.detail.value
      });
      this.triggerEvent('change', {
        value: e.detail.value
      });
    },
    
    onBlur(e) {
      this.triggerEvent('blur', e.detail);
    },
    
    onFocus(e) {
      this.triggerEvent('focus', e.detail);
    }
  }
});