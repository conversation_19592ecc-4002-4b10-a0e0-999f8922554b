// 云函数入口文件
const cloud = require('wx-server-sdk')

cloud.init({
  env: process.env.ENV_ID || 'cloud1-4g85f8xlb8166ff1'
})

const db = cloud.database()

// 云函数入口函数
exports.main = async (event, context) => {
  const wxContext = cloud.getWXContext()
  
  try {
    const { userInfo } = event
    
    if (!userInfo) {
      return {
        success: false,
        error: '用户信息不能为空'
      }
    }

    // 保存用户信息到数据库
    const result = await db.collection('user_profiles').add({
      data: {
        openid: wxContext.OPENID,
        userInfo: userInfo,
        updateTime: new Date(),
        platform: 'miniprogram',
        version: '3.0.0'
      }
    })

    return {
      success: true,
      data: result,
      openid: wxContext.OPENID
    }
  } catch (error) {
    console.error('保存用户信息失败:', error)
    return {
      success: false,
      error: error.message
    }
  }
}