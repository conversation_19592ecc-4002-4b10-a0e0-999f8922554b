// 🔍 快速数据库诊断脚本
// 在管理后台页面打开浏览器控制台，粘贴此代码并按回车运行

(async function quickDiagnose() {
  console.log('🔍 开始快速诊断数据库连接...')
  
  try {
    // 直接调用云函数
    const cloudbase = await import('@cloudbase/js-sdk')
    const app = cloudbase.default.init({
      env: 'cloud1-4g85f8xlb8166ff1'
    })
    
    // 启用匿名登录
    await app.auth().signInAnonymously()
    console.log('✅ 匿名登录成功')
    
    // 调用诊断功能
    const result = await app.callFunction({
      name: 'dataQuery',
      data: {
        action: 'diagnoseDatabaseStructure'
      }
    })
    
    console.log('🔍 诊断结果:', result)
    
    if (result.result && result.result.code === 200) {
      const data = result.result.data
      
      console.log('\n' + '='.repeat(60))
      console.log('📊 数据库诊断报告')
      console.log('='.repeat(60))
      console.log(`🌍 环境: ${data.environment}`)
      console.log(`⏰ 时间: ${data.timestamp}`)
      console.log(`📁 总集合数: ${data.summary.totalCollections}`)
      console.log(`✅ 存在的集合: ${data.summary.existingCollections}`)
      console.log(`📈 有数据的集合: ${data.summary.collectionsWithData}`)
      console.log('')
      
      // 详细集合信息
      for (const [collectionName, info] of Object.entries(data.collections)) {
        if (info.exists) {
          const status = info.hasData ? '✅ 有数据' : '⚠️  无数据'
          console.log(`📦 ${collectionName}: ${status} (${info.count} 条记录)`)
          if (info.sampleFields && info.sampleFields.length > 0) {
            console.log(`   字段: ${info.sampleFields.join(', ')}`)
          }
        } else {
          console.log(`❌ ${collectionName}: 不存在 - ${info.error}`)
        }
      }
      
      console.log('\n' + '='.repeat(60))
      
      // 给出建议
      console.log('🔧 建议和解决方案:')
      console.log('')
      
      const { collections } = data
      
      if (!collections.users?.hasData) {
        console.log('⚠️  users 集合无数据 - 需要在小程序端登录创建用户')
      }
      
      if (!collections.students?.hasData) {
        console.log('⚠️  students 集合无数据 - 需要在小程序端录入学生信息')
      } else {
        console.log('✅ students 集合有数据! 学生数:', collections.students.count)
      }
      
      if (!collections.comments?.hasData) {
        console.log('⚠️  comments 集合无数据 - 需要在小程序端生成评语')
      }
      
      console.log('')
      console.log('💡 下一步：')
      console.log('1. 如果学生数据已存在，刷新管理后台页面')
      console.log('2. 如果数据显示为0，检查字段名是否匹配')
      console.log('3. 确保小程序端生成过评语和用户登录')
      
    } else {
      console.error('❌ 诊断失败:', result)
    }
    
  } catch (error) {
    console.error('❌ 诊断脚本执行失败:', error)
    
    // 提供手动检查方案
    console.log('')
    console.log('🔧 手动检查步骤：')
    console.log('1. 确认云函数部署状态')
    console.log('2. 检查控制台网络请求是否成功')
    console.log('3. 检查匿名登录权限设置')
  }
})()