/**
 * 配置推送服务
 * 负责将管理后台的配置更新推送到小程序
 */

import { RealtimeService, getRealtimeService } from './realtimeService'

export interface AIConfigUpdate {
  modelConfigs: {
    [key: string]: {
      apiKey: string
      baseUrl: string
      modelName: string
      enabled: boolean
    }
  }
  defaultModel: string
  aiParams: {
    temperature: number
    maxTokens: number
    topP: number
    frequencyPenalty: number
    presencePenalty: number
  }
  timestamp: number
}

export interface UserLimitUpdate {
  userId: string
  newLimit: number
  resetUsage?: boolean
  reason?: string
  timestamp: number
}

export interface SystemConfigUpdate {
  maintenanceMode: boolean
  allowNewUsers: boolean
  maxDailyUsage: number
  announcements: Array<{
    id: string
    title: string
    content: string
    type: 'info' | 'warning' | 'error'
    startTime: number
    endTime?: number
  }>
  timestamp: number
}

export interface FeatureToggleUpdate {
  features: {
    [key: string]: boolean
  }
  timestamp: number
}

export class ConfigPusher {
  private realtimeService: RealtimeService | null = null
  private pendingUpdates: Array<{
    type: string
    data: any
    timestamp: number
    retryCount: number
  }> = []
  private maxRetries = 3
  private retryDelay = 5000

  constructor() {
    this.initializeService()
  }

  /**
   * 初始化实时服务
   */
  private initializeService(): void {
    try {
      this.realtimeService = getRealtimeService()
    } catch (error) {
      console.warn('实时服务未初始化，配置推送将在服务可用时重试')
    }
  }

  /**
   * 推送AI配置更新
   */
  async pushAIConfig(config: AIConfigUpdate): Promise<boolean> {
    console.log('推送AI配置更新:', config)
    
    const success = await this.sendUpdate('ai_config_update', config)
    
    if (success) {
      console.log('AI配置推送成功')
      // 可以在这里添加成功后的处理逻辑
      this.notifyConfigUpdate('ai_config', config)
    } else {
      console.error('AI配置推送失败')
      this.addToPendingUpdates('ai_config_update', config)
    }
    
    return success
  }

  /**
   * 推送用户限额更新
   */
  async pushUserLimit(update: UserLimitUpdate): Promise<boolean> {
    console.log('推送用户限额更新:', update)
    
    const success = await this.sendUpdate('user_limit_update', update)
    
    if (success) {
      console.log('用户限额推送成功')
      this.notifyConfigUpdate('user_limit', update)
    } else {
      console.error('用户限额推送失败')
      this.addToPendingUpdates('user_limit_update', update)
    }
    
    return success
  }

  /**
   * 推送系统配置更新
   */
  async pushSystemConfig(config: SystemConfigUpdate): Promise<boolean> {
    console.log('推送系统配置更新:', config)
    
    const success = await this.sendUpdate('system_config_update', config)
    
    if (success) {
      console.log('系统配置推送成功')
      this.notifyConfigUpdate('system_config', config)
    } else {
      console.error('系统配置推送失败')
      this.addToPendingUpdates('system_config_update', config)
    }
    
    return success
  }

  /**
   * 推送功能开关更新
   */
  async pushFeatureToggle(update: FeatureToggleUpdate): Promise<boolean> {
    console.log('推送功能开关更新:', update)
    
    const success = await this.sendUpdate('feature_toggle_update', update)
    
    if (success) {
      console.log('功能开关推送成功')
      this.notifyConfigUpdate('feature_toggle', update)
    } else {
      console.error('功能开关推送失败')
      this.addToPendingUpdates('feature_toggle_update', update)
    }
    
    return success
  }

  /**
   * 批量推送多个配置更新
   */
  async pushBatchUpdates(updates: Array<{
    type: 'ai_config' | 'user_limit' | 'system_config' | 'feature_toggle'
    data: any
  }>): Promise<{ success: number; failed: number }> {
    console.log('批量推送配置更新:', updates)
    
    let success = 0
    let failed = 0
    
    for (const update of updates) {
      let result = false
      
      switch (update.type) {
        case 'ai_config':
          result = await this.pushAIConfig(update.data)
          break
        case 'user_limit':
          result = await this.pushUserLimit(update.data)
          break
        case 'system_config':
          result = await this.pushSystemConfig(update.data)
          break
        case 'feature_toggle':
          result = await this.pushFeatureToggle(update.data)
          break
      }
      
      if (result) {
        success++
      } else {
        failed++
      }
    }
    
    console.log(`批量推送完成: 成功 ${success}, 失败 ${failed}`)
    return { success, failed }
  }

  /**
   * 重试待处理的更新
   */
  async retryPendingUpdates(): Promise<void> {
    console.log('重试待处理的配置更新:', this.pendingUpdates.length)
    
    const updates = [...this.pendingUpdates]
    this.pendingUpdates = []
    
    for (const update of updates) {
      if (update.retryCount < this.maxRetries) {
        const success = await this.sendUpdate(update.type, update.data)
        
        if (!success) {
          update.retryCount++
          this.pendingUpdates.push(update)
        } else {
          console.log('重试成功:', update.type)
        }
      } else {
        console.error('重试次数超限，丢弃更新:', update)
      }
    }
  }

  /**
   * 获取待处理更新数量
   */
  getPendingUpdatesCount(): number {
    return this.pendingUpdates.length
  }

  /**
   * 清空待处理更新
   */
  clearPendingUpdates(): void {
    this.pendingUpdates = []
    console.log('已清空待处理的配置更新')
  }

  /**
   * 检查连接状态
   */
  isConnected(): boolean {
    return this.realtimeService?.getConnectionStatus().isConnected || false
  }

  // 私有方法

  /**
   * 发送更新到小程序
   */
  private async sendUpdate(type: string, data: any): Promise<boolean> {
    try {
      if (!this.realtimeService) {
        this.initializeService()
      }
      
      if (!this.realtimeService || !this.isConnected()) {
        console.warn('实时服务未连接，无法发送配置更新')
        return false
      }
      
      this.realtimeService.sendMessage(type as any, data)
      return true
      
    } catch (error) {
      console.error('发送配置更新失败:', error)
      return false
    }
  }

  /**
   * 添加到待处理更新队列
   */
  private addToPendingUpdates(type: string, data: any): void {
    this.pendingUpdates.push({
      type,
      data,
      timestamp: Date.now(),
      retryCount: 0
    })
    
    // 延迟重试
    setTimeout(() => {
      this.retryPendingUpdates()
    }, this.retryDelay)
  }

  /**
   * 通知配置更新完成
   */
  private notifyConfigUpdate(configType: string, data: any): void {
    // 可以在这里添加通知逻辑，比如显示成功消息
    console.log(`配置更新通知: ${configType}`, data)
    
    // 触发自定义事件
    window.dispatchEvent(new CustomEvent('config-update-success', {
      detail: { configType, data }
    }))
  }
}

// 单例实例
let configPusherInstance: ConfigPusher | null = null

/**
 * 获取配置推送器实例
 */
export const getConfigPusher = (): ConfigPusher => {
  if (!configPusherInstance) {
    configPusherInstance = new ConfigPusher()
  }
  return configPusherInstance
}

/**
 * 创建AI配置更新对象
 */
export const createAIConfigUpdate = (
  modelConfigs: AIConfigUpdate['modelConfigs'],
  defaultModel: string,
  aiParams: AIConfigUpdate['aiParams']
): AIConfigUpdate => ({
  modelConfigs,
  defaultModel,
  aiParams,
  timestamp: Date.now()
})

/**
 * 创建用户限额更新对象
 */
export const createUserLimitUpdate = (
  userId: string,
  newLimit: number,
  resetUsage?: boolean,
  reason?: string
): UserLimitUpdate => ({
  userId,
  newLimit,
  resetUsage,
  reason,
  timestamp: Date.now()
})

/**
 * 创建系统配置更新对象
 */
export const createSystemConfigUpdate = (
  config: Omit<SystemConfigUpdate, 'timestamp'>
): SystemConfigUpdate => ({
  ...config,
  timestamp: Date.now()
})

/**
 * 创建功能开关更新对象
 */
export const createFeatureToggleUpdate = (
  features: FeatureToggleUpdate['features']
): FeatureToggleUpdate => ({
  features,
  timestamp: Date.now()
})
