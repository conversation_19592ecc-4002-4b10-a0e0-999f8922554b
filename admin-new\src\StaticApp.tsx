import React, { useState } from 'react'
import { Button, Input, Form, Typography, message, ConfigProvider } from 'antd'
import zhCN from 'antd/locale/zh_CN'

const { Title } = Typography

const StaticApp: React.FC = () => {
  const [isLoggedIn, setIsLoggedIn] = useState(false)
  const [loading, setLoading] = useState(false)
  
  const handleLogin = async (values: { username: string; password: string }) => {
    setLoading(true)
    
    // 模拟登录验证
    setTimeout(() => {
      if (values.username === 'admin' && values.password === 'admin123') {
        setIsLoggedIn(true)
        message.success('登录成功！')
      } else {
        message.error('用户名或密码错误')
      }
      setLoading(false)
    }, 1000)
  }

  const handleLogout = () => {
    setIsLoggedIn(false)
    message.info('已退出登录')
  }

  if (isLoggedIn) {
    return (
      <ConfigProvider locale={zhCN}>
        <div style={{ 
          padding: '40px', 
          textAlign: 'center',
          background: '#f0f2f5',
          minHeight: '100vh'
        }}>
          <h1>🎉 登录成功！欢迎使用管理后台</h1>
          <p>这是一个无状态管理的纯静态测试版本</p>
          <Button 
            type="primary" 
            danger
            onClick={handleLogout}
            style={{ marginTop: '20px' }}
          >
            退出登录
          </Button>
        </div>
      </ConfigProvider>
    )
  }

  return (
    <ConfigProvider locale={zhCN}>
      <div style={{ 
        display: 'flex', 
        justifyContent: 'center', 
        alignItems: 'center', 
        minHeight: '100vh',
        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)'
      }}>
        <div style={{
          background: 'white',
          padding: '40px',
          borderRadius: '8px',
          boxShadow: '0 4px 20px rgba(0,0,0,0.1)',
          width: '400px'
        }}>
          <Title level={2} style={{ textAlign: 'center', marginBottom: '30px' }}>
            评语灵感君管理后台
          </Title>
          
          <Form onFinish={handleLogin} layout="vertical">
            <Form.Item
              label="用户名"
              name="username"
              rules={[{ required: true, message: '请输入用户名!' }]}
            >
              <Input placeholder="请输入用户名" size="large" />
            </Form.Item>
            
            <Form.Item
              label="密码"
              name="password"
              rules={[{ required: true, message: '请输入密码!' }]}
            >
              <Input.Password placeholder="请输入密码" size="large" />
            </Form.Item>
            
            <Form.Item>
              <Button 
                type="primary" 
                htmlType="submit" 
                loading={loading}
                size="large"
                style={{ width: '100%' }}
              >
                {loading ? '登录中...' : '登录'}
              </Button>
            </Form.Item>
          </Form>
          
          <div style={{ textAlign: 'center', marginTop: '20px', color: '#666' }}>
            <p><strong>测试账号：</strong>admin</p>
            <p><strong>测试密码：</strong>admin123</p>
          </div>
        </div>
      </div>
    </ConfigProvider>
  )
}

export default StaticApp