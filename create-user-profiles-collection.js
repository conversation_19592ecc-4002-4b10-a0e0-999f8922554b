// 创建 user_profiles 数据库集合的脚本
const cloud = require('wx-server-sdk')

cloud.init({
  env: 'cloud1-4g85f8xlb8166ff1' // 你的云环境ID
})

const db = cloud.database()

async function createUserProfilesCollection() {
  try {
    console.log('开始创建 user_profiles 集合...')
    
    // 尝试创建集合 - 如果已存在会报错，但我们可以忽略
    try {
      await db.createCollection('user_profiles')
      console.log('✅ user_profiles 集合创建成功')
    } catch (error) {
      if (error.message.includes('already exists')) {
        console.log('✅ user_profiles 集合已存在')
      } else {
        throw error
      }
    }
    
    // 创建索引以优化查询性能
    await db.collection('user_profiles').createIndex({
      keys: {
        openid: 1,
        updateTime: -1
      },
      name: 'openid_updateTime_index'
    })
    
    console.log('✅ 索引创建成功')
    console.log('🎉 user_profiles 集合配置完成！')
    
  } catch (error) {
    console.error('❌ 创建集合失败:', error)
  }
}

// 如果是直接运行此脚本
if (require.main === module) {
  createUserProfilesCollection()
}

module.exports = {
  createUserProfilesCollection
}