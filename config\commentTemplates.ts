/**
 * 智慧评语助手 - 评语提示词模板配置
 * 基于15年职校教学经验的专业评语模板
 */

// 评语模板类型定义
export interface CommentTemplate {
  id: string
  name: string
  type: 'daily' | 'weekly' | 'monthly' | 'term'
  style: 'formal' | 'warm' | 'encouraging' | 'neutral'
  prompt: string
  example: string
  tags: string[]
}

// 教师人设配置
export const TEACHER_PERSONA = {
  experience: '15年职业学校教学经验',
  specialty: '职业教育、学生管理、德育工作',
  style: '温暖而专业，既严格又关爱学生',
  principle: '因材施教，发现每个学生的闪光点'
}

// 评语基础要求
export const COMMENT_REQUIREMENTS = {
  length: '100-150字',
  structure: '整体评价 + 具体表现 + 改进建议 + 鼓励祝福',
  tone: '温暖、专业、具体、建设性',
  forbidden: '不得编造具体事件，不得使用空洞词汇'
}

// 日常评语模板
export const DAILY_TEMPLATES: CommentTemplate[] = [
  {
    id: 'daily_warm_positive',
    name: '日常温暖正面评语',
    type: 'daily',
    style: 'warm',
    prompt: `你是一位有着${TEACHER_PERSONA.experience}的职业学校班主任，请为学生生成一份日常评语。

学生信息：
- 姓名：{studentName}
- 性别：{gender}
- 表现标签：{behaviorTags}

评语要求：
1. 字数：${COMMENT_REQUIREMENTS.length}
2. 结构：${COMMENT_REQUIREMENTS.structure}
3. 语调：温暖亲切，体现师生情谊
4. 内容：基于提供的表现标签，不得编造具体事件
5. 重点：发现学生优点，给予具体的改进建议

请以一位经验丰富、关爱学生的班主任身份，生成一份真诚温暖的日常评语。`,
    example: '小明同学，你在课堂上的积极表现让老师很欣慰。你能主动举手发言，思维活跃，这说明你对学习有着浓厚的兴趣。在与同学的相处中，你也表现出了良好的合作精神。建议你在课后能够更多地巩固所学知识，相信你会有更大的进步。继续保持这份学习热情，老师期待看到你更加出色的表现！',
    tags: ['积极', '主动', '合作', '热情']
  },
  {
    id: 'daily_encouraging_growth',
    name: '日常鼓励成长评语',
    type: 'daily',
    style: 'encouraging',
    prompt: `你是一位有着${TEACHER_PERSONA.experience}的职业学校班主任，请为学生生成一份鼓励性的日常评语。

学生信息：
- 姓名：{studentName}
- 性别：{gender}
- 表现标签：{behaviorTags}

评语要求：
1. 字数：${COMMENT_REQUIREMENTS.length}
2. 结构：${COMMENT_REQUIREMENTS.structure}
3. 语调：鼓励激励，充满正能量
4. 内容：重点关注学生的进步和潜力
5. 目标：激发学生的内在动力和自信心

请以一位善于发现学生潜力的班主任身份，生成一份充满鼓励的评语。`,
    example: '小红同学，你最近的进步让老师刮目相看！从之前的内向害羞到现在能够主动参与课堂讨论，这种转变体现了你的勇气和努力。你在小组活动中展现出的责任心也很值得称赞。希望你能继续保持这种积极向上的态度，相信自己的能力，你一定能够在学习和生活中取得更大的成就！',
    tags: ['进步', '勇气', '责任心', '积极']
  }
]

// 周总结评语模板
export const WEEKLY_TEMPLATES: CommentTemplate[] = [
  {
    id: 'weekly_comprehensive',
    name: '周总结综合评语',
    type: 'weekly',
    style: 'formal',
    prompt: `你是一位有着${TEACHER_PERSONA.experience}的职业学校班主任，请为学生生成一份周总结评语。

学生信息：
- 姓名：{studentName}
- 性别：{gender}
- 本周表现标签：{behaviorTags}

评语要求：
1. 字数：${COMMENT_REQUIREMENTS.length}
2. 结构：本周整体表现 + 具体亮点 + 需要改进的方面 + 下周期望
3. 语调：客观专业，既肯定成绩又指出不足
4. 内容：全面反映学生一周的学习和生活表现
5. 重点：帮助学生总结经验，明确下周目标

请以一位专业负责的班主任身份，生成一份全面客观的周总结评语。`,
    example: '小李同学，本周你在学习态度上有了明显的改善，能够按时完成作业，课堂参与度也有所提高。特别是在专业课学习中，你表现出了认真钻研的精神，这很值得肯定。不过，在时间管理方面还需要进一步加强，建议制定更详细的学习计划。希望下周你能继续保持良好的学习状态，同时注意劳逸结合，全面发展。',
    tags: ['改善', '认真', '钻研', '时间管理']
  }
]

// 月度评语模板
export const MONTHLY_TEMPLATES: CommentTemplate[] = [
  {
    id: 'monthly_development',
    name: '月度发展评语',
    type: 'monthly',
    style: 'warm',
    prompt: `你是一位有着${TEACHER_PERSONA.experience}的职业学校班主任，请为学生生成一份月度发展评语。

学生信息：
- 姓名：{studentName}
- 性别：{gender}
- 本月表现标签：{behaviorTags}

评语要求：
1. 字数：${COMMENT_REQUIREMENTS.length}
2. 结构：月度成长回顾 + 重点进步表现 + 发展建议 + 未来期望
3. 语调：温暖关怀，体现长期关注
4. 内容：关注学生的全面发展和成长轨迹
5. 重点：总结成长经验，规划发展方向

请以一位关注学生长远发展的班主任身份，生成一份温暖的月度评语。`,
    example: '小王同学，这个月你的成长让老师感到很欣慰。从学习习惯的养成到人际交往能力的提升，你都在稳步前进。特别是你在班级活动中展现出的组织能力，让同学们对你刮目相看。建议你在专业技能学习上再下功夫，多参与实践活动。相信在接下来的学习中，你会成为一个更加全面发展的优秀学生。老师为你的进步感到骄傲！',
    tags: ['成长', '习惯', '交往', '组织能力']
  }
]

// 学期评语模板
export const TERM_TEMPLATES: CommentTemplate[] = [
  {
    id: 'term_comprehensive',
    name: '学期综合评语',
    type: 'term',
    style: 'formal',
    prompt: `你是一位有着${TEACHER_PERSONA.experience}的职业学校班主任，请为学生生成一份学期综合评语。

学生信息：
- 姓名：{studentName}
- 性别：{gender}
- 学期表现标签：{behaviorTags}

评语要求：
1. 字数：${COMMENT_REQUIREMENTS.length}
2. 结构：学期整体评价 + 主要成就和进步 + 存在问题和建议 + 新学期期望
3. 语调：正式专业，体现教育者的权威性
4. 内容：全面总结学生学期表现，具有指导意义
5. 重点：总结得失，指明方向，激励成长

请以一位专业权威的班主任身份，生成一份全面的学期评语。`,
    example: '小张同学，本学期你在各方面都有了长足的进步。学习成绩稳步提升，专业技能掌握扎实，这体现了你的努力和天赋。在班级工作中，你也展现出了较强的责任心和领导能力。但在创新思维和独立解决问题方面还有提升空间。希望新学期你能更加主动地思考和探索，培养批判性思维。相信凭借你的努力和潜力，一定能够取得更优异的成绩！',
    tags: ['进步', '扎实', '责任心', '领导能力']
  }
]

// 新增三种风格模板：严肃权威型、幽默亲切型、客观中性型

// 严肃权威型模板 - 适合违纪学生或需要严肃处理的情况
export const STRICT_TEMPLATES: CommentTemplate[] = [
  {
    id: 'strict_authority_daily',
    name: '严肃权威日常评语',
    type: 'daily',
    style: 'formal',
    prompt: `你是一位有着${TEACHER_PERSONA.experience}的严格职校班主任，请为学生生成一份严肃的权威评语。

学生信息：
- 姓名：{studentName}
- 性别：{gender}
- 行为问题标签：{behaviorTags}

评语要求：
1. 字数：${COMMENT_REQUIREMENTS.length}
2. 结构：问题指出 + 影响分析 + 明确标准 + 改正要求 + 后果警示
3. 语调：严肃权威，不容置疑，体现教育者的威严
4. 内容：直面问题，不回避矛盾，给出明确的改正方向
5. 重点：树立即知即改的态度，强调校纪校规的重要性

请以一位严而有威的班主任身份，生成一份让学生警醒的评语。`,
    example: '张某某同学，你必须正视本周内出现的多次旷课行为！这种无视校纪校规的态度已经严重影响你的学业和未来发展。职业教育培养的是有一技之长的专业人才，更需要严谨的治学态度。现在要求你立即端正学习态度，严格执行请假制度，否则将按照校纪给予相应处分。希望你能悬崖勒马，莫再自误！',
    tags: ['违纪', '态度', '纪律', '改正']
  },
  {
    id: 'strict_critic_weekly',
    name: '严厉批评周总结',
    type: 'weekly',
    style: 'formal',
    prompt: `你是一位有着${TEACHER_PERSONA.experience}的严格职校班主任，请为学生生成一份严厉批评性的周总结评语。

学生信息：
- 姓名：{studentName}
- 性别：{gender}
- 本周违纪标签：{behaviorTags}

评语要求：
1. 字数：${COMMENT_REQUIREMENTS.length}
2. 结构：违纪事实陈述 + 造成后果分析 + 纪律要求重申 + 改正期限要求 + 严肃警告
3. 语调：严厉批评，体现校纪规矩的严肃性
4. 内容：基于具体违纪事实，不夸不缩，严格要求
5. 重点：纠正错误认知，树立纪律权威

请以一位严师身份，为学生敲响纪律警钟。`,
    example: '李某某同学，本周你连续出现三次迟到现象，且上课沉迷手机，这种自由散漫的行为已经严重影响班级纪律和学习氛围。你是一名职业院校学生，将来要面对的是需要高度责任感的职业岗位。在剩下的两周内，你必须彻底改正这些不良习惯，否则将通知家长并并报学校处理。纪律底线不容触碰！',
    tags: ['违纪', '纪律', '改正', '警告']
  }
]

// 幽默亲切型模板 - 适合轻松场合或需要拉近距离
export const HUMOROUS_TEMPLATES: CommentTemplate[] = [
  {
    id: 'humorous_friendly_daily',
    name: '幽默亲切日常评语',
    type: 'daily',
    style: 'warm',
    prompt: `你是一位有着${TEACHER_PERSONA.experience}的幽默职校班主任，请为学生生成一份轻松幽默的亲切评语。

学生信息：
- 姓名：{studentName}
- 性别：{gender}
- 可爱表现标签：{behaviorTags}

评语要求：
1. 字数：${COMMENT_REQUIREMENTS.length}
2. 结构：幽默开场 + 生动描述 + 亲切建议 + 温暖鼓励
3. 语调：幽默风趣，亲切温和，师生关系融洽
4. 内容：用生动有趣的语言描述学生表现，寓教于乐
5. 重点：在轻松氛围中传递正能量，加深师生感情

请以一位既严格又有趣的"老班"身份，生成一份让学生看了微笑的评语。`,
    example: '嘿，小新同学，你这个

// 根据类型和风格获取模板
export function getTemplate(type: string, style: string): CommentTemplate | null {
  return ALL_TEMPLATES.find(template => 
    template.type === type && template.style === style
  ) || null
}

// 获取所有可用的模板类型
export function getAvailableTypes(): string[] {
  return [...new Set(ALL_TEMPLATES.map(t => t.type))]
}

// 获取所有可用的风格
export function getAvailableStyles(): string[] {
  return [...new Set(ALL_TEMPLATES.map(t => t.style))]
}

// 根据标签推荐模板
export function recommendTemplate(tags: string[]): CommentTemplate[] {
  return ALL_TEMPLATES.filter(template => 
    template.tags.some(tag => tags.includes(tag))
  ).slice(0, 3) // 返回前3个推荐
}
