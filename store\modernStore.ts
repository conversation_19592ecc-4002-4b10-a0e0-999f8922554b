/**
 * 现代化状态管理系统
 * 2025年企业级标准，支持TypeScript、持久化、中间件
 */

import { monitoring } from '../utils/monitoring'

// 状态类型定义
interface AppState {
  // 用户状态
  user: {
    info: UserInfo | null
    isLogin: boolean
    token: string | null
    lastLoginTime: number | null
  }
  
  // 应用状态
  app: {
    initialized: boolean
    loading: boolean
    error: string | null
    networkStatus: 'online' | 'offline'
    theme: 'light' | 'dark'
  }
  
  // 数据状态
  data: {
    students: Student[]
    classes: ClassInfo[]
    comments: Comment[]
    records: BehaviorRecord[]
    lastSyncTime: number | null
  }
  
  // AI状态
  ai: {
    config: AIConfig | null
    generating: boolean
    lastGenerateTime: number | null
    cacheStats: {
      count: number
      totalSize: number
    }
  }
  
  // UI状态
  ui: {
    currentPage: string
    tabBarIndex: number
    modalVisible: boolean
    toastMessage: string | null
  }
}

// Action类型定义
type Action = 
  | { type: 'SET_USER_INFO'; payload: UserInfo | null }
  | { type: 'SET_LOGIN_STATUS'; payload: boolean }
  | { type: 'SET_TOKEN'; payload: string | null }
  | { type: 'SET_LOADING'; payload: boolean }
  | { type: 'SET_ERROR'; payload: string | null }
  | { type: 'SET_NETWORK_STATUS'; payload: 'online' | 'offline' }
  | { type: 'SET_STUDENTS'; payload: Student[] }
  | { type: 'ADD_STUDENT'; payload: Student }
  | { type: 'UPDATE_STUDENT'; payload: { id: string; data: Partial<Student> } }
  | { type: 'REMOVE_STUDENT'; payload: string }
  | { type: 'SET_CLASSES'; payload: ClassInfo[] }
  | { type: 'ADD_CLASS'; payload: ClassInfo }
  | { type: 'UPDATE_CLASS'; payload: { id: string; data: Partial<ClassInfo> } }
  | { type: 'REMOVE_CLASS'; payload: string }
  | { type: 'SET_COMMENTS'; payload: Comment[] }
  | { type: 'ADD_COMMENT'; payload: Comment }
  | { type: 'UPDATE_COMMENT'; payload: { id: string; data: Partial<Comment> } }
  | { type: 'REMOVE_COMMENT'; payload: string }
  | { type: 'SET_RECORDS'; payload: BehaviorRecord[] }
  | { type: 'ADD_RECORD'; payload: BehaviorRecord }
  | { type: 'UPDATE_RECORD'; payload: { id: string; data: Partial<BehaviorRecord> } }
  | { type: 'REMOVE_RECORD'; payload: string }
  | { type: 'SET_AI_CONFIG'; payload: AIConfig | null }
  | { type: 'SET_AI_GENERATING'; payload: boolean }
  | { type: 'SET_AI_CACHE_STATS'; payload: { count: number; totalSize: number } }
  | { type: 'SET_CURRENT_PAGE'; payload: string }
  | { type: 'SET_TAB_BAR_INDEX'; payload: number }
  | { type: 'SET_MODAL_VISIBLE'; payload: boolean }
  | { type: 'SET_TOAST_MESSAGE'; payload: string | null }
  | { type: 'RESET_STATE' }

// 中间件类型
type Middleware = (store: ModernStore) => (next: (action: Action) => void) => (action: Action) => void

// 订阅者类型
type Subscriber = (state: AppState, action: Action) => void

class ModernStore {
  private state: AppState
  private subscribers: Set<Subscriber> = new Set()
  private middlewares: Middleware[] = []
  private persistKeys: (keyof AppState)[] = ['user', 'data', 'ai']

  constructor() {
    this.state = this.getInitialState()
    this.loadPersistedState()
    this.setupMiddlewares()
    
    console.log('🏪 Modern Store initialized')
  }

  /**
   * 获取初始状态
   */
  private getInitialState(): AppState {
    return {
      user: {
        info: null,
        isLogin: false,
        token: null,
        lastLoginTime: null
      },
      app: {
        initialized: false,
        loading: false,
        error: null,
        networkStatus: 'online',
        theme: 'light'
      },
      data: {
        students: [],
        classes: [],
        comments: [],
        records: [],
        lastSyncTime: null
      },
      ai: {
        config: null,
        generating: false,
        lastGenerateTime: null,
        cacheStats: {
          count: 0,
          totalSize: 0
        }
      },
      ui: {
        currentPage: 'index',
        tabBarIndex: 0,
        modalVisible: false,
        toastMessage: null
      }
    }
  }

  /**
   * 设置中间件
   */
  private setupMiddlewares(): void {
    // 日志中间件
    this.use(this.createLoggerMiddleware())
    
    // 持久化中间件
    this.use(this.createPersistMiddleware())
    
    // 监控中间件
    this.use(this.createMonitoringMiddleware())
  }

  /**
   * 添加中间件
   */
  use(middleware: Middleware): void {
    this.middlewares.push(middleware)
  }

  /**
   * 获取状态
   */
  getState(): AppState {
    return { ...this.state }
  }

  /**
   * 分发Action
   */
  dispatch(action: Action): void {
    let next = (action: Action) => {
      this.state = this.reducer(this.state, action)
      this.notifySubscribers(action)
    }

    // 应用中间件
    for (let i = this.middlewares.length - 1; i >= 0; i--) {
      next = this.middlewares[i](this)(next)
    }

    next(action)
  }

  /**
   * 状态减速器
   */
  private reducer(state: AppState, action: Action): AppState {
    switch (action.type) {
      case 'SET_USER_INFO':
        return {
          ...state,
          user: {
            ...state.user,
            info: action.payload
          }
        }

      case 'SET_LOGIN_STATUS':
        return {
          ...state,
          user: {
            ...state.user,
            isLogin: action.payload,
            lastLoginTime: action.payload ? Date.now() : null
          }
        }

      case 'SET_TOKEN':
        return {
          ...state,
          user: {
            ...state.user,
            token: action.payload
          }
        }

      case 'SET_LOADING':
        return {
          ...state,
          app: {
            ...state.app,
            loading: action.payload
          }
        }

      case 'SET_ERROR':
        return {
          ...state,
          app: {
            ...state.app,
            error: action.payload
          }
        }

      case 'SET_NETWORK_STATUS':
        return {
          ...state,
          app: {
            ...state.app,
            networkStatus: action.payload
          }
        }

      case 'SET_STUDENTS':
        return {
          ...state,
          data: {
            ...state.data,
            students: action.payload,
            lastSyncTime: Date.now()
          }
        }

      case 'ADD_STUDENT':
        return {
          ...state,
          data: {
            ...state.data,
            students: [...state.data.students, action.payload]
          }
        }

      case 'UPDATE_STUDENT':
        return {
          ...state,
          data: {
            ...state.data,
            students: state.data.students.map(student =>
              student._id === action.payload.id
                ? { ...student, ...action.payload.data }
                : student
            )
          }
        }

      case 'REMOVE_STUDENT':
        return {
          ...state,
          data: {
            ...state.data,
            students: state.data.students.filter(student => student._id !== action.payload)
          }
        }

      case 'SET_CLASSES':
        return {
          ...state,
          data: {
            ...state.data,
            classes: action.payload,
            lastSyncTime: Date.now()
          }
        }

      case 'ADD_CLASS':
        return {
          ...state,
          data: {
            ...state.data,
            classes: [...state.data.classes, action.payload]
          }
        }

      case 'UPDATE_CLASS':
        return {
          ...state,
          data: {
            ...state.data,
            classes: state.data.classes.map(cls =>
              cls._id === action.payload.id
                ? { ...cls, ...action.payload.data }
                : cls
            )
          }
        }

      case 'REMOVE_CLASS':
        return {
          ...state,
          data: {
            ...state.data,
            classes: state.data.classes.filter(cls => cls._id !== action.payload)
          }
        }

      case 'SET_COMMENTS':
        return {
          ...state,
          data: {
            ...state.data,
            comments: action.payload,
            lastSyncTime: Date.now()
          }
        }

      case 'ADD_COMMENT':
        return {
          ...state,
          data: {
            ...state.data,
            comments: [...state.data.comments, action.payload]
          }
        }

      case 'UPDATE_COMMENT':
        return {
          ...state,
          data: {
            ...state.data,
            comments: state.data.comments.map(comment =>
              comment._id === action.payload.id
                ? { ...comment, ...action.payload.data }
                : comment
            )
          }
        }

      case 'REMOVE_COMMENT':
        return {
          ...state,
          data: {
            ...state.data,
            comments: state.data.comments.filter(comment => comment._id !== action.payload)
          }
        }

      case 'SET_RECORDS':
        return {
          ...state,
          data: {
            ...state.data,
            records: action.payload,
            lastSyncTime: Date.now()
          }
        }

      case 'ADD_RECORD':
        return {
          ...state,
          data: {
            ...state.data,
            records: [...state.data.records, action.payload]
          }
        }

      case 'UPDATE_RECORD':
        return {
          ...state,
          data: {
            ...state.data,
            records: state.data.records.map(record =>
              record._id === action.payload.id
                ? { ...record, ...action.payload.data }
                : record
            )
          }
        }

      case 'REMOVE_RECORD':
        return {
          ...state,
          data: {
            ...state.data,
            records: state.data.records.filter(record => record._id !== action.payload)
          }
        }

      case 'SET_AI_CONFIG':
        return {
          ...state,
          ai: {
            ...state.ai,
            config: action.payload
          }
        }

      case 'SET_AI_GENERATING':
        return {
          ...state,
          ai: {
            ...state.ai,
            generating: action.payload,
            lastGenerateTime: action.payload ? Date.now() : state.ai.lastGenerateTime
          }
        }

      case 'SET_AI_CACHE_STATS':
        return {
          ...state,
          ai: {
            ...state.ai,
            cacheStats: action.payload
          }
        }

      case 'SET_CURRENT_PAGE':
        return {
          ...state,
          ui: {
            ...state.ui,
            currentPage: action.payload
          }
        }

      case 'SET_TAB_BAR_INDEX':
        return {
          ...state,
          ui: {
            ...state.ui,
            tabBarIndex: action.payload
          }
        }

      case 'SET_MODAL_VISIBLE':
        return {
          ...state,
          ui: {
            ...state.ui,
            modalVisible: action.payload
          }
        }

      case 'SET_TOAST_MESSAGE':
        return {
          ...state,
          ui: {
            ...state.ui,
            toastMessage: action.payload
          }
        }

      case 'RESET_STATE':
        return this.getInitialState()

      default:
        return state
    }
  }

  /**
   * 订阅状态变化
   */
  subscribe(subscriber: Subscriber): () => void {
    this.subscribers.add(subscriber)
    
    return () => {
      this.subscribers.delete(subscriber)
    }
  }

  /**
   * 通知订阅者
   */
  private notifySubscribers(action: Action): void {
    this.subscribers.forEach(subscriber => {
      try {
        subscriber(this.state, action)
      } catch (error) {
        console.error('订阅者执行失败:', error)
      }
    })
  }

  /**
   * 加载持久化状态
   */
  private loadPersistedState(): void {
    try {
      this.persistKeys.forEach(key => {
        const stored = wx.getStorageSync(`store_${key}`)
        if (stored) {
          ;(this.state as any)[key] = { ...this.state[key], ...stored }
        }
      })
    } catch (error) {
      console.warn('加载持久化状态失败:', error)
    }
  }

  /**
   * 保存持久化状态
   */
  private savePersistedState(): void {
    try {
      this.persistKeys.forEach(key => {
        wx.setStorageSync(`store_${key}`, this.state[key])
      })
    } catch (error) {
      console.warn('保存持久化状态失败:', error)
    }
  }

  /**
   * 创建日志中间件
   */
  private createLoggerMiddleware(): Middleware {
    return (store) => (next) => (action) => {
      if (process.env.NODE_ENV === 'development') {
        console.group(`🔄 Action: ${action.type}`)
        console.log('Previous State:', store.getState())
        console.log('Action:', action)
      }
      
      next(action)
      
      if (process.env.NODE_ENV === 'development') {
        console.log('Next State:', store.getState())
        console.groupEnd()
      }
    }
  }

  /**
   * 创建持久化中间件
   */
  private createPersistMiddleware(): Middleware {
    return (store) => (next) => (action) => {
      next(action)
      
      // 异步保存状态
      setTimeout(() => {
        store.savePersistedState()
      }, 100)
    }
  }

  /**
   * 创建监控中间件
   */
  private createMonitoringMiddleware(): Middleware {
    return (store) => (next) => (action) => {
      const startTime = Date.now()
      
      next(action)
      
      const duration = Date.now() - startTime
      
      // 记录状态变更
      monitoring.trackEvent('store_action', {
        action: action.type,
        duration,
        stateSize: JSON.stringify(store.getState()).length
      })
      
      // 记录性能指标
      if (duration > 10) {
        monitoring.trackPerformance({
          type: 'user_action',
          name: `store_${action.type}`,
          duration,
          success: true
        })
      }
    }
  }

  /**
   * 清理状态
   */
  clear(): void {
    this.dispatch({ type: 'RESET_STATE' })
    
    // 清理持久化数据
    try {
      this.persistKeys.forEach(key => {
        wx.removeStorageSync(`store_${key}`)
      })
    } catch (error) {
      console.warn('清理持久化数据失败:', error)
    }
  }

  /**
   * 获取状态统计
   */
  getStats(): Record<string, any> {
    const state = this.getState()
    return {
      userLoggedIn: state.user.isLogin,
      dataLoaded: state.data.lastSyncTime !== null,
      studentsCount: state.data.students.length,
      classesCount: state.data.classes.length,
      commentsCount: state.data.comments.length,
      recordsCount: state.data.records.length,
      aiCacheCount: state.ai.cacheStats.count,
      subscribersCount: this.subscribers.size,
      middlewaresCount: this.middlewares.length
    }
  }
}

// 创建全局store实例
const modernStore = new ModernStore()

export { ModernStore, modernStore }
export type { AppState, Action, Middleware, Subscriber }
