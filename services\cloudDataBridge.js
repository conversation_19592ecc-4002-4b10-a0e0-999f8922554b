/**
 * 云数据桥接器 - 管理后台与小程序数据互通
 * 支持实时同步、离线缓存、冲突解决
 */

const { config, DataTransformer, ConnectionMonitor } = require('../cloud-connection-config');

class CloudDataBridge {
  constructor() {
    this.cache = new Map();
    this.syncQueue = [];
    this.subscribers = new Map();
    this.connectionManager = ConnectionMonitor;
    this.isOnline = true;
    
    this.init();
  }

  init() {
    console.log('🚀 初始化云数据桥接器');
    
    // 监听网络状态
    this.setupNetworkMonitoring();
    
    // 启动实时同步
    this.startRealTimeSync();
    
    // 处理离线队列
    this.processSyncQueue();
    
    // 定期健康检查
    setInterval(() => {
      this.connectionManager.ping();
    }, 30000);
  }

  /**
   * 网络状态监控
   */
  setupNetworkMonitoring() {
    wx.onNetworkStatusChange((res) => {
      this.isOnline = res.isConnected;
      
      if (this.isOnline) {
        console.log('📶 网络恢复，开始同步离线数据');
        this.processSyncQueue();
      } else {
        console.log('📱 网络断开，启用离线模式');
      }
    });
  }

  /**
   * 启动实时同步
   */
  startRealTimeSync() {
    if (config.realTimeSync.enabled) {
      setInterval(() => {
        this.syncAllData();
      }, config.realTimeSync.interval);
    }
  }

  /**
   * 获取学生列表 - 支持缓存和实时数据
   */
  async getStudents({ forceRefresh = false, filters = {} } = {}) {
    const cacheKey = `students_${JSON.stringify(filters)}`;
    
    if (!forceRefresh && this.cache.has(cacheKey)) {
      const cached = this.cache.get(cacheKey);
      if (Date.now() - cached.timestamp < config.cache.ttl) {
        return cached.data;
      }
    }

    try {
      const result = await wx.cloud.callFunction({
        name: 'getStudents',
        data: { filters }
      });
      
      if (result.result && result.result.success) {
        const data = Array.isArray(result.result.data) ? result.result.data : [];
        
        // 转换为统一格式
        const formattedData = data.map(item => 
          DataTransformer.toAdminFormat(item, 'student')
        );
        
        this.cache.set(cacheKey, {
          data: formattedData,
          timestamp: Date.now()
        });
        
        this.notifySubscribers('students.updated', formattedData);
        
        return formattedData;
      }
      
      return [];
    } catch (error) {
      console.error('获取学生列表失败:', error);
      return this.getLocalFallback('students');
    }
  }

  /**
   * 创建或更新学生
   */
  async saveStudent(studentData) {
    const operation = studentData._id ? 'update' : 'create';
    
    try {
      const data = DataTransformer.toMiniprogramFormat(studentData, 'student');
      
      const result = await wx.cloud.callFunction({
        name: 'addStudent',
        data: { student: data, operation }
      });
      
      if (result.result && result.result.success) {
        const saved = result.result.data;
        this.invalidateCache('students');
        this.notifySubscribers('student.saved', saved);
        
        return saved;
      }
      
      throw new Error(result.result?.error || '保存失败');
    } catch (error) {
      console.error('保存学生失败:', error);
      return this.queueOperation('students', operation, studentData);
    }
  }

  /**
   * 获取行为记录
   */
  async getRecords({ filters = {}, limit = 50, skip = 0 } = {}) {
    const cacheKey = `records_${JSON.stringify(filters)}_${limit}_${skip}`;
    
    if (this.cache.has(cacheKey)) {
      const cached = this.cache.get(cacheKey);
      if (Date.now() - cached.timestamp < config.cache.ttl) {
        return cached.data;
      }
    }

    try {
      const result = await wx.cloud.callFunction({
        name: 'getRecentRecords',
        data: { filters, limit, skip }
      });
      
      if (result.result && result.result.success) {
        const data = Array.isArray(result.result.data) ? result.result.data : [];
        const formatted = data.map(item => 
          DataTransformer.toAdminFormat(item, 'record')
        );
        
        this.cache.set(cacheKey, { data: formatted, timestamp: Date.now() });
        
        return formatted;
      }
      
      return [];
    } catch (error) {
      console.error('获取记录失败:', error);
      return this.getLocalFallback('records');
    }
  }

  /**
   * 创建行为记录
   */
  async createRecord(recordData) {
    try {
      const data = DataTransformer.toMiniprogramFormat(recordData, 'record');
      
      const result = await wx.cloud.callFunction({
        name: 'createRecord',
        data: { record: data }
      });
      
      if (result.result && result.result.success) {
        const saved = result.result.data;
        this.invalidateCache('records');
        this.notifySubscribers('record.created', saved);
        
        return saved;
      }
      
      throw new Error(result.result?.error || '创建失败');
    } catch (error) {
      console.error('创建记录失败:', error);
      return this.queueOperation('records', 'create', recordData);
    }
  }

  /**
   * 获取评语数据
   */
  async getComments({ studentId, classId, startTime, endTime } = {}) {
    const cacheKey = `comments_${studentId || 'all'}_${classId || 'all'}_${startTime || ''}_${endTime || ''}`;
    
    if (this.cache.has(cacheKey)) {
      const cached = this.cache.get(cacheKey);
      if (Date.now() - cached.timestamp < config.cache.ttl) {
        return cached.data;
      }
    }

    try {
      const filters = {};
      if (studentId) filters.studentId = studentId;
      if (classId) filters.classId = classId;
      if (startTime) filters.startTime = startTime;
      if (endTime) filters.endTime = endTime;
      
      const result = await wx.cloud.callFunction({
        name: 'getCommentList',
        data: { filters }
      });
      
      if (result.result && result.result.success) {
        const data = Array.isArray(result.result.data) ? result.result.data : [];
        const formatted = data.map(item => 
          DataTransformer.toAdminFormat(item, 'comment')
        );
        
        this.cache.set(cacheKey, { data: formatted, timestamp: Date.now() });
        
        return formatted;
      }
      
      return [];
    } catch (error) {
      console.error('获取评语失败:', error);
      return this.getLocalFallback('comments');
    }
  }

  /**
   * 生成评语
   */
  async generateComment(params) {
    try {
      const result = await wx.cloud.callFunction({
        name: 'batchGenerate',
        data: { 
          studentIds: [params.studentId],
          period: params.period || 'recent',
          style: params.style || 'positive',
          length: params.length || 'medium'
        }
      });
      
      if (result.result && result.result.success) {
        const generated = result.result.data;
        this.invalidateCache('comments');
        this.notifySubscribers('comment.generated', generated);
        
        return generated;
      }
      
      throw new Error(result.result?.error || '生成失败');
    } catch (error) {
      console.error('生成评语失败:', error);
      throw error;
    }
  }

  /**
   * 获取班级数据
   */
  async getClasses({ teacherId } = {}) {
    try {
      const result = await wx.cloud.callFunction({
        name: 'getClasses',
        data: { teacherId }
      });
      
      if (result.result && result.result.success) {
        const data = Array.isArray(result.result.data) ? result.result.data : [];
        
        this.cache.set('classes', { data, timestamp: Date.now() });
        
        return data;
      }
      
      return [];
    } catch (error) {
      console.error('获取班级失败:', error);
      return this.getLocalFallback('classes');
    }
  }

  /**
   * 同步所有数据
   */
  async syncAllData() {
    if (!this.isOnline) {
      console.log('离线状态，跳过同步');
      return;
    }

    try {
      console.log('🔄 开始全量数据同步');
      
      await Promise.all([
        this.getStudents({ forceRefresh: true }),
        this.getRecords({ forceRefresh: true }),
        this.getClasses({ forceRefresh: true }),
        this.getComments({ forceRefresh: true })
      ]);
      
      console.log('✅ 全量数据同步完成');
    } catch (error) {
      console.error('全量同步失败:', error);
    }
  }

  /**
   * 事件订阅
   */
  subscribe(event, callback) {
    if (!this.subscribers.has(event)) {
      this.subscribers.set(event, []);
    }
    this.subscribers.get(event).push(callback);
  }

  /**
   * 事件通知
   */
  notifySubscribers(event, data) {
    if (this.subscribers.has(event)) {
      this.subscribers.get(event).forEach(callback => {
        try {
          callback(event, data);
        } catch (error) {
          console.error('通知订阅者失败:', error);
        }
      });
    }
  }

  /**
   * 离线队列处理
   */
  async processSyncQueue() {
    if (!this.isOnline || this.syncQueue.length === 0) return;

    console.log(`📝 处理离线队列: ${this.syncQueue.length} 项`);

    const successes = [];
    const failures = [];

    for (const item of this.syncQueue) {
      try {
        const result = await this.executeQueuedItem(item);
        if (result) {
          successes.push(item);
        } else {
          failures.push(item);
        }
      } catch (error) {
        failures.push(item);
        console.error('处理队列项失败:', error);
      }
    }

    this.syncQueue = failures;
    this.saveQueue();
    
    console.log(`✅ 队列处理完成: 成功${successes.length}, 失败${failures.length}`);
  }

  /**
   * 执行队列中的操作
   */
  async executeQueuedItem(item) {
    try {
      const { type, operation, data } = item;
      
      let result;
      switch (type) {
        case 'students':
          result = await this.saveStudent(data);
          break;
        case 'records':
          result = await this.createRecord(data);
          break;
        default:
          console.warn('未知操作类型:', type);
          return false;
      }
      
      return result;
    } catch (error) {
      console.error('执行队列操作失败:', error);
      return false;
    }
  }

  /**
   * 添加到同步队列
   */
  queueOperation(type, operation, data) {
    const item = {
      type,
      operation,
      data,
      timestamp: Date.now(),
      retryCount: 0
    };
    
    this.syncQueue.push(item);
    this.saveQueue();
    
    return this.createLocalPromise(item);
  }

  /**
   * 创建本地Promise
   */
  createLocalPromise(queueItem) {
    const tempId = `temp_${Date.now()}`;
    const localData = { ...queueItem.data, _id: tempId, _temp: true };
    
    return Promise.resolve(localData);
  }

  /**
   * 本地数据降级
   */
  getLocalFallback(type) {
    try {
      const key = `local_${type}`;
      const data = wx.getStorageSync(key) || [];
      return Array.isArray(data) ? data : [];
    } catch (error) {
      console.error('本地降级失败:', error);
      return [];
    }
  }

  /**
   * 保存队列到本地
   */
  saveQueue() {
    try {
      wx.setStorageSync('sync_queue', this.syncQueue);
    } catch (error) {
      console.error('保存队列失败:', error);
    }
  }

  /**
   * 从本地恢复队列
   */
  loadQueue() {
    try {
      this.syncQueue = wx.getStorageSync('sync_queue') || [];
      if (this.syncQueue.length > 0) {
        console.log(`📦 从本地恢复 ${this.syncQueue.length} 个待同步项`);
      }
    } catch (error) {
      console.error('恢复队列失败:', error);
      this.syncQueue = [];
    }
  }

  /**
   * 清空缓存
   */
  invalidateCache(type) {
    if (type === 'all') {
      this.cache.clear();
    } else {
      for (const key of this.cache.keys()) {
        if (key.startsWith(type)) {
          this.cache.delete(key);
        }
      }
    }
  }

  /**
   * 获取连接状态
   */
  getConnectionStatus() {
    return {
      online: this.isOnline,
      queueLength: this.syncQueue.length,
      lastPingTime: ConnectionMonitor.lastPingTime,
      connected: ConnectionMonitor.isConnected()
    };
  }
}

// 创建单例
const cloudDataBridge = new CloudDataBridge();

// 初始化时加载队列
setTimeout(() => {
  cloudDataBridge.loadQueue();
}, 1000);

module.exports = cloudDataBridge;