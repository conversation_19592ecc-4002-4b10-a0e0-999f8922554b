const cloud = require('wx-server-sdk');

// 初始化云开发环境
cloud.init({
  env: 'pylingen-9gcwi5h86dd916d6' // 替换为你的环境ID
});

const db = cloud.database();

async function createAchievementsCollection() {
  try {
    console.log('开始创建 user_achievements 集合...');
    
    // 创建一个测试文档来初始化集合
    const result = await db.collection('user_achievements').add({
      data: {
        userId: 'test_user',
        achievements: [],
        lastUpdated: Date.now(),
        version: 1,
        createTime: Date.now(),
        _test: true // 标记为测试数据
      }
    });
    
    console.log('集合创建成功，测试文档ID:', result._id);
    console.log('');
    console.log('⚠️  重要：请在腾讯云控制台设置权限规则：');
    console.log('进入云开发 → 数据库 → user_achievements → 权限设置');
    console.log('选择"自定义安全规则"，设置：');
    console.log('读权限: "auth.openid && resource.data.userId == auth.openid"');
    console.log('写权限: "auth.openid && resource.data.userId == auth.openid"');
    console.log('');
    
    // 删除测试文档
    await db.collection('user_achievements').doc(result._id).remove();
    console.log('测试文档已删除，集合创建完成');
    
  } catch (error) {
    console.error('创建集合失败:', error);
  }
}

// 云函数入口
exports.main = async (event, context) => {
  await createAchievementsCollection();
  return { success: true };
};