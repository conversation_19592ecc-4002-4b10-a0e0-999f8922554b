@echo off
echo.
echo =========================================
echo   CSP终极解决方案启动器
echo =========================================
echo.

echo 这是最后的CSP解决方案！
echo.
echo 可选方案：
echo   1. 纯Express服务器（无任何CSP）
echo   2. 修改后的Vite服务器（移除所有CSP头）  
echo   3. Chrome无安全模式启动
echo   4. 完全重置浏览器说明
echo.

set /p choice=请选择方案 (1-4): 

if "%choice%"=="1" goto express_server
if "%choice%"=="2" goto vite_server  
if "%choice%"=="3" goto chrome_no_security
if "%choice%"=="4" goto browser_reset
goto default_choice

:express_server
echo.
echo 启动纯Express服务器（完全无CSP限制）...
echo 安装依赖中...
call npm install express http-proxy-middleware --save
if errorlevel 1 (
    echo 依赖安装失败，请检查网络连接
    pause
    exit /b 1
)
echo 依赖安装完成
echo 启动服务器...
call npm run dev:express
goto end

:vite_server
echo.
echo 启动修改后的Vite服务器...
echo 已完全移除所有CSP头
call npm run dev:no-csp
goto end

:chrome_no_security
echo.
echo Chrome无安全模式启动...
echo 警告：这会禁用Chrome的所有安全功能！

set TEMP_CHROME_DIR=%TEMP%\chrome-no-security-%RANDOM%
mkdir "%TEMP_CHROME_DIR%" 2>nul

echo 临时目录: %TEMP_CHROME_DIR%
echo 启动Chrome...

start "" chrome.exe --disable-web-security --disable-features=VizDisplayCompositor --disable-site-isolation-trials --disable-extensions --disable-plugins --no-sandbox --disable-dev-shm-usage --allow-running-insecure-content --user-data-dir="%TEMP_CHROME_DIR%" "http://localhost:8080"

echo Chrome无安全模式已启动！
echo 请在启动后运行 npm run dev 启动开发服务器
goto end

:browser_reset
echo.
echo 完全重置浏览器方案...
echo.
echo 请手动执行以下步骤：
echo.
echo 1. 完全关闭所有浏览器窗口
echo 2. 清除浏览器所有数据：
echo    Chrome: 设置 - 隐私和安全 - 清除浏览数据 - 所有时间
echo    Edge: 设置 - 隐私和安全 - 清除浏览数据 - 所有时间  
echo 3. 禁用所有浏览器扩展
echo 4. 重启浏览器
echo 5. 使用无痕模式访问 http://localhost:8080
echo.
echo 然后运行: npm run dev:no-csp
echo.
goto end

:default_choice
echo.
echo 无效选择，启动默认方案...
call npm run dev:express

:end
echo.
echo 如果以上方案都无效，请检查：
echo   - 企业网络策略（公司防火墙）
echo   - 杀毒软件CSP注入  
echo   - 系统级安全策略
echo   - DNS劫持或代理设置
echo.
pause