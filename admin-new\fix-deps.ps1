# PowerShell版本的依赖修复脚本

Write-Host ""
Write-Host "=============================================" -ForegroundColor Cyan
Write-Host "   修复依赖问题 (PowerShell版本)" -ForegroundColor Cyan  
Write-Host "=============================================" -ForegroundColor Cyan
Write-Host ""

Write-Host "🧹 Cleaning up old dependencies..." -ForegroundColor Yellow

# 删除 node_modules 目录
if (Test-Path "node_modules") {
    Write-Host "Removing node_modules directory..." -ForegroundColor Gray
    Remove-Item -Path "node_modules" -Recurse -Force
    Write-Host "✅ node_modules removed" -ForegroundColor Green
}

# 删除 package-lock.json
if (Test-Path "package-lock.json") {
    Write-Host "Removing package-lock.json..." -ForegroundColor Gray
    Remove-Item -Path "package-lock.json" -Force
    Write-Host "✅ package-lock.json removed" -ForegroundColor Green
}

Write-Host "✅ Cleanup completed" -ForegroundColor Green
Write-Host ""

Write-Host "📦 Installing fresh dependencies..." -ForegroundColor Yellow
Write-Host "This may take a few minutes..." -ForegroundColor Gray
Write-Host ""

# 尝试安装依赖
try {
    npm install
    if ($LASTEXITCODE -eq 0) {
        Write-Host ""
        Write-Host "✅ Dependencies installation completed!" -ForegroundColor Green
    } else {
        throw "npm install failed"
    }
} catch {
    Write-Host ""
    Write-Host "❌ Installation failed. Trying alternative method..." -ForegroundColor Red
    Write-Host ""
    
    Write-Host "🔄 Trying with npm cache clean..." -ForegroundColor Yellow
    npm cache clean --force
    
    try {
        npm install
        if ($LASTEXITCODE -eq 0) {
            Write-Host ""
            Write-Host "✅ Dependencies installation completed!" -ForegroundColor Green
        } else {
            throw "npm install still failed"
        }
    } catch {
        Write-Host ""
        Write-Host "❌ Still failing. Trying with yarn..." -ForegroundColor Red
        Write-Host "First installing yarn..." -ForegroundColor Yellow
        
        npm install -g yarn
        yarn install
        
        if ($LASTEXITCODE -eq 0) {
            Write-Host ""
            Write-Host "✅ Dependencies installed with yarn!" -ForegroundColor Green
        } else {
            Write-Host ""
            Write-Host "❌ All installation methods failed." -ForegroundColor Red
            Write-Host "Please check your network connection and try again." -ForegroundColor Yellow
        }
    }
}

Write-Host ""
Write-Host "=============================================" -ForegroundColor Cyan
Write-Host "   Press any key to continue..." -ForegroundColor Cyan
Write-Host "=============================================" -ForegroundColor Cyan
Read-Host