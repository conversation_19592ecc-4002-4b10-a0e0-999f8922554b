<!--
  优化后的登录页面 - 更好的用户体验
-->
<view class="login-page">
  <!-- 背景装饰 - 更现代的设计 -->
  <view class="bg-decoration">
    <view class="bg-gradient"></view>
    <view class="bg-pattern">
      <view class="pattern-dot" wx:for="{{12}}" wx:key="index"></view>
    </view>
  </view>

  <!-- 返回按钮 - 更明显的位置 -->
  <view class="back-button" bindtap="goBack">
    <van-icon name="arrow-left" size="20px" color="#fff" />
    <text class="back-text">返回</text>
  </view>

  <!-- 主要内容 -->
  <view class="main-content">
    <!-- 应用品牌区域 - 更突出的设计 -->
    <view class="brand-section">
      <view class="app-logo">
        <view class="logo-container">
          <image src="/images/logo.png" class="logo-image" mode="aspectFit" />
        </view>
      </view>
      
      <view class="brand-info">
        <view class="app-title">评语灵感君 3.0</view>
        <view class="app-subtitle">AI驱动的智能教育助手</view>
      </view>
      
      <!-- 核心价值展示 -->
      <view class="value-proposition">
        <view class="value-item">
          <van-icon name="lightning" size="16px" color="#4080FF" />
          <text class="value-text">3秒生成个性化评语</text>
        </view>
        <view class="value-item">
          <van-icon name="chart-trending-o" size="16px" color="#52C873" />
          <text class="value-text">智能学情分析</text>
        </view>
        <view class="value-item">
          <van-icon name="shield-o" size="16px" color="#FF6B6B" />
          <text class="value-text">数据安全保护</text>
        </view>
      </view>
    </view>

    <!-- 登录激励区域 -->
    <view class="login-incentive">
      <view class="incentive-title">立即体验完整功能</view>
      <view class="incentive-benefits">
        <view class="benefit-item">
          <van-icon name="check" size="14px" color="#52C873" />
          <text class="benefit-text">无限制AI评语生成</text>
        </view>
        <view class="benefit-item">
          <van-icon name="check" size="14px" color="#52C873" />
          <text class="benefit-text">个人数据云端同步</text>
        </view>
        <view class="benefit-item">
          <van-icon name="check" size="14px" color="#52C873" />
          <text class="benefit-text">专属学情分析报告</text>
        </view>
      </view>
    </view>

    <!-- 登录按钮区域 - 更突出的设计 -->}
    <view class="login-section">
      <!-- 主登录按钮 -->
      <view class="primary-login-button" bindtap="wxLogin">
        <view class="button-content">
          <van-icon name="wechat" size="20px" color="#fff" />
          <text class="login-text">微信一键登录</text>
        </view>
        <view class="button-subtitle">安全快速，无需注册</view>
      </view>
      
      <!-- 游客模式入口 -->
      <view class="guest-mode-button" bindtap="enterGuestMode">
        <text class="guest-text">先体验一下</text>
        <van-icon name="arrow-right" size="14px" color="#999" />
      </view>
      
      <!-- 法律条款 -->
      <view class="legal-terms">
        <text class="terms-text">登录即表示您同意</text>
        <text class="link-text" bindtap="showUserAgreement">《用户协议》</text>
        <text class="terms-text">和</text>
        <text class="link-text" bindtap="showPrivacyPolicy">《隐私政策》</text>
      </view>
    </view>
  </view>

  <!-- 底部信任标识 -->
  <view class="trust-indicators">
    <view class="trust-item">
      <van-icon name="shield-o" size="16px" color="#52C873" />
      <text class="trust-text">微信官方认证</text>
    </view>
    <view class="trust-item">
      <van-icon name="lock" size="16px" color="#52C873" />
      <text class="trust-text">数据加密保护</text>
    </view>
  </view>

  <!-- 用户协议弹窗 - 保持原有逻辑 -->
  <van-popup
    show="{{showAgreement}}"
    position="bottom"
    custom-style="height: 70%; border-radius: 20px 20px 0 0;"
    bind:close="hideAgreement"
  >
    <view class="agreement-popup">
      <view class="popup-header">
        <view class="popup-title">用户协议</view>
        <van-icon name="cross" size="18px" color="#999" bindtap="hideAgreement" />
      </view>
      
      <scroll-view class="agreement-content" scroll-y="{{true}}">
        <view class="agreement-text">
          <!-- 保持原有协议内容 -->
          <view class="section-title">1. 服务条款</view>
          <view class="section-content">
            欢迎使用评语灵感君智能教育协同与洞察平台。本协议是您与我们之间关于使用本服务的法律协议。
          </view>
          
          <view class="section-title">2. 服务内容</view>
          <view class="section-content">
            评语灵感君为教师提供学生管理、行为记录、智能评语生成等教育辅助功能。
          </view>
          
          <view class="section-title">3. 用户责任</view>
          <view class="section-content">
            用户应当合法、正当地使用本服务，不得利用本服务从事违法违规活动。
          </view>
          
          <view class="section-title">4. 隐私保护</view>
          <view class="section-content">
            我们重视用户隐私，将按照隐私政策保护用户个人信息安全。
          </view>
        </view>
      </scroll-view>
      
      <view class="popup-footer">
        <van-button type="primary" size="large" bindtap="hideAgreement" custom-style="border-radius: 12px;">
          我已阅读并同意
        </van-button>
      </view>
    </view>
  </van-popup>

  <!-- 隐私政策弹窗 - 保持原有逻辑 -->
  <van-popup
    show="{{showPrivacy}}"
    position="bottom"
    custom-style="height: 70%; border-radius: 20px 20px 0 0;"
    bind:close="hidePrivacy"
  >
    <view class="agreement-popup">
      <view class="popup-header">
        <view class="popup-title">隐私政策</view>
        <van-icon name="cross" size="18px" color="#999" bindtap="hidePrivacy" />
      </view>
      
      <scroll-view class="agreement-content" scroll-y="{{true}}">
        <view class="agreement-text">
          <!-- 保持原有隐私政策内容 -->
          <view class="section-title">1. 信息收集</view>
          <view class="section-content">
            我们仅收集为您提供服务所必需的信息，包括基本用户信息和使用数据。
          </view>
          
          <view class="section-title">2. 信息使用</view>
          <view class="section-content">
            收集的信息仅用于提供和改善服务，不会用于其他商业目的。
          </view>
          
          <view class="section-title">3. 信息保护</view>
          <view class="section-content">
            我们采用行业标准的安全措施保护您的个人信息安全。
          </view>
          
          <view class="section-title">4. 信息共享</view>
          <view class="section-content">
            除法律要求外，我们不会与第三方分享您的个人信息。
          </view>
        </view>
      </scroll-view>
      
      <view class="popup-footer">
        <van-button type="primary" size="large" bindtap="hidePrivacy" custom-style="border-radius: 12px;">
          我已阅读并同意
        </van-button>
      </view>
    </view>
  </van-popup>
</view>
