<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>登录页面样式测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .login-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 24px;
            padding: 40px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            width: 100%;
            max-width: 400px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .logo-section {
            text-align: center;
            margin-bottom: 32px;
        }
        
        .logo {
            width: 64px;
            height: 64px;
            background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
            border-radius: 16px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 16px;
            box-shadow: 0 8px 24px rgba(59, 130, 246, 0.3);
        }
        
        .logo::before {
            content: '🤖';
            font-size: 24px;
        }
        
        .title {
            font-size: 24px;
            font-weight: 700;
            color: #1f2937;
            margin: 0 0 8px 0;
        }
        
        .subtitle {
            color: #6b7280;
            font-size: 16px;
            margin: 0 0 12px 0;
        }
        
        .divider {
            width: 48px;
            height: 4px;
            background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
            border-radius: 2px;
            margin: 0 auto;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .input-wrapper {
            position: relative;
            display: flex;
            align-items: center;
        }
        
        .input-icon {
            position: absolute;
            left: 16px;
            color: #6b7280;
            font-size: 16px;
            z-index: 1;
        }
        
        .form-input {
            width: 100%;
            padding: 16px 16px 16px 48px;
            border: 2px solid #e5e7eb;
            border-radius: 12px;
            font-size: 16px;
            font-weight: 500;
            color: #1f2937;
            background: #ffffff;
            transition: all 0.3s ease;
            box-sizing: border-box;
        }
        
        .form-input::placeholder {
            color: #9ca3af;
            font-weight: 400;
        }
        
        .form-input:focus {
            outline: none;
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }
        
        .form-input:hover {
            border-color: #60a5fa;
        }
        
        .password-toggle {
            position: absolute;
            right: 16px;
            color: #6b7280;
            cursor: pointer;
            font-size: 16px;
            z-index: 1;
        }
        
        .password-toggle:hover {
            color: #374151;
        }
        
        .login-button {
            width: 100%;
            padding: 16px;
            background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
            color: white;
            border: none;
            border-radius: 12px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 14px rgba(59, 130, 246, 0.3);
            margin-top: 8px;
        }
        
        .login-button:hover {
            background: linear-gradient(135deg, #2563eb 0%, #7c3aed 100%);
            box-shadow: 0 6px 20px rgba(59, 130, 246, 0.4);
            transform: translateY(-1px);
        }
        
        .login-button:active {
            transform: translateY(0);
            box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);
        }
        
        .copyright {
            text-align: center;
            margin-top: 24px;
            padding-top: 16px;
            border-top: 1px solid #e5e7eb;
            color: #9ca3af;
            font-size: 12px;
        }
        
        .test-info {
            background: rgba(59, 130, 246, 0.1);
            border: 1px solid rgba(59, 130, 246, 0.2);
            border-radius: 8px;
            padding: 12px;
            margin-top: 16px;
            text-align: center;
        }
        
        .test-info h4 {
            margin: 0 0 8px 0;
            color: #1f2937;
            font-size: 14px;
        }
        
        .test-info p {
            margin: 4px 0;
            color: #374151;
            font-size: 13px;
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="logo-section">
            <div class="logo"></div>
            <h1 class="title">评语灵感君</h1>
            <p class="subtitle">智能评语生成管理系统</p>
            <div class="divider"></div>
        </div>
        
        <form>
            <div class="form-group">
                <div class="input-wrapper">
                    <span class="input-icon">👤</span>
                    <input 
                        type="text" 
                        class="form-input" 
                        placeholder="请输入用户名"
                        value="admin"
                    >
                </div>
            </div>
            
            <div class="form-group">
                <div class="input-wrapper">
                    <span class="input-icon">🔒</span>
                    <input 
                        type="password" 
                        class="form-input" 
                        placeholder="请输入密码"
                        value="admin123"
                        id="passwordInput"
                    >
                    <span class="password-toggle" onclick="togglePassword()">👁️</span>
                </div>
            </div>
            
            <button type="button" class="login-button" onclick="testLogin()">
                🚀 立即登录
            </button>
        </form>
        
        <div class="test-info">
            <h4>✅ 样式修复验证</h4>
            <p><strong>输入框文字：</strong>深灰色 (#1f2937)</p>
            <p><strong>占位符文字：</strong>浅灰色 (#9ca3af)</p>
            <p><strong>图标颜色：</strong>中灰色 (#6b7280)</p>
            <p><strong>边框交互：</strong>悬停和聚焦效果正常</p>
        </div>
        
        <div class="copyright">
            © 2024 评语灵感君管理后台 v3.0
        </div>
    </div>

    <script>
        function togglePassword() {
            const input = document.getElementById('passwordInput');
            const toggle = document.querySelector('.password-toggle');
            
            if (input.type === 'password') {
                input.type = 'text';
                toggle.textContent = '🙈';
            } else {
                input.type = 'password';
                toggle.textContent = '👁️';
            }
        }
        
        function testLogin() {
            const username = document.querySelector('input[type="text"]').value;
            const password = document.querySelector('#passwordInput').value;
            
            if (username === 'admin' && password === 'admin123') {
                alert('✅ 登录成功！样式显示正常。');
            } else {
                alert('❌ 请输入正确的用户名和密码');
            }
        }
        
        // 测试自动填充样式
        setTimeout(() => {
            console.log('🔍 登录页面样式测试');
            console.log('✅ 输入框文字颜色已修复');
            console.log('✅ 占位符颜色已优化');
            console.log('✅ 账户提示已移除');
            console.log('✅ 交互效果正常');
        }, 1000);
    </script>
</body>
</html>
