/**
 * 云端徽章系统演示和测试工具
 * 展示如何使用新的云端徽章系统
 */

class CloudAchievementDemo {
  constructor() {
    this.achievementManager = null;
  }

  /**
   * 初始化演示
   */
  async init() {
    try {
      const AchievementManager = require('./achievementManager');
      this.achievementManager = new AchievementManager();
      await this.achievementManager.init();
      
      console.log('🎯 云端徽章系统演示初始化完成');
      return true;
    } catch (error) {
      console.error('演示初始化失败:', error);
      return false;
    }
  }

  /**
   * 演示1：查看当前徽章状态
   */
  async demo1_checkCurrentStatus() {
    console.log('\n📊 演示1：查看当前徽章状态');
    
    try {
      const status = await this.achievementManager.getAllAchievementStatus();
      
      console.log('当前徽章状态:');
      status.forEach(achievement => {
        const statusIcon = achievement.isUnlocked ? '✅' : '⏳';
        const progress = achievement.progress ? `${achievement.progress}%` : '0%';
        console.log(`${statusIcon} ${achievement.icon} ${achievement.name}: ${progress} (${achievement.current}/${achievement.target})`);
      });
      
      return status;
    } catch (error) {
      console.error('查看徽章状态失败:', error);
      return [];
    }
  }

  /**
   * 演示2：模拟解锁徽章
   */
  async demo2_unlockAchievement() {
    console.log('\n🏆 演示2：模拟解锁徽章');
    
    try {
      // 模拟解锁"效率达人"徽章
      const achievement = {
        id: 'efficiency_master',
        name: '效率达人',
        description: '单日记录行为10条',
        icon: '🚀'
      };
      
      const result = await this.achievementManager.unlockAchievement(achievement);
      
      if (result) {
        console.log('✅ 徽章解锁成功:', result.name);
        console.log('📅 解锁时间:', new Date(result.unlockedAt).toLocaleString());
        
        // 显示解锁提示
        if (typeof wx !== 'undefined' && wx.showToast) {
          wx.showToast({
            title: `🎉 解锁 ${result.name}`,
            icon: 'success',
            duration: 2000
          });
        }
        
        return result;
      } else {
        console.log('ℹ️ 徽章已解锁，无需重复解锁');
        return null;
      }
    } catch (error) {
      console.error('解锁徽章失败:', error);
      return null;
    }
  }

  /**
   * 演示3：云端数据同步
   */
  async demo3_cloudSync() {
    console.log('\n☁️ 演示3：云端数据同步');
    
    try {
      const syncResult = await this.achievementManager.forceSync();
      
      if (syncResult.success) {
        console.log('✅ 云端同步成功:', syncResult.data.message);
        console.log('📊 同步来源:', syncResult.data.source);
        console.log('🔢 数据版本:', syncResult.data.version);
        console.log('🏆 徽章数量:', syncResult.data.achievements.length);
        
        return syncResult.data;
      } else {
        console.error('❌ 云端同步失败:', syncResult.error);
        return null;
      }
    } catch (error) {
      console.error('云端同步异常:', error);
      return null;
    }
  }

  /**
   * 演示4：清空徽章数据
   */
  async demo4_clearData() {
    console.log('\n🧹 演示4：清空徽章数据');
    
    try {
      // 先显示清空前的状态
      const beforeStatus = await this.achievementManager.getAllAchievementStatus();
      const unlockedBefore = beforeStatus.filter(a => a.isUnlocked).length;
      console.log(`清空前已解锁徽章: ${unlockedBefore} 个`);
      
      // 执行清空
      const clearResult = await this.achievementManager.clearAllAchievements();
      
      if (clearResult.success) {
        console.log('✅ 徽章数据清空成功:', clearResult.message);
        
        // 显示清空后的状态
        const afterStatus = await this.achievementManager.getAllAchievementStatus();
        const unlockedAfter = afterStatus.filter(a => a.isUnlocked).length;
        console.log(`清空后已解锁徽章: ${unlockedAfter} 个`);
        
        if (typeof wx !== 'undefined' && wx.showToast) {
          wx.showToast({
            title: '徽章数据已清空',
            icon: 'success'
          });
        }
        
        return clearResult;
      } else {
        console.error('❌ 徽章数据清空失败:', clearResult.error);
        return null;
      }
    } catch (error) {
      console.error('清空徽章数据异常:', error);
      return null;
    }
  }

  /**
   * 演示5：完整的徽章生命周期
   */
  async demo5_fullLifecycle() {
    console.log('\n🔄 演示5：完整的徽章生命周期');
    
    try {
      console.log('步骤1: 清空现有数据...');
      await this.demo4_clearData();
      
      console.log('\n步骤2: 查看初始状态...');
      await this.demo1_checkCurrentStatus();
      
      console.log('\n步骤3: 解锁新徽章...');
      await this.demo2_unlockAchievement();
      
      console.log('\n步骤4: 同步到云端...');
      await this.demo3_cloudSync();
      
      console.log('\n步骤5: 查看最终状态...');
      await this.demo1_checkCurrentStatus();
      
      console.log('\n🎉 完整生命周期演示完成！');
      
      return true;
    } catch (error) {
      console.error('完整生命周期演示失败:', error);
      return false;
    }
  }

  /**
   * 运行所有演示
   */
  async runAllDemos() {
    console.log('🚀 开始运行云端徽章系统演示...\n');
    
    const initSuccess = await this.init();
    if (!initSuccess) {
      console.error('❌ 初始化失败，无法运行演示');
      return false;
    }
    
    try {
      await this.demo1_checkCurrentStatus();
      await this.demo2_unlockAchievement();
      await this.demo3_cloudSync();
      // await this.demo4_clearData(); // 注释掉清空演示，避免意外清空数据
      
      console.log('\n🎉 所有演示运行完成！');
      console.log('\n💡 提示：');
      console.log('- 徽章数据现在会自动同步到云端');
      console.log('- 清空学生数据时会同时清空云端徽章');
      console.log('- 支持多设备数据同步');
      console.log('- 网络异常时会降级到本地存储');
      
      return true;
    } catch (error) {
      console.error('演示运行失败:', error);
      return false;
    }
  }
}

// 导出演示类
module.exports = {
  CloudAchievementDemo
};

// 全局函数，方便在控制台中调用
if (typeof global !== 'undefined') {
  global.runCloudAchievementDemo = async function() {
    const demo = new CloudAchievementDemo();
    return await demo.runAllDemos();
  };
  
  global.testCloudAchievement = async function(action = 'status') {
    const demo = new CloudAchievementDemo();
    await demo.init();
    
    switch (action) {
      case 'status':
        return await demo.demo1_checkCurrentStatus();
      case 'unlock':
        return await demo.demo2_unlockAchievement();
      case 'sync':
        return await demo.demo3_cloudSync();
      case 'clear':
        return await demo.demo4_clearData();
      case 'full':
        return await demo.demo5_fullLifecycle();
      default:
        console.log('可用操作: status, unlock, sync, clear, full');
        return null;
    }
  };
}

console.log(`
🎯 云端徽章系统演示工具已加载！

可用命令：
1. runCloudAchievementDemo() - 运行完整演示
2. testCloudAchievement('status') - 查看徽章状态
3. testCloudAchievement('unlock') - 解锁徽章
4. testCloudAchievement('sync') - 云端同步
5. testCloudAchievement('clear') - 清空数据
6. testCloudAchievement('full') - 完整生命周期

特性：
✅ 云端数据同步
✅ 本地缓存机制
✅ 网络异常降级
✅ 数据冲突解决
✅ 多设备同步支持
`);
