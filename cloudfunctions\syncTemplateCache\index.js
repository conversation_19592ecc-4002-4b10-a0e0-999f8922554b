/**
 * 模板缓存同步云函数
 * 当管理后台编辑提示词模板时，实时清理缓存并同步更新
 */

const cloud = require('wx-server-sdk');

// 初始化云开发环境
cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
});

const db = cloud.database();

exports.main = async (event, context) => {
  const { action, templateId, templateType, templateData } = event;
  
  console.log('[syncTemplateCache] 模板缓存同步请求:', {
    action,
    templateId,
    templateType,
    hasTemplateData: !!templateData
  });

  try {
    switch (action) {
      case 'update':
        return await handleTemplateUpdate(templateId, templateType, templateData);
      case 'delete':
        return await handleTemplateDelete(templateId, templateType);
      case 'create':
        return await handleTemplateCreate(templateData);
      case 'clearCache':
        return await handleClearCache(templateType);
      default:
        throw new Error(`不支持的操作类型: ${action}`);
    }
  } catch (error) {
    console.error('[syncTemplateCache] 同步失败:', error);
    return {
      success: false,
      error: error.message || '模板缓存同步失败',
      details: error
    };
  }
};

/**
 * 处理模板更新
 */
async function handleTemplateUpdate(templateId, templateType, templateData) {
  console.log('[syncTemplateCache] 处理模板更新:', templateId);
  
  try {
    // 1. 更新数据库中的模板
    const updateResult = await db.collection('prompt_templates').doc(templateId).update({
      data: {
        ...templateData,
        updateTime: db.serverDate(),
        updateTimestamp: Date.now(),
        'metadata.updatedAt': new Date().toISOString(),
        'metadata.cacheVersion': Date.now() // 用于缓存版本控制
      }
    });

    // 2. 清理相关缓存记录
    await clearCacheRecords(templateType);

    // 3. 通知小程序端更新缓存
    await notifyClientCacheUpdate(templateType);

    console.log('[syncTemplateCache] 模板更新完成:', templateId);
    
    return {
      success: true,
      message: '模板更新并同步成功',
      data: {
        templateId,
        templateType,
        updateTime: new Date().toISOString(),
        cacheCleared: true
      }
    };
    
  } catch (error) {
    console.error('[syncTemplateCache] 模板更新失败:', error);
    throw error;
  }
}

/**
 * 处理模板删除
 */
async function handleTemplateDelete(templateId, templateType) {
  console.log('[syncTemplateCache] 处理模板删除:', templateId);
  
  try {
    // 1. 软删除模板（标记为禁用）
    await db.collection('prompt_templates').doc(templateId).update({
      data: {
        enabled: false,
        deleteTime: db.serverDate(),
        deleteTimestamp: Date.now(),
        'metadata.deletedAt': new Date().toISOString()
      }
    });

    // 2. 清理相关缓存
    await clearCacheRecords(templateType);

    // 3. 通知客户端更新
    await notifyClientCacheUpdate(templateType);

    return {
      success: true,
      message: '模板删除并同步成功',
      data: {
        templateId,
        templateType,
        deleteTime: new Date().toISOString()
      }
    };
    
  } catch (error) {
    console.error('[syncTemplateCache] 模板删除失败:', error);
    throw error;
  }
}

/**
 * 处理模板创建
 */
async function handleTemplateCreate(templateData) {
  console.log('[syncTemplateCache] 处理模板创建');
  
  try {
    // 1. 创建新模板
    const createData = {
      ...templateData,
      createTime: db.serverDate(),
      updateTime: db.serverDate(),
      createTimestamp: Date.now(),
      updateTimestamp: Date.now(),
      metadata: {
        ...templateData.metadata,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        cacheVersion: Date.now(),
        usageCount: 0
      },
      enabled: templateData.enabled !== false,
      version: templateData.version || 1
    };

    const result = await db.collection('prompt_templates').add({
      data: createData
    });

    // 2. 清理相关缓存
    await clearCacheRecords(templateData.type);

    // 3. 通知客户端更新
    await notifyClientCacheUpdate(templateData.type);

    return {
      success: true,
      message: '模板创建并同步成功',
      data: {
        templateId: result._id,
        templateType: templateData.type,
        createTime: new Date().toISOString()
      }
    };
    
  } catch (error) {
    console.error('[syncTemplateCache] 模板创建失败:', error);
    throw error;
  }
}

/**
 * 处理缓存清理
 */
async function handleClearCache(templateType) {
  console.log('[syncTemplateCache] 处理缓存清理:', templateType);
  
  try {
    // 清理指定类型或所有缓存
    await clearCacheRecords(templateType);
    
    // 通知客户端更新
    await notifyClientCacheUpdate(templateType);

    return {
      success: true,
      message: '缓存清理成功',
      data: {
        templateType: templateType || 'all',
        clearTime: new Date().toISOString()
      }
    };
    
  } catch (error) {
    console.error('[syncTemplateCache] 缓存清理失败:', error);
    throw error;
  }
}

/**
 * 清理缓存记录
 */
async function clearCacheRecords(templateType) {
  try {
    // 如果有专门的缓存表，在这里清理
    // 这里主要是更新模板的缓存版本号，让客户端知道需要重新获取
    
    if (templateType) {
      // 清理特定类型的缓存标记
      await db.collection('template_cache_control').where({
        templateType: templateType
      }).remove();
    } else {
      // 清理所有缓存标记
      await db.collection('template_cache_control').where({}).remove();
    }

    // 创建新的缓存控制记录
    await db.collection('template_cache_control').add({
      data: {
        templateType: templateType || 'all',
        cacheVersion: Date.now(),
        clearTime: db.serverDate(),
        clearTimestamp: Date.now()
      }
    });

    console.log('[syncTemplateCache] 缓存记录清理完成');
    
  } catch (error) {
    console.warn('[syncTemplateCache] 缓存记录清理失败:', error);
    // 不影响主流程
  }
}

/**
 * 通知客户端缓存更新
 */
async function notifyClientCacheUpdate(templateType) {
  try {
    // 这里可以通过云函数触发器或消息推送通知客户端
    // 暂时通过数据库记录来实现
    
    await db.collection('cache_update_notifications').add({
      data: {
        type: 'template_update',
        templateType: templateType || 'all',
        timestamp: Date.now(),
        notifyTime: db.serverDate(),
        processed: false
      }
    });

    console.log('[syncTemplateCache] 客户端更新通知已发送');
    
  } catch (error) {
    console.warn('[syncTemplateCache] 发送客户端通知失败:', error);
    // 不影响主流程
  }
}

/**
 * 获取缓存版本信息
 */
async function getCacheVersion(templateType) {
  try {
    const result = await db.collection('template_cache_control')
      .where({ templateType })
      .orderBy('clearTimestamp', 'desc')
      .limit(1)
      .get();
    
    if (result.data.length > 0) {
      return result.data[0].cacheVersion;
    }
    
    return Date.now();
    
  } catch (error) {
    console.warn('[syncTemplateCache] 获取缓存版本失败:', error);
    return Date.now();
  }
}
