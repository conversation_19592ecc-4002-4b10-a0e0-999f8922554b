/**
 * 数据接收Hook
 * 管理从小程序接收的实时数据
 */

import { useState, useEffect, useCallback } from 'react'
import { getDataReceiver, UserActivityData, TokensUpdateData, CommentGeneratedData, SystemStatusData } from '../services/dataReceiver'

interface UseDataReceiverOptions {
  enabled?: boolean
  wsUrl?: string
  apiUrl?: string
  autoConnect?: boolean
}

export const useDataReceiver = (options: UseDataReceiverOptions = {}) => {
  const { enabled = true, wsUrl, apiUrl, autoConnect = true } = options
  
  const [users, setUsers] = useState<any[]>([])
  const [activities, setActivities] = useState<UserActivityData[]>([])
  const [tokensHistory, setTokensHistory] = useState<TokensUpdateData[]>([])
  const [comments, setComments] = useState<CommentGeneratedData[]>([])
  const [systemStatus, setSystemStatus] = useState<SystemStatusData | null>(null)
  const [statistics, setStatistics] = useState({
    totalUsers: 0,
    totalTokensUsed: 0,
    totalCost: 0,
    totalComments: 0,
    lastUpdated: 0
  })

  const dataReceiver = getDataReceiver()

  // 禁用的实时数据处理方法
  const handleUserActivity = useCallback((data: UserActivityData) => {
    console.log('🚫 用户活动处理已禁用')
  }, [])

  const handleTokensUpdate = useCallback((data: TokensUpdateData) => {
    console.log('🚫 Tokens更新处理已禁用')
  }, [])

  const handleCommentGenerated = useCallback((data: CommentGeneratedData) => {
    console.log('🚫 评语生成处理已禁用')
  }, [])

  const handleSystemStatus = useCallback((data: SystemStatusData) => {
    console.log('🚫 系统状态处理已禁用')
  }, [])

  // 禁用的实时连接状态
  const isConnected = false
  const connectionError = null
  const lastUpdate = null
  const reconnectAttempts = 0
  
  // 禁用的消息发送
  const sendMessage = useCallback((type: string, payload: any) => {
    console.log('🚫 实时消息发送已禁用:', type)
  }, [])
  
  // 禁用的同步配置更新
  const updateSyncConfig = useCallback((config: any) => {
    console.log('🚫 同步配置更新已禁用')
  }, [])

  // 更新本地状态
  const updateLocalState = useCallback(() => {
    setUsers(dataReceiver.getUsers())
    setActivities(dataReceiver.getRecentActivities(20))
    setTokensHistory(dataReceiver.getTokensHistory(100))
    setComments(dataReceiver.getRecentComments(20))
    setSystemStatus(dataReceiver.getSystemStatus())
    setStatistics(dataReceiver.getStatistics())
  }, [dataReceiver])

  // 监听数据变化
  useEffect(() => {
    const handleDataUpdate = () => {
      updateLocalState()
    }

    dataReceiver.on('data_updated', handleDataUpdate)
    dataReceiver.on('user_activity', handleDataUpdate)
    dataReceiver.on('tokens_update', handleDataUpdate)
    dataReceiver.on('comment_generated', handleDataUpdate)
    dataReceiver.on('system_status', handleDataUpdate)

    // 初始化数据
    updateLocalState()

    return () => {
      dataReceiver.off('data_updated', handleDataUpdate)
      dataReceiver.off('user_activity', handleDataUpdate)
      dataReceiver.off('tokens_update', handleDataUpdate)
      dataReceiver.off('comment_generated', handleDataUpdate)
      dataReceiver.off('system_status', handleDataUpdate)
    }
  }, [dataReceiver, updateLocalState])

  // 获取用户详情
  const getUserDetail = useCallback((userId: string) => {
    return dataReceiver.getUserDetail(userId)
  }, [dataReceiver])

  // 获取用户Tokens历史
  const getUserTokensHistory = useCallback((userId: string, limit?: number) => {
    return dataReceiver.getUserTokensHistory(userId, limit)
  }, [dataReceiver])

  // 获取用户评语历史
  const getUserComments = useCallback((userId: string, limit?: number) => {
    return dataReceiver.getUserComments(userId, limit)
  }, [dataReceiver])

  // 清空历史数据
  const clearHistory = useCallback(() => {
    dataReceiver.clearHistory()
    updateLocalState()
  }, [dataReceiver, updateLocalState])

  // 导出数据
  const exportData = useCallback(() => {
    return dataReceiver.exportData()
  }, [dataReceiver])

  // 添加测试数据功能已清空（不再生成模拟数据）
  const addTestData = useCallback(() => {
    console.log('测试数据生成功能已禁用')
    // 不再生成任何模拟数据
  }, [])

  // 发送配置更新到小程序
  const sendConfigUpdate = useCallback((config: any) => {
    sendMessage('config_update', config)
  }, [sendMessage])

  // 发送用户限额更新
  const sendUserLimitUpdate = useCallback((userId: string, newLimit: number) => {
    sendMessage('user_limit_update', {
      userId,
      newLimit,
      timestamp: Date.now()
    })
  }, [sendMessage])

  return {
    // 数据状态
    users,
    activities,
    tokensHistory,
    comments,
    systemStatus,
    statistics,
    
    // 连接状态
    isConnected,
    connectionError,
    lastUpdate,
    reconnectAttempts,
    
    // 数据操作方法
    getUserDetail,
    getUserTokensHistory,
    getUserComments,
    clearHistory,
    exportData,
    addTestData,
    
    // 通信方法
    sendConfigUpdate,
    sendUserLimitUpdate,
    updateSyncConfig,
    
    // 状态更新
    refreshData: updateLocalState
  }
}
