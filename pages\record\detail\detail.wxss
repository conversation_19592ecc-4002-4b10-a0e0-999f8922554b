/* 记录详情页面样式 */
.record-detail-page {
  min-height: 100vh;
  background: #f8f9fa;
  padding-bottom: 120rpx;
}

/* 记录信息卡片 */
.record-info-card {
  background: white;
  margin: 24rpx;
  border-radius: 20rpx;
  padding: 32rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.06);
}

.record-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 24rpx;
}

.student-info {
  display: flex;
  align-items: center;
  flex: 1;
}

.student-avatar {
  width: 64rpx;
  height: 64rpx;
  margin-right: 16rpx;
}

.avatar-image {
  width: 100%;
  height: 100%;
  border-radius: 16rpx;
}

.avatar-placeholder {
  width: 100%;
  height: 100%;
  background: #4080FF;
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.avatar-text {
  color: white;
  font-size: 28rpx;
  font-weight: 600;
}

.student-details {
  flex: 1;
}

.student-name {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 6rpx;
}

.student-name.clickable {
  color: #5470C6;
  transition: all 0.2s ease;
}

.student-details:active .student-name.clickable {
  color: #4A63B3;
  transform: scale(0.98);
}

.student-class {
  font-size: 24rpx;
  color: #666;
}

.record-score {
  padding: 12rpx 20rpx;
  border-radius: 20rpx;
  font-weight: 600;
}

.record-score.positive {
  background: rgba(82, 200, 115, 0.1);
  color: #52C873;
}

.record-score.negative {
  background: rgba(255, 82, 71, 0.1);
  color: #FF5247;
}

.record-score.neutral {
  background: rgba(153, 153, 153, 0.1);
  color: #999;
}

.record-score.academic {
  background: rgba(64, 128, 255, 0.1);
  color: #4080FF;
}

.score-text {
  font-size: 28rpx;
}

/* 行为信息 */
.behavior-info {
  display: flex;
  align-items: center;
  gap: 16rpx;
  margin-bottom: 24rpx;
}

.behavior-type {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 8rpx 16rpx;
  border-radius: 16rpx;
  font-size: 22rpx;
  font-weight: 500;
}

.behavior-type.positive {
  background: #52C873;
  color: white;
}

.behavior-type.negative {
  background: #FF5247;
  color: white;
}

.behavior-type.neutral {
  background: #999;
  color: white;
}

.behavior-type.academic {
  background: #4080FF;
  color: white;
}

.behavior-action {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  flex: 1;
}

/* 记录描述 */
.record-description {
  background: #f8f9fa;
  padding: 24rpx;
  border-radius: 12rpx;
  margin-bottom: 24rpx;
}

.desc-title {
  font-size: 26rpx;
  font-weight: 500;
  color: #666;
  margin-bottom: 12rpx;
}

.desc-content {
  font-size: 28rpx;
  color: #333;
  line-height: 1.6;
}

/* 记录元信息 */
.record-meta {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.meta-text {
  font-size: 24rpx;
  color: #666;
}

/* 图片证据 */
.images-section {
  background: white;
  margin: 16rpx 24rpx;
  border-radius: 20rpx;
  padding: 32rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.06);
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 24rpx;
}

.image-list {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 16rpx;
}

.image-item {
  aspect-ratio: 1;
  border-radius: 12rpx;
  overflow: hidden;
}

.record-image {
  width: 100%;
  height: 100%;
}

/* 相关记录 */
.related-section {
  background: white;
  margin: 16rpx 24rpx;
  border-radius: 20rpx;
  padding: 32rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.06);
}

.related-list {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

.related-item {
  display: flex;
  align-items: center;
  padding: 20rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
  transition: all 0.3s ease;
}

.related-item:active {
  transform: scale(0.98);
  background: #f0f0f0;
}

.related-icon {
  width: 32rpx;
  height: 32rpx;
  border-radius: 8rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12rpx;
  flex-shrink: 0;
}

.related-icon.positive { background: #52C873; }
.related-icon.negative { background: #FF5247; }
.related-icon.neutral { background: #999; }
.related-icon.academic { background: #4080FF; }

.related-content {
  flex: 1;
}

.related-action {
  font-size: 26rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 4rpx;
}

.related-time {
  font-size: 22rpx;
  color: #666;
}

.related-score {
  font-size: 24rpx;
  font-weight: 600;
  color: #4080FF;
  flex-shrink: 0;
}

/* 操作历史 */
.history-section {
  background: white;
  margin: 16rpx 24rpx;
  border-radius: 20rpx;
  padding: 32rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.06);
}

.history-list {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.history-item {
  display: flex;
  align-items: center;
  padding: 16rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.history-item:last-child {
  border-bottom: none;
}

.history-icon {
  width: 32rpx;
  height: 32rpx;
  background: rgba(64, 128, 255, 0.1);
  border-radius: 8rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12rpx;
  flex-shrink: 0;
}

.history-content {
  flex: 1;
}

.history-action {
  font-size: 26rpx;
  color: #333;
  margin-bottom: 4rpx;
}

.history-time {
  font-size: 22rpx;
  color: #666;
}

.history-user {
  font-size: 24rpx;
  color: #4080FF;
  flex-shrink: 0;
}

