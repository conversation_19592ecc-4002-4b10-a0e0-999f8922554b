# 一键创建数据库集合

## 方法一：在小程序中运行代码（推荐）

1. **找到任意一个小程序页面**（比如 `pages/settings/settings.js`）

2. **在页面的 onLoad 方法中临时添加以下代码：**

```javascript
async onLoad() {
  // 临时添加：创建数据库集合
  await this.createUserProfilesCollection()
  
  // ... 其他原有代码
},

// 添加这个方法
async createUserProfilesCollection() {
  try {
    console.log('开始创建 user_profiles 集合...')
    
    const db = wx.cloud.database()
    
    // 尝试创建一条测试数据来触发集合创建
    const testResult = await db.collection('user_profiles').add({
      data: {
        _test: true,
        createTime: new Date(),
        note: '测试数据，用于创建集合'
      }
    })
    
    console.log('✅ user_profiles 集合创建成功:', testResult)
    
    // 删除测试数据
    await db.collection('user_profiles').doc(testResult._id).remove()
    console.log('✅ 测试数据已清理')
    
    wx.showToast({
      title: '集合创建成功',
      icon: 'success'
    })
    
  } catch (error) {
    console.error('❌ 创建集合失败:', error)
    wx.showToast({
      title: '创建失败: ' + error.message,
      icon: 'none',
      duration: 3000
    })
  }
}
```

3. **保存文件，在开发者工具中编译**
4. **打开该页面，查看控制台输出**
5. **成功后删除临时添加的代码**

## 方法二：直接在云开发控制台创建

1. **打开微信开发者工具**
2. **点击顶部的"云开发"按钮**
3. **选择"数据库"**
4. **点击"添加集合"按钮**
5. **输入集合名称：`user_profiles`**
6. **点击"确定"**

## 方法三：让云函数自动创建

云函数在第一次写入数据时会自动创建集合，所以：
1. **确保云函数已部署**
2. **在小程序中调用同步功能**
3. **第一次调用时会自动创建集合**

## 验证集合是否创建成功

在云开发控制台的数据库页面，应该能看到 `user_profiles` 集合。

## 重要提醒

- ❌ 不要在小程序控制台运行 Node.js 代码
- ❌ 不要在小程序中使用 `require('wx-server-sdk')`  
- ✅ 使用 `wx.cloud.database()` 进行数据库操作
- ✅ 在云函数中使用 `wx-server-sdk`