/**
 * 数据验证工具
 * 提供统一的数据验证和错误处理
 */

/**
 * 验证学生数据
 */
function validateStudentData(studentData) {
  const errors = [];
  
  // 必填字段验证
  if (!studentData.name || studentData.name.trim() === '') {
    errors.push('学生姓名不能为空');
  }
  
  if (!studentData.classId) {
    errors.push('必须选择班级');
  }
  
  // 姓名长度验证
  if (studentData.name && studentData.name.length > 20) {
    errors.push('学生姓名不能超过20个字符');
  }
  
  // 学号验证（如果提供）
  if (studentData.studentNumber && !/^\d{1,20}$/.test(studentData.studentNumber)) {
    errors.push('学号只能包含数字，且不超过20位');
  }
  
  // 性别验证
  if (studentData.gender && !['male', 'female'].includes(studentData.gender)) {
    errors.push('性别只能是男或女');
  }
  
  return {
    isValid: errors.length === 0,
    errors: errors
  };
}

/**
 * 验证班级数据
 */
function validateClassData(classData) {
  const errors = [];
  
  // 必填字段验证
  if (!classData.className || classData.className.trim() === '') {
    errors.push('班级名称不能为空');
  }
  
  if (!classData.grade) {
    errors.push('年级不能为空');
  }
  
  if (!classData.subject) {
    errors.push('学科不能为空');
  }
  
  // 班级名称长度验证
  if (classData.className && classData.className.length > 50) {
    errors.push('班级名称不能超过50个字符');
  }
  
  // 描述长度验证
  if (classData.description && classData.description.length > 200) {
    errors.push('班级描述不能超过200个字符');
  }
  
  return {
    isValid: errors.length === 0,
    errors: errors
  };
}

/**
 * 验证评语生成参数
 */
function validateCommentParams(params) {
  const errors = [];
  
  // 学生ID验证
  if (!params.studentId) {
    errors.push('必须选择学生');
  }
  
  // 风格验证
  const validStyles = ['warm', 'formal', 'encouraging', 'detailed'];
  if (params.style && !validStyles.includes(params.style)) {
    errors.push('评语风格无效');
  }
  
  // 长度验证
  const validLengths = ['short', 'medium', 'long'];
  if (params.length && !validLengths.includes(params.length)) {
    errors.push('评语长度设置无效');
  }
  
  // 自定义要求长度验证
  if (params.customRequirement && params.customRequirement.length > 500) {
    errors.push('自定义要求不能超过500个字符');
  }
  
  // 日期范围验证
  if (params.startDate && params.endDate) {
    const start = new Date(params.startDate);
    const end = new Date(params.endDate);
    
    if (start > end) {
      errors.push('开始日期不能晚于结束日期');
    }
    
    if (end > new Date()) {
      errors.push('结束日期不能晚于今天');
    }
  }
  
  return {
    isValid: errors.length === 0,
    errors: errors
  };
}

/**
 * 验证记录数据
 */
function validateRecordData(recordData) {
  const errors = [];
  
  // 必填字段验证
  if (!recordData.studentId) {
    errors.push('必须选择学生');
  }
  
  if (!recordData.action || recordData.action.trim() === '') {
    errors.push('行为描述不能为空');
  }
  
  if (!recordData.behaviorType) {
    errors.push('必须选择行为类型');
  }
  
  // 行为类型验证
  const validTypes = ['positive', 'negative', 'academic', 'social', 'creative'];
  if (recordData.behaviorType && !validTypes.includes(recordData.behaviorType)) {
    errors.push('行为类型无效');
  }
  
  // 描述长度验证
  if (recordData.action && recordData.action.length > 200) {
    errors.push('行为描述不能超过200个字符');
  }
  
  // 备注长度验证
  if (recordData.note && recordData.note.length > 500) {
    errors.push('备注不能超过500个字符');
  }
  
  return {
    isValid: errors.length === 0,
    errors: errors
  };
}

/**
 * 验证用户信息
 */
function validateUserInfo(userInfo) {
  const errors = [];
  
  // 姓名验证
  if (userInfo.name && userInfo.name.length > 20) {
    errors.push('姓名不能超过20个字符');
  }
  
  // 手机号验证
  if (userInfo.phone && !/^1[3-9]\d{9}$/.test(userInfo.phone)) {
    errors.push('手机号格式不正确');
  }
  
  // 邮箱验证
  if (userInfo.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(userInfo.email)) {
    errors.push('邮箱格式不正确');
  }
  
  // 学校名称验证
  if (userInfo.school && userInfo.school.length > 100) {
    errors.push('学校名称不能超过100个字符');
  }
  
  return {
    isValid: errors.length === 0,
    errors: errors
  };
}

/**
 * 验证AI配置
 */
function validateAIConfig(config) {
  const errors = [];
  
  // API密钥验证
  if (!config.apiKey || config.apiKey.trim() === '') {
    errors.push('API密钥不能为空');
  }
  
  // 模型验证
  if (!config.model || config.model.trim() === '') {
    errors.push('模型不能为空');
  }
  
  // 温度参数验证
  if (config.temperature !== undefined) {
    const temp = parseFloat(config.temperature);
    if (isNaN(temp) || temp < 0 || temp > 2) {
      errors.push('温度参数必须在0-2之间');
    }
  }
  
  // 最大令牌数验证
  if (config.maxTokens !== undefined) {
    const tokens = parseInt(config.maxTokens);
    if (isNaN(tokens) || tokens < 1 || tokens > 4000) {
      errors.push('最大令牌数必须在1-4000之间');
    }
  }
  
  return {
    isValid: errors.length === 0,
    errors: errors
  };
}

/**
 * 通用数据清理函数
 */
function sanitizeData(data) {
  if (typeof data === 'string') {
    return data.trim();
  }
  
  if (Array.isArray(data)) {
    return data.map(item => sanitizeData(item));
  }
  
  if (typeof data === 'object' && data !== null) {
    const cleaned = {};
    for (const key in data) {
      if (data.hasOwnProperty(key)) {
        cleaned[key] = sanitizeData(data[key]);
      }
    }
    return cleaned;
  }
  
  return data;
}

/**
 * 格式化验证错误信息
 */
function formatValidationErrors(errors) {
  if (!errors || errors.length === 0) {
    return '';
  }
  
  if (errors.length === 1) {
    return errors[0];
  }
  
  return errors.map((error, index) => `${index + 1}. ${error}`).join('\n');
}

/**
 * 创建统一的验证结果
 */
function createValidationResult(isValid, errors = [], data = null) {
  return {
    isValid,
    errors,
    data: isValid ? data : null,
    message: isValid ? '验证通过' : formatValidationErrors(errors)
  };
}

module.exports = {
  validateStudentData,
  validateClassData,
  validateCommentParams,
  validateRecordData,
  validateUserInfo,
  validateAIConfig,
  sanitizeData,
  formatValidationErrors,
  createValidationResult
};
