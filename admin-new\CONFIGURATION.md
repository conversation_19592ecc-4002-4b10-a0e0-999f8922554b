# 环境配置指南

## 📋 配置文件说明

项目使用多个环境配置文件来管理不同环境下的配置：

### 文件优先级
```
.env.local > .env.[mode] > .env.example
```

### 配置文件类型

| 文件名 | 用途 | Git追踪 | 说明 |
|--------|------|---------|------|
| `.env.example` | 模板文件 | ✅ | 包含所有可配置项的示例 |
| `.env.development` | 开发环境 | ✅ | 开发环境特定配置 |
| `.env.production` | 生产环境 | ✅ | 生产环境特定配置 |
| `.env.local` | 本地配置 | ❌ | 本地开发的敏感配置 |

## 🚀 快速配置

### 1. 创建本地配置文件
```bash
# 复制示例文件
npm run env:example

# 或手动复制
cp .env.example .env.local
```

### 2. 配置关键参数

编辑 `.env.local` 文件，填入你的实际配置：

```bash
# 必须配置的参数
VITE_API_BASE_URL=https://your-env-id.ap-beijing.app.tcloudbase.com/admin-api
VITE_WECHAT_CLOUD_ENV_ID=cloud1-4g85f8xlb8166ff1
```

### 3. 验证配置
```bash
# 验证环境变量配置
npm run env:check
```

## 📝 配置详解

### 应用配置
```bash
# 应用标题
VITE_APP_TITLE=评语灵感君管理后台

# 应用版本
VITE_APP_VERSION=2.0.0

# 应用描述
VITE_APP_DESCRIPTION=现代化AI评语生成系统管理平台
```

### API配置
```bash
# 主API地址 - 云函数网关地址
VITE_API_BASE_URL=https://your-env-id.ap-beijing.app.tcloudbase.com/admin-api

# 文件上传地址 - 云存储地址
VITE_UPLOAD_BASE_URL=https://your-env-id.tcb.qcloud.la
```

### 微信云开发配置
```bash
# 云开发环境ID - 在微信开发者工具中查看
VITE_WECHAT_CLOUD_ENV_ID=cloud1-4g85f8xlb8166ff1

# 云开发区域
VITE_WECHAT_CLOUD_REGION=ap-beijing
```

### 功能开关
```bash
# 启用Mock数据 - 开发环境推荐true，生产环境必须false
VITE_ENABLE_MOCK=false

# 启用开发工具 - 生产环境必须false
VITE_ENABLE_DEVTOOLS=true

# 启用PWA功能
VITE_ENABLE_PWA=true

# 启用错误上报
VITE_ENABLE_ERROR_REPORTING=true
```

### AI服务配置
```bash
# 支持的AI服务商
VITE_AI_PROVIDERS=doubao,openai,wenxin,tongyi

# 最大上传文件大小 (字节)
VITE_MAX_UPLOAD_SIZE=10485760

# 支持的文件类型
VITE_SUPPORTED_FILE_TYPES=.jpg,.jpeg,.png,.pdf,.xlsx,.csv
```

### 缓存和性能配置
```bash
# 缓存过期时间 (毫秒)
VITE_CACHE_TTL=300000

# 请求超时时间 (毫秒)
VITE_REQUEST_TIMEOUT=30000

# 最大重试次数
VITE_MAX_RETRIES=3
```

### 安全配置
```bash
# 启用CSRF保护
VITE_ENABLE_CSRF=true

# 会话超时时间 (毫秒)
VITE_SESSION_TIMEOUT=3600000

# Token刷新阈值 (毫秒)
VITE_TOKEN_REFRESH_THRESHOLD=300000
```

## 🔧 获取配置值的方法

### 1. 获取云开发环境ID
```bash
# 方法1: 微信开发者工具
# 打开微信开发者工具 → 云开发 → 设置 → 环境ID

# 方法2: 查看现有项目配置
# 在你的小程序项目中查看 project.config.json
```

### 2. 获取云函数API地址
```bash
# 格式: https://环境ID.ap-beijing.app.tcloudbase.com/云函数名
# 示例: https://cloud1-4g85f8xlb8166ff1.ap-beijing.app.tcloudbase.com/adminAPI
```

### 3. 获取云存储地址
```bash
# 格式: https://环境ID.tcb.qcloud.la
# 示例: https://cloud1-4g85f8xlb8166ff1.tcb.qcloud.la
```

## 🌍 不同环境的配置策略

### 开发环境 (.env.development)
- 启用Mock数据
- 启用开发工具
- 启用Source Map
- 连接测试环境的云函数

### 生产环境 (.env.production)
- 禁用Mock数据
- 禁用开发工具
- 禁用Source Map
- 启用压缩和优化
- 启用错误监控

### 本地环境 (.env.local)
- 包含敏感配置信息
- 覆盖其他环境的配置
- 不提交到Git仓库

## ⚠️ 注意事项

### 安全提醒
1. **不要**在 `.env.example` 中包含真实的敏感信息
2. **不要**将 `.env.local` 提交到Git仓库
3. **不要**在生产环境中启用开发工具和Mock数据
4. **确保**生产环境中禁用Source Map

### 常见问题

#### Q: 环境变量修改后不生效？
A: 重启开发服务器：`npm run dev`

#### Q: 如何确认当前使用的环境变量？
A: 在浏览器控制台查看输出的环境配置信息（仅开发环境）

#### Q: 云函数连接失败？
A: 检查 `VITE_API_BASE_URL` 和 `VITE_WECHAT_CLOUD_ENV_ID` 是否配置正确

#### Q: 上传文件失败？
A: 检查 `VITE_UPLOAD_BASE_URL` 和文件大小限制配置

## 🛠 配置验证

项目提供了自动的配置验证功能：

```bash
# 验证所有环境配置文件
npm run env:check

# 在构建前自动验证
npm run build
```

验证内容包括：
- 必需参数是否配置
- URL格式是否正确
- 布尔值格式是否正确
- 端口冲突检查
- 生产环境安全检查

## 📞 获取帮助

如果配置过程中遇到问题：

1. 运行配置验证：`npm run env:check`
2. 查看控制台错误信息
3. 参考示例配置：`.env.example`
4. 联系技术支持