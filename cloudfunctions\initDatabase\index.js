// 初始化数据库 - 创建所有必要的集合和初始数据
const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()

exports.main = async (event, context) => {
  console.log('开始检查数据库状态...')

  try {
    const results = []

    // 首先检查现有集合
    console.log('检查现有数据库集合...')

    // 检查实际存在的集合和需要的集合
    const actualCollections = [
      'admins', 'ai_configs', 'ai_error_logs', 'ai_generation_logs', 'ai_usage',
      'classes', 'comments', 'files', 'logs', 'records', 'settings',
      'students', 'system_config'
    ]

    const neededCollections = [
      'admins', 'ai_configs', 'settings', 'logs', 'students', 'classes',
      'comments', 'records', 'files', 'ai_usage', 'ai_error_logs',
      'ai_generation_logs', 'system_config', 'notifications'
    ]

    const existingCollections = []
    const missingCollections = []

    for (const collectionName of neededCollections) {
      try {
        const count = await db.collection(collectionName).count()
        existingCollections.push({
          name: collectionName,
          count: count.total,
          status: 'exists'
        })
        console.log(`✅ ${collectionName}: ${count.total} 条记录`)
      } catch (error) {
        if (error.errCode === -502005) {
          missingCollections.push(collectionName)
          console.log(`❌ ${collectionName}: 不存在`)
        } else {
          console.log(`⚠️ ${collectionName}: 检查失败 - ${error.message}`)
        }
      }
    }

    results.push(`现有集合: ${existingCollections.length} 个`)
    results.push(`缺失集合: ${missingCollections.length} 个`)
    
    // 只创建缺失的集合
    for (const collectionName of missingCollections) {
      try {
        await db.createCollection(collectionName)
        console.log(`✅ 创建 ${collectionName} 集合成功`)
        results.push(`${collectionName} 集合创建成功`)
      } catch (error) {
        console.log(`❌ 创建 ${collectionName} 集合失败: ${error.message}`)
        results.push(`${collectionName} 集合创建失败: ${error.message}`)
      }
    }

    
    // 8. 创建默认管理员账号
    try {
      const adminCount = await db.collection('admins').count()
      if (adminCount.total === 0) {
        await db.collection('admins').add({
          data: {
            username: 'admin',
            password: 'admin123', // 生产环境应该加密
            email: '<EMAIL>',
            role: 'super_admin',
            permissions: ['*'],
            status: 'active',
            profile: {
              name: '超级管理员',
              avatar: '',
              phone: '',
              department: '系统管理部'
            },
            createdAt: db.serverDate(),
            updatedAt: db.serverDate()
          }
        })
        console.log('✅ 创建默认管理员账号成功')
        results.push('默认管理员账号创建成功 (用户名: admin, 密码: admin123)')
      } else {
        console.log('ℹ️ 管理员账号已存在')
        results.push('管理员账号已存在')
      }
    } catch (error) {
      console.error('创建默认管理员失败:', error)
      results.push('创建默认管理员失败: ' + error.message)
    }
    
    // 9. 创建默认AI模型配置
    try {
      const modelCount = await db.collection('ai_configs').count()
      if (modelCount.total === 0) {
        await db.collection('ai_configs').add({
          data: {
            name: 'doubao_default',
            displayName: '豆包AI',
            provider: 'doubao',
            config: {
              apiKey: '',
              model: 'ep-20241201193454-8xqzx',
              maxTokens: 2000,
              temperature: 0.7
            },
            status: 'active',
            isDefault: true,
            createdAt: db.serverDate(),
            updatedAt: db.serverDate()
          }
        })
        console.log('✅ 创建默认AI模型配置成功')
        results.push('默认AI模型配置创建成功')
      } else {
        console.log('ℹ️ AI模型配置已存在')
        results.push('AI模型配置已存在')
      }
    } catch (error) {
      console.error('创建默认AI模型配置失败:', error)
      results.push('创建默认AI模型配置失败: ' + error.message)
    }
    
    console.log('数据库检查完成!')

    return {
      code: 200,
      message: '数据库检查成功',
      data: {
        existingCollections: existingCollections,
        missingCollections: missingCollections,
        results: results,
        timestamp: new Date().toISOString(),
        environment: cloud.DYNAMIC_CURRENT_ENV
      }
    }
    
  } catch (error) {
    console.error('数据库初始化失败:', error)
    return {
      code: 500,
      message: '数据库初始化失败',
      error: error.message
    }
  }
}
