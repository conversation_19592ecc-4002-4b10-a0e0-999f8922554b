# 💻 PowerShell 命令指南

## 🎯 快速修复Rollup依赖错误

### 方法1：使用PowerShell脚本（推荐）
```powershell
# 修复依赖问题
.\fix-deps.ps1

# 启动项目
.\start.ps1

# 验证配置
.\validate.ps1
```

### 方法2：手动PowerShell命令
```powershell
# 删除旧依赖
Remove-Item -Path "node_modules" -Recurse -Force -ErrorAction SilentlyContinue
Remove-Item -Path "package-lock.json" -Force -ErrorAction SilentlyContinue

# 清理npm缓存
npm cache clean --force

# 重新安装依赖
npm install
```

### 方法3：一键命令
```powershell
# 复制粘贴这个完整命令
if(Test-Path "node_modules"){Remove-Item -Path "node_modules" -Recurse -Force}; if(Test-Path "package-lock.json"){Remove-Item -Path "package-lock.json" -Force}; npm cache clean --force; npm install
```

## 🚀 启动开发环境

### 完整流程
```powershell
# 1. 修复依赖（如果有问题）
.\fix-deps.ps1

# 2. 启动开发服务器
npm run dev

# 或者使用启动脚本
.\start.ps1
```

## 🔧 PowerShell执行策略

如果无法运行.ps1脚本，需要设置执行策略：

```powershell
# 检查当前策略
Get-ExecutionPolicy

# 设置允许本地脚本执行（推荐）
Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser

# 或者临时绕过策略执行单个脚本
PowerShell -ExecutionPolicy Bypass -File .\fix-deps.ps1
```

## 📋 常用PowerShell命令对照

| CMD命令 | PowerShell命令 |
|---------|----------------|
| `rmdir /s /q folder` | `Remove-Item -Path "folder" -Recurse -Force` |
| `del file.txt` | `Remove-Item -Path "file.txt" -Force` |
| `dir` | `Get-ChildItem` 或 `ls` |
| `cd folder` | `Set-Location folder` 或 `cd folder` |
| `copy file1 file2` | `Copy-Item file1 file2` |

## 🎯 立即解决Rollup问题

**选择一种方式执行：**

### 选项A：使用脚本
右键点击PowerShell文件 → "使用PowerShell运行"
- `fix-deps.ps1` - 修复依赖
- `start.ps1` - 启动项目

### 选项B：手动命令
在PowerShell中逐行执行：
```powershell
Remove-Item -Path "node_modules" -Recurse -Force -ErrorAction SilentlyContinue
Remove-Item -Path "package-lock.json" -Force -ErrorAction SilentlyContinue  
npm cache clean --force
npm install
npm run dev
```

### 选项C：一行解决
```powershell
if(Test-Path "node_modules"){Remove-Item -Path "node_modules" -Recurse -Force}; if(Test-Path "package-lock.json"){Remove-Item -Path "package-lock.json" -Force}; npm install; npm run dev
```

## ✅ 验证修复成功

修复成功后应该看到：
1. 依赖安装无错误
2. `npm run dev` 正常启动
3. 浏览器自动打开 http://localhost:3000
4. 控制台无Rollup相关错误

---

**选择最适合你的方法，立即开始修复！** 🚀