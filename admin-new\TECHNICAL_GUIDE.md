# 技术实现指南

## 🎨 核心设计系统

### 配色变量系统
```css
/* 主要渐变色组合 */
.gradient-primary { @apply bg-gradient-to-r from-blue-500 to-purple-600; }
.gradient-success { @apply bg-gradient-to-r from-green-500 to-emerald-500; }
.gradient-warning { @apply bg-gradient-to-r from-orange-500 to-red-600; }
.gradient-info { @apply bg-gradient-to-r from-purple-500 to-pink-500; }

/* 背景渐变 */
.bg-light { @apply bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50; }
.bg-dark { @apply bg-gradient-to-br from-gray-900 via-blue-900 to-indigo-900; }

/* 玻璃拟态效果 */
.glass-card { 
  @apply bg-white/80 dark:bg-gray-800/80 backdrop-blur-xl rounded-2xl 
         border border-white/50 dark:border-gray-700/50 shadow-xl transition-colors;
}
```

### 组件设计模式

#### 1. 统计卡片组件
```tsx
interface StatCardProps {
  title: string
  value: number | string
  icon: React.ReactNode
  trend?: { value: number; type: 'up' | 'down' }
  suffix?: string
  color: string
  bgColor: string
  description?: string
}

const StatCard: React.FC<StatCardProps> = ({ 
  title, value, icon, trend, suffix, color, bgColor, description 
}) => (
  <div className={`${bgColor} rounded-2xl p-6 border border-white/50 shadow-xl hover:shadow-2xl transition-all duration-300 hover:-translate-y-1 relative overflow-hidden group cursor-pointer`}>
    {/* 背景装饰 */}
    <div className="absolute top-0 right-0 w-32 h-32 opacity-10">
      <div className={`w-full h-full bg-gradient-to-br ${color} rounded-full transform translate-x-6 -translate-y-6 group-hover:scale-110 transition-transform duration-500`}></div>
    </div>
    
    <div className="relative z-10">
      <div className="flex items-center justify-between mb-4">
        <div className={`w-12 h-12 bg-gradient-to-r ${color} rounded-xl flex items-center justify-center text-white shadow-lg`}>
          {icon}
        </div>
        {trend && (
          <div className="flex items-center space-x-1">
            <ArrowUpOutlined className={trend.type === 'up' ? 'text-green-500' : 'text-red-500'} />
            <span className={`text-sm font-semibold ${trend.type === 'up' ? 'text-green-500' : 'text-red-500'}`}>
              {trend.value}%
            </span>
          </div>
        )}
      </div>
      
      <div className="space-y-2">
        <div className="text-3xl font-bold text-gray-800 dark:text-gray-100">
          {value}{suffix}
        </div>
        <div className="text-sm font-medium text-gray-600 dark:text-gray-300">
          {title}
        </div>
        {description && (
          <div className="text-xs text-gray-500 dark:text-gray-400">
            {description}
          </div>
        )}
      </div>
    </div>
  </div>
)
```

#### 2. 实时数据Hook
```tsx
interface UseDataReceiverOptions {
  enabled: boolean
  autoConnect: boolean
  reconnectInterval?: number
}

interface DataReceiverState {
  users: any[]
  activities: any[]
  comments: any[]
  statistics: any
  systemStatus: any
  isConnected: boolean
  connectionError: string | null
  lastUpdate: number
}

export const useDataReceiver = (options: UseDataReceiverOptions): DataReceiverState & {
  addTestData: () => void
} => {
  const [state, setState] = useState<DataReceiverState>({
    users: [],
    activities: [],
    comments: [],
    statistics: {},
    systemStatus: {},
    isConnected: false,
    connectionError: null,
    lastUpdate: 0
  })

  const [ws, setWs] = useState<WebSocket | null>(null)

  // WebSocket连接管理
  const connect = useCallback(() => {
    if (!options.enabled) return

    try {
      const websocket = new WebSocket('ws://localhost:3001')
      
      websocket.onopen = () => {
        setState(prev => ({ ...prev, isConnected: true, connectionError: null }))
      }

      websocket.onmessage = (event) => {
        const data = JSON.parse(event.data)
        setState(prev => ({
          ...prev,
          ...data,
          lastUpdate: Date.now()
        }))
      }

      websocket.onclose = () => {
        setState(prev => ({ ...prev, isConnected: false }))
        // 自动重连
        if (options.autoConnect) {
          setTimeout(connect, options.reconnectInterval || 5000)
        }
      }

      websocket.onerror = (error) => {
        setState(prev => ({ 
          ...prev, 
          connectionError: '连接失败，使用模拟数据',
          isConnected: false 
        }))
      }

      setWs(websocket)
    } catch (error) {
      setState(prev => ({ 
        ...prev, 
        connectionError: '无法建立连接，使用模拟数据',
        isConnected: false 
      }))
    }
  }, [options.enabled, options.autoConnect, options.reconnectInterval])

  // 添加测试数据
  const addTestData = useCallback(() => {
    const newActivity = {
      id: Date.now().toString(),
      wechatName: `测试用户${Math.floor(Math.random() * 100)}`,
      action: '生成了AI评语',
      timestamp: Date.now(),
      details: '测试数据'
    }

    setState(prev => ({
      ...prev,
      activities: [newActivity, ...prev.activities].slice(0, 10),
      lastUpdate: Date.now()
    }))
  }, [])

  useEffect(() => {
    if (options.enabled && options.autoConnect) {
      connect()
    }

    return () => {
      if (ws) {
        ws.close()
      }
    }
  }, [connect, options.enabled, options.autoConnect])

  return { ...state, addTestData }
}
```

#### 3. Excel导出工具函数
```tsx
import * as XLSX from 'xlsx'

interface ExportOptions {
  filename: string
  sheetName: string
  data: any[]
  columns?: { key: string; title: string; width?: number }[]
}

export const exportToExcel = (options: ExportOptions) => {
  try {
    const { filename, sheetName, data, columns } = options
    
    // 创建工作簿
    const wb = XLSX.utils.book_new()
    
    // 如果指定了列配置，则按配置导出
    let exportData = data
    if (columns) {
      exportData = data.map(item => {
        const newItem: any = {}
        columns.forEach(col => {
          newItem[col.title] = item[col.key]
        })
        return newItem
      })
    }
    
    // 创建工作表
    const ws = XLSX.utils.json_to_sheet(exportData)
    
    // 设置列宽
    if (columns) {
      ws['!cols'] = columns.map(col => ({ wch: col.width || 15 }))
    }
    
    XLSX.utils.book_append_sheet(wb, ws, sheetName)
    
    // 导出文件
    const timestamp = new Date().toISOString().slice(0, 19).replace(/:/g, '-')
    XLSX.writeFile(wb, `${filename}_${timestamp}.xlsx`)
    
    return { success: true, message: '导出成功' }
  } catch (error) {
    console.error('导出失败:', error)
    return { success: false, message: '导出失败' }
  }
}

// 使用示例
const handleExport = () => {
  const result = exportToExcel({
    filename: '学生数据',
    sheetName: '学生信息',
    data: studentsData,
    columns: [
      { key: 'id', title: '学号', width: 10 },
      { key: 'name', title: '姓名', width: 12 },
      { key: 'class', title: '班级', width: 15 },
      { key: 'grade', title: '年级', width: 10 }
    ]
  })
  
  if (result.success) {
    message.success(result.message)
  } else {
    message.error(result.message)
  }
}
```

## 🎯 关键技术实现

### 1. 暗黑模式实现
```tsx
// 主题Context
const ThemeContext = createContext<{
  theme: 'light' | 'dark'
  toggleTheme: () => void
}>({
  theme: 'light',
  toggleTheme: () => {}
})

// 主题Provider
export const ThemeProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [theme, setTheme] = useState<'light' | 'dark'>(() => {
    const saved = localStorage.getItem('theme')
    return (saved as 'light' | 'dark') || 'light'
  })

  const toggleTheme = useCallback(() => {
    setTheme(prev => {
      const newTheme = prev === 'light' ? 'dark' : 'light'
      localStorage.setItem('theme', newTheme)
      return newTheme
    })
  }, [])

  useEffect(() => {
    document.documentElement.setAttribute('data-theme', theme)
    if (theme === 'dark') {
      document.documentElement.classList.add('dark')
    } else {
      document.documentElement.classList.remove('dark')
    }
  }, [theme])

  return (
    <ThemeContext.Provider value={{ theme, toggleTheme }}>
      {children}
    </ThemeContext.Provider>
  )
}

// 使用Hook
export const useTheme = () => {
  const context = useContext(ThemeContext)
  if (!context) {
    throw new Error('useTheme must be used within ThemeProvider')
  }
  return context
}
```

### 2. 响应式布局系统
```tsx
// 响应式Hook
export const useResponsive = () => {
  const [screenSize, setScreenSize] = useState({
    width: window.innerWidth,
    height: window.innerHeight
  })

  const [breakpoint, setBreakpoint] = useState<'xs' | 'sm' | 'md' | 'lg' | 'xl' | '2xl'>('lg')

  useEffect(() => {
    const handleResize = () => {
      const width = window.innerWidth
      setScreenSize({ width, height: window.innerHeight })

      if (width < 640) setBreakpoint('xs')
      else if (width < 768) setBreakpoint('sm')
      else if (width < 1024) setBreakpoint('md')
      else if (width < 1280) setBreakpoint('lg')
      else if (width < 1536) setBreakpoint('xl')
      else setBreakpoint('2xl')
    }

    window.addEventListener('resize', handleResize)
    handleResize()

    return () => window.removeEventListener('resize', handleResize)
  }, [])

  return {
    ...screenSize,
    breakpoint,
    isMobile: breakpoint === 'xs' || breakpoint === 'sm',
    isTablet: breakpoint === 'md',
    isDesktop: breakpoint === 'lg' || breakpoint === 'xl' || breakpoint === '2xl'
  }
}

// 响应式组件示例
const ResponsiveGrid: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { isMobile, isTablet } = useResponsive()

  const gridCols = useMemo(() => {
    if (isMobile) return 'grid-cols-1'
    if (isTablet) return 'grid-cols-2'
    return 'grid-cols-4'
  }, [isMobile, isTablet])

  return (
    <div className={`grid ${gridCols} gap-6`}>
      {children}
    </div>
  )
}
```

### 3. 性能优化技巧
```tsx
// 虚拟化长列表
import { FixedSizeList as List } from 'react-window'

const VirtualizedTable: React.FC<{ data: any[]; height: number }> = ({ data, height }) => {
  const Row = ({ index, style }: { index: number; style: React.CSSProperties }) => (
    <div style={style} className="flex items-center p-4 border-b">
      {/* 行内容 */}
      <span>{data[index].name}</span>
    </div>
  )

  return (
    <List
      height={height}
      itemCount={data.length}
      itemSize={60}
      width="100%"
    >
      {Row}
    </List>
  )
}

// 防抖Hook
export const useDebounce = <T>(value: T, delay: number): T => {
  const [debouncedValue, setDebouncedValue] = useState<T>(value)

  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedValue(value)
    }, delay)

    return () => {
      clearTimeout(handler)
    }
  }, [value, delay])

  return debouncedValue
}

// 节流Hook
export const useThrottle = <T>(value: T, limit: number): T => {
  const [throttledValue, setThrottledValue] = useState<T>(value)
  const lastRan = useRef(Date.now())

  useEffect(() => {
    const handler = setTimeout(() => {
      if (Date.now() - lastRan.current >= limit) {
        setThrottledValue(value)
        lastRan.current = Date.now()
      }
    }, limit - (Date.now() - lastRan.current))

    return () => {
      clearTimeout(handler)
    }
  }, [value, limit])

  return throttledValue
}
```

## 🔧 开发工具配置

### Vite配置
```typescript
// vite.config.ts
import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'
import path from 'path'

export default defineConfig({
  plugins: [react()],
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src'),
      '@components': path.resolve(__dirname, './src/components'),
      '@pages': path.resolve(__dirname, './src/pages'),
      '@hooks': path.resolve(__dirname, './src/hooks'),
      '@utils': path.resolve(__dirname, './src/utils')
    }
  },
  css: {
    postcss: {
      plugins: [
        require('tailwindcss'),
        require('autoprefixer')
      ]
    }
  },
  build: {
    rollupOptions: {
      output: {
        manualChunks: {
          vendor: ['react', 'react-dom'],
          antd: ['antd'],
          charts: ['recharts', 'xlsx']
        }
      }
    }
  }
})
```

### TypeScript配置
```json
{
  "compilerOptions": {
    "target": "ES2020",
    "useDefineForClassFields": true,
    "lib": ["ES2020", "DOM", "DOM.Iterable"],
    "module": "ESNext",
    "skipLibCheck": true,
    "moduleResolution": "bundler",
    "allowImportingTsExtensions": true,
    "resolveJsonModule": true,
    "isolatedModules": true,
    "noEmit": true,
    "jsx": "react-jsx",
    "strict": true,
    "noUnusedLocals": true,
    "noUnusedParameters": true,
    "noFallthroughCasesInSwitch": true,
    "baseUrl": ".",
    "paths": {
      "@/*": ["src/*"],
      "@components/*": ["src/components/*"],
      "@pages/*": ["src/pages/*"],
      "@hooks/*": ["src/hooks/*"],
      "@utils/*": ["src/utils/*"]
    }
  },
  "include": ["src"],
  "references": [{ "path": "./tsconfig.node.json" }]
}
```

### Tailwind配置
```javascript
// tailwind.config.js
module.exports = {
  content: [
    "./index.html",
    "./src/**/*.{js,ts,jsx,tsx}",
  ],
  darkMode: 'class',
  theme: {
    extend: {
      colors: {
        primary: {
          50: '#eff6ff',
          500: '#3b82f6',
          600: '#2563eb',
          900: '#1e3a8a'
        }
      },
      animation: {
        'fade-in': 'fadeIn 0.5s ease-in-out',
        'slide-up': 'slideUp 0.3s ease-out',
        'pulse-slow': 'pulse 3s infinite'
      },
      keyframes: {
        fadeIn: {
          '0%': { opacity: '0' },
          '100%': { opacity: '1' }
        },
        slideUp: {
          '0%': { transform: 'translateY(10px)', opacity: '0' },
          '100%': { transform: 'translateY(0)', opacity: '1' }
        }
      }
    }
  },
  plugins: []
}
```

## 📦 部署配置

### Docker配置
```dockerfile
# Dockerfile
FROM node:18-alpine as builder

WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production

COPY . .
RUN npm run build

FROM nginx:alpine
COPY --from=builder /app/dist /usr/share/nginx/html
COPY nginx.conf /etc/nginx/nginx.conf

EXPOSE 80
CMD ["nginx", "-g", "daemon off;"]
```

### Nginx配置
```nginx
# nginx.conf
server {
    listen 80;
    server_name localhost;
    root /usr/share/nginx/html;
    index index.html;

    # 启用gzip压缩
    gzip on;
    gzip_types text/plain text/css application/json application/javascript text/xml application/xml application/xml+rss text/javascript;

    # 静态资源缓存
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }

    # SPA路由支持
    location / {
        try_files $uri $uri/ /index.html;
    }

    # API代理
    location /api/ {
        proxy_pass http://backend:3000/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

---

这份技术指南提供了项目的核心技术实现细节，可以作为后续开发和维护的参考文档。
