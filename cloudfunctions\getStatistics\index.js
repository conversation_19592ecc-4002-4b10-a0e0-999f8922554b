/**
 * 数据统计云函数
 * 获取用户的各项数据统计信息
 */

const cloud = require('wx-server-sdk');

// 初始化云开发环境
cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
});

const db = cloud.database();
const _ = db.command;

/**
 * 云函数入口函数
 */
exports.main = async (event, context) => {
  const { type = 'all' } = event;
  
  try {
    // 获取用户信息
    const { OPENID } = cloud.getWXContext();
    
    let result = {};
    
    switch (type) {
      case 'comments':
        result = await getCommentStatistics(OPENID);
        break;
      case 'students':
        result = await getStudentStatistics(OPENID);
        break;
      case 'records':
        result = await getRecordStatistics(OPENID);
        break;
      case 'all':
      default:
        result = await getAllStatistics(OPENID);
        break;
    }
    
    return {
      success: true,
      data: result
    };
    
  } catch (error) {
    console.error('获取统计数据失败:', error);
    return {
      success: false,
      error: error.message || '获取统计数据失败'
    };
  }
};

/**
 * 获取评语统计
 */
async function getCommentStatistics(teacherId) {
  const now = new Date();
  const todayStart = new Date(now.getFullYear(), now.getMonth(), now.getDate());
  const weekStart = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
  const monthStart = new Date(now.getFullYear(), now.getMonth(), 1);
  
  // 总评语数
  const totalResult = await db.collection('comments')
    .where({ teacherId })
    .count();
  
  // 今日评语数
  const todayResult = await db.collection('comments')
    .where({
      teacherId,
      createTime: _.gte(todayStart)
    })
    .count();
  
  // 本周评语数
  const weekResult = await db.collection('comments')
    .where({
      teacherId,
      createTime: _.gte(weekStart)
    })
    .count();
  
  // 本月评语数
  const monthResult = await db.collection('comments')
    .where({
      teacherId,
      createTime: _.gte(monthStart)
    })
    .count();
  
  // 风格分布
  const styleStats = await getStyleDistribution(teacherId);
  
  return {
    total: totalResult.total || 0,
    today: todayResult.total || 0,
    thisWeek: weekResult.total || 0,
    thisMonth: monthResult.total || 0,
    styleDistribution: styleStats
  };
}

/**
 * 获取学生统计
 */
async function getStudentStatistics(teacherId) {
  // 总学生数
  const totalResult = await db.collection('students')
    .where({ teacherId })
    .count();
  
  // 班级分布
  const classStats = await getClassDistribution(teacherId);
  
  return {
    total: totalResult.total || 0,
    classDistribution: classStats
  };
}

/**
 * 获取记录统计
 */
async function getRecordStatistics(teacherId) {
  const now = new Date();
  const monthStart = new Date(now.getFullYear(), now.getMonth(), 1);
  
  // 总记录数
  const totalResult = await db.collection('records')
    .where({ teacherId })
    .count();
  
  // 本月记录数
  const monthResult = await db.collection('records')
    .where({
      teacherId,
      createTime: _.gte(monthStart)
    })
    .count();
  
  // 类型分布
  const typeStats = await getRecordTypeDistribution(teacherId);
  
  return {
    total: totalResult.total || 0,
    thisMonth: monthResult.total || 0,
    typeDistribution: typeStats
  };
}

/**
 * 获取全部统计
 */
async function getAllStatistics(teacherId) {
  const [commentStats, studentStats, recordStats] = await Promise.all([
    getCommentStatistics(teacherId),
    getStudentStatistics(teacherId),
    getRecordStatistics(teacherId)
  ]);
  
  return {
    comments: commentStats,
    students: studentStats,
    records: recordStats,
    summary: {
      totalComments: commentStats.total,
      totalStudents: studentStats.total,
      totalRecords: recordStats.total,
      todayComments: commentStats.today
    }
  };
}

/**
 * 获取风格分布
 */
async function getStyleDistribution(teacherId) {
  const styles = ['formal', 'warm', 'encouraging', 'detailed'];
  const distribution = {};
  
  for (const style of styles) {
    const result = await db.collection('comments')
      .where({
        teacherId,
        style
      })
      .count();
    distribution[style] = result.total || 0;
  }
  
  return distribution;
}

/**
 * 获取班级分布
 */
async function getClassDistribution(teacherId) {
  try {
    const result = await db.collection('students')
      .where({ teacherId })
      .field({ className: true })
      .get();
    
    const distribution = {};
    result.data.forEach(student => {
      const className = student.className || '未分班';
      distribution[className] = (distribution[className] || 0) + 1;
    });
    
    return distribution;
  } catch (error) {
    console.error('获取班级分布失败:', error);
    return {};
  }
}

/**
 * 获取记录类型分布
 */
async function getRecordTypeDistribution(teacherId) {
  const types = ['positive', 'negative', 'neutral'];
  const distribution = {};
  
  for (const type of types) {
    const result = await db.collection('records')
      .where({
        teacherId,
        type
      })
      .count();
    distribution[type] = result.total || 0;
  }
  
  return distribution;
}
