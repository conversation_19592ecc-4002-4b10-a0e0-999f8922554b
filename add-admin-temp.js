/**
 * 临时添加管理员权限脚本
 * 将当前用户添加为管理员，然后可以正常使用原有同步脚本
 * 在小程序开发者工具的控制台中运行此脚本
 */

console.log('🔧 开始添加临时管理员权限...');

// 获取当前用户的openid
const currentOpenId = wx.cloud.getWXContext ? wx.cloud.getWXContext().OPENID : 'temp_openid';

console.log('👤 当前用户OpenID:', currentOpenId);

// 管理员数据
const adminData = {
  openid: currentOpenId,
  username: '临时管理员',
  role: 'super_admin',
  permissions: ['ai_manage', 'data_manage', 'system_manage', 'user_manage'],
  status: 'active',
  profile: {
    name: '临时管理员',
    email: '<EMAIL>',
    phone: ''
  },
  createTime: new Date(),
  updateTime: new Date(),
  createTimestamp: Date.now(),
  updateTimestamp: Date.now(),
  isTemporary: true // 标记为临时管理员
};

// 检查是否已经是管理员
wx.cloud.database().collection('admins')
  .where({
    openid: currentOpenId,
    status: 'active'
  })
  .get()
  .then(existingAdmin => {
    if (existingAdmin.data.length > 0) {
      console.log('✅ 您已经是管理员，无需重复添加');
      console.log('👤 管理员信息:', existingAdmin.data[0]);
      return Promise.resolve('already_admin');
    } else {
      console.log('📝 添加管理员权限...');
      return wx.cloud.database().collection('admins').add({
        data: adminData
      });
    }
  })
  .then(result => {
    if (result === 'already_admin') {
      console.log('\n🎯 现在可以运行原始同步脚本了:');
    } else {
      console.log('✅ 管理员权限添加成功!');
      console.log('📄 管理员ID:', result._id);
      console.log('\n🎯 现在可以运行原始同步脚本了:');
    }
    
    console.log(`
// 复制下面的脚本并在控制台中执行:
const aiConfigData = {
  type: 'ai_config',
  status: 'active',
  model: 'doubao-seed-1-6-flash-250715',
  provider: 'bytedance',
  apiKey: '4d73215c-5512-418b-8749-db9514df3c75',
  apiUrl: 'https://ark.cn-beijing.volces.com/api/v3/chat/completions',
  temperature: 0.7,
  maxTokens: 2000,
  topP: 0.9,
  frequencyPenalty: 0,
  presencePenalty: 0,
  enableStream: false,
  enableCache: true,
  timeout: 30,
  updateTime: new Date().toISOString()
};

wx.cloud.callFunction({
  name: 'adminAPI',
  data: {
    action: 'ai.saveConfig',
    ...aiConfigData
  }
}).then(result => {
  console.log('AI配置同步结果:', result);
  if (result.result && result.result.code === 200) {
    console.log('✅ 配置同步成功!');
  } else {
    console.error('❌ 配置同步失败:', result.result?.message);
  }
}).catch(error => {
  console.error('❌ 同步过程出错:', error);
});
`);
  })
  .catch(error => {
    console.error('❌ 添加管理员权限失败:', error);
    console.log('\n🔧 建议使用直接同步脚本: sync-ai-config-direct.js');
  });

console.log('⏳ 正在处理管理员权限...');