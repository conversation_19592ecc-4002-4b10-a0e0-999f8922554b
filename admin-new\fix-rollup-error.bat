@echo off
echo =============================================
echo   修复Rollup依赖错误 - 专项修复
echo =============================================
echo.

echo 🎯 针对性修复rollup win32-x64-msvc依赖问题...
echo.

echo 步骤1: 删除现有文件
if exist "node_modules" (
    echo 删除 node_modules...
    rmdir /s /q node_modules
)

if exist "package-lock.json" (
    echo 删除 package-lock.json...
    del package-lock.json
)

echo.
echo 步骤2: 清理npm缓存
npm cache clean --force

echo.
echo 步骤3: 使用特定参数重新安装
echo 这会解决rollup的optional dependency问题...
npm install --no-optional --legacy-peer-deps

echo.
echo 步骤4: 手动安装rollup依赖
npm install @rollup/rollup-win32-x64-msvc --save-dev --no-optional

echo.
echo 步骤5: 验证安装
echo 检查rollup是否正常...
npm list rollup

echo.
echo 步骤6: 尝试启动开发服务器
echo ✅ 修复完成，启动开发服务器...
npm run dev

echo.
echo =============================================
echo   如果还有问题，请查看输出信息
echo =============================================
pause