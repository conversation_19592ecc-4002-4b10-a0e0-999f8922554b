# 🔗 评语灵感君前后端数据连通架构文档

## 📋 架构总览

### 🎯 设计原则
- **职责分离**: 小程序负责业务功能，管理后台负责数据管理
- **统一入口**: 前端通过统一的服务层访问数据
- **错误降级**: 提供默认数据，确保前端稳定运行
- **权限验证**: 管理操作需要完整的权限检查

### 🏗️ 系统架构图
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   小程序端      │    │   管理后台      │    │   云数据库      │
│                 │    │                 │    │                 │
│  ┌─────────┐   │    │  ┌─────────┐   │    │  ┌─────────┐   │
│  │业务页面 │   │    │  │React组件│   │    │  │10个集合 │   │
│  └─────────┘   │    │  └─────────┘   │    │  └─────────┘   │
│       │        │    │       │        │    │       ↑        │
│  ┌─────────┐   │    │  ┌─────────┐   │    │       │        │
│  │云函数调用│   │    │  │数据服务 │   │    │       │        │
│  └─────────┘   │    │  └─────────┘   │    │       │        │
└─────────────────┘    └─────────────────┘    └─────────────────┘
       │                        │                        ↑
       │                        │                        │
       ▼                        ▼                        │
┌─────────────────┐    ┌─────────────────┐               │
│   云函数层      │    │   HTTP API      │               │
│                 │    │                 │               │
│ generateComment │    │    adminAPI     │───────────────┘
│ doubaoAI        │    │                 │
│ login           │    │ (HTTP触发器)    │
│ getStudents     │    │                 │
│ ...             │    │                 │
└─────────────────┘    └─────────────────┘
       │                        │
       └────────────────────────┘
                 │
                 ▼
       ┌─────────────────┐
       │   云数据库      │
       │                 │
       │ 10个核心集合    │
       │ + 完整索引      │
       └─────────────────┘
```

---

## 🎯 小程序端数据连通

### 📱 技术栈
- **框架**: 微信小程序原生 + TypeScript
- **云服务**: 腾讯云开发 (cloud1-4g85f8xlb8166ff1)
- **数据连接**: wx.cloud.callFunction()
- **状态管理**: 页面级data + globalData

### 🔌 连接方式

#### 1. 云函数直接调用
```javascript
// 示例：生成评语
const result = await wx.cloud.callFunction({
  name: 'generateComment',
  data: {
    studentId: 'student123',
    options: {
      style: 'warm',
      length: 'medium',
      includeAdvice: true
    }
  }
})

if (result.result.success) {
  // 处理成功结果
  const comment = result.result.data
  console.log('评语生成成功:', comment.content)
} else {
  // 处理错误
  console.error('评语生成失败:', result.result.error)
}
```

#### 2. 用户认证流程
```javascript
// 用户登录
const loginResult = await wx.cloud.callFunction({
  name: 'login',
  data: {
    userInfo: app.globalData.userInfo
  }
})

// 存储用户信息
app.globalData.userInfo = loginResult.result.data.userInfo
app.globalData.isLoggedIn = true
```

#### 3. 学生管理
```javascript
// 获取学生列表
const studentsResult = await wx.cloud.callFunction({
  name: 'getStudents',
  data: {
    page: 1,
    limit: 20,
    keyword: '张三'
  }
})

const students = studentsResult.result.data.list
```

#### 4. AI调用流程
```javascript
// 步骤1: generateComment收集数据并构建提示词
// 步骤2: generateComment调用doubaoAI
// 步骤3: doubaoAI返回生成内容
// 步骤4: generateComment保存到数据库

const aiResult = await wx.cloud.callFunction({
  name: 'generateComment',
  data: {
    studentId: 'student123',
    options: {
      style: 'encouraging',
      focus: ['学习态度', '团队合作']
    }
  }
})
```

### 📊 数据流向
```
小程序页面 → wx.cloud.callFunction() → 云函数 → 云数据库
    ↑                                            ↓
响应数据处理 ←────────────────────────────── 数据处理和返回
```

---

## 🖥️ 管理后台数据连通

### 💻 技术栈
- **框架**: React 18 + TypeScript + Vite
- **UI库**: Ant Design + Tailwind CSS
- **HTTP客户端**: 云开发Web SDK + Fetch API
- **状态管理**: 局部状态 + SWR缓存

### 🔌 连接方式

#### 1. 数据查询 (通过dataQuery云函数)
```typescript
// services/index.ts - 统一数据服务
export class UnifiedDataService {
  async getDashboardStats() {
    try {
      const app = cloudbaseService.getApp()
      const result = await app.callFunction({
        name: 'dataQuery',
        data: {
          action: 'getDashboardStats'
        }
      })
      
      if (result.result.code === 200) {
        return result.result.data
      } else {
        throw new Error(result.result.message)
      }
    } catch (error) {
      // 错误降级处理
      return {
        totalUsers: 0,
        todayComments: 0,
        aiCalls: 0,
        satisfaction: 0
      }
    }
  }
}
```

#### 2. 管理操作 (通过adminAPI HTTP触发器)
```typescript
// 管理操作使用HTTP调用，确保module.method格式
export const adminService = {
  // 认证相关
  login: (credentials: any) => 
    unifiedDataService.callAdminAPI('auth.login', credentials),
  
  // 用户管理 
  getUsers: (params: any) => 
    unifiedDataService.callAdminAPI('data.getUsers', params),
  
  // AI配置
  updateAIConfig: (config: any) => 
    unifiedDataService.callAdminAPI('ai.updateConfig', config)
}

// HTTP调用实现
async callAdminAPI(action: string, data: any = {}) {
  const response = await fetch(this.adminApiUrl, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'X-WX-SOURCE': 'admin-dashboard'
    },
    body: JSON.stringify({
      action, // 格式: 'module.method'
      ...data,
      timestamp: Date.now()
    })
  })
  
  return await response.json()
}
```

#### 3. 实时数据更新
```typescript
// React组件中使用SWR进行数据获取和缓存
import useSWR from 'swr'

function Dashboard() {
  const { data: stats, error } = useSWR(
    'dashboard-stats',
    () => dataService.getDashboardStats(),
    { refreshInterval: 30000 } // 30秒自动刷新
  )
  
  const { data: activities } = useSWR(
    'recent-activities',
    () => dataService.getRecentActivities(10),
    { refreshInterval: 10000 } // 10秒自动刷新
  )
  
  return (
    <div>
      <StatisticsCards data={stats} />
      <ActivityList data={activities} />
    </div>
  )
}
```

### 📊 数据流向
```
React组件 → dataService → cloudbaseService → dataQuery云函数 → 云数据库
   ↑                                                              ↓
SWR缓存 ←─────────────────────────────────────────────── 数据处理和返回

React组件 → adminService → HTTP Fetch → adminAPI触发器 → 云数据库
   ↑                                                      ↓
错误处理 ←─────────────────────────────────────── 权限验证 + 数据操作
```

---

## 🔐 权限和认证体系

### 👤 小程序端认证
```javascript
// 微信授权 + 云开发OpenID
const { OPENID } = cloud.getWXContext()

// 权限验证在云函数中进行
async function checkUserPermission(openid) {
  const userResult = await db.collection('users')
    .where({ _id: openid })
    .get()
  
  if (userResult.data.length === 0) {
    throw new Error('用户不存在')
  }
  
  return userResult.data[0]
}
```

### 🔒 管理后台认证
```typescript
// JWT Token + 管理员权限验证
const adminHandler = {
  async login(credentials) {
    // 1. 验证用户名密码
    const admin = await validateCredentials(credentials)
    
    // 2. 生成JWT Token
    const token = generateJWT(admin)
    
    // 3. 记录登录日志
    await logAdminOperation('login', admin)
    
    return { admin, token }
  }
}

// 权限中间件
async function checkAdminPermission(event, requiredPermission) {
  const { OPENID } = cloud.getWXContext()
  
  const admin = await db.collection('admins')
    .where({ openid: OPENID, status: 'active' })
    .get()
  
  if (admin.data.length === 0) {
    throw new Error('无管理员权限')
  }
  
  const adminInfo = admin.data[0]
  
  if (adminInfo.role !== 'super_admin' && 
      !adminInfo.permissions.includes(requiredPermission)) {
    throw new Error(`缺少${requiredPermission}权限`)
  }
  
  return adminInfo
}
```

---

## 🗄️ 数据库设计

### 📚 核心集合详情

#### 1. users (用户信息)
```javascript
{
  _id: "用户OpenID",
  nickName: "张老师",
  school: "清华附中",
  role: "数学教师",
  phone: "13800138000",
  createTime: ISODate,
  updateTime: ISODate
}
// 索引: createTime_-1
```

#### 2. students (学生信息)
```javascript
{
  _id: "学生ID",
  name: "李小明",
  studentId: "2024001",
  classId: "class123",
  className: "高一(1)班",
  teacherId: "teacher_openid",
  gender: "男",
  createTime: ISODate
}
// 索引: teacherId_1_classId_1, studentId_1(unique)
```

#### 3. comments (评语数据)
```javascript
{
  _id: "评语ID",
  studentId: "student123",
  studentName: "李小明",
  content: "该学生本学期表现优秀...",
  style: "warm",
  length: "medium",
  aiGenerated: true,
  teacherId: "teacher_openid",
  createTime: ISODate
}
// 索引: teacherId_1_createTime_-1, studentId_1_createTime_-1
```

#### 4. records (行为记录)
```javascript
{
  _id: "记录ID",
  studentId: "student123",
  type: "positive", // positive/negative/neutral
  category: "学习态度",
  content: "课堂积极发言",
  score: 5,
  teacherId: "teacher_openid",
  createTime: ISODate
}
// 索引: teacherId_1_createTime_-1, studentId_1_createTime_-1
```

#### 5. admins (管理员信息)
```javascript
{
  _id: "管理员ID",
  username: "admin",
  password: "$2b$10$hashed_password",
  role: "super_admin", // super_admin/admin
  permissions: ["user_manage", "ai_config", "system_manage"],
  status: "active",
  lastLoginTime: ISODate
}
// 索引: username_1(unique), status_1_createTime_-1
```

### 🔍 索引优化策略
- **复合索引**: 查询条件 + 排序字段
- **唯一索引**: 防止数据重复
- **时间索引**: 支持时间范围查询
- **用户索引**: 按用户隔离数据

---

## 🚀 性能优化

### ⚡ 缓存策略
```typescript
// 1. 前端SWR缓存
const { data } = useSWR(
  'dashboard-stats',
  fetcher,
  { 
    refreshInterval: 30000,
    revalidateOnFocus: false,
    dedupingInterval: 5000
  }
)

// 2. 云函数级缓存
let dashboardCache = null
let cacheTimestamp = 0
const CACHE_TTL = 60 * 1000 // 1分钟

async function getDashboardStats() {
  const now = Date.now()
  
  if (dashboardCache && (now - cacheTimestamp) < CACHE_TTL) {
    return dashboardCache
  }
  
  dashboardCache = await fetchFromDatabase()
  cacheTimestamp = now
  
  return dashboardCache
}
```

### 🔧 错误处理
```typescript
// 分层错误处理
try {
  const result = await dataService.getDashboardStats()
  return result
} catch (error) {
  // 1. 记录错误日志
  console.error('数据获取失败:', error)
  
  // 2. 错误上报
  errorReporter.report(error)
  
  // 3. 返回默认数据
  return getDefaultDashboardStats()
}
```

### 📊 数据分页
```typescript
// 统一分页接口
interface PaginationParams {
  page: number
  limit: number
  keyword?: string
}

async function getStudents(params: PaginationParams) {
  const { page, limit, keyword } = params
  const skip = (page - 1) * limit
  
  let query = db.collection('students')
  
  if (keyword) {
    query = query.where({
      name: db.RegExp({ regexp: keyword, options: 'i' })
    })
  }
  
  const [countResult, dataResult] = await Promise.all([
    query.count(),
    query.orderBy('updateTime', 'desc')
         .skip(skip)
         .limit(limit)
         .get()
  ])
  
  return {
    list: dataResult.data,
    total: countResult.total,
    page,
    limit
  }
}
```

---

## 🔍 调试和监控

### 📝 日志记录
```javascript
// 云函数日志
console.log('📥 收到请求:', event)
console.log('✅ 处理成功:', result)
console.error('❌ 处理失败:', error)

// 管理操作日志
await db.collection('logs').add({
  data: {
    action: 'user.update',
    adminId: admin._id,
    adminName: admin.username,
    requestData: sanitizeData(event),
    result: 'success',
    createTime: new Date()
  }
})
```

### 📊 性能监控
```typescript
// 响应时间监控
const startTime = Date.now()
const result = await processRequest(event)
const duration = Date.now() - startTime

console.log(`🕐 ${event.action} 耗时: ${duration}ms`)

if (duration > 3000) {
  console.warn(`⚠️ 慢查询警告: ${event.action} 耗时 ${duration}ms`)
}
```

---

## 🛠️ 开发和部署

### 💻 本地开发
```bash
# 小程序端
1. 用微信开发者工具打开项目
2. 配置云开发环境: cloud1-4g85f8xlb8166ff1
3. 上传云函数到云端测试

# 管理后台
cd admin-new
npm install
npm run dev  # 启动开发服务器
```

### 🚀 生产部署
```bash
# 1. 云函数部署
微信开发者工具 → 云开发 → 云函数 → 上传并部署

# 2. 管理后台部署
cd admin-new
npm run build    # 构建生产版本
# 将dist目录部署到服务器或静态托管
```

### 🔧 环境配置
```typescript
// admin-new/.env.production
VITE_API_BASE_URL=https://cloud1-4g85f8xlb8166ff1-1365982463.ap-shanghai.app.tcloudbase.com/adminAPI
VITE_WECHAT_CLOUD_ENV_ID=cloud1-4g85f8xlb8166ff1
VITE_ENABLE_MOCK=false
```

---

## 📋 API接口文档

### 🔌 云函数接口

#### generateComment
```typescript
// 请求
{
  studentId: string,
  options: {
    style?: 'formal' | 'warm' | 'encouraging' | 'detailed',
    length?: 'short' | 'medium' | 'long',
    focus?: string[],
    includeAdvice?: boolean,
    customRequirement?: string
  }
}

// 响应
{
  success: boolean,
  data?: {
    _id: string,
    content: string,
    style: string,
    length: string,
    createTime: Date
  },
  error?: string
}
```

#### dataQuery
```typescript
// 请求
{
  action: 'getDashboardStats' | 'getRecentActivities' | 'getStudents' | 'getComments',
  params?: any
}

// 响应
{
  code: number,
  message: string,
  data: any,
  timestamp: string
}
```

### 🌐 HTTP API接口

#### adminAPI
```typescript
// 请求格式
POST /adminAPI
{
  action: string, // 格式: 'module.method'
  ...data,
  timestamp: number
}

// 响应格式
{
  code: number,
  message: string,
  data: any,
  timestamp: string
}

// 示例
// 登录
POST /adminAPI
{
  action: 'auth.login',
  username: 'admin',
  password: 'password'
}

// 获取用户列表
POST /adminAPI
{
  action: 'data.getUsers',
  page: 1,
  limit: 20
}
```

---

这份文档涵盖了评语灵感君项目的完整数据连通架构，包括小程序端、管理后台、云函数、数据库等所有层面的技术细节和最佳实践。