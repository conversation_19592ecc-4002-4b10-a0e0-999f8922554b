# PowerShell版本的启动脚本

Write-Host ""
Write-Host "=============================================" -ForegroundColor Cyan
Write-Host "   评语灵感君管理后台 v2.0 启动器" -ForegroundColor Cyan
Write-Host "=============================================" -ForegroundColor Cyan
Write-Host ""

# 检查node_modules是否存在
if (-not (Test-Path "node_modules")) {
    Write-Host "📦 Installing dependencies..." -ForegroundColor Yellow
    
    npm install
    
    if ($LASTEXITCODE -ne 0) {
        Write-Host "❌ Installation failed. This might be the rollup bug." -ForegroundColor Red
        Write-Host "🔧 Running fix-deps.ps1 to resolve the issue..." -ForegroundColor Yellow
        
        & ".\fix-deps.ps1"
        
        if ($LASTEXITCODE -ne 0) {
            Write-Host "❌ Failed to install dependencies" -ForegroundColor Red
            Write-Host "💡 Please run fix-deps.ps1 manually" -ForegroundColor Yellow
            Read-Host "Press Enter to exit"
            exit 1
        }
    }
    Write-Host "✅ Dependencies installed successfully" -ForegroundColor Green
    Write-Host ""
}

# 检查环境配置
Write-Host "🔧 Validating environment configuration..." -ForegroundColor Yellow
node scripts\validate-env.js

if ($LASTEXITCODE -ne 0) {
    Write-Host ""
    Write-Host "⚠️  Configuration issues found, but you can still continue." -ForegroundColor Yellow
    Write-Host "💡 The most important thing is that .env.local exists and is configured." -ForegroundColor Gray
    Write-Host ""
}

Write-Host "🚀 Starting development server..." -ForegroundColor Green
Write-Host ""
Write-Host "📱 The application will open at: http://localhost:3000" -ForegroundColor Cyan
Write-Host "🛑 Press Ctrl+C to stop the server" -ForegroundColor Gray
Write-Host ""

npm run dev