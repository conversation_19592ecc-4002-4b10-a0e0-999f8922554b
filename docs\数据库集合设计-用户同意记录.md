# 用户同意记录数据库集合设计

**设计目的：** 法律合规和举证需要  
**法律依据：** 《个人信息保护法》、《网络安全法》  
**保存期限：** 7年（法律要求）  
**设计日期：** 2025年8月1日  

---

## 📊 数据库集合概览

### 1. user_consent_records（用户同意记录表）
**用途：** 存储用户对隐私政策和用户协议的同意记录  
**重要性：** ⭐⭐⭐⭐⭐ 法律合规核心数据  

### 2. consent_audit_logs（同意审计日志表）
**用途：** 记录所有与同意相关的操作日志  
**重要性：** ⭐⭐⭐⭐ 审计和监管需要  

### 3. consent_error_logs（同意错误日志表）
**用途：** 记录同意记录过程中的错误信息  
**重要性：** ⭐⭐⭐ 系统维护和问题排查  

---

## 🗃️ 详细集合设计

### 1. user_consent_records 集合

```javascript
{
  _id: "自动生成的文档ID",
  
  // === 用户标识信息 ===
  userId: "用户ID（业务层面）",
  openId: "微信OpenID",
  unionId: "微信UnionID（可选）",
  
  // === 用户基本信息（脱敏处理） ===
  userNickname: "用户昵称",
  userAvatar: "用户头像URL",
  
  // === 同意信息 ===
  consentType: "同意类型", // 'privacy_policy' | 'user_agreement' | 'both'
  consentVersion: "协议版本号", // 如 '1.0', '1.1'
  consentStatus: "同意状态", // 'agreed' | 'withdrawn'
  consentTime: "同意时间（Date对象）",
  
  // === 技术信息（用于举证） ===
  ipAddress: "用户IP地址",
  userAgent: "用户代理字符串",
  deviceInfo: {
    platform: "平台", // 'ios' | 'android' | 'windows'
    system: "系统版本", // 'iOS 15.0' | 'Android 11'
    version: "微信版本",
    model: "设备型号",
    brand: "设备品牌",
    screenWidth: "屏幕宽度",
    screenHeight: "屏幕高度",
    SDKVersion: "小程序SDK版本"
  },
  
  // === 法律相关信息 ===
  legalBasis: "法律依据", // 'explicit_consent'
  dataProcessingPurpose: "数据处理目的", // 'service_provision'
  retentionPeriod: "保存期限", // '7_years'
  
  // === 系统信息 ===
  recordSource: "记录来源", // 'miniprogram_login' | 'settings_page'
  recordVersion: "记录格式版本", // '1.0'
  isActive: "是否有效", // true | false
  deactivatedReason: "失效原因", // 'version_updated' | 'user_withdrawn'
  
  // === 时间戳 ===
  createdAt: "创建时间（Date对象）",
  updatedAt: "更新时间（Date对象）"
}
```

**索引设计：**
```javascript
// 复合索引：用户ID + 同意类型 + 是否有效
db.user_consent_records.createIndex({
  "userId": 1,
  "consentType": 1,
  "isActive": 1
});

// 复合索引：OpenID + 同意时间（用于身份验证和时间排序）
db.user_consent_records.createIndex({
  "openId": 1,
  "consentTime": -1
});

// 单字段索引：创建时间（用于数据清理）
db.user_consent_records.createIndex({
  "createdAt": 1
});
```

### 2. consent_audit_logs 集合

```javascript
{
  _id: "自动生成的文档ID",
  
  // === 基本信息 ===
  userId: "用户ID",
  action: "操作类型", // 'consent_recorded' | 'consent_queried' | 'consent_withdrawn'
  consentType: "同意类型",
  consentVersion: "协议版本号",
  
  // === 操作信息 ===
  timestamp: "操作时间（Date对象）",
  ipAddress: "操作IP地址",
  userAgent: "用户代理字符串",
  recordId: "关联的同意记录ID",
  
  // === 详细信息 ===
  details: {
    source: "操作来源", // 'miniprogram_login' | 'user_query' | 'admin_operation'
    method: "操作方法", // 'create' | 'update' | 'query'
    requestor: "请求者OpenID",
    additionalInfo: "其他信息"
  }
}
```

**索引设计：**
```javascript
// 复合索引：用户ID + 时间戳
db.consent_audit_logs.createIndex({
  "userId": 1,
  "timestamp": -1
});

// 单字段索引：操作类型
db.consent_audit_logs.createIndex({
  "action": 1
});
```

### 3. consent_error_logs 集合

```javascript
{
  _id: "自动生成的文档ID",
  
  // === 错误信息 ===
  userId: "用户ID",
  consentType: "同意类型",
  error: "错误消息",
  stack: "错误堆栈",
  
  // === 上下文信息 ===
  timestamp: "错误时间（Date对象）",
  context: "错误上下文（原始请求数据）",
  
  // === 处理状态 ===
  resolved: "是否已解决", // true | false
  resolvedAt: "解决时间（Date对象）",
  resolvedBy: "解决人员"
}
```

---

## 🔒 数据安全和隐私保护

### 1. 数据加密
- **传输加密：** 所有数据传输使用HTTPS
- **存储加密：** 敏感字段进行加密存储
- **IP地址脱敏：** 存储时对IP地址进行部分脱敏

### 2. 访问控制
- **身份验证：** 用户只能查看自己的记录
- **权限控制：** 管理员需要特殊权限才能查看
- **操作日志：** 所有访问操作都有审计日志

### 3. 数据保护
- **最小化原则：** 只收集必要的信息
- **目的限制：** 数据仅用于法律合规目的
- **保存期限：** 严格按照7年期限保存

---

## ⚖️ 法律合规要点

### 1. 《个人信息保护法》合规

**第13条 - 同意的条件：**
- ✅ 记录了用户的明确同意
- ✅ 保存了同意的时间和方式
- ✅ 记录了同意的具体内容版本

**第14条 - 同意的撤回：**
- ✅ 支持用户撤回同意（注销功能）
- ✅ 记录撤回同意的时间和方式

**第51条 - 举证责任：**
- ✅ 保存完整的同意证据
- ✅ 包含技术信息用于举证
- ✅ 审计日志支持举证需要

### 2. 数据保存期限

**法律要求：** 7年保存期限
- 从用户最后一次同意时间开始计算
- 用户注销后仍需保存用于举证
- 超过期限后自动删除或匿名化

### 3. 监管配合

**数据提供：** 支持监管部门调取
- 完整的同意记录
- 详细的审计日志
- 技术实现说明文档

---

## 🔧 技术实现要点

### 1. 数据一致性
- 使用事务确保数据一致性
- 同时写入主记录和审计日志
- 错误时进行回滚处理

### 2. 性能优化
- 合理的索引设计
- 分页查询大量数据
- 定期清理过期数据

### 3. 容错处理
- 网络异常时的重试机制
- 数据库异常时的降级处理
- 错误日志记录和告警

---

## 📊 数据统计和分析

### 1. 合规统计
- 同意记录总数和增长趋势
- 不同协议版本的同意情况
- 用户撤回同意的统计

### 2. 技术统计
- 记录成功率和失败率
- 不同设备平台的分布
- 系统性能和响应时间

### 3. 法律风险评估
- 未记录同意的用户数量
- 协议版本更新的影响范围
- 潜在的合规风险点

---

## 🚀 部署和维护

### 1. 数据库部署
```bash
# 创建集合
db.createCollection("user_consent_records")
db.createCollection("consent_audit_logs")
db.createCollection("consent_error_logs")

# 创建索引
db.user_consent_records.createIndex({"userId": 1, "consentType": 1, "isActive": 1})
db.user_consent_records.createIndex({"openId": 1, "consentTime": -1})
db.user_consent_records.createIndex({"createdAt": 1})

db.consent_audit_logs.createIndex({"userId": 1, "timestamp": -1})
db.consent_audit_logs.createIndex({"action": 1})
```

### 2. 定期维护
- **数据备份：** 每日自动备份
- **性能监控：** 监控查询性能
- **数据清理：** 定期清理过期数据
- **安全审计：** 定期安全检查

### 3. 应急处理
- **数据恢复：** 备份数据恢复流程
- **安全事件：** 数据泄露应急预案
- **法律请求：** 监管部门数据调取流程

---

**总结：** 该数据库设计完全符合法律合规要求，为用户同意记录提供了完整的技术保障和法律举证支持。
