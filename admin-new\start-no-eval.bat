@echo off
chcp 65001 >nul
echo.
echo ==========================================
echo   评语灵感君 - 无eval安全启动器
echo ==========================================
echo.

echo 🔧 正在应用CSP安全配置...

REM 设置环境变量
set NODE_ENV=development
set VITE_CSP_MODE=safe

echo ✅ 环境配置完成
echo 📋 当前设置:
echo    - 开发模式: %NODE_ENV%
echo    - CSP模式: 安全模式（允许开发工具）
echo    - eval使用: 仅限开发工具
echo.

echo 🚀 启动开发服务器...
echo.
echo 💡 提示：
echo   - 如果仍有CSP错误，请清除浏览器缓存
echo   - 首次启动可能需要安装依赖
echo   - 服务器将在 http://localhost:8080 启动
echo.

REM 检查依赖
if not exist "node_modules\terser" (
    echo 📦 安装新依赖...
    call npm install terser --save-dev
)

REM 启动服务器
call npm run dev

if errorlevel 1 (
    echo.
    echo ❌ 启动失败，尝试备用方案...
    echo 🔄 使用无CSP配置启动...
    call npm run dev:no-csp
)

echo.
pause