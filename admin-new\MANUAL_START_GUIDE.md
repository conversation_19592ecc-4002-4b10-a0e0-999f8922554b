# 手动启动指南

由于Windows批处理文件可能存在中文编码问题，这里提供手动启动的详细步骤。

## 🚀 快速启动（推荐）

### 方法1：使用简单批处理
```bash
# 双击运行（无中文字符）
quick-start.bat
```

### 方法2：使用PowerShell（支持中文）
```powershell
# 右键 -> 以PowerShell运行
./start-test.ps1
```

## 📋 手动启动步骤

### 步骤1：启动API服务器
1. 打开命令提示符或PowerShell
2. 进入admin-new目录：
   ```bash
   cd admin-new
   ```
3. 启动API服务器：
   ```bash
   node local-api-server.cjs
   ```
4. 看到以下信息表示启动成功：
   ```
   🚀 本地API服务器启动成功!
   📡 服务地址: http://localhost:3000/admin
   ```

### 步骤2：启动前端应用
1. **保持API服务器运行**，打开新的命令提示符窗口
2. 进入admin-new目录：
   ```bash
   cd admin-new
   ```
3. 启动前端开发服务器：
   ```bash
   npm run dev
   ```
4. 看到以下信息表示启动成功：
   ```
   Local:   http://localhost:5173/
   Network: use --host to expose
   ```

### 步骤3：打开浏览器测试
1. 打开浏览器
2. 访问：http://localhost:5173
3. 进入AI配置页面进行测试

## 🧪 测试功能

### 必测功能
- [ ] 添加AI模型
- [ ] 编辑AI模型
- [ ] **删除AI模型（重点测试）**
- [ ] 测试AI连接
- [ ] 保存系统配置

### 验证要点
1. **删除功能**：删除模型后刷新页面，确认模型不会重新出现
2. **配置同步**：保存配置后，检查API服务器日志是否显示保存请求
3. **界面检查**：确认"测试云函数"按钮已被移除

## 🔍 故障排除

### 问题1：API服务器启动失败
**错误信息**：`Error: listen EADDRINUSE :::3000`
**解决方案**：
```bash
# 查找占用3000端口的进程
netstat -ano | findstr :3000

# 结束进程（替换PID为实际进程ID）
taskkill /PID <PID> /F
```

### 问题2：前端启动失败
**错误信息**：`npm ERR! missing script: dev`
**解决方案**：
```bash
# 安装依赖
npm install

# 重新启动
npm run dev
```

### 问题3：浏览器无法访问
**检查项**：
1. 确认两个服务器都在运行
2. 检查防火墙设置
3. 尝试使用127.0.0.1替代localhost

### 问题4：API请求失败
**检查项**：
1. 确认API服务器在http://localhost:3000运行
2. 检查浏览器控制台的网络请求
3. 查看API服务器窗口的日志输出

## 📊 成功标志

当看到以下情况时，说明环境启动成功：

1. **API服务器窗口**显示：
   ```
   🚀 本地API服务器启动成功!
   📡 服务地址: http://localhost:3000/admin
   ```

2. **前端服务器窗口**显示：
   ```
   ➜  Local:   http://localhost:5173/
   ➜  Network: use --host to expose
   ```

3. **浏览器**能正常访问http://localhost:5173并显示管理后台界面

4. **API请求**在API服务器窗口能看到请求日志，如：
   ```
   📥 收到请求: ai.getModels (req_123)
   ✅ 响应: ai.getModels - 156 bytes
   ```

## 💡 提示

- 保持两个命令行窗口都打开
- 可以通过Ctrl+C停止对应的服务器
- 所有测试数据都是模拟的，重启后会重置
- 如果遇到问题，可以查看对应窗口的错误信息
