/* 班级列表页面样式 */
.class-list-page {
  background-color: #f5f5f5;
  min-height: 100vh;
}

/* 搜索区域 */
.search-section {
  padding: 24rpx;
  background: white;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
}

/* 班级列表 */
.class-list-container {
  padding: 24rpx;
}

.class-item {
  background: white;
  border-radius: 20rpx;
  padding: 32rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.06);
  transition: all 0.3s ease;
}

.class-item:active {
  transform: scale(0.98);
}

.class-header {
  display: flex;
  align-items: center;
  margin-bottom: 24rpx;
}

.class-icon {
  width: 56rpx;
  height: 56rpx;
  border-radius: 50%;
  background: linear-gradient(135deg, #E3F2FD, #BBDEFB);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20rpx;
}

.class-info {
  flex: 1;
}

.class-name {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 8rpx;
}

.class-desc {
  font-size: 26rpx;
  color: #666;
  line-height: 1.4;
}

.class-stats {
  display: flex;
  justify-content: space-between;
  margin-bottom: 24rpx;
  padding: 20rpx;
  background: #f8f9fa;
  border-radius: 16rpx;
}

.stat-item {
  text-align: center;
  flex: 1;
}

.stat-number {
  font-size: 32rpx;
  font-weight: 700;
  color: #4080FF;
  margin-bottom: 8rpx;
}

.stat-label {
  font-size: 22rpx;
  color: #666;
  line-height: 1.4;
}

.class-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.class-time {
  font-size: 24rpx;
  color: #999;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 120rpx 40rpx;
  background: white;
  border-radius: 20rpx;
  margin: 24rpx;
}

.empty-text {
  font-size: 32rpx;
  color: #666;
  font-weight: 500;
  margin-bottom: 16rpx;
}

.empty-desc {
  font-size: 26rpx;
  color: #999;
  line-height: 1.5;
}

/* 创建按钮 */
.create-button {
  position: fixed;
  bottom: 40rpx;
  left: 50%;
  transform: translateX(-50%);
  width: 320rpx;
  z-index: 100;
}

.create-button .van-button {
  height: 96rpx !important;
  font-size: 32rpx !important;
  font-weight: 600 !important;
  border-radius: 48rpx !important;
  box-shadow: 0 8rpx 24rpx rgba(64, 128, 255, 0.3) !important;
}