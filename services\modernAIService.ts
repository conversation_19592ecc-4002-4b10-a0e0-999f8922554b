/**
 * 现代化AI服务层
 * 2025年企业级标准，支持多模型、智能缓存、质量评估
 */

import { z } from 'zod'
import { monitoring } from '../utils/monitoring'
import { getTemplate, recommendTemplate, TEACHER_PERSONA, COMMENT_REQUIREMENTS } from '../config/commentTemplates'

// AI请求参数Schema
const AIRequestSchema = z.object({
  prompt: z.string().min(1).max(2000),
  style: z.enum(['formal', 'warm', 'encouraging', 'neutral']).default('warm'),
  type: z.enum(['daily', 'weekly', 'monthly', 'term']).default('daily'),
  length: z.enum(['short', 'medium', 'long']).default('medium'),
  temperature: z.number().min(0).max(2).default(0.7),
  maxTokens: z.number().min(50).max(1000).default(300),
  studentInfo: z.object({
    name: z.string(),
    gender: z.enum(['male', 'female']),
    grade: z.string().optional(),
    subject: z.string().optional()
  }),
  behaviorTags: z.array(z.string()).min(1).max(10),
  customPrompt: z.string().optional()
})

type AIRequest = z.infer<typeof AIRequestSchema>

// AI响应类型
interface AIResponse {
  success: boolean
  content?: string
  quality?: number
  suggestions?: string[]
  usage?: {
    promptTokens: number
    completionTokens: number
    totalTokens: number
  }
  model?: string
  processingTime?: number
  error?: string
}

// 质量评估结果
interface QualityAssessment {
  score: number // 0-10分
  dimensions: {
    objectivity: number // 客观性
    specificity: number // 具体性
    constructiveness: number // 建设性
    appropriateness: number // 适当性
  }
  suggestions: string[]
  highlights: string[]
}

// 缓存键生成
function generateCacheKey(request: AIRequest): string {
  const keyData = {
    style: request.style,
    type: request.type,
    length: request.length,
    behaviorTags: request.behaviorTags.sort(),
    studentGender: request.studentInfo.gender,
    promptHash: request.prompt.slice(0, 50)
  }
  return `ai_cache_${JSON.stringify(keyData).replace(/[^a-zA-Z0-9]/g, '_')}`
}

class ModernAIService {
  private cacheEnabled = true
  private cacheExpiry = 24 * 60 * 60 * 1000 // 24小时
  private qualityThreshold = 7.0 // 质量阈值

  constructor() {
    console.log('🤖 Modern AI Service initialized')
  }

  /**
   * 生成AI评语
   */
  async generateComment(request: Partial<AIRequest>): Promise<AIResponse> {
    const startTime = Date.now()
    
    try {
      // 验证输入参数
      const validatedRequest = AIRequestSchema.parse(request)
      
      // 检查缓存
      if (this.cacheEnabled) {
        const cached = await this.getFromCache(validatedRequest)
        if (cached) {
          monitoring.trackEvent('ai_cache_hit', {
            cacheKey: generateCacheKey(validatedRequest)
          })
          return cached
        }
      }

      // 构建提示词
      const prompt = this.buildPrompt(validatedRequest)
      
      // 调用AI服务
      const aiResult = await this.callAIService(prompt, validatedRequest)
      
      if (!aiResult.success) {
        throw new Error(aiResult.error || 'AI服务调用失败')
      }

      // 质量评估
      const quality = await this.assessQuality(aiResult.content!, validatedRequest)
      
      // 构建响应
      const response: AIResponse = {
        success: true,
        content: aiResult.content,
        quality: quality.score,
        suggestions: quality.suggestions,
        usage: aiResult.usage,
        model: aiResult.model,
        processingTime: Date.now() - startTime
      }

      // 缓存高质量结果
      if (quality.score >= this.qualityThreshold && this.cacheEnabled) {
        await this.saveToCache(validatedRequest, response)
      }

      // 记录监控数据
      monitoring.trackApiCall('ai_generate_comment', response.processingTime!, true, {
        quality: quality.score,
        style: validatedRequest.style,
        type: validatedRequest.type,
        cached: false
      })

      monitoring.trackEvent('ai_comment_generated', {
        quality: quality.score,
        style: validatedRequest.style,
        type: validatedRequest.type,
        processingTime: response.processingTime
      })

      return response
    } catch (error) {
      const processingTime = Date.now() - startTime
      
      monitoring.trackApiCall('ai_generate_comment', processingTime, false)
      monitoring.captureError({
        message: `AI评语生成失败: ${error}`,
        category: 'api',
        level: 'error',
        context: { request }
      })

      return {
        success: false,
        error: error instanceof Error ? error.message : 'AI评语生成失败',
        processingTime
      }
    }
  }

  /**
   * 构建AI提示词
   */
  private buildPrompt(request: AIRequest): string {
    const { studentInfo, behaviorTags, style, type, length, customPrompt } = request

    // 尝试获取专业模板
    const template = getTemplate(type, style)

    if (template) {
      // 使用专业模板
      return this.buildProfessionalPrompt(template, request)
    } else {
      // 使用基础模板
      return this.buildBasicPrompt(request)
    }
  }

  /**
   * 构建专业评语提示词
   */
  private buildProfessionalPrompt(template: any, request: AIRequest): string {
    const { studentInfo, behaviorTags, customPrompt } = request

    // 替换模板中的变量
    let prompt = template.prompt
      .replace('{studentName}', studentInfo.name)
      .replace('{gender}', studentInfo.gender === 'male' ? '男' : '女')
      .replace('{behaviorTags}', behaviorTags.join('、'))

    // 添加教师人设和专业要求
    const professionalPrompt = `
【教师身份】
你是一位${TEACHER_PERSONA.experience}的职业学校班主任，${TEACHER_PERSONA.specialty}专家。
教学风格：${TEACHER_PERSONA.style}
教育理念：${TEACHER_PERSONA.principle}

【评语标准】
- 字数要求：${COMMENT_REQUIREMENTS.length}
- 结构要求：${COMMENT_REQUIREMENTS.structure}
- 语调要求：${COMMENT_REQUIREMENTS.tone}
- 禁止事项：${COMMENT_REQUIREMENTS.forbidden}

【具体任务】
${prompt}

${customPrompt ? `\n【特殊要求】\n${customPrompt}` : ''}

【重要提醒】
1. 必须基于提供的表现标签生成评语，不得编造具体事件
2. 评语要体现15年教学经验的专业水准
3. 语言要温暖而专业，既有师者风范又有人文关怀
4. 要给出具体可行的改进建议，不要空洞的鼓励

请生成评语：`

    return professionalPrompt.trim()
  }

  /**
   * 构建基础提示词（备用方案）
   */
  private buildBasicPrompt(request: AIRequest): string {
    const { studentInfo, behaviorTags, style, type, length, customPrompt } = request

    const basePrompt = `
请为学生${studentInfo.name}（${studentInfo.gender === 'male' ? '男' : '女'}生）生成一份${this.getStyleDescription(style)}的${this.getTypeDescription(type)}评语。

学生表现标签：${behaviorTags.join('、')}

要求：
1. 评语长度：${this.getLengthDescription(length)}
2. 语言风格：${this.getStyleDescription(style)}
3. 评语类型：${this.getTypeDescription(type)}
4. 必须客观、具体、建设性
5. 避免空洞的赞美或批评
6. 提供具体的改进建议

${customPrompt ? `特殊要求：${customPrompt}` : ''}

请生成评语：`

    return basePrompt.trim()
  }

  /**
   * 调用AI服务
   */
  private async callAIService(prompt: string, request: AIRequest): Promise<any> {
    try {
      // 优先尝试调用真实的豆包AI云函数
      let result
      try {
        result = await wx.cloud.callFunction({
          name: 'doubaoAI',
          data: {
            prompt,
            model: 'doubao-pro-4k',
            temperature: request.temperature,
            max_tokens: request.maxTokens
          }
        })
      } catch (error) {
        console.warn('真实AI云函数调用失败，使用备用云函数:', error)

        // 如果真实云函数失败，使用备用的callDoubaoAPI云函数
        result = await wx.cloud.callFunction({
          name: 'callDoubaoAPI',
          data: {
            prompt,
            style: 'warm',
            length: 'medium',
            temperature: request.temperature,
            max_tokens: request.maxTokens
          }
        })
      }

      return result.result
    } catch (error) {
      throw new Error(`AI服务调用失败: ${error}`)
    }
  }

  /**
   * 质量评估
   */
  private async assessQuality(content: string, request: AIRequest): Promise<QualityAssessment> {
    try {
      // 基础质量检查
      const basicScore = this.calculateBasicQuality(content, request)
      
      // AI质量评估（可选，调用专门的质量评估模型）
      const aiScore = await this.callQualityAssessmentAI(content, request)
      
      // 综合评分
      const finalScore = (basicScore + aiScore) / 2
      
      return {
        score: Math.round(finalScore * 10) / 10,
        dimensions: {
          objectivity: this.assessObjectivity(content),
          specificity: this.assessSpecificity(content, request.behaviorTags),
          constructiveness: this.assessConstructiveness(content),
          appropriateness: this.assessAppropriateness(content, request.style)
        },
        suggestions: this.generateSuggestions(content, request),
        highlights: this.extractHighlights(content)
      }
    } catch (error) {
      console.warn('质量评估失败，使用基础评分:', error)
      return {
        score: 7.0,
        dimensions: {
          objectivity: 7.0,
          specificity: 7.0,
          constructiveness: 7.0,
          appropriateness: 7.0
        },
        suggestions: [],
        highlights: []
      }
    }
  }

  /**
   * 基础质量计算
   */
  private calculateBasicQuality(content: string, request: AIRequest): number {
    let score = 5.0 // 基础分

    // 长度检查
    const wordCount = content.length
    const expectedLength = this.getExpectedLength(request.length)
    if (wordCount >= expectedLength.min && wordCount <= expectedLength.max) {
      score += 1.0
    }

    // 标签覆盖度检查
    const tagCoverage = request.behaviorTags.filter(tag => 
      content.includes(tag) || this.findSimilarConcept(content, tag)
    ).length / request.behaviorTags.length
    score += tagCoverage * 2.0

    // 风格一致性检查
    if (this.checkStyleConsistency(content, request.style)) {
      score += 1.0
    }

    // 具体性检查
    if (this.hasSpecificExamples(content)) {
      score += 1.0
    }

    return Math.min(score, 10.0)
  }

  /**
   * AI质量评估
   */
  private async callQualityAssessmentAI(content: string, request: AIRequest): Promise<number> {
    try {
      const assessmentPrompt = `
请评估以下学生评语的质量（1-10分）：

评语内容：${content}

评估维度：
1. 客观性：是否基于具体行为，避免主观判断
2. 具体性：是否有具体的例子和描述
3. 建设性：是否提供了改进建议
4. 适当性：语言风格是否符合要求

请只返回一个1-10的数字分数。`

      const result = await wx.cloud.callFunction({
        name: 'doubaoAI',
        data: {
          prompt: assessmentPrompt,
          model: 'doubao-lite-4k',
          temperature: 0.1,
          max_tokens: 10
        }
      })

      const scoreText = result.result?.data?.content || '7'
      const score = parseFloat(scoreText.match(/\d+(\.\d+)?/)?.[0] || '7')
      return Math.max(1, Math.min(10, score))
    } catch (error) {
      console.warn('AI质量评估失败:', error)
      return 7.0
    }
  }

  /**
   * 缓存相关方法
   */
  private async getFromCache(request: AIRequest): Promise<AIResponse | null> {
    try {
      const cacheKey = generateCacheKey(request)
      const cached = wx.getStorageSync(cacheKey)
      
      if (cached && cached.timestamp && Date.now() - cached.timestamp < this.cacheExpiry) {
        return cached.data
      }
      
      return null
    } catch (error) {
      console.warn('缓存读取失败:', error)
      return null
    }
  }

  private async saveToCache(request: AIRequest, response: AIResponse): Promise<void> {
    try {
      const cacheKey = generateCacheKey(request)
      wx.setStorageSync(cacheKey, {
        data: response,
        timestamp: Date.now()
      })
    } catch (error) {
      console.warn('缓存保存失败:', error)
    }
  }

  /**
   * 辅助方法
   */
  private getStyleDescription(style: string): string {
    const descriptions = {
      formal: '正式严谨',
      warm: '温暖亲切',
      encouraging: '鼓励激励',
      neutral: '客观中性'
    }
    return descriptions[style as keyof typeof descriptions] || '温暖亲切'
  }

  private getTypeDescription(type: string): string {
    const descriptions = {
      daily: '日常',
      weekly: '周总结',
      monthly: '月度',
      term: '学期'
    }
    return descriptions[type as keyof typeof descriptions] || '日常'
  }

  private getLengthDescription(length: string): string {
    const descriptions = {
      short: '简洁（50-100字）',
      medium: '适中（100-200字）',
      long: '详细（200-300字）'
    }
    return descriptions[length as keyof typeof descriptions] || '适中'
  }

  private getExpectedLength(length: string): { min: number; max: number } {
    const lengths = {
      short: { min: 50, max: 100 },
      medium: { min: 100, max: 200 },
      long: { min: 200, max: 300 }
    }
    return lengths[length as keyof typeof lengths] || { min: 100, max: 200 }
  }

  private checkStyleConsistency(content: string, style: string): boolean {
    // 简单的风格检查逻辑
    const styleKeywords = {
      formal: ['表现', '建议', '需要', '应当'],
      warm: ['孩子', '小朋友', '真棒', '加油'],
      encouraging: ['优秀', '进步', '努力', '继续'],
      neutral: ['观察', '记录', '情况', '表现']
    }
    
    const keywords = styleKeywords[style as keyof typeof styleKeywords] || []
    return keywords.some(keyword => content.includes(keyword))
  }

  private hasSpecificExamples(content: string): boolean {
    // 检查是否包含具体例子的关键词
    const exampleKeywords = ['例如', '比如', '具体', '在...时', '当...的时候']
    return exampleKeywords.some(keyword => content.includes(keyword))
  }

  private findSimilarConcept(content: string, tag: string): boolean {
    // 简单的概念匹配逻辑
    const conceptMap: Record<string, string[]> = {
      '积极': ['主动', '热情', '认真'],
      '合作': ['团队', '协作', '配合'],
      '创新': ['创意', '想法', '思考']
    }
    
    const similarConcepts = conceptMap[tag] || []
    return similarConcepts.some(concept => content.includes(concept))
  }

  private assessObjectivity(content: string): number {
    // 客观性评估逻辑
    const subjectiveWords = ['非常', '特别', '极其', '超级']
    const subjectiveCount = subjectiveWords.filter(word => content.includes(word)).length
    return Math.max(5, 10 - subjectiveCount * 2)
  }

  private assessSpecificity(content: string, tags: string[]): number {
    // 具体性评估逻辑
    const tagMentions = tags.filter(tag => content.includes(tag)).length
    return Math.min(10, 5 + (tagMentions / tags.length) * 5)
  }

  private assessConstructiveness(content: string): number {
    // 建设性评估逻辑
    const constructiveWords = ['建议', '可以', '尝试', '改进', '提高']
    const constructiveCount = constructiveWords.filter(word => content.includes(word)).length
    return Math.min(10, 5 + constructiveCount * 1.5)
  }

  private assessAppropriateness(content: string, style: string): number {
    // 适当性评估逻辑
    return this.checkStyleConsistency(content, style) ? 8 : 6
  }

  private generateSuggestions(content: string, request: AIRequest): string[] {
    const suggestions: string[] = []
    
    if (content.length < 50) {
      suggestions.push('评语内容可以更详细一些')
    }
    
    if (!this.hasSpecificExamples(content)) {
      suggestions.push('可以添加具体的行为例子')
    }
    
    const tagCoverage = request.behaviorTags.filter(tag => content.includes(tag)).length
    if (tagCoverage < request.behaviorTags.length * 0.5) {
      suggestions.push('可以更多地体现学生的行为特点')
    }
    
    return suggestions
  }

  private extractHighlights(content: string): string[] {
    // 提取评语亮点
    const sentences = content.split(/[。！？]/).filter(s => s.trim().length > 0)
    return sentences.slice(0, 2) // 返回前两句作为亮点
  }

  /**
   * 批量生成评语
   */
  async generateBatchComments(
    students: Array<{ studentInfo: AIRequest['studentInfo']; behaviorTags: string[] }>,
    commonOptions: Partial<AIRequest> = {}
  ): Promise<Array<AIResponse & { studentId: string }>> {
    const results: Array<AIResponse & { studentId: string }> = []

    monitoring.trackEvent('ai_batch_generate_start', {
      studentCount: students.length
    })

    for (const student of students) {
      try {
        const request: Partial<AIRequest> = {
          ...commonOptions,
          studentInfo: student.studentInfo,
          behaviorTags: student.behaviorTags
        }

        const result = await this.generateComment(request)
        results.push({
          ...result,
          studentId: student.studentInfo.name // 临时使用name作为ID
        })

        // 批量生成时添加延迟，避免API限流
        await new Promise(resolve => setTimeout(resolve, 500))
      } catch (error) {
        monitoring.captureError({
          message: `批量生成评语失败: ${error}`,
          category: 'api',
          level: 'error',
          context: { student: student.studentInfo.name }
        })

        results.push({
          success: false,
          error: error instanceof Error ? error.message : '生成失败',
          studentId: student.studentInfo.name
        })
      }
    }

    monitoring.trackEvent('ai_batch_generate_complete', {
      studentCount: students.length,
      successCount: results.filter(r => r.success).length,
      failureCount: results.filter(r => !r.success).length
    })

    return results
  }

  /**
   * 获取AI使用统计
   */
  async getUsageStats(): Promise<{
    totalGenerated: number
    todayGenerated: number
    averageQuality: number
    topStyles: Array<{ style: string; count: number }>
    cacheHitRate: number
  }> {
    try {
      // 从本地存储获取统计数据
      const stats = wx.getStorageSync('ai_usage_stats') || {
        totalGenerated: 0,
        todayGenerated: 0,
        qualitySum: 0,
        styleStats: {},
        cacheHits: 0,
        totalRequests: 0,
        lastResetDate: new Date().toDateString()
      }

      // 检查是否需要重置今日统计
      const today = new Date().toDateString()
      if (stats.lastResetDate !== today) {
        stats.todayGenerated = 0
        stats.lastResetDate = today
        wx.setStorageSync('ai_usage_stats', stats)
      }

      // 计算平均质量
      const averageQuality = stats.totalGenerated > 0
        ? Math.round((stats.qualitySum / stats.totalGenerated) * 10) / 10
        : 0

      // 获取热门风格
      const topStyles = Object.entries(stats.styleStats || {})
        .map(([style, count]) => ({ style, count: count as number }))
        .sort((a, b) => b.count - a.count)
        .slice(0, 5)

      // 计算缓存命中率
      const cacheHitRate = stats.totalRequests > 0
        ? Math.round((stats.cacheHits / stats.totalRequests) * 100)
        : 0

      return {
        totalGenerated: stats.totalGenerated,
        todayGenerated: stats.todayGenerated,
        averageQuality,
        topStyles,
        cacheHitRate
      }
    } catch (error) {
      console.warn('获取AI使用统计失败:', error)
      return {
        totalGenerated: 0,
        todayGenerated: 0,
        averageQuality: 0,
        topStyles: [],
        cacheHitRate: 0
      }
    }
  }

  /**
   * 更新使用统计
   */
  private updateUsageStats(request: AIRequest, response: AIResponse): void {
    try {
      const stats = wx.getStorageSync('ai_usage_stats') || {
        totalGenerated: 0,
        todayGenerated: 0,
        qualitySum: 0,
        styleStats: {},
        cacheHits: 0,
        totalRequests: 0,
        lastResetDate: new Date().toDateString()
      }

      if (response.success) {
        stats.totalGenerated++
        stats.todayGenerated++

        if (response.quality) {
          stats.qualitySum += response.quality
        }

        // 更新风格统计
        if (!stats.styleStats[request.style]) {
          stats.styleStats[request.style] = 0
        }
        stats.styleStats[request.style]++
      }

      stats.totalRequests++
      wx.setStorageSync('ai_usage_stats', stats)
    } catch (error) {
      console.warn('更新使用统计失败:', error)
    }
  }

  /**
   * 清理缓存
   */
  clearCache(): void {
    try {
      const keys = wx.getStorageInfoSync().keys
      const cacheKeys = keys.filter(key => key.startsWith('ai_cache_'))
      cacheKeys.forEach(key => wx.removeStorageSync(key))

      monitoring.trackEvent('ai_cache_cleared', {
        clearedCount: cacheKeys.length
      })

      console.log(`🗑️ AI缓存已清理，共清理${cacheKeys.length}条记录`)
    } catch (error) {
      console.warn('清理AI缓存失败:', error)
    }
  }

  /**
   * 获取缓存统计
   */
  getCacheStats(): { count: number; totalSize: number } {
    try {
      const keys = wx.getStorageInfoSync().keys
      const cacheKeys = keys.filter(key => key.startsWith('ai_cache_'))

      let totalSize = 0
      cacheKeys.forEach(key => {
        try {
          const data = wx.getStorageSync(key)
          totalSize += JSON.stringify(data).length
        } catch (error) {
          // 忽略单个缓存读取错误
        }
      })

      return {
        count: cacheKeys.length,
        totalSize
      }
    } catch (error) {
      console.warn('获取缓存统计失败:', error)
      return { count: 0, totalSize: 0 }
    }
  }

  /**
   * 获取推荐的评语模板
   */
  getRecommendedTemplates(behaviorTags: string[]): any[] {
    try {
      return recommendTemplate(behaviorTags)
    } catch (error) {
      console.warn('获取推荐模板失败:', error)
      return []
    }
  }

  /**
   * 预览评语模板
   */
  previewTemplate(templateId: string, studentInfo: any, behaviorTags: string[]): string {
    try {
      // 这里可以根据模板ID获取模板并生成预览
      const template = getTemplate('daily', 'warm') // 示例
      if (template) {
        return template.example
      }
      return '暂无预览'
    } catch (error) {
      console.warn('预览模板失败:', error)
      return '预览失败'
    }
  }
}

// 创建全局实例
const modernAIService = new ModernAIService()

export { ModernAIService, modernAIService }
export type { AIRequest, AIResponse, QualityAssessment }
