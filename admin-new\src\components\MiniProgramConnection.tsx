/**
 * 小程序数据连通配置组件
 * 用于设置和管理与小程序的数据连接
 */

import React, { useState, useCallback } from 'react'

// 通用按钮点击动效
const addButtonClickEffect = (e: React.MouseEvent<HTMLButtonElement>) => {
  const button = e.currentTarget
  const originalTransform = button.style.transform || 'scale(1)'
  button.style.transform = originalTransform.replace('scale(1)', 'scale(0.95)')
  setTimeout(() => {
    button.style.transform = originalTransform
  }, 150)
}

interface MiniProgramConnectionProps {
  onConfigSave?: (config: any) => void
}

const MiniProgramConnection: React.FC<MiniProgramConnectionProps> = ({ onConfigSave }) => {
  const [config, setConfig] = useState({
    apiBaseUrl: process.env.REACT_APP_API_BASE_URL || '',
    appId: '',
    appSecret: '',
    enableRealtime: true,
    syncInterval: 30, // 秒
    dataTypes: {
      users: true,
      comments: true,
      tokens: true,
      activities: true
    }
  })

  const [isConnecting, setIsConnecting] = useState(false)
  const [connectionStatus, setConnectionStatus] = useState<'idle' | 'success' | 'error'>('idle')
  const [statusMessage, setStatusMessage] = useState('')

  // 测试连接
  const testConnection = useCallback(async () => {
    setIsConnecting(true)
    setConnectionStatus('idle')
    setStatusMessage('正在测试连接...')

    try {
      // 模拟API测试
      const response = await fetch(`${config.apiBaseUrl}/health`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        }
      })

      if (response.ok) {
        setConnectionStatus('success')
        setStatusMessage('连接测试成功！')
      } else {
        throw new Error(`HTTP ${response.status}`)
      }
    } catch (error: any) {
      setConnectionStatus('error')
      setStatusMessage(`连接失败: ${error.message}`)
    } finally {
      setIsConnecting(false)
    }
  }, [config.apiBaseUrl])

  // 保存配置
  const saveConfig = useCallback(() => {
    localStorage.setItem('miniprogram_config', JSON.stringify(config))
    onConfigSave?.(config)
    alert('配置已保存！')
  }, [config, onConfigSave])

  // 重置配置
  const resetConfig = useCallback(() => {
    if (confirm('确认重置为默认配置吗？')) {
      setConfig({
        apiBaseUrl: '',
        appId: '',
        appSecret: '',
        enableRealtime: true,
        syncInterval: 30,
        dataTypes: {
          users: true,
          comments: true,
          tokens: true,
          activities: true
        }
      })
      setConnectionStatus('idle')
      setStatusMessage('')
    }
  }, [])

  return (
    <div style={{ display: 'grid', gap: '25px' }}>
      {/* 基础配置 */}
      <div style={{
        background: 'var(--bg-secondary)',
        backdropFilter: 'blur(20px)',
        padding: '30px',
        borderRadius: '20px',
        border: '1px solid var(--border-color)',
        boxShadow: 'var(--shadow-lg)',
        transition: 'all 0.3s ease'
      }}>
        <div style={{ display: 'flex', alignItems: 'center', marginBottom: '25px' }}>
          <div style={{
            width: '50px',
            height: '50px',
            background: 'linear-gradient(135deg, var(--primary-500) 0%, var(--primary-600) 100%)',
            borderRadius: '12px',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            marginRight: '15px',
            fontSize: '20px',
            color: 'white'
          }}>
            🔗
          </div>
          <div>
            <h3 style={{ margin: '0 0 5px 0', fontSize: '24px', fontWeight: '700', color: 'var(--text-primary)' }}>
              小程序连接配置
            </h3>
            <p style={{ margin: 0, color: 'var(--text-secondary)', fontSize: '14px' }}>
              配置与小程序后端的数据连接参数
            </p>
          </div>
        </div>

        <div style={{ display: 'grid', gap: '20px' }}>
          {/* API基础URL */}
          <div>
            <label style={{ display: 'block', fontSize: '14px', fontWeight: '600', color: 'var(--text-primary)', marginBottom: '8px' }}>
              API基础URL *
            </label>
            <input
              type="url"
              placeholder="https://your-miniprogram-api.com/api"
              value={config.apiBaseUrl}
              onChange={(e) => setConfig(prev => ({ ...prev, apiBaseUrl: e.target.value }))}
              style={{
                width: '100%',
                padding: '12px 16px',
                border: '2px solid var(--border-color)',
                borderRadius: '8px',
                fontSize: '14px',
                fontFamily: 'monospace',
                backgroundColor: 'var(--bg-primary)',
                color: 'var(--text-primary)',
                transition: 'all 0.3s ease'
              }}
            />
          </div>

          {/* 小程序凭证 */}
          <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '20px' }}>
            <div>
              <label style={{ display: 'block', fontSize: '14px', fontWeight: '600', color: 'var(--text-primary)', marginBottom: '8px' }}>
                小程序 AppId
              </label>
              <input
                type="text"
                placeholder="wx1234567890abcdef"
                value={config.appId}
                onChange={(e) => setConfig(prev => ({ ...prev, appId: e.target.value }))}
                style={{
                  width: '100%',
                  padding: '12px 16px',
                  border: '2px solid var(--border-color)',
                  borderRadius: '8px',
                  fontSize: '14px',
                  fontFamily: 'monospace',
                  backgroundColor: 'var(--bg-primary)',
                  color: 'var(--text-primary)',
                  transition: 'all 0.3s ease'
                }}
              />
            </div>
            <div>
              <label style={{ display: 'block', fontSize: '14px', fontWeight: '600', color: 'var(--text-primary)', marginBottom: '8px' }}>
                小程序 AppSecret
              </label>
              <input
                type="password"
                placeholder="请输入AppSecret"
                value={config.appSecret}
                onChange={(e) => setConfig(prev => ({ ...prev, appSecret: e.target.value }))}
                style={{
                  width: '100%',
                  padding: '12px 16px',
                  border: '2px solid var(--border-color)',
                  borderRadius: '8px',
                  fontSize: '14px',
                  fontFamily: 'monospace',
                  backgroundColor: 'var(--bg-primary)',
                  color: 'var(--text-primary)',
                  transition: 'all 0.3s ease'
                }}
              />
            </div>
          </div>

          {/* 同步设置 */}
          <div style={{ display: 'grid', gridTemplateColumns: '200px 1fr', gap: '20px', alignItems: 'center' }}>
            <div>
              <label style={{ display: 'block', fontSize: '14px', fontWeight: '600', color: 'var(--text-primary)', marginBottom: '8px' }}>
                数据同步间隔
              </label>
              <div style={{ display: 'flex', alignItems: 'center', gap: '10px' }}>
                <input
                  type="number"
                  min="10"
                  max="300"
                  value={config.syncInterval}
                  onChange={(e) => setConfig(prev => ({ ...prev, syncInterval: parseInt(e.target.value) }))}
                  style={{
                    width: '80px',
                    padding: '8px',
                    border: '1px solid var(--border-color)',
                    borderRadius: '6px',
                    fontSize: '14px',
                    textAlign: 'center',
                    backgroundColor: 'var(--bg-primary)',
                    color: 'var(--text-primary)'
                  }}
                />
                <span style={{ fontSize: '14px', color: 'var(--text-secondary)' }}>秒</span>
              </div>
            </div>
            
            <div style={{ display: 'flex', alignItems: 'center', gap: '10px' }}>
              <input
                type="checkbox"
                id="enableRealtime"
                checked={config.enableRealtime}
                onChange={(e) => setConfig(prev => ({ ...prev, enableRealtime: e.target.checked }))}
                style={{ width: '16px', height: '16px' }}
              />
              <label htmlFor="enableRealtime" style={{ fontSize: '14px', fontWeight: '600', color: 'var(--text-primary)' }}>
                启用实时数据推送
              </label>
            </div>
          </div>

          {/* 连接测试 */}
          <div style={{ 
            padding: '20px', 
            background: 'transparent', 
            borderRadius: '12px',
            border: '1px solid var(--border-color)'
          }}>
            <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', marginBottom: '15px' }}>
              <h4 style={{ margin: 0, fontSize: '16px', fontWeight: '700', color: 'var(--text-primary)' }}>
                🔍 连接测试
              </h4>
              <button
                onClick={testConnection}
                onMouseDown={!isConnecting && config.apiBaseUrl ? addButtonClickEffect : undefined}
                disabled={isConnecting || !config.apiBaseUrl}
                style={{
                  background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                  color: 'white',
                  border: 'none',
                  padding: '8px 16px',
                  borderRadius: '8px',
                  fontSize: '12px',
                  fontWeight: '600',
                  cursor: isConnecting ? 'not-allowed' : 'pointer',
                  opacity: isConnecting || !config.apiBaseUrl ? 0.6 : 1,
                  transition: 'all 0.15s ease',
                  transform: 'scale(1)'
                }}
              >
                {isConnecting ? '测试中...' : '测试连接'}
              </button>
            </div>
            
            {statusMessage && (
              <div style={{
                padding: '10px',
                borderRadius: '8px',
                fontSize: '14px',
                fontWeight: '500',
                background: connectionStatus === 'success' ? 'rgba(16, 185, 129, 0.15)' : 
                          connectionStatus === 'error' ? 'rgba(239, 68, 68, 0.15)' : 'rgba(245, 158, 11, 0.15)',
                color: connectionStatus === 'success' ? 'var(--success-color)' : 
                       connectionStatus === 'error' ? 'var(--error-color)' : 'var(--warning-color)',
                border: `1px solid ${connectionStatus === 'success' ? 'var(--success-color)' : connectionStatus === 'error' ? 'var(--error-color)' : 'var(--warning-color)'}`
              }}>
                {statusMessage}
              </div>
            )}
          </div>

          {/* 数据类型选择 */}
          <div>
            <label style={{ display: 'block', fontSize: '14px', fontWeight: '600', color: 'var(--text-primary)', marginBottom: '12px' }}>
              同步数据类型
            </label>
            <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(150px, 1fr))', gap: '15px' }}>
              {[
                { key: 'users', label: '用户数据', icon: '👥' },
                { key: 'comments', label: '评语数据', icon: '📝' },
                { key: 'tokens', label: 'Tokens消耗', icon: '📊' },
                { key: 'activities', label: '活动日志', icon: '📈' }
              ].map(item => (
                <div key={item.key} style={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: '8px',
                  padding: '12px',
                  background: config.dataTypes[item.key as keyof typeof config.dataTypes] ? 'var(--primary-50)' : 'var(--bg-tertiary)',
                  borderRadius: '8px',
                  border: `1px solid ${config.dataTypes[item.key as keyof typeof config.dataTypes] ? 'var(--primary-200)' : 'var(--border-color)'}`, 
                  cursor: 'pointer',
                  transition: 'all 0.3s ease',
                  transform: 'scale(1)'
                }}
                onMouseDown={(e) => {
                  const div = e.currentTarget
                  div.style.transform = 'scale(0.95)'
                  setTimeout(() => {
                    div.style.transform = 'scale(1)'
                  }, 150)
                }}>
                  <input
                    type="checkbox"
                    id={item.key}
                    checked={config.dataTypes[item.key as keyof typeof config.dataTypes]}
                    onChange={(e) => setConfig(prev => ({ 
                      ...prev, 
                      dataTypes: { ...prev.dataTypes, [item.key]: e.target.checked }
                    }))}
                    style={{ width: '16px', height: '16px' }}
                  />
                  <span style={{ fontSize: '16px' }}>{item.icon}</span>
                  <label htmlFor={item.key} style={{ fontSize: '14px', fontWeight: '600', cursor: 'pointer', color: 'var(--text-primary)' }}>
                    {item.label}
                  </label>
                </div>
              ))}
            </div>
          </div>

          {/* 操作按钮 */}
          <div style={{ display: 'flex', gap: '15px', marginTop: '20px' }}>
            <button
              onClick={saveConfig}
              onMouseDown={addButtonClickEffect}
              style={{
                background: 'linear-gradient(135deg, #51cf66 0%, #40c057 100%)',
                color: 'white',
                border: 'none',
                padding: '12px 24px',
                borderRadius: '12px',
                fontSize: '16px',
                fontWeight: '600',
                cursor: 'pointer',
                transition: 'all 0.15s ease',
                transform: 'scale(1)'
              }}
            >
              💾 保存配置
            </button>
            <button
              onClick={resetConfig}
              onMouseDown={addButtonClickEffect}
              style={{
                background: 'rgba(102, 126, 234, 0.1)',
                color: '#667eea',
                border: '1px solid rgba(102, 126, 234, 0.3)',
                padding: '12px 24px',
                borderRadius: '12px',
                fontSize: '16px',
                fontWeight: '600',
                cursor: 'pointer',
                transition: 'all 0.15s ease',
                transform: 'scale(1)'
              }}
            >
              🔄 重置配置
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}

export default MiniProgramConnection