/**
 * AI配置手动同步脚本
 * 使用方法：在小程序开发者工具的控制台中运行此脚本
 */

// 请替换为你的实际配置
const aiConfigData = {
  type: 'ai_config',
  status: 'active',
  model: 'doubao-seed-1-6-flash-250715', // 更正模型名称
  provider: 'bytedance',
  apiKey: '4d73215c-5512-418b-8749-db9514df3c75', // 更新的豆包AI密钥
  apiUrl: 'https://ark.cn-beijing.volces.com/api/v3/chat/completions', // 豆包AI标准端点
  temperature: 0.7,
  maxTokens: 2000,
  topP: 0.9,
  frequencyPenalty: 0,
  presencePenalty: 0,
  enableStream: false,
  enableCache: true,
  timeout: 30,
  updateTime: new Date().toISOString()
};

// 调用云函数保存配置
wx.cloud.callFunction({
  name: 'adminAPI',
  data: {
    action: 'ai.saveConfig',
    ...aiConfigData
  }
}).then(result => {
  console.log('AI配置同步成功:', result);
  if (result.result && result.result.success) {
    console.log('✅ 配置已成功保存到云数据库');
  } else {
    console.error('❌ 配置保存失败:', result.result?.message);
  }
}).catch(error => {
  console.error('❌ 云函数调用失败:', error);
});

console.log('🚀 正在同步AI配置...');
console.log('📋 配置内容:', aiConfigData);