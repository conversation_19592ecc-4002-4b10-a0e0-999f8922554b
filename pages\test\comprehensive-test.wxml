<!--综合测试页面-->
<view class="test-container">
  <!-- 页面标题 -->
  <view class="page-header">
    <text class="page-title">🧪 小程序综合测试</text>
    <text class="page-subtitle">功能 · 性能 · 兼容性全面检测</text>
  </view>

  <!-- 测试控制面板 -->
  <view class="control-panel">
    <button 
      class="test-button {{testing ? 'testing' : ''}}" 
      bindtap="startComprehensiveTest"
      disabled="{{testing}}"
    >
      <text class="button-text">
        {{testing ? '🔄 测试进行中...' : '🚀 开始综合测试'}}
      </text>
    </button>
  </view>

  <!-- 测试进度 -->
  <view class="progress-section" wx:if="{{testing || testResults}}">
    <view class="progress-header">
      <text class="progress-title">测试进度</text>
      <text class="progress-percent">{{testProgress}}%</text>
    </view>
    
    <view class="progress-bar">
      <view class="progress-fill" style="width: {{testProgress}}%"></view>
    </view>
    
    <view class="current-test">
      <text class="current-test-text">{{currentTest}}</text>
    </view>
  </view>

  <!-- 测试统计 -->
  <view class="stats-section" wx:if="{{testResults}}">
    <view class="stats-header">
      <text class="stats-title">📊 测试统计</text>
    </view>
    
    <view class="stats-grid">
      <view class="stat-item">
        <text class="stat-number">{{totalTests}}</text>
        <text class="stat-label">总测试数</text>
      </view>
      
      <view class="stat-item success">
        <text class="stat-number">{{passedTests}}</text>
        <text class="stat-label">通过</text>
      </view>
      
      <view class="stat-item fail">
        <text class="stat-number">{{failedTests}}</text>
        <text class="stat-label">失败</text>
      </view>
      
      <view class="stat-item">
        <text class="stat-number">{{passRate}}%</text>
        <text class="stat-label">通过率</text>
      </view>
    </view>
  </view>

  <!-- 操作按钮 -->
  <view class="action-buttons" wx:if="{{testResults}}">
    <button class="action-button secondary" bindtap="showTestReport">
      📋 查看报告
    </button>
    
    <button class="action-button primary" bindtap="exportTestResults">
      📤 导出结果
    </button>
  </view>

  <!-- 测试日志 -->
  <view class="log-section">
    <view class="log-header">
      <text class="log-title">📝 测试日志</text>
      <button class="clear-log-button" bindtap="clearLog" wx:if="{{testLog.length > 0}}">
        清空
      </button>
    </view>
    
    <scroll-view class="log-content" scroll-y="true" scroll-top="{{testLog.length * 50}}">
      <view class="log-item" wx:for="{{testLog}}" wx:key="index">
        <text class="log-text">{{item}}</text>
      </view>
      
      <view class="log-empty" wx:if="{{testLog.length === 0}}">
        <text class="empty-text">暂无日志记录</text>
      </view>
    </scroll-view>
  </view>

  <!-- 测试报告弹窗 -->
  <view class="report-modal {{showReport ? 'show' : ''}}" wx:if="{{showReport}}">
    <view class="report-mask" bindtap="hideTestReport"></view>
    <view class="report-content">
      <view class="report-header">
        <text class="report-title">📊 测试报告</text>
        <view class="close-button" bindtap="hideTestReport">×</view>
      </view>

      <scroll-view class="report-body" scroll-y="true">
        <text class="report-text">{{reportContent}}</text>
      </scroll-view>

      <view class="report-footer">
        <button class="report-button" bindtap="exportTestResults">导出完整结果</button>
      </view>
    </view>
  </view>
</view>
