/**
 * 云函数调用服务层 - 管理后台专用
 * 替换HTTP调用，实现管理后台免费运行
 */

import cloudbaseService from '../utils/cloudbaseConfig'

interface ApiResponse<T = any> {
  code: number
  message: string
  data?: T
  timestamp?: number
  error?: string
}

interface AdminApiRequest {
  action: string
  [key: string]: any
}

class CloudFunctionService {
  private isInitialized = false
  private fallbackToHttp = false // HTTP调用备用方案

  constructor() {
    this.initialize()
  }

  /**
   * 初始化服务
   */
  private async initialize() {
    try {
      console.log('🚀 初始化云函数服务...')
      await cloudbaseService.initialize()
      
      // 健康检查
      const isHealthy = await cloudbaseService.healthCheck()
      if (!isHealthy) {
        console.warn('⚠️ 云函数服务不健康，将使用HTTP备用方案')
        this.fallbackToHttp = true
      }
      
      this.isInitialized = true
      console.log('✅ 云函数服务初始化完成')
    } catch (error) {
      console.error('❌ 云函数服务初始化失败:', error)
      this.fallbackToHttp = true
    }
  }

  /**
   * 调用adminAPI云函数（主要接口）
   */
  async callAdminAPI<T = any>(action: string, data: any = {}): Promise<ApiResponse<T>> {
    try {
      console.log(`📡 调用AdminAPI: ${action}`)
      
      const requestData: AdminApiRequest = {
        action,
        ...data,
        timestamp: Date.now()
      }

      const result = await cloudbaseService.callFunction('adminAPI', requestData)
      
      // 标准化响应格式
      return this.normalizeResponse(result)
    } catch (error: any) {
      console.error(`❌ AdminAPI调用失败: ${action}`, error)
      
      // 如果云函数调用失败，考虑fallback到HTTP
      if (this.fallbackToHttp) {
        return this.fallbackToHttpCall(action, data)
      }
      
      return {
        code: -1,
        message: error?.message || '云函数调用失败',
        error: 'CLOUD_FUNCTION_ERROR'
      }
    }
  }

  /**
   * 管理员登录
   */
  async adminLogin(username: string, password: string): Promise<ApiResponse> {
    return this.callAdminAPI('login', {
      username,
      password,
      loginTime: new Date().toISOString()
    })
  }

  /**
   * 验证管理员token
   */
  async validateToken(token: string): Promise<ApiResponse> {
    return this.callAdminAPI('validateToken', { token })
  }

  /**
   * 获取仪表盘统计数据
   */
  async getDashboardStats(): Promise<ApiResponse> {
    return this.callAdminAPI('getDashboardStats', {
      includeRealtime: true,
      includeGrowth: true
    })
  }

  /**
   * 获取实时数据
   */
  async getRealtimeData(): Promise<ApiResponse> {
    return this.callAdminAPI('getRealtimeData', {
      dataTypes: ['users', 'comments', 'ai_usage', 'system']
    })
  }

  /**
   * 获取用户列表
   */
  async getUsers(page: number = 1, limit: number = 20): Promise<ApiResponse> {
    return this.callAdminAPI('getUsers', {
      page,
      limit,
      includeStats: true
    })
  }

  /**
   * 获取评语列表
   */
  async getComments(page: number = 1, limit: number = 20, filters: any = {}): Promise<ApiResponse> {
    return this.callAdminAPI('getComments', {
      page,
      limit,
      filters,
      includeUser: true
    })
  }

  /**
   * 批量操作
   */
  async batchOperation(operation: string, items: any[]): Promise<ApiResponse> {
    return this.callAdminAPI('batchOperation', {
      operation,
      items,
      batchSize: Math.min(items.length, 50) // 限制批量大小
    })
  }

  /**
   * 系统配置管理
   */
  async getSystemConfig(): Promise<ApiResponse> {
    return this.callAdminAPI('getSystemConfig')
  }

  async updateSystemConfig(config: any): Promise<ApiResponse> {
    return this.callAdminAPI('updateSystemConfig', { config })
  }

  /**
   * AI使用情况统计
   */
  async getAIUsageStats(timeRange: string = '7d'): Promise<ApiResponse> {
    return this.callAdminAPI('getAIUsageStats', { timeRange })
  }

  /**
   * 数据导出
   */
  async exportData(dataType: string, filters: any = {}): Promise<ApiResponse> {
    return this.callAdminAPI('exportData', {
      dataType,
      filters,
      format: 'xlsx'
    })
  }

  /**
   * 直接调用其他云函数
   */
  async callFunction<T = any>(functionName: string, data: any = {}): Promise<ApiResponse<T>> {
    try {
      const result = await cloudbaseService.callFunction(functionName, data)
      return this.normalizeResponse(result)
    } catch (error: any) {
      return {
        code: -1,
        message: error?.message || '云函数调用失败',
        error: 'CLOUD_FUNCTION_ERROR'
      }
    }
  }

  /**
   * 标准化响应格式
   */
  private normalizeResponse<T = any>(result: any): ApiResponse<T> {
    if (!result) {
      return {
        code: -1,
        message: '空响应',
        error: 'EMPTY_RESPONSE'
      }
    }

    // 如果已经是标准格式
    if (typeof result.code === 'number') {
      return {
        code: result.code,
        message: result.message || '操作完成',
        data: result.data,
        timestamp: result.timestamp || Date.now()
      }
    }

    // 兼容其他格式
    return {
      code: 0,
      message: '操作成功',
      data: result,
      timestamp: Date.now()
    }
  }

  /**
   * HTTP备用方案（保留原有HTTP调用能力）
   */
  private async fallbackToHttpCall<T = any>(action: string, data: any = {}): Promise<ApiResponse<T>> {
    try {
      console.log(`🔄 启用HTTP备用方案: ${action}`)
      
      const response = await fetch('https://cloud1-4g85f8xlb8166ff1-1365982463.ap-shanghai.app.tcloudbase.com/adminAPI', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ action, ...data })
      })

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }

      const result = await response.json()
      return this.normalizeResponse(result)
    } catch (error: any) {
      console.error('❌ HTTP备用方案也失败了:', error)
      return {
        code: -1,
        message: error?.message || 'HTTP请求失败',
        error: 'HTTP_FALLBACK_FAILED'
      }
    }
  }

  /**
   * 启用/禁用HTTP备用方案
   */
  setFallbackMode(enabled: boolean) {
    this.fallbackToHttp = enabled
    console.log(`🔄 HTTP备用方案已${enabled ? '启用' : '禁用'}`)
  }

  /**
   * 获取服务状态
   */
  getStatus() {
    return {
      isInitialized: this.isInitialized,
      fallbackToHttp: this.fallbackToHttp,
      environment: cloudbaseService.getConfig()
    }
  }

  /**
   * 重新初始化服务
   */
  async reinitialize() {
    this.isInitialized = false
    this.fallbackToHttp = false
    await this.initialize()
  }
}

// 单例实例
const cloudFunctionService = new CloudFunctionService()

export default cloudFunctionService
export { cloudFunctionService, CloudFunctionService }
export type { ApiResponse, AdminApiRequest }
