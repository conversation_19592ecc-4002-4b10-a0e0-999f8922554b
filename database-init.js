/**
 * 云数据库初始化脚本
 * 在微信开发者工具的云开发控制台中运行此脚本
 *
 * 使用立即执行函数避免变量冲突
 */

(async function initDatabase() {
  console.log('🚀 开始初始化云数据库...')

  // 1. 创建管理员用户表
  console.log('📋 创建管理员用户表...')
  try {
    await db.createCollection('admin_users')
    console.log('✅ admin_users 表创建成功')
  } catch (err) {
    console.log('⚠️ admin_users 表可能已存在')
  }

  // 2. 创建系统设置表
  console.log('📋 创建系统设置表...')
  try {
    await db.createCollection('system_settings')
    console.log('✅ system_settings 表创建成功')
  } catch (err) {
    console.log('⚠️ system_settings 表可能已存在')
  }

  // 3. 创建用户偏好表
  console.log('📋 创建用户偏好表...')
  try {
    await db.createCollection('user_preferences')
    console.log('✅ user_preferences 表创建成功')
  } catch (err) {
    console.log('⚠️ user_preferences 表可能已存在')
  }

  // 4. 创建管理员日志表
  console.log('📋 创建管理员日志表...')
  try {
    await db.createCollection('admin_logs')
    console.log('✅ admin_logs 表创建成功')
  } catch (err) {
    console.log('⚠️ admin_logs 表可能已存在')
  }

  // 5. 初始化默认管理员账户
  console.log('👤 初始化默认管理员账户...')

  // 注意：在云开发控制台中，crypto模块可能不可用，使用简单的哈希方法
  function simpleHash(password) {
    let hash = 0
    const salt = 'admin_salt_2024'
    const str = password + salt
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i)
      hash = ((hash << 5) - hash) + char
      hash = hash & hash // 转换为32位整数
    }
    return hash.toString(16)
  }

  // 检查管理员账户是否已存在
  try {
    const existingAdmin = await db.collection('admin_users').where({
      username: 'admin'
    }).get()

    if (existingAdmin.data.length === 0) {
      const adminResult = await db.collection('admin_users').add({
        data: {
          username: 'admin',
          password_hash: simpleHash('admin123'),
          role: 'super_admin',
          status: 'active',
          created_at: new Date(),
          updated_at: new Date()
        }
      })
      console.log('✅ 默认管理员账户创建成功:', adminResult._id)
    } else {
      console.log('⚠️ 管理员账户已存在，跳过创建')
    }
  } catch (err) {
    console.log('❌ 管理员账户创建失败:', err)
  }

  // 6. 初始化默认系统设置
  console.log('⚙️ 初始化默认系统设置...')
  const systemSettings = [
    { key: 'systemName', value: '评语灵感君管理后台', type: 'string' },
    { key: 'version', value: 'v2.0.0', type: 'string' },
    { key: 'maxUsers', value: 1000, type: 'number' },
    { key: 'sessionTimeout', value: 30, type: 'number' },
    { key: 'autoRefresh', value: false, type: 'boolean' },
    { key: 'darkMode', value: false, type: 'boolean' },
    { key: 'debugMode', value: false, type: 'boolean' },
    { key: 'enableCache', value: true, type: 'boolean' },
    { key: 'logLevel', value: 'info', type: 'string' },
    { key: 'backupFrequency', value: 'daily', type: 'string' }
  ]

  for (const setting of systemSettings) {
    try {
      const existing = await db.collection('system_settings').where({
        setting_key: setting.key
      }).get()

      if (existing.data.length === 0) {
        await db.collection('system_settings').add({
          data: {
            setting_key: setting.key,
            setting_value: setting.value,
            setting_type: setting.type,
            updated_by: 'system',
            updated_at: new Date(),
            created_at: new Date()
          }
        })
        console.log(`✅ 系统设置 ${setting.key} 创建成功`)
      } else {
        console.log(`⚠️ 系统设置 ${setting.key} 已存在，跳过创建`)
      }
    } catch (err) {
      console.log(`❌ 系统设置 ${setting.key} 创建失败:`, err)
    }
  }

// 7. 创建数据库索引
console.log('📊 创建数据库索引...')

// 管理员用户表索引
db.collection('admin_users').createIndex({
  keys: {
    username: 1
  },
  unique: true
}).then(() => {
  console.log('✅ admin_users.username 索引创建成功')
}).catch(err => {
  console.log('⚠️ admin_users.username 索引可能已存在')
})

db.collection('admin_users').createIndex({
  keys: {
    openid: 1
  }
}).then(() => {
  console.log('✅ admin_users.openid 索引创建成功')
}).catch(err => {
  console.log('⚠️ admin_users.openid 索引可能已存在')
})

// 系统设置表索引
db.collection('system_settings').createIndex({
  keys: {
    setting_key: 1
  },
  unique: true
}).then(() => {
  console.log('✅ system_settings.setting_key 索引创建成功')
}).catch(err => {
  console.log('⚠️ system_settings.setting_key 索引可能已存在')
})

// 用户偏好表索引
db.collection('user_preferences').createIndex({
  keys: {
    user_id: 1,
    preference_key: 1
  },
  unique: true
}).then(() => {
  console.log('✅ user_preferences 复合索引创建成功')
}).catch(err => {
  console.log('⚠️ user_preferences 复合索引可能已存在')
})

// 管理员日志表索引
db.collection('admin_logs').createIndex({
  keys: {
    timestamp: -1
  }
}).then(() => {
  console.log('✅ admin_logs.timestamp 索引创建成功')
}).catch(err => {
  console.log('⚠️ admin_logs.timestamp 索引可能已存在')
})

db.collection('admin_logs').createIndex({
  keys: {
    user_id: 1
  }
}).then(() => {
  console.log('✅ admin_logs.user_id 索引创建成功')
}).catch(err => {
  console.log('⚠️ admin_logs.user_id 索引可能已存在')
})

  console.log('🎉 数据库初始化完成！')
  console.log('')
  console.log('📋 数据库表结构:')
  console.log('  - admin_users: 管理员用户表')
  console.log('  - system_settings: 系统设置表')
  console.log('  - user_preferences: 用户偏好表')
  console.log('  - admin_logs: 管理员操作日志表')
  console.log('')
  console.log('👤 默认管理员账户:')
  console.log('  - 用户名: admin')
  console.log('  - 密码: admin123')
  console.log('  - 角色: super_admin')
  console.log('')
  console.log('⚙️ 默认系统设置已初始化')
  console.log('')
  console.log('🔧 下一步操作:')
  console.log('  1. 部署 settingsAPI 云函数')
  console.log('  2. 在管理后台中测试登录功能')
  console.log('  3. 验证设置保存和加载功能')
  console.log('')
  console.log('✅ 初始化脚本执行完成！')

})() // 立即执行函数结束
