@echo off
echo Checking port 8080...
echo.

echo Finding processes using port 8080...
netstat -ano | findstr :8080

echo.
echo Killing processes on port 8080...
for /f "tokens=5" %%i in ('netstat -ano ^| findstr :8080') do (
    echo Killing process ID: %%i
    taskkill /f /pid %%i >nul 2>&1
)

echo.
echo Killing Node.js processes...
taskkill /f /im node.exe >nul 2>&1

echo.
echo Killing Chrome processes...
taskkill /f /im chrome.exe >nul 2>&1

echo.
echo Port 8080 should now be free
timeout /t 2 /nobreak >nul

echo.
echo Checking port 8080 again...
netstat -ano | findstr :8080

if errorlevel 1 (
    echo SUCCESS: Port 8080 is now free
) else (
    echo WARNING: Port 8080 still in use
)

pause