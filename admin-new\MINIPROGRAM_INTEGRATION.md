# 小程序数据连通集成指南

## 🔗 概述

本管理后台已经完成与小程序的数据连通准备工作，包括实时数据同步、API接口调用、WebSocket连接等功能。

## 📋 已实现功能

### 1. 实时数据连接
- ✅ WebSocket实时数据推送
- ✅ 连接状态监控
- ✅ 自动重连机制
- ✅ 数据类型分发处理

### 2. API服务接口
- ✅ 用户管理API (获取、更新限额、重置使用次数)
- ✅ 评语管理API (列表获取、统计数据)
- ✅ Tokens消耗API (实时统计、详细记录)
- ✅ AI配置API (配置管理、连接测试)
- ✅ 系统统计API (概览数据、活动日志)
- ✅ 数据导入导出API (学生数据管理)

### 3. 用户界面功能
- ✅ Tokens消耗图表 (支持小时/日视图切换)
- ✅ 用户使用次数管理 (调额、重置、详情查看)
- ✅ 实时活动日志显示
- ✅ 连接状态指示器
- ✅ 小程序连接配置页面

### 4. 交互体验优化
- ✅ 所有按钮点击动效
- ✅ 性能优化 (useMemo, useCallback)
- ✅ 响应式设计
- ✅ 错误处理机制

## 🛠️ 技术架构

### 前端技术栈
```
React 18 + TypeScript
├── 状态管理: useState + useCallback + useMemo
├── 实时连接: WebSocket + Custom Hooks
├── API调用: Fetch + 错误处理
├── UI组件: 内联样式 (避免CSP问题)
└── 构建工具: Vite
```

### API接口设计
```
/api
├── /users              # 用户管理
├── /comments           # 评语管理  
├── /tokens             # Token消耗统计
├── /ai                 # AI配置管理
├── /stats              # 系统统计
├── /data               # 数据导入导出
└── /ws                 # WebSocket连接
```

## 🔧 部署配置

### 1. 环境变量设置
在 `.env` 文件中配置：
```bash
# API基础URL
REACT_APP_API_BASE_URL=https://your-miniprogram-api.com/api

# 小程序配置
REACT_APP_MINIPROGRAM_APPID=wx**********abcdef
REACT_APP_MINIPROGRAM_SECRET=your-app-secret

# 其他配置
REACT_APP_ENV=production
```

### 2. 后端API要求
后端需要实现以下接口规范：

#### 健康检查
```http
GET /health
Response: { "status": "ok", "timestamp": ********** }
```

#### 用户管理
```http
GET /users?page=1&limit=20&search=keyword
PUT /users/{userId}/limit
POST /users/{userId}/reset-usage
```

#### 实时数据推送
```javascript
// WebSocket消息格式
{
  "type": "user_activity" | "tokens_update" | "comment_generated" | "system_status",
  "payload": { /* 具体数据 */ },
  "timestamp": **********
}
```

### 3. 小程序端配置
小程序需要配置管理后台的域名到服务器域名白名单：
```javascript
// app.json
{
  "permission": {
    "scope.userLocation": {
      "desc": "用于获取用户位置信息"
    }
  },
  "requiredBackgroundModes": ["audio"],
  "networkTimeout": {
    "request": 10000,
    "downloadFile": 10000
  }
}
```

## 📊 数据同步说明

### 1. 同步数据类型
- **用户数据**: 用户列表、使用次数、限额设置
- **评语数据**: 生成记录、统计信息
- **Tokens消耗**: 实时消耗、成本统计
- **活动日志**: 操作记录、系统事件

### 2. 同步机制
- **实时推送**: WebSocket连接，即时更新
- **定时同步**: 30秒间隔轮询，确保数据一致性
- **手动刷新**: 用户主动触发数据更新

### 3. 数据格式示例
```javascript
// 用户活动数据
{
  type: 'user_activity',
  payload: {
    user: '张老师',
    action: '生成了5条学生评语', 
    time: '2分钟前',
    icon: '📝',
    color: '#51cf66'
  }
}

// Tokens更新数据
{
  type: 'tokens_update',
  payload: {
    totalToday: 112960,
    totalWeek: 772260,
    costToday: 0.23,
    costWeek: 1.54,
    hourlyData: [/* 小时数据 */],
    dailyData: [/* 日数据 */]
  }
}
```

## 🚀 快速启动

### 1. 安装依赖
```bash
cd admin-new
npm install
```

### 2. 配置环境
```bash
cp .env.example .env
# 编辑 .env 文件，填入实际的API地址
```

### 3. 启动开发服务器
```bash
npm run dev
# 或使用简化服务器（如果遇到依赖问题）
node start-dev.js
```

### 4. 访问管理后台
```
http://localhost:3000
用户名: admin
密码: admin123
```

## 🔍 测试连接

在系统设置页面的"小程序连接配置"中：
1. 填入API基础URL
2. 配置小程序AppId和AppSecret
3. 选择同步数据类型
4. 点击"测试连接"验证配置

## 📝 注意事项

### 1. 安全考虑
- 所有API请求需要携带Authorization头
- 敏感信息使用HTTPS传输
- 定期更新访问令牌

### 2. 性能优化
- 使用React.memo优化组件渲染
- WebSocket连接复用
- 图表数据懒加载

### 3. 错误处理
- 网络异常自动重试
- 用户友好的错误提示
- 日志记录和监控

## 🛡️ 故障排除

### 常见问题
1. **连接失败**: 检查API地址是否正确，网络是否通畅
2. **数据不更新**: 检查WebSocket连接状态，确认后端推送功能
3. **按钮无响应**: 检查浏览器控制台错误信息
4. **图表显示异常**: 检查数据格式是否符合预期

### 调试工具
- 浏览器开发者工具
- Network面板查看API请求
- Console面板查看错误日志
- WebSocket连接状态监控

## 📞 技术支持

如遇到技术问题，请提供以下信息：
- 错误详细描述
- 浏览器控制台日志
- 网络请求详情
- 操作步骤重现

---

**版本**: v3.0.0  
**更新时间**: 2024-01-29  
**维护者**: Claude Code Assistant