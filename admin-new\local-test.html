<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>本地API测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }
        .container {
            background: white;
            border-radius: 12px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        h1 {
            color: #2c3e50;
            text-align: center;
            margin-bottom: 30px;
        }
        .success-banner {
            background: linear-gradient(135deg, #00b894, #00cec9);
            color: white;
            padding: 20px;
            border-radius: 8px;
            text-align: center;
            margin-bottom: 30px;
            font-weight: bold;
        }
        button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
            transition: transform 0.2s;
        }
        button:hover { transform: translateY(-2px); }
        button:disabled { opacity: 0.6; cursor: not-allowed; transform: none; }
        
        .result {
            margin: 10px 0;
            padding: 15px;
            border-radius: 8px;
            font-family: monospace;
            font-size: 14px;
        }
        .success { background: #d4edda; color: #155724; border-left: 4px solid #28a745; }
        .error { background: #f8d7da; color: #721c24; border-left: 4px solid #dc3545; }
        .info { background: #d1ecf1; color: #0c5460; border-left: 4px solid #17a2b8; }
        
        .api-info {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        
        .data-preview {
            background: #2c3e50;
            color: #ecf0f1;
            padding: 15px;
            border-radius: 6px;
            font-family: monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
            margin-top: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="success-banner">
            🎉 本地API服务器已启动成功！
            <br>现在可以测试管理后台的所有功能了
        </div>
        
        <h1>🚀 本地API连通性测试</h1>
        
        <div class="api-info">
            <h3>📡 API服务器信息</h3>
            <p><strong>地址：</strong>http://localhost:3000/admin</p>
            <p><strong>状态：</strong><span style="color: #28a745;">✅ 运行中</span></p>
            <p><strong>功能：</strong>完整的管理后台API，包含模拟数据</p>
        </div>
        
        <div style="text-align: center; margin: 30px 0;">
            <button onclick="testHealthCheck()">健康检查</button>
            <button onclick="testDashboardStats()">仪表板数据</button>
            <button onclick="testRealtimeStats()">实时统计</button>
            <button onclick="testStudents()">学生数据</button>
            <button onclick="testAIModels()">AI模型</button>
            <button onclick="clearResults()">清空结果</button>
        </div>
        
        <div id="results"></div>
    </div>

    <script>
        const API_URL = 'http://localhost:3000/admin'
        
        function addResult(message, type = 'info', data = null) {
            const results = document.getElementById('results')
            const div = document.createElement('div')
            div.className = `result ${type}`
            
            let content = `[${new Date().toLocaleTimeString()}] ${message}`
            
            if (data) {
                content += `<div class="data-preview">${JSON.stringify(data, null, 2)}</div>`
            }
            
            div.innerHTML = content
            results.appendChild(div)
            results.scrollTop = results.scrollHeight
        }
        
        async function callAPI(action, data = {}) {
            try {
                addResult(`🚀 调用 ${action}...`, 'info')
                
                const response = await fetch(API_URL, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        action,
                        ...data,
                        timestamp: Date.now(),
                        requestId: `local_test_${Date.now()}`
                    })
                })
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`)
                }
                
                const result = await response.json()
                
                if (result.code === 200) {
                    addResult(`✅ ${action} 成功`, 'success', result.data)
                    return result.data
                } else {
                    addResult(`❌ ${action} 失败: ${result.message}`, 'error')
                    return null
                }
                
            } catch (error) {
                addResult(`❌ ${action} 错误: ${error.message}`, 'error')
                return null
            }
        }
        
        async function testHealthCheck() {
            const result = await callAPI('healthCheck', { source: 'local-test' })
            if (result) {
                addResult(`🎉 健康检查成功！版本: ${result.version}, 状态: ${result.status}`, 'success')
            }
        }
        
        async function testDashboardStats() {
            const result = await callAPI('data.getDashboardStats')
            if (result) {
                addResult(`📊 仪表板数据获取成功: 用户${result.totalUsers}个, 评语${result.todayComments}条, AI调用${result.aiCalls}次`, 'success')
            }
        }
        
        async function testRealtimeStats() {
            const result = await callAPI('realtime.getStats')
            if (result) {
                addResult(`⚡ 实时统计获取成功: 在线${result.current.onlineUsers}人, 今日评语${result.current.todayComments}条`, 'success')
            }
        }
        
        async function testStudents() {
            const result = await callAPI('data.getStudents', { params: { limit: 5 } })
            if (result) {
                addResult(`👥 学生数据获取成功: 共${result.total}条记录`, 'success')
            }
        }
        
        async function testAIModels() {
            const result = await callAPI('ai.getModels')
            if (result) {
                addResult(`🤖 AI模型获取成功: 共${result.length}个模型`, 'success')
            }
        }
        
        function clearResults() {
            document.getElementById('results').innerHTML = ''
        }
        
        // 页面加载时自动测试健康检查
        window.addEventListener('load', () => {
            addResult('🔗 本地API测试页面加载完成', 'info')
            addResult('💡 本地服务器已启动，现在可以测试所有API接口', 'info')
            setTimeout(testHealthCheck, 1000)
        })
    </script>
</body>
</html>
