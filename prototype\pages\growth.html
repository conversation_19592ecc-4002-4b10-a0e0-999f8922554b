<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>成长报告</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
            background: linear-gradient(135deg, #F0F2F5 0%, #E4E7ED 100%);
            min-height: 100vh;
            color: #333;
        }

        .page-container {
            padding: 20px 16px 100px;
            max-width: 375px;
            margin: 0 auto;
        }

        /* 页面头部 */
        .page-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 24px;
            padding-top: 10px;
        }

        .header-title {
            font-size: 20px;
            font-weight: 600;
            color: #333;
        }

        .header-action {
            color: #5470C6;
            font-size: 14px;
            cursor: pointer;
        }

        /* 成长概览卡片 */
        .growth-overview {
            background: linear-gradient(135deg, #5470C6 0%, #73C0DE 100%);
            border-radius: 20px;
            padding: 24px;
            margin-bottom: 24px;
            color: white;
            position: relative;
            overflow: hidden;
        }

        .growth-overview::before {
            content: '';
            position: absolute;
            top: -50%;
            right: -50%;
            width: 200px;
            height: 200px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 50%;
        }

        .overview-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            position: relative;
            z-index: 1;
        }

        .overview-title {
            font-size: 18px;
            font-weight: 600;
        }

        .overview-period {
            font-size: 12px;
            opacity: 0.8;
            background: rgba(255, 255, 255, 0.2);
            padding: 4px 12px;
            border-radius: 12px;
        }

        .overview-stats {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 16px;
            position: relative;
            z-index: 1;
        }

        .overview-stat {
            text-align: center;
        }

        .stat-value {
            font-size: 28px;
            font-weight: 700;
            display: block;
            margin-bottom: 4px;
        }

        .stat-label {
            font-size: 12px;
            opacity: 0.9;
        }

        .stat-change {
            font-size: 11px;
            margin-top: 4px;
            opacity: 0.8;
        }

        /* 效率统计 */
        .efficiency-section {
            background: rgba(255, 255, 255, 0.9);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 20px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.3);
        }

        .section-title {
            font-size: 16px;
            font-weight: 600;
            color: #333;
            margin-bottom: 16px;
            display: flex;
            align-items: center;
        }

        .section-icon {
            margin-right: 8px;
            color: #5470C6;
        }

        .efficiency-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 16px;
        }

        .efficiency-item {
            text-align: center;
            padding: 16px;
            background: #F8F9FA;
            border-radius: 12px;
        }

        .efficiency-icon {
            font-size: 24px;
            margin-bottom: 8px;
            color: #5470C6;
        }

        .efficiency-number {
            font-size: 20px;
            font-weight: 700;
            color: #333;
            margin-bottom: 4px;
        }

        .efficiency-label {
            font-size: 12px;
            color: #666;
        }

        /* 质量分析 */
        .quality-section {
            background: rgba(255, 255, 255, 0.9);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 20px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.3);
        }

        .quality-chart {
            background: #F8F9FA;
            border-radius: 12px;
            padding: 16px;
            margin-bottom: 16px;
            text-align: center;
        }

        .chart-placeholder {
            height: 120px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #666;
            font-size: 14px;
            border: 2px dashed #E4E7ED;
            border-radius: 8px;
        }

        .quality-metrics {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 12px;
        }

        .quality-metric {
            text-align: center;
            padding: 12px;
            background: #F8F9FA;
            border-radius: 8px;
        }

        .metric-score {
            font-size: 18px;
            font-weight: 700;
            color: #52C41A;
            margin-bottom: 4px;
        }

        .metric-label {
            font-size: 11px;
            color: #666;
        }

        /* 成就展示 */
        .achievement-section {
            background: rgba(255, 255, 255, 0.9);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 20px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.3);
        }

        .achievement-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
        }

        .achievement-item {
            background: linear-gradient(135deg, #FFD700 0%, #FFA500 100%);
            border-radius: 12px;
            padding: 16px;
            text-align: center;
            color: white;
            position: relative;
            overflow: hidden;
        }

        .achievement-item.locked {
            background: #F5F5F5;
            color: #999;
        }

        .achievement-icon {
            font-size: 24px;
            margin-bottom: 8px;
        }

        .achievement-title {
            font-size: 12px;
            font-weight: 600;
            margin-bottom: 4px;
        }

        .achievement-desc {
            font-size: 10px;
            opacity: 0.9;
        }

        .achievement-progress {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: rgba(255, 255, 255, 0.3);
        }

        .progress-fill {
            height: 100%;
            background: rgba(255, 255, 255, 0.8);
            transition: width 0.3s ease;
        }

        /* 趋势图表 */
        .trend-section {
            background: rgba(255, 255, 255, 0.9);
            border-radius: 16px;
            padding: 20px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.3);
        }

        .trend-chart {
            background: #F8F9FA;
            border-radius: 12px;
            padding: 16px;
            height: 150px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #666;
            font-size: 14px;
            border: 2px dashed #E4E7ED;
        }

        /* 底部导航 */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-top: 1px solid rgba(0, 0, 0, 0.05);
            padding: 8px 0 20px;
        }

        .nav-grid {
            display: grid;
            grid-template-columns: repeat(5, 1fr);
            max-width: 375px;
            margin: 0 auto;
        }

        .nav-item {
            text-align: center;
            padding: 8px;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .nav-item.active {
            color: #5470C6;
        }

        .nav-icon {
            font-size: 20px;
            margin-bottom: 4px;
        }

        .nav-label {
            font-size: 10px;
            font-weight: 500;
        }
    </style>
</head>
<body>
    <div class="page-container">
        <!-- 页面头部 -->
        <div class="page-header">
            <div class="header-title">成长报告</div>
            <div class="header-action">分享报告</div>
        </div>

        <!-- 成长概览 -->
        <div class="growth-overview">
            <div class="overview-header">
                <div class="overview-title">本月成长概览</div>
                <div class="overview-period">2024年1月</div>
            </div>
            
            <div class="overview-stats">
                <div class="overview-stat">
                    <span class="stat-value">156</span>
                    <span class="stat-label">生成评语</span>
                    <div class="stat-change">↑ 比上月+23%</div>
                </div>
                <div class="overview-stat">
                    <span class="stat-value">48h</span>
                    <span class="stat-label">节省时间</span>
                    <div class="stat-change">↑ 比上月+18%</div>
                </div>
                <div class="overview-stat">
                    <span class="stat-value">9.1</span>
                    <span class="stat-label">平均质量</span>
                    <div class="stat-change">↑ 比上月+0.3</div>
                </div>
                <div class="overview-stat">
                    <span class="stat-value">89%</span>
                    <span class="stat-label">优秀率</span>
                    <div class="stat-change">↑ 比上月+5%</div>
                </div>
            </div>
        </div>

        <!-- 效率统计 -->
        <div class="efficiency-section">
            <div class="section-title">
                <i class="fas fa-tachometer-alt section-icon"></i>
                效率统计
            </div>
            
            <div class="efficiency-grid">
                <div class="efficiency-item">
                    <div class="efficiency-icon">⚡</div>
                    <div class="efficiency-number">3.2</div>
                    <div class="efficiency-label">平均生成时间(分钟)</div>
                </div>
                <div class="efficiency-item">
                    <div class="efficiency-icon">🎯</div>
                    <div class="efficiency-number">85%</div>
                    <div class="efficiency-label">一次通过率</div>
                </div>
                <div class="efficiency-item">
                    <div class="efficiency-icon">📈</div>
                    <div class="efficiency-number">156</div>
                    <div class="efficiency-label">累计字数(千字)</div>
                </div>
                <div class="efficiency-item">
                    <div class="efficiency-icon">🏆</div>
                    <div class="efficiency-number">12</div>
                    <div class="efficiency-label">连续使用天数</div>
                </div>
            </div>
        </div>

        <!-- 质量分析 -->
        <div class="quality-section">
            <div class="section-title">
                <i class="fas fa-chart-bar section-icon"></i>
                质量分析
            </div>
            
            <div class="quality-chart">
                <div class="chart-placeholder">
                    <i class="fas fa-chart-line" style="margin-right: 8px;"></i>
                    质量趋势图表
                </div>
            </div>
            
            <div class="quality-metrics">
                <div class="quality-metric">
                    <div class="metric-score">9.2</div>
                    <div class="metric-label">专业度</div>
                </div>
                <div class="quality-metric">
                    <div class="metric-score">8.8</div>
                    <div class="metric-label">个性化</div>
                </div>
                <div class="quality-metric">
                    <div class="metric-score">9.0</div>
                    <div class="metric-label">完整性</div>
                </div>
            </div>
        </div>

        <!-- 成就展示 -->
        <div class="achievement-section">
            <div class="section-title">
                <i class="fas fa-trophy section-icon"></i>
                成就徽章
            </div>
            
            <div class="achievement-grid">
                <div class="achievement-item">
                    <div class="achievement-icon">🚀</div>
                    <div class="achievement-title">效率达人</div>
                    <div class="achievement-desc">单日生成10条评语</div>
                    <div class="achievement-progress">
                        <div class="progress-fill" style="width: 100%;"></div>
                    </div>
                </div>
                
                <div class="achievement-item">
                    <div class="achievement-icon">⭐</div>
                    <div class="achievement-title">质量专家</div>
                    <div class="achievement-desc">连续5次8分以上</div>
                    <div class="achievement-progress">
                        <div class="progress-fill" style="width: 100%;"></div>
                    </div>
                </div>
                
                <div class="achievement-item">
                    <div class="achievement-icon">📚</div>
                    <div class="achievement-title">进步导师</div>
                    <div class="achievement-desc">累计生成100条评语</div>
                    <div class="achievement-progress">
                        <div class="progress-fill" style="width: 100%;"></div>
                    </div>
                </div>
                
                <div class="achievement-item locked">
                    <div class="achievement-icon">👑</div>
                    <div class="achievement-title">AI教育专家</div>
                    <div class="achievement-desc">累计生成500条评语</div>
                    <div class="achievement-progress">
                        <div class="progress-fill" style="width: 31%;"></div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 趋势分析 -->
        <div class="trend-section">
            <div class="section-title">
                <i class="fas fa-chart-line section-icon"></i>
                使用趋势
            </div>
            
            <div class="trend-chart">
                <i class="fas fa-chart-area" style="margin-right: 8px;"></i>
                30天使用趋势图表
            </div>
        </div>
    </div>

    <!-- 底部导航 -->
    <div class="bottom-nav">
        <div class="nav-grid">
            <div class="nav-item">
                <div class="nav-icon"><i class="fas fa-home"></i></div>
                <div class="nav-label">首页</div>
            </div>
            <div class="nav-item">
                <div class="nav-icon"><i class="fas fa-magic"></i></div>
                <div class="nav-label">生成</div>
            </div>
            <div class="nav-item">
                <div class="nav-icon"><i class="fas fa-file-alt"></i></div>
                <div class="nav-label">作品</div>
            </div>
            <div class="nav-item active">
                <div class="nav-icon"><i class="fas fa-chart-line"></i></div>
                <div class="nav-label">报告</div>
            </div>
            <div class="nav-item">
                <div class="nav-icon"><i class="fas fa-cog"></i></div>
                <div class="nav-label">设置</div>
            </div>
        </div>
    </div>
</body>
</html>
