/**
 * 数据同步工具
 * 提供本地存储和云端同步功能
 */

/**
 * 数据同步管理器
 */
class DataSyncManager {
  constructor() {
    this.syncInProgress = false;
    this.lastSyncTime = wx.getStorageSync('lastSyncTime') || 0;
    this.listeners = new Map(); // 数据变更监听器
    this.realTimeSync = true; // 是否启用实时同步
    this.syncQueue = []; // 同步队列
    this.isOnline = true; // 网络状态

    // 初始化网络状态监听
    this.initNetworkListener();
  }

  /**
   * 初始化网络状态监听
   */
  initNetworkListener() {
    // 监听网络状态变化
    wx.onNetworkStatusChange((res) => {
      this.isOnline = res.isConnected;
      console.log('网络状态变化:', res.isConnected ? '已连接' : '已断开');

      // 网络恢复时自动同步
      if (res.isConnected && this.syncQueue.length > 0) {
        this.processSyncQueue();
      }
    });

    // 获取初始网络状态
    wx.getNetworkType({
      success: (res) => {
        this.isOnline = res.networkType !== 'none';
      }
    });
  }

  /**
   * 保存数据到本地和云端
   */
  async saveData(key, data, options = {}) {
    try {
      // 1. 立即保存到本地
      wx.setStorageSync(key, data);
      
      // 2. 标记需要同步
      this.markForSync(key, data);
      
      // 3. 如果需要立即同步
      if (options.immediate) {
        await this.syncToCloud();
      }
      
      return { success: true };
    } catch (error) {
      console.error('保存数据失败:', error);
      return { success: false, error };
    }
  }

  /**
   * 标记数据需要同步
   */
  markForSync(key, data) {
    const pendingSync = wx.getStorageSync('pendingSync') || {};
    pendingSync[key] = {
      data: data,
      timestamp: Date.now(),
      synced: false
    };
    wx.setStorageSync('pendingSync', pendingSync);
  }

  /**
   * 同步数据到云端
   */
  async syncToCloud() {
    if (this.syncInProgress) {
      return { success: false, message: '同步进行中' };
    }

    try {
      this.syncInProgress = true;
      
      const pendingSync = wx.getStorageSync('pendingSync') || {};
      const unsyncedData = Object.keys(pendingSync).filter(
        key => !pendingSync[key].synced
      );

      if (unsyncedData.length === 0) {
        return { success: true, message: '没有需要同步的数据' };
      }

      // 模拟云端同步（后续替换为真实API）
      await this.mockCloudSync(pendingSync, unsyncedData);

      // 标记为已同步
      unsyncedData.forEach(key => {
        pendingSync[key].synced = true;
      });
      wx.setStorageSync('pendingSync', pendingSync);
      wx.setStorageSync('lastSyncTime', Date.now());

      return { 
        success: true, 
        message: `成功同步${unsyncedData.length}项数据` 
      };

    } catch (error) {
      console.error('同步失败:', error);
      return { success: false, error };
    } finally {
      this.syncInProgress = false;
    }
  }

  /**
   * 模拟云端同步（开发阶段）
   */
  async mockCloudSync(pendingSync, unsyncedData) {
    return new Promise((resolve) => {
      setTimeout(() => {
        console.log('模拟云端同步:', unsyncedData.map(key => ({
          key,
          dataSize: JSON.stringify(pendingSync[key].data).length
        })));
        resolve();
      }, 2000);
    });
  }

  /**
   * 从云端恢复数据
   */
  async restoreFromCloud() {
    try {
      // 模拟从云端获取数据（后续替换为真实API）
      const cloudData = await this.mockCloudRestore();
      
      if (cloudData && Object.keys(cloudData).length > 0) {
        // 恢复数据到本地
        Object.keys(cloudData).forEach(key => {
          wx.setStorageSync(key, cloudData[key]);
        });
        
        return { 
          success: true, 
          message: `成功恢复${Object.keys(cloudData).length}项数据`,
          data: cloudData
        };
      }
      
      return { success: true, message: '云端暂无数据' };
      
    } catch (error) {
      console.error('数据恢复失败:', error);
      return { success: false, error };
    }
  }

  /**
   * 模拟云端数据恢复
   */
  async mockCloudRestore() {
    return new Promise((resolve) => {
      setTimeout(() => {
        // 模拟返回云端数据
        resolve({
          // userInfo: { nickName: '云端老师', school: '云端学校' }
        });
      }, 1500);
    });
  }

  /**
   * 获取同步状态
   */
  getSyncStatus() {
    const pendingSync = wx.getStorageSync('pendingSync') || {};
    const totalItems = Object.keys(pendingSync).length;
    const syncedItems = Object.keys(pendingSync).filter(
      key => pendingSync[key].synced
    ).length;
    
    return {
      totalItems,
      syncedItems,
      pendingItems: totalItems - syncedItems,
      lastSyncTime: this.lastSyncTime,
      lastSyncTimeText: this.formatSyncTime(this.lastSyncTime)
    };
  }

  /**
   * 格式化同步时间
   */
  formatSyncTime(timestamp) {
    if (!timestamp) return '从未同步';
    
    const now = Date.now();
    const diff = now - timestamp;
    
    if (diff < 60000) return '刚刚';
    if (diff < 3600000) return `${Math.floor(diff / 60000)}分钟前`;
    if (diff < 86400000) return `${Math.floor(diff / 3600000)}小时前`;
    
    const date = new Date(timestamp);
    return `${date.getMonth() + 1}月${date.getDate()}日`;
  }

  /**
   * 清除本地数据
   */
  clearLocalData() {
    const keysToKeep = ['lastSyncTime']; // 保留的关键数据
    const storage = wx.getStorageInfoSync();
    
    storage.keys.forEach(key => {
      if (!keysToKeep.includes(key)) {
        wx.removeStorageSync(key);
      }
    });
  }

  /**
   * 数据备份
   */
  async backupData() {
    try {
      const allData = {};
      const storage = wx.getStorageInfoSync();

      storage.keys.forEach(key => {
        allData[key] = wx.getStorageSync(key);
      });

      // 生成备份文件内容
      const backupContent = JSON.stringify({
        version: '3.0.0',
        timestamp: Date.now(),
        data: allData
      }, null, 2);

      return {
        success: true,
        content: backupContent,
        size: backupContent.length
      };

    } catch (error) {
      console.error('数据备份失败:', error);
      return { success: false, error };
    }
  }

  /**
   * 云端数据同步
   */
  async cloudSync() {
    try {
      const { cloudService } = require('../services/cloudService');
      const { realTimeSyncManager } = require('./realTimeSync');

      // 触发全量数据同步
      await realTimeSyncManager.syncAllData();

      return {
        success: true,
        message: '数据已同步到云端',
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      console.error('云端同步失败:', error);
      return {
        success: false,
        message: '云端同步失败: ' + error.message
      };
    }
  }

  /**
   * 云端数据恢复
   */
  async cloudRestore() {
    try {
      const { cloudService } = require('../services/cloudService');

      // 获取所有云端数据
      const [studentsResult, classesResult, recordsResult] = await Promise.all([
        cloudService.getStudentList(),
        cloudService.getClassList(),
        cloudService.getRecordList({ pageSize: 1000 })
      ]);

      const data = {
        students: studentsResult.success ? studentsResult.data : [],
        classes: classesResult.success ? classesResult.data : [],
        records: recordsResult.success ? recordsResult.data : []
      };

      return {
        success: true,
        message: '数据已从云端恢复',
        data: data
      };
    } catch (error) {
      console.error('云端恢复失败:', error);
      return {
        success: false,
        message: '云端恢复失败: ' + error.message
      };
    }
  }

  /**
   * 添加数据变更监听器
   */
  addListener(dataType, callback) {
    if (!this.listeners.has(dataType)) {
      this.listeners.set(dataType, new Set());
    }
    this.listeners.get(dataType).add(callback);

    return () => {
      // 返回取消监听的函数
      const listeners = this.listeners.get(dataType);
      if (listeners) {
        listeners.delete(callback);
      }
    };
  }

  /**
   * 移除数据变更监听器
   */
  removeListener(dataType, callback) {
    const listeners = this.listeners.get(dataType);
    if (listeners) {
      listeners.delete(callback);
    }
  }

  /**
   * 通知数据变更
   */
  notifyDataChange(dataType, data, action = 'update') {
    const listeners = this.listeners.get(dataType);
    if (listeners && listeners.size > 0) {
      listeners.forEach(callback => {
        try {
          callback({ dataType, data, action, timestamp: Date.now() });
        } catch (error) {
          console.error('数据变更通知失败:', error);
        }
      });
    }
  }

  /**
   * 处理同步队列
   */
  async processSyncQueue() {
    if (!this.isOnline || this.syncQueue.length === 0) {
      return;
    }

    console.log('开始处理同步队列，待同步项目:', this.syncQueue.length);

    while (this.syncQueue.length > 0) {
      const syncItem = this.syncQueue.shift();
      try {
        await this.executeSyncItem(syncItem);
      } catch (error) {
        console.error('同步项目执行失败:', error);
        // 失败的项目重新加入队列（最多重试3次）
        if (syncItem.retryCount < 3) {
          syncItem.retryCount = (syncItem.retryCount || 0) + 1;
          this.syncQueue.push(syncItem);
        }
      }
    }
  }

  /**
   * 执行单个同步项目
   */
  async executeSyncItem(syncItem) {
    const { type, action, data, key } = syncItem;

    switch (type) {
      case 'student':
        await this.syncStudentData(action, data);
        break;
      case 'class':
        await this.syncClassData(action, data);
        break;
      case 'record':
        await this.syncRecordData(action, data);
        break;
      case 'comment':
        await this.syncCommentData(action, data);
        break;
      default:
        console.warn('未知的同步类型:', type);
    }

    // 同步成功后通知数据变更
    this.notifyDataChange(type, data, action);
  }

  /**
   * 同步学生数据
   */
  async syncStudentData(action, data) {
    const { cloudService } = require('../services/cloudService');

    switch (action) {
      case 'create':
        return await cloudService.addStudent(data);
      case 'update':
        return await cloudService.updateStudent(data._id, data);
      case 'delete':
        return await cloudService.deleteStudent(data._id);
    }
  }

  /**
   * 同步班级数据
   */
  async syncClassData(action, data) {
    const { cloudService } = require('../services/cloudService');

    switch (action) {
      case 'create':
        return await cloudService.addClass(data);
      case 'update':
        return await cloudService.updateClass(data._id, data);
      case 'delete':
        return await cloudService.deleteClass(data._id);
    }
  }

  /**
   * 同步记录数据
   */
  async syncRecordData(action, data) {
    const { cloudService } = require('../services/cloudService');

    switch (action) {
      case 'create':
        return await cloudService.addRecord(data);
      case 'update':
        return await cloudService.updateRecord(data._id, data);
      case 'delete':
        return await cloudService.deleteRecord(data._id);
    }
  }

  /**
   * 同步评语数据
   */
  async syncCommentData(action, data) {
    const { cloudService } = require('../services/cloudService');

    switch (action) {
      case 'create':
        return await cloudService.saveComment(data);
      case 'update':
        return await cloudService.updateComment(data._id, data);
      case 'delete':
        return await cloudService.deleteComment(data._id);
    }
  }

  /**
   * 添加到同步队列
   */
  addToSyncQueue(type, action, data) {
    const syncItem = {
      type,
      action,
      data,
      timestamp: Date.now(),
      retryCount: 0
    };

    this.syncQueue.push(syncItem);

    // 如果在线且启用实时同步，立即处理
    if (this.isOnline && this.realTimeSync) {
      this.processSyncQueue();
    }
  }

  /**
   * 启用/禁用实时同步
   */
  setRealTimeSync(enabled) {
    this.realTimeSync = enabled;
    console.log('实时同步已', enabled ? '启用' : '禁用');
  }

  /**
   * 获取同步状态
   */
  getSyncStatus() {
    return {
      isOnline: this.isOnline,
      realTimeSync: this.realTimeSync,
      queueLength: this.syncQueue.length,
      lastSyncTime: this.lastSyncTime,
      syncInProgress: this.syncInProgress
    };
  }
}

// 创建全局实例
const dataSyncManager = new DataSyncManager();

module.exports = {
  dataSyncManager,
  DataSyncManager
};
