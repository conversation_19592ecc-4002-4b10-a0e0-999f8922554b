/**
 * 生产环境数据验证器
 * 确保所有数据都来自真实数据库，没有测试数据
 */

/**
 * 数据来源验证配置
 */
const DATA_SOURCE_CONFIG = {
  // 必须从数据库获取的数据类型
  REQUIRED_DB_SOURCES: [
    'students',
    'comments', 
    'classes',
    'records',
    'users'
  ],
  
  // 测试数据特征
  TEST_DATA_PATTERNS: {
    // 测试ID模式
    testIds: /^(test_|mock_|demo_|sample_)/,
    
    // 测试姓名
    testNames: [
      '张小明', '李小红', '王小刚', '陈小美', '刘小强', 
      '赵小丽', '孙小军', '周小花', '吴小东', '郑小西'
    ],
    
    // 测试班级
    testClasses: /^(一|二|三|四|五|六)年级[1-9]班$/,
    
    // 测试学号
    testStudentNumbers: /^00[0-9]$/,
    
    // 测试评语内容
    testCommentContent: [
      '同学在本学期表现优秀',
      '学习态度认真，课堂积极发言',
      '希望继续保持这种良好的学习习惯'
    ]
  }
};

/**
 * 验证单个数据项是否为测试数据
 */
function isTestDataItem(item, dataType) {
  if (!item || typeof item !== 'object') {
    return false;
  }
  
  const patterns = DATA_SOURCE_CONFIG.TEST_DATA_PATTERNS;
  
  // 检查ID
  if (item.id && patterns.testIds.test(item.id.toString())) {
    return true;
  }
  
  // 检查姓名
  if (item.name && patterns.testNames.includes(item.name)) {
    return true;
  }
  
  if (item.studentName && patterns.testNames.includes(item.studentName)) {
    return true;
  }
  
  // 检查班级
  if (item.className && patterns.testClasses.test(item.className)) {
    return true;
  }
  
  // 检查学号
  if (item.studentNumber && patterns.testStudentNumbers.test(item.studentNumber)) {
    return true;
  }
  
  // 检查评语内容
  if (dataType === 'comments') {
    const content = item.content || item.comment || '';
    if (patterns.testCommentContent.some(pattern => content.includes(pattern))) {
      return true;
    }
  }
  
  return false;
}

/**
 * 验证数据数组
 */
function validateDataArray(dataArray, dataType) {
  if (!Array.isArray(dataArray)) {
    return {
      isValid: true,
      testItems: [],
      message: '数据格式正确'
    };
  }
  
  const testItems = [];
  
  dataArray.forEach((item, index) => {
    if (isTestDataItem(item, dataType)) {
      testItems.push({
        index,
        item: {
          id: item.id,
          name: item.name || item.studentName,
          className: item.className,
          type: dataType
        },
        reason: '检测到测试数据特征'
      });
    }
  });
  
  return {
    isValid: testItems.length === 0,
    testItems,
    totalCount: dataArray.length,
    testCount: testItems.length,
    message: testItems.length === 0 
      ? `✅ ${dataType} 数据验证通过 (${dataArray.length}条)`
      : `❌ 发现 ${testItems.length} 条测试数据 (总计${dataArray.length}条)`
  };
}

/**
 * 验证页面数据
 */
function validatePageData(pageData, pageName) {
  console.log(`🔍 验证页面数据: ${pageName}`);
  
  const results = {};
  
  // 验证学生数据
  if (pageData.allStudents) {
    results.students = validateDataArray(pageData.allStudents, 'students');
  }
  if (pageData.studentList) {
    results.students = validateDataArray(pageData.studentList, 'students');
  }
  
  // 验证评语数据
  if (pageData.generatedComments) {
    results.comments = validateDataArray(pageData.generatedComments, 'comments');
  }
  if (pageData.commentList) {
    results.comments = validateDataArray(pageData.commentList, 'comments');
  }
  if (pageData.works) {
    results.works = validateDataArray(pageData.works, 'comments');
  }
  
  // 验证班级数据
  if (pageData.classList) {
    results.classes = validateDataArray(pageData.classList, 'classes');
  }
  
  // 统计结果
  const allValid = Object.values(results).every(result => result.isValid);
  const totalTestItems = Object.values(results).reduce((sum, result) => sum + result.testCount, 0);
  
  return {
    pageName,
    isValid: allValid,
    results,
    totalTestItems,
    message: allValid 
      ? `✅ ${pageName} 页面数据验证通过`
      : `❌ ${pageName} 页面发现 ${totalTestItems} 条测试数据`
  };
}

/**
 * 强制清理测试数据
 */
function forceCleanTestData(dataArray, dataType) {
  if (!Array.isArray(dataArray)) {
    return dataArray;
  }
  
  const cleanData = dataArray.filter(item => !isTestDataItem(item, dataType));
  
  if (cleanData.length !== dataArray.length) {
    console.log(`🧹 ${dataType} 数据清理: ${dataArray.length} → ${cleanData.length} (移除${dataArray.length - cleanData.length}条测试数据)`);
  }
  
  return cleanData;
}

/**
 * 数据来源标记
 */
function markDataSource(dataArray, source = 'database') {
  if (!Array.isArray(dataArray)) {
    return dataArray;
  }
  
  return dataArray.map(item => ({
    ...item,
    _dataSource: source,
    _validatedAt: new Date().toISOString()
  }));
}

/**
 * 生产环境数据检查
 */
function performProductionCheck() {
  console.log('🔍 执行生产环境数据检查...');
  
  const issues = [];
  
  // 检查本地存储
  try {
    const storageInfo = wx.getStorageInfoSync();
    const suspiciousKeys = storageInfo.keys.filter(key => 
      key.includes('test') || key.includes('mock') || key.includes('demo') || key.includes('sample')
    );
    
    if (suspiciousKeys.length > 0) {
      issues.push({
        type: 'localStorage',
        severity: 'warning',
        message: `本地存储中发现疑似测试数据: ${suspiciousKeys.join(', ')}`
      });
    }
  } catch (error) {
    console.warn('⚠️ 无法检查本地存储:', error);
  }
  
  // 检查全局数据
  try {
    const app = getApp();
    if (app && app.globalData) {
      const globalKeys = Object.keys(app.globalData);
      const suspiciousGlobalKeys = globalKeys.filter(key => 
        key.includes('test') || key.includes('mock') || key.includes('demo')
      );
      
      if (suspiciousGlobalKeys.length > 0) {
        issues.push({
          type: 'globalData',
          severity: 'error',
          message: `全局数据中发现测试数据: ${suspiciousGlobalKeys.join(', ')}`
        });
      }
    }
  } catch (error) {
    console.warn('⚠️ 无法检查全局数据:', error);
  }
  
  return {
    isClean: issues.length === 0,
    issues,
    summary: issues.length === 0 
      ? '✅ 生产环境检查通过，未发现测试数据'
      : `❌ 发现 ${issues.length} 个问题需要处理`
  };
}

/**
 * 数据库连接验证
 */
async function validateDatabaseConnection() {
  try {
    console.log('🔗 验证数据库连接...');
    
    const app = getApp();
    const cloudService = app.globalData.cloudService;
    
    if (!cloudService) {
      return {
        isConnected: false,
        message: '❌ 云服务未初始化'
      };
    }
    
    // 尝试获取用户信息来测试连接
    const result = await cloudService.getCurrentUser();
    
    return {
      isConnected: true,
      message: '✅ 数据库连接正常',
      userInfo: result.success ? result.data : null
    };
  } catch (error) {
    return {
      isConnected: false,
      message: `❌ 数据库连接失败: ${error.message}`,
      error
    };
  }
}

module.exports = {
  isTestDataItem,
  validateDataArray,
  validatePageData,
  forceCleanTestData,
  markDataSource,
  performProductionCheck,
  validateDatabaseConnection,
  DATA_SOURCE_CONFIG
};
