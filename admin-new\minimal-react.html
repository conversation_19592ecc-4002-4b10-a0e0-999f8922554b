<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>极简React测试 - 评语灵感君</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 1px solid #e8e8e8;
        }
        .card {
            border: 1px solid #d9d9d9;
            border-radius: 6px;
            padding: 16px;
            margin: 16px 0;
            background: #fafafa;
        }
        .button {
            background: #1890ff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .button:hover {
            background: #40a9ff;
        }
        .counter {
            font-size: 24px;
            font-weight: bold;
            color: #1890ff;
        }
    </style>
</head>
<body>
    <div id="root">
        <div class="container">
            <div class="header">
                <h1>⏳ 正在加载React应用...</h1>
                <p>如果这个消息一直显示，说明React加载失败</p>
            </div>
        </div>
    </div>

    <!-- React CDN -->
    <script crossorigin src="https://unpkg.com/react@18/umd/react.development.js"></script>
    <script crossorigin src="https://unpkg.com/react-dom@18/umd/react-dom.development.js"></script>
    <script src="https://unpkg.com/@babel/standalone/babel.min.js"></script>

    <script type="text/babel">
        const { useState, useEffect } = React;

        // 简单的计数器组件
        function Counter() {
            const [count, setCount] = useState(0);
            
            return (
                <div className="card">
                    <h3>🔢 计数器测试</h3>
                    <div className="counter">当前计数: {count}</div>
                    <button className="button" onClick={() => setCount(count + 1)}>
                        增加
                    </button>
                    <button className="button" onClick={() => setCount(count - 1)}>
                        减少
                    </button>
                    <button className="button" onClick={() => setCount(0)}>
                        重置
                    </button>
                </div>
            );
        }

        // 状态管理测试组件
        function StateTest() {
            const [message, setMessage] = useState('初始消息');
            const [items, setItems] = useState(['项目1', '项目2', '项目3']);
            
            const addItem = () => {
                const newItem = `项目${items.length + 1}`;
                setItems([...items, newItem]);
            };
            
            return (
                <div className="card">
                    <h3>📝 状态管理测试</h3>
                    <p>消息: {message}</p>
                    <button className="button" onClick={() => setMessage('消息已更新!')}>
                        更新消息
                    </button>
                    
                    <h4>列表项目:</h4>
                    <ul>
                        {items.map((item, index) => (
                            <li key={index}>{item}</li>
                        ))}
                    </ul>
                    <button className="button" onClick={addItem}>
                        添加项目
                    </button>
                </div>
            );
        }

        // 网络请求测试组件
        function NetworkTest() {
            const [status, setStatus] = useState('未测试');
            const [loading, setLoading] = useState(false);
            
            const testNetwork = async () => {
                setLoading(true);
                setStatus('测试中...');
                
                try {
                    const response = await fetch('https://api.github.com/zen');
                    if (response.ok) {
                        const data = await response.text();
                        setStatus(`✅ 网络正常: ${data.substring(0, 50)}...`);
                    } else {
                        setStatus(`❌ 网络错误: ${response.status}`);
                    }
                } catch (error) {
                    setStatus(`❌ 请求失败: ${error.message}`);
                }
                
                setLoading(false);
            };
            
            return (
                <div className="card">
                    <h3>🌐 网络请求测试</h3>
                    <p>状态: {status}</p>
                    <button 
                        className="button" 
                        onClick={testNetwork}
                        disabled={loading}
                    >
                        {loading ? '测试中...' : '测试网络'}
                    </button>
                </div>
            );
        }

        // 主应用组件
        function App() {
            const [currentTime, setCurrentTime] = useState(new Date());
            
            useEffect(() => {
                const timer = setInterval(() => {
                    setCurrentTime(new Date());
                }, 1000);
                
                return () => clearInterval(timer);
            }, []);
            
            return (
                <div className="container">
                    <div className="header">
                        <h1>🚀 极简React测试成功!</h1>
                        <p>当前时间: {currentTime.toLocaleString()}</p>
                        <p>如果你能看到这个页面和时间更新，说明React基本功能正常</p>
                    </div>
                    
                    <Counter />
                    <StateTest />
                    <NetworkTest />
                    
                    <div className="card">
                        <h3>✅ 测试结果</h3>
                        <ul>
                            <li>✅ React组件渲染正常</li>
                            <li>✅ useState Hook正常</li>
                            <li>✅ useEffect Hook正常</li>
                            <li>✅ 事件处理正常</li>
                            <li>✅ 列表渲染正常</li>
                            <li>✅ 条件渲染正常</li>
                        </ul>
                        <p><strong>结论:</strong> React基础功能完全正常，问题出在Vite配置或依赖上</p>
                    </div>
                </div>
            );
        }

        // 渲染应用
        const root = ReactDOM.createRoot(document.getElementById('root'));
        root.render(<App />);
    </script>
</body>
</html>
