/**
 * 广告组件 - 统一管理小程序广告
 */

// 广告位ID配置（需要在微信公众平台申请）
const AD_UNIT_IDS = {
  banner: 'adunit-xxxxxxxxxxxxxxxx',      // 横幅广告
  interstitial: 'adunit-yyyyyyyyyyyyyyyy', // 插屏广告
  video: 'adunit-zzzzzzzzzzzzzzzz',       // 激励视频广告
  grid: 'adunit-aaaaaaaaaaaaaaaa'         // 格子广告
}

// Banner广告组件
export const BannerAd = () => {
  return (
    <ad 
      unit-id={AD_UNIT_IDS.banner}
      ad-intervals={30}
      bindload="onAdLoad"
      binderror="onAdError"
      style="width: 100%; height: 100px; margin: 10px 0;"
    />
  )
}

// 激励视频广告
export const showRewardVideoAd = () => {
  const videoAd = wx.createRewardedVideoAd({
    adUnitId: AD_UNIT_IDS.video
  })
  
  return new Promise((resolve, reject) => {
    videoAd.onLoad(() => {
      console.log('激励视频广告加载成功')
    })
    
    videoAd.onClose(res => {
      if (res.isEnded) {
        // 用户看完了广告，给予奖励
        resolve(true)
      } else {
        // 用户中途关闭
        resolve(false)
      }
    })
    
    videoAd.onError(err => {
      console.error('激励视频广告错误:', err)
      reject(err)
    })
    
    videoAd.show()
  })
}

// 插屏广告
export const showInterstitialAd = () => {
  const interstitialAd = wx.createInterstitialAd({
    adUnitId: AD_UNIT_IDS.interstitial
  })
  
  interstitialAd.onLoad(() => {
    console.log('插屏广告加载成功')
  })
  
  interstitialAd.onError(err => {
    console.error('插屏广告错误:', err)
  })
  
  interstitialAd.show()
}

// 广告展示频率控制
let adShowCount = 0
export const shouldShowAd = () => {
  adShowCount++
  // 每使用5次功能显示1次广告
  return adShowCount % 5 === 0
}