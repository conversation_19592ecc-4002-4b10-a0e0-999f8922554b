<!--
  班级数据分析页面
-->
<view class="analytics-page">
  <!-- 加载状态 -->
  <van-loading wx:if="{{loading}}" type="spinner" size="20px" />

  <view wx:else>
    <!-- 页面头部 -->
    <view class="page-header">
      <view class="class-info">
        <view class="class-name">{{classInfo.name}}</view>
        <view class="class-desc">{{classInfo.grade}} - {{classInfo.subject}}</view>
      </view>
      
      <!-- 时间范围选择 -->
      <view class="time-range-selector">
        <van-dropdown-menu>
          <van-dropdown-item value="{{timeRange}}" options="{{timeRangeOptions}}" bind:change="onTimeRangeChange" />
        </van-dropdown-menu>
      </view>
    </view>

    <!-- 数据概览卡片 -->
    <view class="overview-cards">
      <view class="overview-card">
        <view class="card-icon student-icon">
          <van-icon name="friends-o" size="20px" color="#fff" />
        </view>
        <view class="card-content">
          <view class="card-number">{{statistics.studentCount}}</view>
          <view class="card-label">学生总数</view>
        </view>
      </view>

      <view class="overview-card">
        <view class="card-icon record-icon">
          <van-icon name="edit" size="20px" color="#fff" />
        </view>
        <view class="card-content">
          <view class="card-number">{{statistics.totalRecords}}</view>
          <view class="card-label">记录总数</view>
        </view>
      </view>

      <view class="overview-card">
        <view class="card-icon score-icon">
          <van-icon name="star-o" size="20px" color="#fff" />
        </view>
        <view class="card-content">
          <view class="card-number">{{statistics.avgScore}}</view>
          <view class="card-label">平均分</view>
        </view>
      </view>
    </view>

    <!-- 行为分布统计 -->
    <view class="behavior-stats-section">
      <view class="section-title">行为分布</view>
      <view class="behavior-stats">
        <view class="behavior-stat positive">
          <view class="stat-icon">
            <van-icon name="like-o" size="18px" color="#52C873" />
          </view>
          <view class="stat-content">
            <view class="stat-percentage">{{statistics.positiveRate}}%</view>
            <view class="stat-label">积极行为</view>
          </view>
        </view>

        <view class="behavior-stat negative">
          <view class="stat-icon">
            <van-icon name="warning-o" size="18px" color="#FF5247" />
          </view>
          <view class="stat-content">
            <view class="stat-percentage">{{statistics.negativeRate}}%</view>
            <view class="stat-label">消极行为</view>
          </view>
        </view>

        <view class="behavior-stat neutral">
          <view class="stat-icon">
            <van-icon name="info-o" size="18px" color="#FFB800" />
          </view>
          <view class="stat-content">
            <view class="stat-percentage">{{statistics.neutralRate}}%</view>
            <view class="stat-label">中性行为</view>
          </view>
        </view>

        <view class="behavior-stat academic">
          <view class="stat-icon">
            <van-icon name="bookmark-o" size="18px" color="#4080FF" />
          </view>
          <view class="stat-content">
            <view class="stat-percentage">{{statistics.academicRate}}%</view>
            <view class="stat-label">学习表现</view>
          </view>
        </view>
      </view>
    </view>

    <!-- 图表区域 -->
    <view class="charts-section">
      <!-- 分数分布图 -->
      <view class="chart-card">
        <view class="chart-title">分数分布</view>
        <view class="chart-content">
          <view class="score-distribution">
            <view 
              wx:for="{{chartData.scoreDistribution}}" 
              wx:key="name"
              class="score-bar"
            >
              <view class="bar-label">{{item.name}}</view>
              <view class="bar-container">
                <view 
                  class="bar-fill" 
                  style="width: {{item.value / statistics.totalRecords * 100}}%"
                ></view>
              </view>
              <view class="bar-value">{{item.value}}</view>
            </view>
          </view>
        </view>
      </view>

      <!-- 周活跃度 -->
      <view class="chart-card">
        <view class="chart-title">每日活跃度</view>
        <view class="chart-content">
          <view class="weekly-trend">
            <view 
              wx:for="{{chartData.weeklyTrend}}" 
              wx:key="name"
              class="trend-item"
            >
              <view class="trend-bar-container">
                <view 
                  class="trend-bar" 
                  style="height: {{item.heightPercent}}%"
                ></view>
              </view>
              <view class="trend-label">{{item.name}}</view>
              <view class="trend-value">{{item.value}}</view>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 学生排行榜 -->
    <view class="ranking-section">
      <view class="section-header">
        <view class="section-title">学生排行榜</view>
        <view class="ranking-subtitle">按平均分排序</view>
      </view>

      <view class="ranking-list">
        <view
          wx:for="{{studentRanking}}"
          wx:key="id"
          wx:if="{{index < 10}}"
          class="ranking-item"
        >
          <view class="ranking-number">
            <text class="rank">{{index + 1}}</text>
          </view>
          
          <view class="student-info">
            <view class="student-name">{{item.name}}</view>
            <view class="student-stats">
              <text class="record-count">{{item.totalRecords}}条记录</text>
              <text class="positive-rate">积极{{item.positiveCount}}条</text>
            </view>
          </view>

          <view class="student-score">
            <view class="avg-score">{{item.avgScore}}</view>
            <view class="score-label">平均分</view>
          </view>
        </view>
      </view>
    </view>

    <!-- 最近记录 -->
    <view class="recent-records-section">
      <view class="section-title">最近记录</view>
      
      <view wx:if="{{recentRecords.length > 0}}" class="records-list">
        <view 
          wx:for="{{recentRecords}}" 
          wx:key="id"
          wx:if="{{index < 10}}"
          class="record-item"
        >
          <view class="record-icon {{item.behaviorType}}">
            <van-icon 
              name="{{item.behaviorType === 'positive' ? 'like-o' : item.behaviorType === 'negative' ? 'warning-o' : item.behaviorType === 'academic' ? 'edit' : 'info-o'}}" 
              size="14px" 
              color="#fff" 
            />
          </view>
          
          <view class="record-content">
            <view class="record-header">
              <text class="student-name">{{item.studentName}}</text>
              <text class="record-time">{{item.timeText}}</text>
            </view>
            <view class="record-action">{{item.action}}</view>
            <view wx:if="{{item.description}}" class="record-desc">{{item.description}}</view>
          </view>

          <view class="record-score">
            <van-tag 
              type="{{item.behaviorType === 'positive' || item.behaviorType === 'academic' ? 'success' : item.behaviorType === 'negative' ? 'danger' : 'warning'}}"
              size="small"
            >
              {{item.score}}分
            </van-tag>
          </view>
        </view>
      </view>

      <view wx:else class="empty-records">
        <van-icon name="notes-o" size="48px" color="#ddd" />
        <view class="empty-text">暂无记录数据</view>
      </view>
    </view>

    <!-- 底部操作 -->
    <view class="bottom-actions">
      <van-button 
        type="primary" 
        size="large" 
        custom-class="export-btn"
        bindtap="exportReport"
      >
        导出分析报告
      </van-button>
    </view>
  </view>
</view>