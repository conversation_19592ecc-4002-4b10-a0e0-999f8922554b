/**
 * AI评语生成云函数
 * 调用豆包AI API生成个性化评语
 */

const cloud = require('wx-server-sdk');

// 初始化云开发环境
cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
});

const db = cloud.database();

/**
 * 云函数入口函数
 */
exports.main = async (event, context) => {
  const { studentId, options = {} } = event;
  
  try {
    // 获取用户信息
    const { OPENID } = cloud.getWXContext();
    
    // 获取学生信息
    const studentResult = await db.collection('students')
      .where({
        _id: studentId,
        teacherId: OPENID
      })
      .get();
    
    if (studentResult.data.length === 0) {
      throw new Error('学生不存在或无权限访问');
    }
    
    const student = studentResult.data[0];
    
    // 获取学生的行为记录
    let recordQuery = db.collection('records')
      .where({
        studentId: studentId,
        teacherId: OPENID
      });
    
    // 时间范围筛选
    if (options.startDate && options.endDate) {
      recordQuery = recordQuery.where({
        createTime: db.command.gte(new Date(options.startDate))
          .and(db.command.lte(new Date(options.endDate)))
      });
    }
    
    const recordsResult = await recordQuery
      .orderBy('createTime', 'desc')
      .limit(20)
      .get();
    
    const records = recordsResult.data;
    
    // 构建AI提示词
    const prompt = buildPrompt(student, records, options);
    
    // 调用AI服务生成评语
    const aiResult = await callAIService(prompt, options);
    
    if (!aiResult.success) {
      throw new Error(aiResult.error || 'AI生成失败');
    }
    
    // 保存评语到数据库
    const commentData = {
      studentId: studentId,
      studentName: student.name,
      className: student.className,
      content: aiResult.content,
      style: options.style || 'warm',
      length: options.length || 'medium',
      generateOptions: options,
      teacherId: OPENID,
      createTime: new Date(),
      updateTime: new Date()
    };
    
    const saveResult = await db.collection('comments').add({
      data: commentData
    });
    
    return {
      success: true,
      data: {
        ...commentData,
        _id: saveResult._id
      }
    };
    
  } catch (error) {
    console.error('生成评语失败:', error);
    return {
      success: false,
      error: error.message || '生成评语失败'
    };
  }
};

/**
 * 构建AI提示词
 */
function buildPrompt(student, records, options) {
  let prompt = `请为学生生成一份个性化评语。\n\n`;
  
  // 学生信息
  prompt += `学生信息：\n`;
  prompt += `姓名：${student.name}\n`;
  if (student.className) {
    prompt += `班级：${student.className}\n`;
  }
  
  // 时间范围
  if (options.startDate && options.endDate) {
    const startDate = new Date(options.startDate).toLocaleDateString();
    const endDate = new Date(options.endDate).toLocaleDateString();
    prompt += `评价时间段：${startDate} 至 ${endDate}\n`;
  }
  
  // 行为记录
  if (records && records.length > 0) {
    prompt += `\n行为记录：\n`;
    records.forEach((record, index) => {
      const date = new Date(record.createTime).toLocaleDateString();
      prompt += `${index + 1}. [${date}] ${record.content}`;
      if (record.type) {
        prompt += ` (${record.type === 'positive' ? '积极表现' : record.type === 'negative' ? '需要改进' : '一般表现'})`;
      }
      prompt += `\n`;
    });
  } else {
    prompt += `\n暂无具体行为记录，请生成一份通用的鼓励性评语。\n`;
  }
  
  // 评语要求
  prompt += `\n评语要求：\n`;
  prompt += `- 风格：${getStyleDescription(options.style || 'warm')}\n`;
  prompt += `- 长度：${getLengthDescription(options.length || 'medium')}\n`;
  
  if (options.focus && options.focus.length > 0) {
    prompt += `- 重点关注：${options.focus.join('、')}\n`;
  }
  
  if (options.includeAdvice) {
    prompt += `- 包含具体的学习建议\n`;
  }
  
  if (options.includeEncouragement) {
    prompt += `- 包含鼓励和期望\n`;
  }
  
  if (options.customRequirement) {
    prompt += `- 特殊要求：${options.customRequirement}\n`;
  }
  
  prompt += `\n请生成一份温暖、具体、有针对性的学生评语。`;
  
  return prompt;
}

/**
 * 调用AI服务
 */
async function callAIService(prompt, options = {}) {
  try {
    // 这里应该调用实际的AI API
    // 由于云函数环境限制，这里使用模拟数据
    
    // 模拟AI生成的评语
    const mockComments = [
      '该学生在本学期表现优秀，学习态度认真，积极参与课堂讨论，与同学相处融洽。希望继续保持这种良好的学习状态，在新的学期里取得更大的进步。',
      '学生学习努力，课堂表现积极，能够按时完成作业。在团队合作中表现出色，乐于助人。建议在学习方法上多加思考，提高学习效率。',
      '该学生性格开朗，乐观向上，在学习中遇到困难能够主动寻求帮助。课堂参与度较高，与老师和同学的互动良好。期待在下个学期看到更多的成长。'
    ];
    
    const randomComment = mockComments[Math.floor(Math.random() * mockComments.length)];
    
    // 模拟API延迟
    await new Promise(resolve => setTimeout(resolve, 1000 + Math.random() * 2000));
    
    return {
      success: true,
      content: randomComment,
      usage: {
        prompt_tokens: prompt.length,
        completion_tokens: randomComment.length,
        total_tokens: prompt.length + randomComment.length
      }
    };
    
  } catch (error) {
    console.error('AI服务调用失败:', error);
    return {
      success: false,
      error: error.message || 'AI服务调用失败'
    };
  }
}

/**
 * 获取风格描述
 */
function getStyleDescription(style) {
  const styleMap = {
    'formal': '正式严谨，客观专业',
    'warm': '温暖亲切，充满关爱',
    'encouraging': '积极向上，充满鼓励',
    'detailed': '详细具体，深入分析'
  };
  return styleMap[style] || '温暖亲切';
}

/**
 * 获取长度描述
 */
function getLengthDescription(length) {
  const lengthMap = {
    'short': '简洁明了（50-80字）',
    'medium': '适中详细（100-150字）',
    'long': '详细全面（200-300字）'
  };
  return lengthMap[length] || '适中详细（100-150字）';
}
