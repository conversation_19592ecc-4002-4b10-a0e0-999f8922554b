# 小程序端数据服务接口文档

## 概述

本文档定义了小程序端数据服务的实现接口，用于与管理后台保持数据访问的一致性。

## 文件位置建议

```
/interfaces/
  ├── IDataService.ts        # 统一接口定义
/utils/
  ├── dataService.js         # 小程序数据服务实现
  ├── cloudFunctionHelper.js # 云函数调用辅助工具
```

## 小程序数据服务实现示例

### 1. 数据服务主类 (/utils/dataService.js)

```javascript
/**
 * 小程序端数据服务实现
 * 实现与管理后台一致的数据访问接口
 */

class MiniProgramDataService {
  constructor() {
    console.log('📱 小程序数据服务初始化')
  }

  // ==================== 基础数据查询 ====================

  async getDashboardStats() {
    try {
      const res = await wx.cloud.callFunction({
        name: 'dataQuery',
        data: { action: 'getDashboardStats' }
      })

      if (res.result.code === 200) {
        return res.result.data
      } else {
        throw new Error(res.result.message || '数据获取失败')
      }
    } catch (error) {
      console.error('获取仪表板统计失败:', error)
      return {
        totalUsers: 0,
        todayComments: 0,
        aiCalls: 0,
        satisfaction: 0,
        lastUpdated: new Date().toISOString()
      }
    }
  }

  async getRecentActivities(limit = 10) {
    try {
      const res = await wx.cloud.callFunction({
        name: 'dataQuery',
        data: { 
          action: 'getRecentActivities',
          params: { limit }
        }
      })

      if (res.result.code === 200) {
        return res.result.data
      } else {
        throw new Error(res.result.message || '活动记录获取失败')
      }
    } catch (error) {
      console.error('获取活动记录失败:', error)
      return []
    }
  }

  // ==================== 学生数据管理 ====================

  async getStudents(params = {}) {
    try {
      const res = await wx.cloud.callFunction({
        name: 'getStudents',
        data: params
      })

      if (res.result.code === 200) {
        return res.result.data
      } else {
        throw new Error(res.result.message || '学生数据获取失败')
      }
    } catch (error) {
      console.error('获取学生数据失败:', error)
      return {
        list: [],
        pagination: {
          total: 0,
          page: 1,
          limit: 20,
          pages: 0
        }
      }
    }
  }

  async createStudent(studentData) {
    try {
      const res = await wx.cloud.callFunction({
        name: 'adminAPI',
        data: {
          action: 'data.createStudent',
          ...studentData
        }
      })

      if (res.result.code === 200) {
        return res.result.data
      } else {
        throw new Error(res.result.message || '学生创建失败')
      }
    } catch (error) {
      console.error('创建学生失败:', error)
      throw error
    }
  }

  async updateStudent(studentId, updates) {
    try {
      const res = await wx.cloud.callFunction({
        name: 'adminAPI',
        data: {
          action: 'data.updateStudent',
          id: studentId,
          ...updates
        }
      })

      if (res.result.code === 200) {
        return res.result.data
      } else {
        throw new Error(res.result.message || '学生信息更新失败')
      }
    } catch (error) {
      console.error('更新学生信息失败:', error)
      throw error
    }
  }

  async deleteStudent(studentId) {
    try {
      const res = await wx.cloud.callFunction({
        name: 'adminAPI',
        data: {
          action: 'data.deleteStudent',
          id: studentId
        }
      })

      return res.result.code === 200
    } catch (error) {
      console.error('删除学生失败:', error)
      return false
    }
  }

  // ==================== 评语数据管理 ====================

  async generateComment(commentData) {
    try {
      const res = await wx.cloud.callFunction({
        name: 'generateComment',
        data: commentData
      })

      if (res.result.code === 200) {
        return res.result.data
      } else {
        throw new Error(res.result.message || '评语生成失败')
      }
    } catch (error) {
      console.error('生成评语失败:', error)
      throw error
    }
  }

  async getComments(params = {}) {
    try {
      const res = await wx.cloud.callFunction({
        name: 'dataQuery',
        data: { 
          action: 'getComments',
          params
        }
      })

      if (res.result.code === 200) {
        return res.result.data
      } else {
        throw new Error(res.result.message || '评语数据获取失败')
      }
    } catch (error) {
      console.error('获取评语数据失败:', error)
      return {
        list: [],
        total: 0,
        page: 1,
        limit: 20
      }
    }
  }

  // ==================== 用户管理 ====================

  async login(userInfo = {}) {
    try {
      const res = await wx.cloud.callFunction({
        name: 'login',
        data: {
          userInfo,
          role: 'teacher'
        }
      })

      if (res.result.code === 200) {
        return res.result.data
      } else {
        throw new Error(res.result.message || '登录失败')
      }
    } catch (error) {
      console.error('用户登录失败:', error)
      throw error
    }
  }

  async getCurrentUser() {
    try {
      const res = await wx.cloud.callFunction({
        name: 'adminAPI',
        data: {
          action: 'auth.getCurrentUser'
        }
      })

      if (res.result.code === 200) {
        return res.result.data
      } else {
        throw new Error(res.result.message || '获取用户信息失败')
      }
    } catch (error) {
      console.error('获取当前用户失败:', error)
      return null
    }
  }

  // ==================== 系统配置 ====================

  async testConnection() {
    try {
      const res = await wx.cloud.callFunction({
        name: 'dataQuery',
        data: { action: 'testConnection' }
      })

      if (res.result.code === 200) {
        return res.result.data
      } else {
        throw new Error(res.result.message || '连接测试失败')
      }
    } catch (error) {
      console.error('连接测试失败:', error)
      return {
        success: false,
        message: error.message,
        collections: [],
        sampleData: null
      }
    }
  }
}

// 创建单例实例
const miniProgramDataService = new MiniProgramDataService()

// 导出数据服务
module.exports = {
  dataService: miniProgramDataService,
  MiniProgramDataService
}
```

### 2. 云函数调用辅助工具 (/utils/cloudFunctionHelper.js)

```javascript
/**
 * 云函数调用辅助工具
 * 提供统一的错误处理和重试机制
 */

class CloudFunctionHelper {
  constructor() {
    this.maxRetries = 3
    this.retryDelay = 1000 // 1秒
  }

  /**
   * 调用云函数并处理错误
   */
  async callFunction(functionName, data = {}, options = {}) {
    const { maxRetries = this.maxRetries, showLoading = true } = options

    if (showLoading) {
      wx.showLoading({
        title: '加载中...',
        mask: true
      })
    }

    try {
      for (let attempt = 1; attempt <= maxRetries; attempt++) {
        try {
          console.log(`☁️ 调用云函数 ${functionName} (尝试 ${attempt}/${maxRetries})`)
          
          const result = await wx.cloud.callFunction({
            name: functionName,
            data: {
              ...data,
              timestamp: Date.now(),
              requestId: this.generateRequestId()
            }
          })

          if (result.result) {
            console.log(`✅ 云函数 ${functionName} 调用成功`)
            return result
          } else {
            throw new Error('云函数返回结果为空')
          }
        } catch (error) {
          console.error(`❌ 云函数 ${functionName} 调用失败 (尝试 ${attempt}):`, error)
          
          if (attempt < maxRetries) {
            await this.delay(this.retryDelay * attempt)
            continue
          } else {
            throw error
          }
        }
      }
    } finally {
      if (showLoading) {
        wx.hideLoading()
      }
    }
  }

  /**
   * 批量调用云函数
   */
  async callFunctions(calls = []) {
    try {
      wx.showLoading({
        title: '批量处理中...',
        mask: true
      })

      const results = await Promise.allSettled(
        calls.map(call => this.callFunction(call.name, call.data, { showLoading: false }))
      )

      return results.map((result, index) => ({
        functionName: calls[index].name,
        success: result.status === 'fulfilled',
        data: result.status === 'fulfilled' ? result.value : null,
        error: result.status === 'rejected' ? result.reason : null
      }))
    } finally {
      wx.hideLoading()
    }
  }

  /**
   * 生成请求ID
   */
  generateRequestId() {
    return `mp_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  /**
   * 延迟函数
   */
  delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms))
  }

  /**
   * 显示错误提示
   */
  showError(title, message) {
    wx.showModal({
      title: title || '操作失败',
      content: message || '请稍后重试',
      showCancel: false,
      confirmText: '确定'
    })
  }

  /**
   * 显示成功提示
   */
  showSuccess(message) {
    wx.showToast({
      title: message || '操作成功',
      icon: 'success',
      duration: 2000
    })
  }
}

// 创建单例实例
const cloudHelper = new CloudFunctionHelper()

module.exports = {
  cloudHelper,
  CloudFunctionHelper
}
```

### 3. 页面中的使用示例

```javascript
// pages/index/index.js
const { dataService } = require('../../utils/dataService')
const { cloudHelper } = require('../../utils/cloudFunctionHelper')

Page({
  data: {
    stats: null,
    students: [],
    loading: true
  },

  onLoad() {
    this.loadData()
  },

  async loadData() {
    try {
      this.setData({ loading: true })

      // 并行获取数据
      const [stats, students] = await Promise.all([
        dataService.getDashboardStats(),
        dataService.getStudents({ page: 1, limit: 10 })
      ])

      this.setData({
        stats,
        students: students.list || [],
        loading: false
      })

    } catch (error) {
      console.error('数据加载失败:', error)
      cloudHelper.showError('数据加载失败', error.message)
      this.setData({ loading: false })
    }
  },

  async onStudentTap(e) {
    const studentId = e.currentTarget.dataset.id
    
    try {
      const student = await dataService.getStudent(studentId)
      if (student) {
        wx.navigateTo({
          url: `/pages/student/detail?id=${studentId}`
        })
      }
    } catch (error) {
      cloudHelper.showError('学生信息获取失败', error.message)
    }
  },

  async onGenerateComment(e) {
    const studentId = e.currentTarget.dataset.id
    
    try {
      wx.showLoading({ title: '生成中...' })
      
      const comment = await dataService.generateComment({
        studentId,
        templateType: 'general',
        style: 'formal',
        length: 'medium'
      })

      wx.hideLoading()
      cloudHelper.showSuccess('评语生成成功')
      
      // 跳转到评语详情页面
      wx.navigateTo({
        url: `/pages/comment/detail?id=${comment.id}`
      })

    } catch (error) {
      wx.hideLoading()
      cloudHelper.showError('评语生成失败', error.message)
    }
  },

  onPullDownRefresh() {
    this.loadData().finally(() => {
      wx.stopPullDownRefresh()
    })
  }
})
```

## 接口统一性保证

### 1. 数据格式统一

确保小程序端和管理后台返回相同的数据格式：

```javascript
// 统一的响应格式
{
  code: 200,
  message: 'success',
  data: { /* 实际数据 */ },
  timestamp: '2024-01-01T00:00:00.000Z'
}

// 统一的分页格式
{
  list: [],
  total: 0,
  page: 1,
  limit: 20,
  pages: 0
}
```

### 2. 错误处理统一

```javascript
// 统一的错误处理
try {
  const result = await dataService.someOperation()
  return result
} catch (error) {
  console.error('操作失败:', error)
  
  // 小程序端显示方式
  wx.showToast({
    title: error.message || '操作失败',
    icon: 'none'
  })
  
  // 返回默认值或重新抛出错误
  return defaultValue || throw error
}
```

### 3. 缓存策略统一

```javascript
// 数据缓存辅助函数
class CacheHelper {
  static set(key, data, ttl = 300000) { // 5分钟
    const cacheData = {
      data,
      timestamp: Date.now(),
      ttl
    }
    wx.setStorageSync(key, cacheData)
  }

  static get(key) {
    try {
      const cacheData = wx.getStorageSync(key)
      if (!cacheData) return null

      const now = Date.now()
      if (now - cacheData.timestamp > cacheData.ttl) {
        wx.removeStorageSync(key)
        return null
      }

      return cacheData.data
    } catch (error) {
      return null
    }
  }

  static clear(pattern) {
    const info = wx.getStorageInfoSync()
    info.keys.forEach(key => {
      if (!pattern || key.includes(pattern)) {
        wx.removeStorageSync(key)
      }
    })
  }
}
```

## 部署和维护建议

1. **代码复用**: 将通用的数据处理逻辑抽取到共享模块
2. **版本控制**: 保持小程序端和管理后台的接口版本同步
3. **测试覆盖**: 确保两端的数据服务都有完整的测试用例
4. **文档更新**: 接口变更时同步更新两端的实现

## 总结

通过实现统一的数据服务接口，可以确保小程序端和管理后台在数据访问上的一致性，减少维护成本，提高代码质量。