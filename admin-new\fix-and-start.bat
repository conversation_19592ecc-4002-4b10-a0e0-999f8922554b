@echo off
title 评语灵感君管理后台 - 修复启动
color 0A
echo.
echo 🔧 正在检查并修复管理后台启动问题...
echo ==================================================

REM 检查Node.js
echo ✅ 检查Node.js环境...
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Node.js未安装或不在PATH中
    pause
    exit /b 1
)

REM 检查端口占用
echo 📡 检查端口占用情况...
for /f "tokens=5" %%a in ('netstat -ano ^| findstr 8080') do (
    taskkill /f /pid %%a >nul 2>&1
    echo 🔄 已释放被占用的端口8080
)

REM 检查依赖
echo 📦 检查必要依赖...
if not exist node_modules (
    echo 📥 正在安装依赖...
    call npm install
    if %errorlevel% neq 0 (
        echo ❌ 依赖安装失败
        pause
        exit /b 1
    )
)

REM 检查Vite是否安装
echo 🔍 检查Vite...
npx vite --version >nul 2>&1
if %errorlevel% neq 0 (
    echo 📥 正在安装Vite...
    call npm install -D vite
)

REM 清理缓存
echo 🧹 清理缓存...
rmdir /s /q node_modules\.vite 2>nul
if exist .vite rmdir /s /q .vite 2>nul

echo.
echo 🌟 启动管理后台服务...
echo --------------------------------------------------
echo 📱 访问地址: http://localhost:8080
echo 🔄 热重载端口: 8081
echo 🔧 使用修复配置: vite.config.fixed.ts
echo --------------------------------------------------

SET VITE_HMR_PORT=8081
SET VITE_DEV_SERVER_PORT=8080
SET NODE_ENV=development

call npx vite --config vite.config.fixed.ts --port 8080 --host --strictPort=false --force

if %errorlevel% neq 0 (
    echo.
    echo ❌ 启动失败，尝试备用方案...
    call npx vite --port 8080 --host --force
)

pause