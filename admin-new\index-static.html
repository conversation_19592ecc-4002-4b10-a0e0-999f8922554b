<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>评语灵感君管理后台</title>
    <style>
      /* 基础样式，确保在JS加载前页面不会完全空白 */
      body {
        margin: 0;
        padding: 0;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        background: #f5f5f5;
      }
      
      #root {
        min-height: 100vh;
        display: flex;
        align-items: center;
        justify-content: center;
      }
      
      .loading {
        text-align: center;
        color: #666;
      }
      
      .loading::after {
        content: '';
        display: inline-block;
        width: 20px;
        height: 20px;
        border: 2px solid #ddd;
        border-top: 2px solid #1890ff;
        border-radius: 50%;
        animation: spin 1s linear infinite;
        margin-left: 10px;
      }
      
      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }
    </style>
  </head>
  <body>
    <div id="root">
      <div class="loading">正在加载评语灵感君管理后台...</div>
    </div>
    
    <!-- 使用传统script标签，不使用type="module" -->
    <script src="./assets/app.js"></script>
  </body>
</html>
