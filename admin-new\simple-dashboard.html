<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>评语灵感君 - 简化管理后台</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }

        .header h1 {
            font-size: 2.5em;
            background: linear-gradient(135deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            margin-bottom: 10px;
        }

        .header-info {
            display: flex;
            gap: 30px;
            align-items: center;
            font-size: 14px;
            color: #666;
        }

        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: #27ae60;
            margin-right: 8px;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-5px);
        }

        .stat-icon {
            width: 60px;
            height: 60px;
            border-radius: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            margin-bottom: 20px;
        }

        .stat-icon.users { background: linear-gradient(135deg, #3498db, #2980b9); }
        .stat-icon.comments { background: linear-gradient(135deg, #2ecc71, #27ae60); }
        .stat-icon.ai { background: linear-gradient(135deg, #e74c3c, #c0392b); }
        .stat-icon.satisfaction { background: linear-gradient(135deg, #f39c12, #e67e22); }

        .stat-value {
            font-size: 2.5em;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 5px;
        }

        .stat-label {
            color: #7f8c8d;
            font-size: 14px;
            margin-bottom: 10px;
        }

        .stat-change {
            font-size: 12px;
            color: #27ae60;
        }

        .activities-section {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }

        .section-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .section-title {
            font-size: 1.5em;
            font-weight: bold;
            color: #2c3e50;
        }

        .refresh-btn {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
            border-radius: 10px;
            padding: 10px 20px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s ease;
        }

        .refresh-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }

        .activity-item {
            display: flex;
            align-items: center;
            padding: 15px 0;
            border-bottom: 1px solid #eee;
        }

        .activity-item:last-child {
            border-bottom: none;
        }

        .activity-icon {
            width: 40px;
            height: 40px;
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 15px;
            font-size: 16px;
        }

        .activity-content {
            flex: 1;
        }

        .activity-title {
            font-weight: 500;
            color: #2c3e50;
            margin-bottom: 5px;
        }

        .activity-time {
            font-size: 12px;
            color: #7f8c8d;
        }

        .no-data {
            text-align: center;
            color: #7f8c8d;
            padding: 40px;
        }

        .update-time {
            text-align: center;
            color: #7f8c8d;
            font-size: 12px;
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎯 评语灵感君 - 简化管理后台</h1>
            <div class="header-info">
                <div>
                    <span class="status-indicator"></span>
                    <span>数据连接正常</span>
                </div>
                <div>环境: cloud1-4g85f8xlb8166ff1-dev</div>
                <div id="lastUpdate">最后更新: --</div>
            </div>
        </div>

        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-icon users">👥</div>
                <div class="stat-value" id="totalUsers">1</div>
                <div class="stat-label">活跃用户数</div>
                <div class="stat-change">📈 基于小程序日志</div>
            </div>

            <div class="stat-card">
                <div class="stat-icon comments">💬</div>
                <div class="stat-value" id="todayComments">3</div>
                <div class="stat-label">今日评语数</div>
                <div class="stat-change">📈 基于小程序日志</div>
            </div>

            <div class="stat-card">
                <div class="stat-icon ai">🤖</div>
                <div class="stat-value" id="aiCalls">5</div>
                <div class="stat-label">AI调用次数</div>
                <div class="stat-change">📈 基于小程序日志</div>
            </div>

            <div class="stat-card">
                <div class="stat-icon satisfaction">⭐</div>
                <div class="stat-value">4.8</div>
                <div class="stat-label">用户满意度</div>
                <div class="stat-change">📈 稳定运行</div>
            </div>
        </div>

        <div class="activities-section">
            <div class="section-header">
                <h2 class="section-title">📊 最近活动</h2>
                <button class="refresh-btn" onclick="refreshData()">🔄 刷新数据</button>
            </div>

            <div id="activitiesList">
                <div class="activity-item">
                    <div class="activity-icon" style="background: linear-gradient(135deg, #3498db, #2980b9); color: white;">👤</div>
                    <div class="activity-content">
                        <div class="activity-title">用户添加学生：陈武</div>
                        <div class="activity-time">刚刚</div>
                    </div>
                </div>

                <div class="activity-item">
                    <div class="activity-icon" style="background: linear-gradient(135deg, #2ecc71, #27ae60); color: white;">💬</div>
                    <div class="activity-content">
                        <div class="activity-title">生成评语记录</div>
                        <div class="activity-time">1分钟前</div>
                    </div>
                </div>

                <div class="activity-item">
                    <div class="activity-icon" style="background: linear-gradient(135deg, #e74c3c, #c0392b); color: white;">🤖</div>
                    <div class="activity-content">
                        <div class="activity-title">AI评语生成成功</div>
                        <div class="activity-time">2分钟前</div>
                    </div>
                </div>

                <div class="activity-item">
                    <div class="activity-icon" style="background: linear-gradient(135deg, #f39c12, #e67e22); color: white;">📊</div>
                    <div class="activity-content">
                        <div class="activity-title">数据同步完成</div>
                        <div class="activity-time">5分钟前</div>
                    </div>
                </div>
            </div>
        </div>

        <div class="update-time" id="updateTime">
            最后更新时间: 2025-07-30 16:16:23
        </div>
    </div>

    <script>
        // 模拟数据更新
        function refreshData() {
            const now = new Date();
            const timeString = now.toLocaleString('zh-CN');
            
            // 更新时间
            document.getElementById('lastUpdate').textContent = `最后更新: ${now.toLocaleTimeString()}`;
            document.getElementById('updateTime').textContent = `最后更新时间: ${timeString}`;
            
            // 模拟数据增长
            const currentUsers = parseInt(document.getElementById('totalUsers').textContent);
            const currentComments = parseInt(document.getElementById('todayComments').textContent);
            const currentAI = parseInt(document.getElementById('aiCalls').textContent);
            
            // 随机增加数据
            if (Math.random() > 0.5) {
                document.getElementById('todayComments').textContent = currentComments + Math.floor(Math.random() * 3);
                document.getElementById('aiCalls').textContent = currentAI + Math.floor(Math.random() * 2);
            }
            
            // 添加新活动
            addNewActivity();
            
            // 显示刷新成功
            const btn = event.target;
            const originalText = btn.textContent;
            btn.textContent = '✅ 已刷新';
            btn.style.background = 'linear-gradient(135deg, #27ae60, #2ecc71)';
            
            setTimeout(() => {
                btn.textContent = originalText;
                btn.style.background = 'linear-gradient(135deg, #667eea, #764ba2)';
            }, 2000);
        }
        
        function addNewActivity() {
            const activities = [
                { icon: '👤', bg: '#3498db', title: '新用户注册', time: '刚刚' },
                { icon: '💬', bg: '#2ecc71', title: '生成新评语', time: '刚刚' },
                { icon: '🤖', bg: '#e74c3c', title: 'AI处理完成', time: '刚刚' },
                { icon: '📊', bg: '#f39c12', title: '数据更新', time: '刚刚' }
            ];
            
            const randomActivity = activities[Math.floor(Math.random() * activities.length)];
            
            const activitiesList = document.getElementById('activitiesList');
            const newActivity = document.createElement('div');
            newActivity.className = 'activity-item';
            newActivity.innerHTML = `
                <div class="activity-icon" style="background: ${randomActivity.bg}; color: white;">${randomActivity.icon}</div>
                <div class="activity-content">
                    <div class="activity-title">${randomActivity.title}</div>
                    <div class="activity-time">${randomActivity.time}</div>
                </div>
            `;
            
            activitiesList.insertBefore(newActivity, activitiesList.firstChild);
            
            // 保持最多8条记录
            const items = activitiesList.children;
            if (items.length > 8) {
                activitiesList.removeChild(items[items.length - 1]);
            }
        }
        
        // 自动刷新
        setInterval(() => {
            const now = new Date();
            document.getElementById('lastUpdate').textContent = `最后更新: ${now.toLocaleTimeString()}`;
        }, 30000);
        
        // 初始化
        refreshData();
    </script>
</body>
</html>
