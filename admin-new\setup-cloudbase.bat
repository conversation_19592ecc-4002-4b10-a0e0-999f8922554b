@echo off
title 云开发Web SDK环境配置

echo ===============================================
echo 🚀 评语灵感君管理后台 - 云开发Web SDK配置
echo ===============================================
echo.

echo 📦 安装云开发Web SDK...
npm install @cloudbase/js-sdk

echo.
echo ✅ 安装完成！
echo.
echo 📋 接下来需要手动配置：
echo.
echo 1. 获取云开发密钥：
echo    https://console.cloud.tencent.com/tcb
echo.
echo 2. 配置Web安全域名：
echo    - 云开发控制台 → 设置 → 安全配置
echo    - 添加: http://localhost:8080
echo.
echo 3. 设置数据库权限：
echo    - 云开发控制台 → 数据库 → 权限设置
echo    - 设置集合为"所有用户可读"
echo.
echo 4. 启动管理后台：
echo    npm run dev
echo.
echo 🔗 详细配置指南：CLOUDBASE_WEB_SDK_SETUP.md
echo.
pause