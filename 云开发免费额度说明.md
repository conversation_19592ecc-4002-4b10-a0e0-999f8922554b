# 🌸 腾讯云开发免费额度说明

## 📊 每日免费额度（新用户30天内）
- **云函数调用次数**：1000次/日
- **数据库读取次数**：50,000次/日  
- **数据库写入次数**：50,000次/日
- **OSS存储**：5GB/日

## 🧮 Dashboard刷新频率计算
- **当前设置**：30秒刷新1次
- **每日总计**：24小时 × 60分钟 × 2次/分钟 = 2880次
- **当前状态**：超出1000次免费额度 2880-1000=1880次

## ⚡ 优化建议
1. **建议频率修改**：45秒刷新1次
   - 每天总计：24×60×1.33=1920次（仍略超）
   - **最终建议**：60秒刷新（每天1440次，超出440次）

2. **智能降频方案**：
   - 高峰时段（8:00-18:00）：30秒
   - 低峰时段：3分钟
   - 夜间：5分钟

3. **实测建议**：先将用户身份验证改为Web SDK，避免CORS导致的失败调用消耗免费次数。