/* 紧急修复页面样式 */

.container {
  padding: 20rpx;
  background: #f5f7fa;
  min-height: 100vh;
}

.header {
  text-align: center;
  margin-bottom: 40rpx;
  padding: 40rpx 20rpx;
  background: linear-gradient(135deg, #e74c3c, #c0392b);
  border-radius: 20rpx;
  color: white;
}

.title {
  display: block;
  font-size: 48rpx;
  font-weight: bold;
  margin-bottom: 10rpx;
}

.subtitle {
  display: block;
  font-size: 28rpx;
  opacity: 0.9;
}

.status-card {
  background: white;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.1);
}

.status-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
  padding-bottom: 20rpx;
  border-bottom: 1rpx solid #eee;
}

.status-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #2c3e50;
}

.status-indicator {
  width: 20rpx;
  height: 20rpx;
  border-radius: 50%;
  background: #e74c3c;
}

.status-indicator.success {
  background: #27ae60;
}

.status-indicator.warning {
  background: #f39c12;
}

.status-item {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 15rpx;
}

.status-item:last-child {
  margin-bottom: 0;
}

.label {
  font-size: 28rpx;
  color: #7f8c8d;
  flex-shrink: 0;
  margin-right: 20rpx;
}

.value {
  font-size: 28rpx;
  color: #2c3e50;
  text-align: right;
  flex: 1;
  word-break: break-all;
}

.value.error {
  color: #e74c3c;
}

.actions {
  margin-bottom: 30rpx;
}

.btn {
  width: 100%;
  height: 88rpx;
  border-radius: 44rpx;
  font-size: 32rpx;
  margin-bottom: 20rpx;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
}

.btn.large {
  height: 100rpx;
  font-size: 36rpx;
}

.btn.small {
  height: 60rpx;
  font-size: 24rpx;
  width: auto;
  padding: 0 30rpx;
  margin: 0 10rpx;
}

.btn.primary {
  background: linear-gradient(135deg, #e74c3c, #c0392b);
  color: white;
}

.btn.secondary {
  background: linear-gradient(135deg, #3498db, #2980b9);
  color: white;
}

.btn.outline {
  background: white;
  color: #3498db;
  border: 2rpx solid #3498db;
}

.action-row {
  display: flex;
  gap: 20rpx;
}

.action-row .btn {
  flex: 1;
  margin-bottom: 0;
}

.info-card {
  background: white;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.1);
}

.info-header {
  margin-bottom: 20rpx;
  padding-bottom: 20rpx;
  border-bottom: 1rpx solid #eee;
}

.info-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #2c3e50;
}

.info-text {
  display: block;
  font-size: 28rpx;
  color: #2c3e50;
  line-height: 1.6;
  margin-bottom: 15rpx;
}

.info-list {
  margin: 20rpx 0;
  padding-left: 20rpx;
}

.info-item {
  display: block;
  font-size: 26rpx;
  color: #7f8c8d;
  line-height: 1.8;
  margin-bottom: 8rpx;
}

.logs-card {
  background: white;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.1);
}

.logs-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
  padding-bottom: 20rpx;
  border-bottom: 1rpx solid #eee;
  cursor: pointer;
}

.logs-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #2c3e50;
}

.logs-toggle {
  font-size: 26rpx;
  color: #3498db;
}

.logs-list {
  background: #2c3e50;
  color: #ecf0f1;
  padding: 20rpx;
  border-radius: 10rpx;
  font-family: monospace;
  font-size: 24rpx;
  max-height: 400rpx;
  overflow-y: auto;
  margin-bottom: 20rpx;
}

.log-item {
  display: block;
  line-height: 1.5;
  margin-bottom: 5rpx;
  word-break: break-all;
}

.logs-actions {
  text-align: center;
}

.footer {
  text-align: center;
  padding: 20rpx 0;
}
