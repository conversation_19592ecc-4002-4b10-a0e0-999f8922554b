{"compilerOptions": {"target": "ES2020", "module": "CommonJS", "lib": ["ES2020", "DOM"], "allowJs": true, "checkJs": false, "declaration": false, "outDir": "./dist", "rootDir": "./", "removeComments": true, "strict": true, "noImplicitAny": true, "strictNullChecks": true, "strictFunctionTypes": true, "strictBindCallApply": true, "strictPropertyInitialization": true, "noImplicitReturns": true, "noFallthroughCasesInSwitch": true, "noUncheckedIndexedAccess": true, "noImplicitOverride": true, "moduleResolution": "node", "baseUrl": "./", "paths": {"@/*": ["./*"], "@/components/*": ["./components/*"], "@/pages/*": ["./pages/*"], "@/utils/*": ["./utils/*"], "@/services/*": ["./services/*"], "@/store/*": ["./store/*"], "@/types/*": ["./types/*"]}, "allowSyntheticDefaultImports": true, "esModuleInterop": true, "experimentalDecorators": true, "emitDecoratorMetadata": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "isolatedModules": true, "noEmit": true}, "include": ["**/*.ts", "**/*.js", "types/**/*.d.ts"], "exclude": ["node_modules", "miniprogram_npm", "dist", "**/*.test.ts", "**/*.spec.ts", "admin-simple", "backend"], "typeAcquisition": {"enable": true, "include": ["miniprogram-api-typings"]}}