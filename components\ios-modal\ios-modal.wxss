/* iOS风格弹窗组件 - 高级简约设计 */

/* 遮罩层 */
.ios-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  backdrop-filter: blur(0px);
}

.ios-modal-overlay.show {
  background: rgba(0, 0, 0, 0.4);
  opacity: 1;
  visibility: visible;
  backdrop-filter: blur(20rpx);
}

/* 弹窗容器 */
.ios-modal-container {
  width: 540rpx;
  max-width: 90vw;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 28rpx;
  overflow: hidden;
  transform: scale(0.7) translateY(100rpx);
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  backdrop-filter: blur(40rpx);
  box-shadow: 
    0 20rpx 60rpx rgba(0, 0, 0, 0.15),
    0 8rpx 24rpx rgba(0, 0, 0, 0.1),
    inset 0 1rpx 0 rgba(255, 255, 255, 0.8);
}

.ios-modal-overlay.show .ios-modal-container {
  transform: scale(1) translateY(0);
}

/* 弹窗头部 */
.ios-modal-header {
  padding: 48rpx 40rpx 24rpx;
  text-align: center;
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.05);
}

.ios-modal-icon {
  margin-bottom: 16rpx;
}

.icon-emoji {
  font-size: 64rpx;
  line-height: 1;
}

.ios-modal-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #1d1d1f;
  line-height: 1.3;
  letter-spacing: -0.5rpx;
}

/* 弹窗内容 */
.ios-modal-content {
  padding: 32rpx 40rpx;
}

.ios-modal-description {
  font-size: 28rpx;
  color: #86868b;
  line-height: 1.5;
  text-align: center;
  margin-bottom: 32rpx;
}

/* 列表样式 */
.ios-modal-list {
  background: rgba(242, 242, 247, 0.6);
  border-radius: 20rpx;
  overflow: hidden;
  margin-bottom: 24rpx;
}

.ios-modal-list-item {
  display: flex;
  align-items: center;
  padding: 24rpx 32rpx;
  background: rgba(255, 255, 255, 0.8);
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.05);
  transition: all 0.2s ease;
}

.ios-modal-list-item:last-child {
  border-bottom: none;
}

.ios-modal-list-item.checked {
  background: rgba(52, 199, 89, 0.1);
}

.list-item-icon {
  width: 56rpx;
  height: 56rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 24rpx;
  flex-shrink: 0;
}

.item-emoji {
  font-size: 32rpx;
  line-height: 1;
}

.list-item-content {
  flex: 1;
  min-width: 0;
}

.list-item-title {
  font-size: 30rpx;
  font-weight: 500;
  color: #1d1d1f;
  line-height: 1.3;
  margin-bottom: 4rpx;
}

.list-item-subtitle {
  font-size: 24rpx;
  color: #86868b;
  line-height: 1.2;
}

.list-item-check {
  margin-left: 24rpx;
  flex-shrink: 0;
}

.check-circle {
  width: 44rpx;
  height: 44rpx;
  border-radius: 50%;
  border: 3rpx solid #d1d1d6;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.check-circle.checked {
  background: #34c759;
  border-color: #34c759;
}

.check-icon {
  font-size: 24rpx;
  color: white;
  font-weight: 600;
  line-height: 1;
}

/* 特性说明 */
.ios-modal-features {
  margin-top: 24rpx;
}

.feature-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 16rpx;
}

.feature-item:last-child {
  margin-bottom: 0;
}

.feature-dot {
  width: 8rpx;
  height: 8rpx;
  background: #86868b;
  border-radius: 50%;
  margin-top: 16rpx;
  margin-right: 16rpx;
  flex-shrink: 0;
}

.feature-text {
  font-size: 26rpx;
  color: #86868b;
  line-height: 1.4;
  flex: 1;
}

/* 弹窗按钮 */
.ios-modal-actions {
  display: flex;
  border-top: 1rpx solid rgba(0, 0, 0, 0.05);
}

.ios-modal-button {
  flex: 1;
  height: 88rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  position: relative;
}

.ios-modal-button:active {
  background: rgba(0, 0, 0, 0.05);
}

.ios-modal-button.cancel {
  border-right: 1rpx solid rgba(0, 0, 0, 0.05);
}

.ios-modal-button.cancel .button-text {
  color: #86868b;
  font-weight: 400;
}

.ios-modal-button.confirm .button-text {
  color: #007aff;
  font-weight: 600;
}

.button-text {
  font-size: 32rpx;
  line-height: 1;
}
