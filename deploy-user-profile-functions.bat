@echo off
chcp 65001 >nul
echo 部署用户信息相关云函数...
echo 当前目录: %cd%

echo.
echo 检查目录结构...
if not exist "cloudfunctions" (
    echo ❌ cloudfunctions 目录不存在！
    echo 请确保在项目根目录执行此脚本
    pause
    exit /b 1
)

echo ✅ cloudfunctions 目录存在
echo.

echo 1. 部署 saveUserProfile 云函数
if not exist "cloudfunctions\saveUserProfile" (
    echo ❌ saveUserProfile 目录不存在！
    pause
    exit /b 1
)

echo 进入 saveUserProfile 目录...
cd cloudfunctions\saveUserProfile
echo 当前目录: %cd%

echo 安装依赖...
npm install
if %errorlevel% neq 0 (
    echo ❌ npm install 失败
    cd ..\..
    pause
    exit /b 1
)

echo 返回根目录...  
cd ..\..
echo 当前目录: %cd%

echo 检查微信开发者工具命令行...
where wx >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 未找到微信开发者工具命令行工具
    echo 请在微信开发者工具中手动上传部署云函数
    echo 或者使用以下命令（在开发者工具终端中）:
    echo   右键 saveUserProfile 文件夹 -^> 上传并部署：云端安装依赖
    pause
    exit /b 1
)

echo 部署 saveUserProfile...
wx cloud deploy saveUserProfile
echo.

echo 2. 部署 getUserProfile 云函数
if not exist "cloudfunctions\getUserProfile" (
    echo ❌ getUserProfile 目录不存在！
    pause
    exit /b 1
)

echo 进入 getUserProfile 目录...
cd cloudfunctions\getUserProfile
npm install
if %errorlevel% neq 0 (
    echo ❌ npm install 失败
    cd ..\..
    pause
    exit /b 1
)

cd ..\..
echo 部署 getUserProfile...
wx cloud deploy getUserProfile

echo.
echo 🎉 部署完成！
pause