<!--
  登录页面
-->
<view class="login-page">
  <!-- 背景装饰 -->
  <view class="bg-decoration">
    <view class="bg-circle circle-1"></view>
    <view class="bg-circle circle-2"></view>
    <view class="bg-circle circle-3"></view>
  </view>

  <!-- 返回按钮 -->
  <view class="back-button" bindtap="goBack">
    <simple-icon name="arrow-left" size="20px" color="#fff" />
  </view>

  <!-- 主要内容 -->
  <view class="main-content">
    <!-- 应用图标和标题 -->
    <view class="app-info">
      <view class="app-icon">
        <image src="/images/logo-174.jpg" class="logo-image" mode="aspectFit" />
      </view>
      
      <view class="app-title">评语灵感君</view>
      <view class="app-subtitle">让每一句评语都充满温度</view>
      
      <view class="app-features">
        <view class="feature-item">
          <simple-icon name="check" size="14px" color="#52C873" />
          <text class="feature-text">AI智能评语</text>
        </view>
        <view class="feature-item">
          <simple-icon name="check" size="14px" color="#52C873" />
          <text class="feature-text">学生信息管理</text>
        </view>
        <view class="feature-item">
          <simple-icon name="check" size="14px" color="#52C873" />
          <text class="feature-text">完全免费使用</text>
        </view>
      </view>
    </view>

    <!-- 登录按钮 -->
    <view class="login-section">
      <view class="login-button" bindtap="wxLogin">
        <simple-icon name="wechat" size="20px" color="#fff" />
        <text class="login-text">微信登录/注册</text>
      </view>

      <!-- 游客模式入口 -->
      <view class="guest-button" bindtap="enterGuestMode">
        <text class="guest-text">先体验一下</text>
        <simple-icon name="arrow-right" size="14px" color="#999" />
      </view>

      <view class="login-tips">
        <text class="tips-text">登录即表示您同意我们的</text>
        <text class="link-text" bindtap="showUserAgreement">用户协议</text>
        <text class="tips-text">和</text>
        <text class="link-text" bindtap="showPrivacyPolicy">隐私政策</text>
        <text class="tips-text">。</text>
      </view>
    </view>
  </view>

  <!-- 用户协议弹窗 -->
  <simple-popup
    show="{{showAgreement}}"
    position="bottom"
    custom-style="height: 70%"
    bind:close="hideAgreement"
  >
    <view class="agreement-popup">
      <view class="popup-header">
        <view class="popup-title">用户协议</view>
        <simple-icon name="cross" size="18px" color="#999" bindtap="hideAgreement" />
      </view>
      
      <scroll-view class="agreement-content" scroll-y="{{true}}">
        <view class="agreement-text">
          <view class="section-title">1. 服务条款</view>
          <view class="section-content">
            欢迎使用评语灵感君AI评语助手。本协议是您与我们之间关于使用本服务的法律协议。
          </view>
          
          <view class="section-title">2. 服务内容</view>
          <view class="section-content">
            评语灵感君为教师提供学生管理、AI智能评语生成等教育辅助功能，完全免费使用。
          </view>
          
          <view class="section-title">3. 用户责任</view>
          <view class="section-content">
            用户应当合法、正当地使用本服务，不得利用本服务从事违法违规活动。
          </view>
          
          <view class="section-title">4. 隐私保护</view>
          <view class="section-content">
            我们重视用户隐私，将按照隐私政策保护用户个人信息安全。
          </view>
        </view>
      </scroll-view>
      
      <view class="popup-footer">
        <simple-button type="primary" size="large" bindtap="hideAgreement">
          我已阅读并同意
        </simple-button>
      </view>
    </view>
  </simple-popup>

  <!-- 隐私政策弹窗 -->
  <simple-popup
    show="{{showPrivacy}}"
    position="bottom"
    custom-style="height: 70%"
    bind:close="hidePrivacy"
  >
    <view class="agreement-popup">
      <view class="popup-header">
        <view class="popup-title">隐私政策</view>
        <simple-icon name="cross" size="18px" color="#999" bindtap="hidePrivacy" />
      </view>
      
      <scroll-view class="agreement-content" scroll-y="{{true}}">
        <view class="agreement-text">
          <view class="section-title">1. 信息收集</view>
          <view class="section-content">
            我们仅收集为您提供服务所必需的信息，包括基本用户信息和使用数据。
          </view>
          
          <view class="section-title">2. 信息使用</view>
          <view class="section-content">
            收集的信息仅用于提供和改善服务，不会用于其他商业目的。
          </view>
          
          <view class="section-title">3. 信息保护</view>
          <view class="section-content">
            我们采用行业标准的安全措施保护您的个人信息安全。
          </view>
          
          <view class="section-title">4. 信息共享</view>
          <view class="section-content">
            除法律要求外，我们不会与第三方分享您的个人信息。
          </view>
        </view>
      </scroll-view>
      
      <view class="popup-footer">
        <simple-button type="primary" size="large" bindtap="hidePrivacy">
          我已阅读并同意
        </simple-button>
      </view>
    </view>
  </simple-popup>
</view>
