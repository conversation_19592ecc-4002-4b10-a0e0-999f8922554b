/**
 * 安全升级后的主应用组件
 * 集成所有安全防护和性能优化
 */

import React, { useState, useEffect, useCallback, useMemo } from 'react'
import { useAuth } from './hooks/useAuth'
import { useAPIKeys } from './hooks/useAuth'
import { ErrorBoundary } from './components/ErrorBoundary'
import { LoadingSpinner } from './components/LoadingSpinner'
import { SecureStorageService } from './services/secureStorageService'

// 按需导入的组件将在这个整合版本中实现

const SecureApp: React.FC = () => {
  const { isAuthenticated, loading, login, logout, user } = useAuth()
  const [isLoading, setIsLoading] = useState(true)
  const [currentPage, setCurrentPage] = useState('dashboard')
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false)

  // 初始化
  useEffect(() => {
    setIsLoading(false)
  }, [])

  // 侧边栏切换函数
  const toggleSidebar = useCallback(() => {
    setSidebarCollapsed(prev => !prev)
  }, [])

  // 安全登出处理
  const handleLogout = useCallback(async () => {
    await logout()
    SecureStorageService.cache.clear() // 清理缓存
  }, [logout])

  // 菜单配置
  const menuItems = useMemo(() => [
    { key: 'dashboard', icon: '📊', label: '数据大屏', description: '实时数据监控' },
    { key: 'ai-config', icon: '🤖', label: 'AI配置', description: '模型参数设置' },
    { key: 'data-management', icon: '📝', label: '数据管理', description: '教师用户管理' },
    { key: 'settings', icon: '⚙️', label: '系统设置', description: '个人与系统配置' }
  ], [])

  // 页面渲染函数
  const renderContent = () => {
    switch (currentPage) {
      case 'dashboard':
        return <DashboardSecure />
      case 'ai-config':
        return <AIConfigSecure />
      case 'data-management':
        return <DataManagementSecure />
      case 'settings':
        return <SettingsSecure />
      default:
        return <DashboardSecure />
    }
  }

  // 安全登录组件
  const SecureLogin: React.FC = () => {
    const [credentials, setCredentials] = useState({ username: '', password: '' })
    const [localError, setLocalError] = useState('')

    const handleSubmit = useCallback(async (e: React.FormEvent) => {
      e.preventDefault()
      setLocalError('')
      
      const result = await login(credentials)
      if (!result.success) {
        setLocalError(result.error || '认证失败')
      }
    }, [credentials, login])

    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 flex items-center justify-center">
        <div className="w-full max-w-md">
          <div className="text-center mb-8">
            <div className="inline-flex items-center justify-center w-16 h-16 bg-green-100 rounded-full mb-4">
              <span className="text-3xl">🔒</span>
            </div>
            <h1 className="text-3xl font-bold text-white mb-2">AI管理后台</h1>
            <p className="text-purple-200">安全加密登录系统</p>
          </div>

          <div className="bg-white/10 backdrop-blur-lg rounded-2xl p-8 space-y-6">
            {/* 安全警告 */}
            <div className="bg-yellow-500/10 border border-yellow-500/30 rounded-lg p-3">
              <div className="text-xs text-yellow-200">
                ⚠️ 使用军用级AES-256加密，请确保网络安全
              </div>
            </div>

            <form onSubmit={handleSubmit} className="space-y-6">
              <div>
                <label className="block text-sm font-medium text-white mb-2">用户名</label>
                <input
                  type="text"
                  value={credentials.username}
                  onChange={(e) => setCredentials(prev => ({ ...prev, username: e.target.value }))}
                  className="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-lg text-white placeholder-gray-300"
                  placeholder="请输入用户名"
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-white mb-2">密码</label>
                <input
                  type="password"
                  value={credentials.password}
                  onChange={(e) => setCredentials(prev => ({ ...prev, password: e.target.value }))}
                  className="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-lg text-white placeholder-gray-300"
                  placeholder="请输入密码"
                  required
                />
              </div>

              {localError && (
                <div className="text-red-300 text-sm">{localError}</div>
              )}

              <button
                type="submit"
                disabled={loading}
                className="w-full bg-purple-600 hover:bg-purple-700 disabled:bg-gray-600 text-white font-semibold py-3 rounded-lg"
              >
                {loading ? '🔄 登录中...' : '🚀 安全登录'}
              </button>
            </form>

            {process.env.NODE_ENV === 'development' && (
              <div className="text-center">
                <p className="text-xs text-purple-300">
                  开发环境: admin / admin123
                </p>
              </div>
            )}
          </div>
        </div>
      </div>
    )
  }

  // Dashboard安全组件
  const DashboardSecure: React.FC = () => {
    return (
      <div style={{ display: 'grid', gap: '25px' }}>
        {/* 核心数据卡片 - 安全版 */}
        <div style={{
          display: 'grid',
          gridTemplateColumns: 'repeat(4, 1fr)',
          gap: '25px',
          marginBottom: '30px'
        }}>
          {[
            { title: '总用户数', value: '12,345', icon: '👥', color: '#667eea' },
            { title: '今日生成评语', value: '156', icon: '📝', color: '#51cf66' },
            { title: 'AI调用次数', value: '8,432', icon: '🤖', color: '#ff6b6b' },
            { title: '系统安全', value: '高', icon: '🔒', color: '#21c55e' }
          ].map((stat, index) => (
            <div
              key={index}
              className="theme-card theme-shadow"
              style={{
                backdropFilter: 'blur(20px)',
                padding: '30px',
                borderRadius: '20px',
                transition: 'all 0.3s ease',
                cursor: 'pointer'
              }}
            >
              <div style={{
                position: 'absolute',
                top: '-50px',
                right: '-50px',
                width: '120px',
                height: '120px',
                background: `${stat.color}15`,
                borderRadius: '50%'
              }}></div>
              
              <div style={{ position: 'relative', zIndex: 2 }}>
                <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', marginBottom: '15px' }}>
                  <div style={{
                    width: '50px',
                    height: '50px',
                    background: stat.color,
                    borderRadius: '12px',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    fontSize: '20px'
                  }}>
                    {stat.icon}
                  </div>
                </div>
                <h3 className="theme-text-primary" style={{
                  fontSize: '36px',
                  fontWeight: '700',
                  margin: '0 0 8px 0'
                }}>
                  {stat.value}
                </h3>
                <p className="theme-text-secondary" style={{
                  fontSize: '16px',
                  fontWeight: '500',
                  margin: 0
                }}>
                  {stat.title}
                </p>
              </div>
            </div>
          ))}
        </div>

        {/* 安全配置快速访问 */}
        <div 
          className="theme-card theme-shadow"
          style={{
            backdropFilter: 'blur(20px)',
            padding: '30px',
            borderRadius: '20px'
          }}>
          <h4 className="theme-text-primary" style={{ margin: '0 0 20px 0', fontSize: '18px', fontWeight: '700' }}>
            🔐 安全配置概览
          </h4>
          <div style={{ display: 'grid', gridTemplateColumns: 'repeat(3, 1fr)', gap: '15px' }}>
            <div className="bg-red-50 p-4 rounded-lg border border-red-200">
              <h5 className="text-red-800 font-semibold">API密钥状态</h5>
              <p className="text-sm text-red-600">已安全加密存储</p>
            </div>
            <div className="bg-green-50 p-4 rounded-lg border border-green-200">
              <h5 className="text-green-800 font-semibold">认证状态</h5>
              <p className="text-sm text-green-600">JWT验证已启用</p>
            </div>
            <div className="bg-blue-50 p-4 rounded-lg border border-blue-200">
              <h5 className="text-blue-800 font-semibold">数据保护</h5>
              <p className="text-sm text-blue-600">AES-256加密</p>
            </div>
          </div>
        </div>
      </div>
    )
  }

  // AI配置安全组件
  const AIConfigSecure: React.FC = () => {
    const { saveAPIConfig, getAllAPIConfigs } = useAPIKeys()
    const [configs, setConfigs] = useState({})

    useEffect(() => {
      loadConfigs()
    }, [])

    const loadConfigs = async () => {
      const result = await getAllAPIConfigs()
      if (result.success) {
        setConfigs(result.configs || {})
      }
    }

    return (
      <div style={{ display: 'grid', gap: '25px' }}>
        {/* API配置卡片 - 包含安全加密 */}
        <div className="theme-card theme-shadow">
          <h3 className="text-xl font-bold">AI模型配置（安全加密版）</h3>
          <p className="text-sm text-gray-600 mt-2">
            所有API密钥已使用AES-256加密存储，永不明文保存到本地
          </p>
        </div>
      </div>
    )
  }

  // 数据管理安全组件
  const DataManagementSecure: React.FC = () => {
    return (
      <div style={{ display: 'grid', gap: '25px' }}>
        <div className="theme-card theme-shadow">
          <h3 className="text-xl font-bold">教师用户管理（安全配置）</h3>
          <p className="text-sm text-gray-600 mt-2">
            用户数据已加密存储，支持批量安全管理
          </p>
        </div>
      </div>
    )
  }

  // 设置安全组件
  const SettingsSecure: React.FC = () => {
    return (
      <div style={{ display: 'grid', gap: '25px' }}>
        <div className="theme-card theme-shadow">
          <h3 className="text-xl font-bold">系统安全设置</h3>
          <div className="space-y-4 mt-4">
            <div>
              <h4>🔐 认证配置</h4>
              <p className="text-sm text-gray-600">JWT令牌有效期: 24小时</p>
            </div>
            <div>
              <h4>🔒 加密配置</h4>
              <p className="text-sm text-gray-600">所有敏感数据使用AES-256加密存储</p>
            </div>
            <div>
              <h4>🛡️ 安全监控</h4>
              <p className="text-sm text-gray-600">登录锁定机制和错误日志监控已启用</p>
            </div>
          </div>
        </div>
      </div>
    )
  }

  // 主要应用界面
  const MainApplication: React.FC = () => {
    return (
      <div className="theme-container theme-gradient-light" style={{
        minHeight: '100vh',
        fontFamily: '"SF Pro Display", -apple-system, sans-serif',
        display: 'flex'
      }}>
        {/* 侧边栏 - 虚化背景 */}
        <div className="theme-card" style={{
          width: sidebarCollapsed ? '80px' : '240px',
          backdropFilter: 'blur(20px)',
          transition: 'all 0.3s ease',
          position: 'fixed',
          height: '100vh',
          zIndex: 1000,
          borderRight: '1px solid var(--border-color)'
        }}>
          <div className="theme-border" style={{ padding: '25px', borderBottom: '1px solid', textAlign: 'center' }}>
            {!sidebarCollapsed && (
              <div>
                <h3 className="text-2xl font-bold">评语灵感君</h3>
                <p className="text-sm text-gray-600">安全管理系统</p>
              </div>
            )}
          </div>

          <div style={{ padding: '20px 15px' }}>
            {menuItems.map((item) => (
              <div
                key={item.key}
                onClick={() => setCurrentPage(item.key)}
                style={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: '15px',
                  padding: '15px',
                  marginBottom: '8px',
                  borderRadius: '12px',
                  cursor: 'pointer',
                  transition: 'all 0.3s ease',
                  background: currentPage === item.key 
                    ? 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)' 
                    : 'transparent',
                  color: currentPage === item.key ? 'white' : 'var(--text-primary)'
                }}
              >
                <span style={{ fontSize: '20px' }}>{item.icon}</span>
                {!sidebarCollapsed && (
                  <div>
                    <div style={{ fontSize: '20px', fontWeight: '800' }}>{item.label}</div>
                    <div style={{ fontSize: '16px', opacity: 0.7 }}>{item.description}</div>
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>

        {/* 主要内容区域 */}
        <div style={{
          flex: 1,
          marginLeft: sidebarCollapsed ? '80px' : '240px',
          transition: 'all 0.3s ease'
        }}>
          {/* 顶部导航 */}
          <div 
            className="theme-card theme-border"
            style={{
              backdropFilter: 'blur(20px)',
              padding: '20px 30px',
              borderBottom: '1px solid',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'space-between',
              position: 'sticky',
              top: 0,
              zIndex: 100
            }}
          >
            <div style={{ display: 'flex', alignItems: 'center', gap: '20px' }}>
              <button
                onClick={toggleSidebar}
                style={{
                  background: 'none',
                  border: 'none',
                  fontSize: '20px',
                  cursor: 'pointer',
                  padding: '8px',
                  borderRadius: '8px'
                }}
              >
                ☰
              </button>
              <div>
                <h2 className="text-2xl font-bold">
                  {currentPage === 'dashboard' ? '数据大屏' :
                   currentPage === 'ai-config' ? 'AI配置' :
                   currentPage === 'data-management' ? '数据管理' : '系统设置'}
                </h2>
                <p className="text-sm text-gray-600">
                  安全管理系统 v2.0 - AES-256加密保护
                </p>
              </div>
            </div>
            
            <div style={{ display: 'flex', alignItems: 'center', gap: '15px' }}>
              <div className="flex items-center gap-2 px-3 py-2 bg-green-100 rounded-full">
                <span className="text-green-600 font-medium">
                  {user?.username || '系统管理员'}
                </span>
                <span className="text-xs text-green-500">🔒已认证</span>
              </div>
              <button
                onClick={handleLogout}
                className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
              >
                安全退出
              </button>
            </div>
          </div>

          {/* 页面内容 */}
          <div style={{ padding: '30px' }}>
            {renderContent()}
          </div>
        </div>
      </div>
    )
  }

  // 全局错误边界包装
  if (isLoading) {
    return <LoadingSpinner size="lg" />
  }

  return (
    <ErrorBoundary>
      {isAuthenticated ? <MainApplication /> : <SecureLogin />}
    </ErrorBoundary>
  )
}

export default SecureApp