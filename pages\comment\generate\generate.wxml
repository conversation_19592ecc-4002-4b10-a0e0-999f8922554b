<!--
  AI魔法生成 - 原型图风格设计
-->
<view class="prototype-page">
  <!-- 页面头部 -->
  <view class="page-header">
    <view class="header-title">AI魔法生成</view>
  </view>

  <!-- 魔法进度指示器 -->
  <view class="magic-progress">
    <view class="progress-icon">
      <text class="magic-icon">✨</text>
    </view>
    <view class="progress-title">准备施展魔法</view>
    <view class="progress-desc">选择学生和评语风格，3分钟生成专业评语</view>
  </view>

  <!-- 学生选择区域 -->
  <view class="student-section">
    <view class="section-title">
      <text class="section-icon">🎓</text>
      <text class="section-text">选择学生</text>
    </view>

    <!-- 学生选择按钮 -->
    <view class="student-selector" bindtap="showStudentPicker" wx:if="{{selectedStudents.length === 0 && !isRegenerateMode}}">
      <view class="selector-content">
        <text class="selector-text">点击选择学生</text>
        <text class="selector-hint">支持多选</text>
      </view>
      <view class="selector-arrow">→</view>
    </view>

    <!-- 重新生成模式下显示已选学生 -->
    <view class="selected-student-info" wx:if="{{isRegenerateMode && selectedStudent}}">
      <view class="student-info-card">
        <view class="student-avatar">{{selectedStudent.name.charAt(0)}}</view>
        <view class="student-details">
          <text class="student-name">{{selectedStudent.name}}</text>
          <text class="student-class">{{selectedStudent.className}}</text>
        </view>
        <view class="regenerate-badge">重新生成</view>
      </view>
    </view>

    <!-- 已选学生列表 -->
    <view class="selected-students" wx:if="{{selectedStudents.length > 0}}">
      <view class="student-chips">
        <view class="student-chip" wx:for="{{selectedStudents}}" wx:key="id">
          <text class="student-name">{{item.name}}</text>
          <view class="remove-btn" data-student="{{item}}" bindtap="removeStudent">×</view>
        </view>
      </view>
      <view class="add-more-btn" bindtap="showStudentPicker">
        <text class="add-text">+ 添加更多</text>
      </view>
    </view>
  </view>

  <!-- 评语配置 -->
  <view class="config-section">
    <view class="section-title">
      <text class="section-icon">🎨</text>
      <text class="section-text">评语风格</text>
    </view>

    <view class="config-grid">
      <view class="config-item {{formData.style === item.value ? 'selected' : ''}}"
            wx:for="{{styleOptions}}"
            wx:key="value"
            data-value="{{item.value}}"
            bindtap="onStyleChange">
        <view class="config-icon">{{item.emoji}}</view>
        <view class="config-title">{{item.name}}</view>
        <view class="config-desc">{{item.desc || '综合表现'}}</view>
      </view>
    </view>
  </view>

  <!-- 生成按钮 -->
  <view class="generate-button {{selectedStudentIds.length === 0 ? 'disabled' : ''}}"
        bindtap="generateComments">
    <view class="button-content">
      <text class="button-icon" wx:if="{{!generating}}">✨</text>
      <van-loading size="20px" color="white" wx:if="{{generating}}" />
      <text class="button-text">{{generating ? '正在生成中...' : '开始生成魔法评语'}}</text>
    </view>
  </view>

  <!-- 生成进度条 -->
  <view class="generate-progress-section" wx:if="{{generating}}">
    <view class="progress-info">
      <text class="progress-text">正在生成 {{generateProgress.current || 0}}/{{generateProgress.total || 0}}</text>
      <text class="progress-percent">{{progressWidth || 0}}%</text>
    </view>
    <view class="progress-current-student" wx:if="{{generateProgress.currentStudent}}">
      <text class="current-student-text">当前：{{generateProgress.currentStudent}}</text>
    </view>
    <view class="progress-bar-container" wx:if="{{generateProgress.currentStudent}}">
      <view class="progress-bar-bg">
        <view class="progress-bar-fill" style="width: {{progressWidth || 0}}%;"></view>
      </view>
    </view>
    <view class="progress-status" wx:if="{{generateProgress.status}}">
      <text class="status-text">{{generateProgress.status}}</text>
    </view>
  </view>

  <!-- 生成结果 -->
  <view class="result-section {{showResult ? 'show' : ''}}" wx:if="{{showResult}}">
    <view class="result-header">
      <view class="section-title">
        <text class="section-icon">⭐</text>
        <text class="section-text">生成结果</text>
      </view>
      <view class="result-score">质量评分: {{resultScore || '9.2'}}</view>
    </view>

    <scroll-view class="result-content" scroll-y="{{true}}" enhanced="{{true}}" show-scrollbar="{{false}}">
      <view class="content-text">
        {{generatedComment || '李小明同学在本学期表现优异，学习态度端正，积极参与课堂讨论。该生思维活跃，善于思考，作业完成质量较高，字迹工整。在团队合作中表现出良好的协调能力，乐于帮助同学。建议在课外阅读方面继续加强，拓宽知识面，相信会有更大的进步空间。'}}
      </view>
    </scroll-view>

    <view class="result-actions">
      <button class="action-button secondary" bindtap="regenerateComment">重新生成</button>
      <button class="action-button viral-share" bindtap="shareAchievement">🎉 炫耀分享</button>
      <button class="action-button primary" bindtap="saveComment">保存使用</button>
    </view>
  </view>

  <!-- 学生选择底部弹窗 -->
  <view class="bottom-popup-mask {{showStudentPopup ? 'show' : ''}}" bindtap="onMaskTap" wx:if="{{showStudentPopup}}">
    <view class="bottom-popup {{showStudentPopup ? 'show' : ''}}" catchtap="preventClose">
      <!-- 拖拽指示器 -->
      <view class="drag-indicator"></view>
      
      <!-- 弹窗标题 -->
      <view class="popup-title-bar">
        <text class="popup-title">选择学生</text>
        <view class="close-icon" bindtap="hideStudentPicker">
          <text>×</text>
        </view>
      </view>

      <!-- 搜索栏 -->
      <view class="search-container">
        <view class="search-box">
          <input 
            class="search-input" 
            placeholder="输入学生姓名或班级" 
            value="{{studentSearchKeyword}}"
            bindinput="onStudentSearchChange"
            focus="{{false}}"
          />
          <view class="search-clear {{studentSearchKeyword ? 'show' : ''}}" bindtap="onStudentSearchClear">
            <text class="clear-icon">×</text>
          </view>
        </view>
        <view class="search-tip" wx:if="{{studentSearchKeyword}}">
          <text class="tip-text">找到 {{filteredStudents.length}} 个匹配的学生</text>
        </view>
      </view>

      <!-- 弹窗操作栏 -->
      <view class="popup-operations">
        <view class="operation-left">
          <text class="selected-info">已选择 {{selectedStudentIds.length}} 人</text>
        </view>
        <view class="operation-right">
          <view class="operation-btn" catchtap="selectAllInPopup" wx:if="{{filteredStudents.length > selectedStudentIds.length}}">
            <text class="operation-text">全选</text>
          </view>
          <view class="operation-btn" catchtap="unselectAllInPopup" wx:if="{{selectedStudentIds.length > 0}}">
            <text class="operation-text">取消全选</text>
          </view>
        </view>
      </view>

      <!-- 学生列表 -->
      <scroll-view class="student-scroll-list" scroll-y="{{true}}">
        <view
          class="student-row {{item.isSelected ? 'selected' : ''}}"
          wx:for="{{filteredStudents}}"
          wx:key="id"
          data-student="{{item}}"
          catchtap="toggleStudentSelection"
        >
          <view class="student-info">
            <view class="student-avatar">{{item.surname || item.name.charAt(0)}}</view>
            <view class="student-details">
              <text class="student-name">{{item.name}}</text>
              <text class="student-class">{{item.className}}</text>
            </view>
          </view>
          <view class="selection-indicator {{item.isSelected ? 'active' : ''}}" data-debug-id="{{item.id}}" data-debug-selected="{{item.isSelected}}">
            <text class="check-mark">✓</text>
          </view>
        </view>
        
        <!-- 空状态 -->
        <view class="empty-state" wx:if="{{filteredStudents.length === 0}}">
          <view class="empty-icon">🔍</view>
          <text class="empty-text">{{studentSearchKeyword ? '没有找到匹配的学生' : '暂无学生数据'}}</text>
          <text class="empty-hint" wx:if="{{studentSearchKeyword}}">请尝试输入其他关键词</text>
        </view>
      </scroll-view>

      <!-- 底部按钮 -->
      <view class="popup-footer-bar">
        <view class="footer-buttons">
          <view class="cancel-button" bindtap="hideStudentPicker">
            <text class="cancel-text">取消</text>
          </view>
          <view class="confirm-button {{selectedStudentIds.length > 0 ? 'active' : ''}}" bindtap="confirmStudentSelection">
            <text class="confirm-text">确定选择 ({{selectedStudentIds.length}})</text>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 生成完成弹窗 -->
  <view class="success-popup-mask {{showSuccessPopup ? 'show' : ''}}" wx:if="{{showSuccessPopup}}">
    <view class="success-popup {{showSuccessPopup ? 'show' : ''}}" catchtap="preventClose">
      <view class="success-icon">✅</view>
      <view class="success-title">生成完成</view>
      <view class="success-content">
        <text class="success-text">成功生成 {{generateResult.successCount}} 条评语</text>
        <text class="success-detail" wx:if="{{generateResult.failCount > 0}}">失败 {{generateResult.failCount}} 条</text>
      </view>

      <view class="success-actions">
        <view class="success-btn secondary" bindtap="closeSuccessPopup">
          <text>稍后处理</text>
        </view>
        <view class="success-btn viral-share" bindtap="shareSuccess" data-safe="true">
          <text>🎉 炫耀分享</text>
        </view>
        <view class="success-btn primary" bindtap="viewGeneratedComments">
          <text>查看评语</text>
        </view>
      </view>
      
      <!-- 显示评语内容预览 -->
      <view class="comment-preview" wx:if="{{generateResult.comments.length > 0}}">
        <view class="preview-title">生成的评语预览：</view>
        <view class="comment-item" wx:for="{{generateResult.comments}}" wx:key="id">
          <view class="comment-student">{{item.studentName}}:</view>
          <view class="comment-content">{{item.content}}</view>
        </view>
      </view>

      <view class="success-footer">
        <view class="footer-btn" bindtap="saveDirectly">
          <text class="footer-text">直接保存</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 隐藏的Canvas用于生成分享卡片 -->
  <canvas
    canvas-id="shareCanvas"
    class="viral-canvas"
    style="width: 375px; height: 667px; position: fixed; top: -3000px; left: -2000px; z-index: -999;"
  ></canvas>
</view>