<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>清理管理后台浏览器数据</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }
        .container {
            background: white;
            border-radius: 12px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        h1 {
            color: #2c3e50;
            text-align: center;
            margin-bottom: 30px;
        }
        .status {
            padding: 15px;
            border-radius: 8px;
            margin: 10px 0;
            font-weight: 500;
        }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        .warning { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        
        button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
            transition: transform 0.2s;
        }
        button:hover {
            transform: translateY(-2px);
        }
        button:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }
        
        .section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #e9ecef;
            border-radius: 8px;
        }
        .section h3 {
            margin-top: 0;
            color: #495057;
        }
        
        .data-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid #f8f9fa;
        }
        .data-item:last-child {
            border-bottom: none;
        }
        
        .progress {
            width: 100%;
            height: 20px;
            background: #f8f9fa;
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
        }
        .progress-bar {
            height: 100%;
            background: linear-gradient(90deg, #667eea, #764ba2);
            width: 0%;
            transition: width 0.3s ease;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧹 管理后台数据清理工具</h1>
        
        <div class="status info">
            <strong>📋 清理说明：</strong>此工具将清空管理后台的所有本地数据，包括缓存、存储和会话数据，为连接小程序做准备。
        </div>

        <div class="section">
            <h3>📊 当前数据状态</h3>
            <div id="dataStatus">
                <div class="data-item">
                    <span>localStorage 项目数：</span>
                    <span id="localStorageCount">检测中...</span>
                </div>
                <div class="data-item">
                    <span>sessionStorage 项目数：</span>
                    <span id="sessionStorageCount">检测中...</span>
                </div>
                <div class="data-item">
                    <span>IndexedDB 数据库数：</span>
                    <span id="indexedDBCount">检测中...</span>
                </div>
                <div class="data-item">
                    <span>缓存存储大小：</span>
                    <span id="cacheSize">检测中...</span>
                </div>
            </div>
        </div>

        <div class="section">
            <h3>🚀 清理操作</h3>
            <div class="progress">
                <div class="progress-bar" id="progressBar"></div>
            </div>
            <div id="progressText">准备就绪</div>
            
            <button onclick="startCleaning()" id="cleanBtn">开始清理</button>
            <button onclick="checkStatus()" id="checkBtn">重新检测</button>
            <button onclick="window.location.reload()" id="refreshBtn" disabled>刷新页面</button>
        </div>

        <div class="section">
            <h3>📝 清理日志</h3>
            <div id="logContainer" style="max-height: 300px; overflow-y: auto; background: #f8f9fa; padding: 15px; border-radius: 6px; font-family: monospace; font-size: 14px;">
                等待开始清理...
            </div>
        </div>

        <div class="status warning">
            <strong>⚠️ 注意：</strong>清理完成后请重新启动管理后台应用，确保所有更改生效。
        </div>
    </div>

    <script>
        let logContainer;
        let progressBar;
        let progressText;

        function log(message, type = 'info') {
            if (!logContainer) logContainer = document.getElementById('logContainer');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] ${message}\n`;
            logContainer.textContent += logEntry;
            logContainer.scrollTop = logContainer.scrollHeight;
        }

        function updateProgress(percent, text) {
            if (!progressBar) progressBar = document.getElementById('progressBar');
            if (!progressText) progressText = document.getElementById('progressText');
            
            progressBar.style.width = percent + '%';
            progressText.textContent = text;
        }

        async function checkStatus() {
            log('🔍 开始检测当前数据状态...');
            
            // 检测 localStorage
            const localStorageCount = localStorage.length;
            document.getElementById('localStorageCount').textContent = localStorageCount;
            log(`📦 localStorage: ${localStorageCount} 个项目`);

            // 检测 sessionStorage
            const sessionStorageCount = sessionStorage.length;
            document.getElementById('sessionStorageCount').textContent = sessionStorageCount;
            log(`📦 sessionStorage: ${sessionStorageCount} 个项目`);

            // 检测 IndexedDB
            try {
                const databases = await indexedDB.databases();
                document.getElementById('indexedDBCount').textContent = databases.length;
                log(`🗄️ IndexedDB: ${databases.length} 个数据库`);
            } catch (error) {
                document.getElementById('indexedDBCount').textContent = '检测失败';
                log(`❌ IndexedDB 检测失败: ${error.message}`);
            }

            // 检测缓存大小
            try {
                if ('storage' in navigator && 'estimate' in navigator.storage) {
                    const estimate = await navigator.storage.estimate();
                    const usedMB = (estimate.usage / 1024 / 1024).toFixed(2);
                    document.getElementById('cacheSize').textContent = `${usedMB} MB`;
                    log(`💾 存储使用量: ${usedMB} MB`);
                } else {
                    document.getElementById('cacheSize').textContent = '不支持';
                    log(`💾 浏览器不支持存储估算`);
                }
            } catch (error) {
                document.getElementById('cacheSize').textContent = '检测失败';
                log(`❌ 缓存大小检测失败: ${error.message}`);
            }

            log('✅ 数据状态检测完成');
        }

        async function startCleaning() {
            const cleanBtn = document.getElementById('cleanBtn');
            const checkBtn = document.getElementById('checkBtn');
            const refreshBtn = document.getElementById('refreshBtn');
            
            cleanBtn.disabled = true;
            checkBtn.disabled = true;
            
            log('🧹 开始清理管理后台数据...');
            logContainer.textContent = '';
            
            try {
                // 步骤1: 清理 localStorage
                updateProgress(10, '清理 localStorage...');
                log('🗑️ 清理 localStorage...');
                const localKeys = Object.keys(localStorage);
                localKeys.forEach(key => {
                    localStorage.removeItem(key);
                    log(`  - 删除: ${key}`);
                });
                log(`✅ localStorage 清理完成，删除了 ${localKeys.length} 个项目`);

                // 步骤2: 清理 sessionStorage
                updateProgress(25, '清理 sessionStorage...');
                log('🗑️ 清理 sessionStorage...');
                const sessionKeys = Object.keys(sessionStorage);
                sessionKeys.forEach(key => {
                    sessionStorage.removeItem(key);
                    log(`  - 删除: ${key}`);
                });
                log(`✅ sessionStorage 清理完成，删除了 ${sessionKeys.length} 个项目`);

                // 步骤3: 清理 IndexedDB
                updateProgress(50, '清理 IndexedDB...');
                log('🗑️ 清理 IndexedDB...');
                try {
                    const databases = await indexedDB.databases();
                    for (const db of databases) {
                        if (db.name) {
                            const deleteReq = indexedDB.deleteDatabase(db.name);
                            await new Promise((resolve, reject) => {
                                deleteReq.onsuccess = () => {
                                    log(`  - 删除数据库: ${db.name}`);
                                    resolve();
                                };
                                deleteReq.onerror = () => reject(deleteReq.error);
                            });
                        }
                    }
                    log(`✅ IndexedDB 清理完成，删除了 ${databases.length} 个数据库`);
                } catch (error) {
                    log(`⚠️ IndexedDB 清理部分失败: ${error.message}`);
                }

                // 步骤4: 清理缓存
                updateProgress(75, '清理缓存...');
                log('🗑️ 清理浏览器缓存...');
                try {
                    if ('caches' in window) {
                        const cacheNames = await caches.keys();
                        for (const cacheName of cacheNames) {
                            await caches.delete(cacheName);
                            log(`  - 删除缓存: ${cacheName}`);
                        }
                        log(`✅ 缓存清理完成，删除了 ${cacheNames.length} 个缓存`);
                    } else {
                        log(`⚠️ 浏览器不支持 Cache API`);
                    }
                } catch (error) {
                    log(`⚠️ 缓存清理失败: ${error.message}`);
                }

                // 步骤5: 完成
                updateProgress(100, '清理完成！');
                log('🎉 所有数据清理完成！');
                log('📝 建议操作：');
                log('  1. 重新启动管理后台应用');
                log('  2. 确保环境变量 VITE_ENABLE_MOCK=false');
                log('  3. 测试与小程序的数据连通性');
                
                refreshBtn.disabled = false;
                
            } catch (error) {
                log(`❌ 清理过程中发生错误: ${error.message}`);
                updateProgress(0, '清理失败');
            } finally {
                cleanBtn.disabled = false;
                checkBtn.disabled = false;
            }
        }

        // 页面加载时自动检测状态
        window.addEventListener('load', checkStatus);
    </script>
</body>
</html>
