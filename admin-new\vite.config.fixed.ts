import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'
import path from 'path'

export default defineConfig({
  plugins: [
    react({
      babel: {
        plugins: []
      }
    })
  ],
  
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src'),
      '@/components': path.resolve(__dirname, './src/components'),
      '@/utils': path.resolve(__dirname, './src/utils'),
      '@/hooks': path.resolve(__dirname, './src/hooks'),
      '@/services': path.resolve(__dirname, './src/services')
    }
  },

  // 修正的端口配置
  server: {
    port: 8080,
    host: '0.0.0.0',
    strictPort: false,  // 允许端口被占用时自动寻找下一个端口
    // 修正HMR配置，使用相同端口避免WebSocket连接错误
    hmr: {
      port: 8080,  // 使用相同端口避免WebSocket连接错误
      host: 'localhost',
      overlay: true
    },
    cors: true,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization'
    }
  },

  build: {
    outDir: 'dist',
    sourcemap: true,
    minify: 'esbuild',
    target: 'es2020',
    rollupOptions: {
      output: {
        format: 'es',
        manualChunks: {
          'react-vendor': ['react', 'react-dom'],
          'ui-vendor': ['antd', '@ant-design/icons'],
          'utils-vendor': ['dayjs', 'lodash-es', 'axios']
        }
      }
    },
    commonjsOptions: {
      include: [/node_modules/],
      extensions: ['.js', '.cjs']
    }
  },

  esbuild: {
    target: 'es2020',
    format: 'esm',
    keepNames: true,
    supported: {
      'top-level-await': true
    }
  },

  define: {
    __DEV__: JSON.stringify(process.env.NODE_ENV === 'development'),
    'process.env.NODE_ENV': JSON.stringify(process.env.NODE_ENV || 'development'),
    global: 'globalThis'
  },

  optimizeDeps: {
    include: [
      'react',
      'react-dom',
      'antd',
      '@ant-design/icons',
      'dayjs',
      'lodash-es',
      'axios',
      'react-router-dom'
    ],
    esbuildOptions: {
      target: 'es2020',
      outExtension: { '.js': '.js' },
      sourcemap: false  // 禁用依赖的源代码映射以避免警告
    }
  },

  // 开发模式下禁用源代码映射警告
  css: {
    devSourcemap: false
  }
})