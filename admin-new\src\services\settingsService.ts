/**
 * 设置管理服务
 * 使用云数据库替代localStorage，提供安全的设置管理
 */

import { cloudFunctionService } from './cloudFunctionService'

export interface SystemSettings {
  systemName: string
  version: string
  maxUsers: number
  sessionTimeout: number
  autoRefresh: boolean
  darkMode: boolean
  debugMode: boolean
  enableCache: boolean
  logLevel: string
  backupFrequency: string
}

export interface UserPreferences {
  browserEnabled: boolean
  pageAlert: boolean
  soundAlert: boolean
  systemAlert: boolean
  dataBackup: boolean
  errorAlert: boolean
}

export interface AdminUser {
  id: string
  username: string
  role: string
  token: string
}

class SettingsService {
  private systemSettings: SystemSettings | null = null
  private userPreferences: UserPreferences | null = null
  private currentAdmin: AdminUser | null = null

  /**
   * 管理员登录
   */
  async adminLogin(username: string, password: string): Promise<{
    success: boolean
    message: string
    data?: AdminUser
  }> {
    try {
      console.log('🔐 管理员登录:', { username })
      
      const result = await cloudFunctionService.callFunction('settingsAPI', {
        action: 'admin.login',
        data: { username, password }
      })

      if (result.code === 200) {
        this.currentAdmin = result.data
        // 保存登录状态到sessionStorage（临时存储）
        sessionStorage.setItem('adminUser', JSON.stringify(result.data))
        
        console.log('✅ 管理员登录成功:', result.data)
        return {
          success: true,
          message: result.message,
          data: result.data
        }
      } else {
        console.error('❌ 管理员登录失败:', result.message)
        return {
          success: false,
          message: result.message
        }
      }
    } catch (error) {
      console.error('❌ 管理员登录异常:', error)
      return {
        success: false,
        message: '登录失败，请检查网络连接'
      }
    }
  }

  /**
   * 修改管理员密码
   */
  async changePassword(currentPassword: string, newPassword: string): Promise<{
    success: boolean
    message: string
  }> {
    try {
      console.log('🔑 修改管理员密码')
      
      const result = await cloudFunctionService.callFunction('settingsAPI', {
        action: 'admin.changePassword',
        data: { currentPassword, newPassword }
      })

      if (result.code === 200) {
        console.log('✅ 密码修改成功')
        return {
          success: true,
          message: result.message
        }
      } else {
        console.error('❌ 密码修改失败:', result.message)
        return {
          success: false,
          message: result.message
        }
      }
    } catch (error) {
      console.error('❌ 密码修改异常:', error)
      return {
        success: false,
        message: '密码修改失败，请检查网络连接'
      }
    }
  }

  /**
   * 获取系统设置
   */
  async getSystemSettings(): Promise<SystemSettings> {
    try {
      // 如果已缓存，直接返回
      if (this.systemSettings) {
        return this.systemSettings
      }

      console.log('⚙️ 获取系统设置')
      
      const result = await cloudFunctionService.callFunction('settingsAPI', {
        action: 'settings.getSystem'
      })

      if (result.code === 200) {
        this.systemSettings = result.data
        console.log('✅ 系统设置获取成功:', result.data)
        return result.data
      } else {
        console.error('❌ 系统设置获取失败:', result.message)
        // 返回默认设置
        return this.getDefaultSystemSettings()
      }
    } catch (error) {
      console.error('❌ 系统设置获取异常:', error)
      // 返回默认设置
      return this.getDefaultSystemSettings()
    }
  }

  /**
   * 更新系统设置
   */
  async updateSystemSettings(settings: Partial<SystemSettings>): Promise<{
    success: boolean
    message: string
  }> {
    try {
      console.log('💾 更新系统设置:', settings)
      
      const result = await cloudFunctionService.callFunction('settingsAPI', {
        action: 'settings.updateSystem',
        data: { settings }
      })

      if (result.code === 200) {
        // 更新本地缓存
        this.systemSettings = { ...this.systemSettings, ...settings } as SystemSettings
        console.log('✅ 系统设置更新成功')
        return {
          success: true,
          message: result.message
        }
      } else {
        console.error('❌ 系统设置更新失败:', result.message)
        return {
          success: false,
          message: result.message
        }
      }
    } catch (error) {
      console.error('❌ 系统设置更新异常:', error)
      return {
        success: false,
        message: '设置更新失败，请检查网络连接'
      }
    }
  }

  /**
   * 获取用户偏好
   */
  async getUserPreferences(): Promise<UserPreferences> {
    try {
      // 如果已缓存，直接返回
      if (this.userPreferences) {
        return this.userPreferences
      }

      console.log('👤 获取用户偏好')
      
      const result = await cloudFunctionService.callFunction('settingsAPI', {
        action: 'preferences.get'
      })

      if (result.code === 200) {
        this.userPreferences = result.data
        console.log('✅ 用户偏好获取成功:', result.data)
        return result.data
      } else {
        console.error('❌ 用户偏好获取失败:', result.message)
        // 返回默认偏好
        return this.getDefaultUserPreferences()
      }
    } catch (error) {
      console.error('❌ 用户偏好获取异常:', error)
      // 返回默认偏好
      return this.getDefaultUserPreferences()
    }
  }

  /**
   * 更新用户偏好
   */
  async updateUserPreferences(preferences: Partial<UserPreferences>): Promise<{
    success: boolean
    message: string
  }> {
    try {
      console.log('💾 更新用户偏好:', preferences)
      
      const result = await cloudFunctionService.callFunction('settingsAPI', {
        action: 'preferences.update',
        data: { preferences }
      })

      if (result.code === 200) {
        // 更新本地缓存
        this.userPreferences = { ...this.userPreferences, ...preferences } as UserPreferences
        console.log('✅ 用户偏好更新成功')
        return {
          success: true,
          message: result.message
        }
      } else {
        console.error('❌ 用户偏好更新失败:', result.message)
        return {
          success: false,
          message: result.message
        }
      }
    } catch (error) {
      console.error('❌ 用户偏好更新异常:', error)
      return {
        success: false,
        message: '偏好更新失败，请检查网络连接'
      }
    }
  }

  /**
   * 初始化系统数据
   */
  async initializeSystem(): Promise<{
    success: boolean
    message: string
  }> {
    try {
      console.log('🚀 初始化系统数据')
      
      const result = await cloudFunctionService.callFunction('settingsAPI', {
        action: 'system.init'
      })

      if (result.code === 200) {
        console.log('✅ 系统初始化成功')
        return {
          success: true,
          message: result.message
        }
      } else {
        console.error('❌ 系统初始化失败:', result.message)
        return {
          success: false,
          message: result.message
        }
      }
    } catch (error) {
      console.error('❌ 系统初始化异常:', error)
      return {
        success: false,
        message: '系统初始化失败，请检查网络连接'
      }
    }
  }

  /**
   * 获取当前登录的管理员信息
   */
  getCurrentAdmin(): AdminUser | null {
    if (this.currentAdmin) {
      return this.currentAdmin
    }
    
    // 尝试从sessionStorage恢复
    const saved = sessionStorage.getItem('adminUser')
    if (saved) {
      try {
        this.currentAdmin = JSON.parse(saved)
        return this.currentAdmin
      } catch (error) {
        console.error('恢复管理员信息失败:', error)
        sessionStorage.removeItem('adminUser')
      }
    }
    
    return null
  }

  /**
   * 管理员登出
   */
  logout(): void {
    this.currentAdmin = null
    this.systemSettings = null
    this.userPreferences = null
    sessionStorage.removeItem('adminUser')
    console.log('👋 管理员已登出')
  }

  /**
   * 清除缓存
   */
  clearCache(): void {
    this.systemSettings = null
    this.userPreferences = null
    console.log('🧹 设置缓存已清除')
  }

  /**
   * 获取默认系统设置
   */
  private getDefaultSystemSettings(): SystemSettings {
    return {
      systemName: '评语灵感君管理后台',
      version: 'v2.0.0',
      maxUsers: 1000,
      sessionTimeout: 30,
      autoRefresh: false,
      darkMode: false,
      debugMode: false,
      enableCache: true,
      logLevel: 'info',
      backupFrequency: 'daily'
    }
  }

  /**
   * 获取默认用户偏好
   */
  private getDefaultUserPreferences(): UserPreferences {
    return {
      browserEnabled: true,
      pageAlert: true,
      soundAlert: false,
      systemAlert: true,
      dataBackup: true,
      errorAlert: true
    }
  }
}

// 导出单例
export const settingsService = new SettingsService()
