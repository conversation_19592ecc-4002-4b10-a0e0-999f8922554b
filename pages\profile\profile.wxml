<!--
  个人中心 - 工作台
-->
<view class="profile-page">
  <!-- 个人信息区域 -->
  <view class="user-section">
    <view class="user-avatar" bindtap="changeAvatar">
      <image
        wx:if="{{userInfo.avatar}}"
        src="{{userInfo.avatar}}"
        class="avatar-image"
        mode="aspectFill"
      />
      <view wx:else class="avatar-placeholder">
        <van-icon name="user-o" size="32px" color="#fff" />
      </view>
      <view class="avatar-edit">
        <van-icon name="photo-o" size="12px" color="#fff" />
      </view>
    </view>
    <view class="user-info">
      <view class="user-name">{{userInfo.nickName || '老师'}}</view>
      <view class="user-subtitle">{{userInfo.school || '智慧小学'}} · {{userInfo.role || '教师'}}</view>
    </view>
    <view class="quick-edit" bindtap="quickEditProfile">
      <van-icon name="edit" size="18px" color="#4080FF" />
      <text class="edit-text">编辑</text>
    </view>
  </view>

  <!-- 数据概览 -->
  <view class="overview-section">
    <view class="section-title">我的工作概览</view>
    <view class="overview-grid">
      <view class="overview-item">
        <view class="overview-number">{{stats.totalClasses}}</view>
        <view class="overview-label">个班级</view>
      </view>
      <view class="overview-item">
        <view class="overview-number">{{stats.totalStudents}}</view>
        <view class="overview-label">名学生</view>
      </view>
      <view class="overview-item">
        <view class="overview-number">{{stats.totalRecords}}</view>
        <view class="overview-label">条记录</view>
      </view>
      <view class="overview-item">
        <view class="overview-number">{{stats.totalComments}}</view>
        <view class="overview-label">条评语</view>
      </view>
    </view>
  </view>

  <!-- 功能导航 -->
  <view class="function-section">
    <view class="section-title">功能导航</view>
    <view class="function-grid">
      <view class="function-card" bindtap="goToClassList">
        <view class="function-icon class-icon">
          <van-icon name="friends-o" size="24px" color="#fff" />
        </view>
        <view class="function-info">
          <view class="function-name">班级管理</view>
          <view class="function-desc">{{stats.totalClasses}}个班级</view>
        </view>
      </view>

      <view class="function-card" bindtap="goToStudentList">
        <view class="function-icon student-icon">
          <van-icon name="user-o" size="24px" color="#fff" />
        </view>
        <view class="function-info">
          <view class="function-name">学生管理</view>
          <view class="function-desc">{{stats.totalStudents}}名学生</view>
        </view>
      </view>

      <view class="function-card" bindtap="goToRecordList">
        <view class="function-icon record-icon">
          <van-icon name="edit" size="24px" color="#fff" />
        </view>
        <view class="function-info">
          <view class="function-name">记录管理</view>
          <view class="function-desc">{{stats.totalRecords}}条记录</view>
        </view>
      </view>

      <view class="function-card" bindtap="goToCommentList">
        <view class="function-icon comment-icon">
          <van-icon name="chat-o" size="24px" color="#fff" />
        </view>
        <view class="function-info">
          <view class="function-name">评语管理</view>
          <view class="function-desc">{{stats.totalComments}}条评语</view>
        </view>
      </view>

      <view class="function-card" bindtap="goToAnalytics">
        <view class="function-icon analytics-icon">
          <van-icon name="bar-chart-o" size="24px" color="#fff" />
        </view>
        <view class="function-info">
          <view class="function-name">数据分析</view>
          <view class="function-desc">查看统计报告</view>
        </view>
      </view>
    </view>
  </view>

  <!-- 工具箱 -->
  <view class="tools-section">
    <view class="section-title">工具箱</view>
    <view class="tools-list">
      <view class="tool-item" bindtap="goToImportExport">
        <van-icon name="down" size="20px" color="#4080FF" />
        <text class="tool-name">导入导出</text>
        <text class="tool-desc">数据备份管理</text>
        <van-icon name="arrow" size="16px" color="#c8c9cc" />
      </view>

      <view class="tool-item" bindtap="syncData">
        <van-icon name="replay" size="20px" color="#52C873" />
        <text class="tool-name">数据同步</text>
        <text class="tool-desc">{{lastSyncTime}}</text>
        <van-icon name="arrow" size="16px" color="#c8c9cc" />
      </view>

      <view class="tool-item" bindtap="backupData">
        <van-icon name="records" size="20px" color="#FF8C00" />
        <text class="tool-name">数据备份</text>
        <text class="tool-desc">导出备份文件</text>
        <van-icon name="arrow" size="16px" color="#c8c9cc" />
      </view>

      <view class="tool-item" bindtap="restoreData">
        <van-icon name="revoke" size="20px" color="#9C27B0" />
        <text class="tool-name">数据恢复</text>
        <text class="tool-desc">从云端恢复数据</text>
        <van-icon name="arrow" size="16px" color="#c8c9cc" />
      </view>

      <view class="tool-item" bindtap="goToHelp">
        <van-icon name="question-o" size="20px" color="#FF8C00" />
        <text class="tool-name">帮助中心</text>
        <text class="tool-desc">使用指南</text>
        <van-icon name="arrow" size="16px" color="#c8c9cc" />
      </view>

      <view class="tool-item" bindtap="goToSettings">
        <van-icon name="setting-o" size="20px" color="#999" />
        <text class="tool-name">系统设置</text>
        <text class="tool-desc">通知和偏好</text>
        <van-icon name="arrow" size="16px" color="#c8c9cc" />
      </view>
    </view>
  </view>

  <!-- 快速编辑弹窗 -->
  <van-popup
    show="{{showQuickEdit}}"
    position="bottom"
    custom-style="height: 40%"
    bind:close="hideQuickEdit"
  >
    <view class="quick-edit-popup">
      <view class="popup-header">
        <view class="popup-title">编辑个人信息</view>
        <van-icon name="cross" size="18px" color="#999" bindtap="hideQuickEdit" />
      </view>

      <view class="quick-edit-form">
        <van-field
          label="姓名"
          value="{{editForm.nickName}}"
          placeholder="请输入姓名"
          bind:change="onNickNameChange"
        />
        <van-field
          label="学校"
          value="{{editForm.school}}"
          placeholder="请输入学校名称"
          bind:change="onSchoolChange"
        />
        <van-field
          label="职位"
          value="{{editForm.role}}"
          placeholder="请输入职位"
          bind:change="onRoleChange"
        />
      </view>

      <view class="popup-actions">
        <van-button
          type="default"
          size="large"
          custom-class="cancel-btn"
          bindtap="hideQuickEdit"
        >
          取消
        </van-button>
        <van-button
          type="primary"
          size="large"
          custom-class="save-btn"
          bindtap="saveQuickEdit"
          loading="{{saving}}"
        >
          保存
        </van-button>
      </view>
    </view>
  </van-popup>
</view>