@echo off
chcp 65001 >nul
echo.
echo ========================================
echo    🚀 评语灵感君管理后台启动器
echo ========================================
echo.
echo 请选择启动方式：
echo.
echo 1. 简化版本（推荐）- 无HMR，稳定运行
echo    端口: 8083
echo    特点: 无自动刷新，需手动刷新页面
echo.
echo 2. CSP代理版本 - 解决CSP错误
echo    端口: 8082 (代理) + 8081 (Vite)
echo    特点: 支持热更新，解决CSP问题
echo.
echo 3. 直接访问Vite - 查看原始错误
echo    端口: 8081
echo    特点: 可能有CSP错误，用于调试
echo.
set /p choice="请输入选择 (1-3): "

if "%choice%"=="1" goto simple
if "%choice%"=="2" goto csp
if "%choice%"=="3" goto direct
echo 无效选择，默认使用简化版本
goto simple

:simple
echo.
echo 🚀 启动简化版本...
echo 📍 访问地址: http://localhost:8083
echo 💡 特点: 稳定运行，无自动刷新
echo.
npm run dev:simple
goto end

:csp
echo.
echo 🚀 启动CSP代理版本...
echo 📍 访问地址: http://localhost:8082
echo 💡 特点: 支持热更新，解决CSP问题
echo.
echo 正在启动Vite服务器（后台）...
start /B npm run dev
timeout /t 3 /nobreak >nul
echo 正在启动CSP代理服务器...
npm run dev:csp
goto end

:direct
echo.
echo 🚀 启动直接访问版本...
echo 📍 访问地址: http://localhost:8081
echo ⚠️  可能有CSP错误，用于调试
echo.
npm run dev
goto end

:end
echo.
echo 🛑 服务器已停止
pause
