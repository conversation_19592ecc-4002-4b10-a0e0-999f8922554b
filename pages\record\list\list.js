/**
 * 学生行为记录管理页面
 * 提供记录的查看、编辑、删除、筛选等功能
 */
const app = getApp();
const { cloudService } = require('../../../services/cloudService');
const ExcelShareUtils = require('../../../utils/excelShareUtils');

Page({
  /**
   * 页面的初始数据
   */
  data: {
    // 记录列表
    recordList: [],

    // 筛选条件
    filterOptions: {
      classId: '',
      studentId: '',
      behaviorType: '',
      dateRange: ''
    },

    // 班级和学生选项
    classList: [],
    studentList: [],

    // 行为类型选项
    behaviorTypes: [
      { value: '', label: '全部类型' },
      { value: 'positive', label: '积极行为' },
      { value: 'negative', label: '消极行为' },
      { value: 'neutral', label: '中性行为' },
      { value: 'academic', label: '学习表现' }
    ],

    // 页面状态
    loading: true,
    refreshing: false,

    // 分页
    pageSize: 20,
    currentPage: 1,
    hasMore: true,

    // 统计信息
    statistics: {
      totalRecords: 0,
      todayRecords: 0,
      avgScore: 0,
      positiveRate: 0
    },

    // UI状态
    showFilterSheet: false,
    showActionSheet: false,
    selectedRecord: null,

    // 筛选状态
    hasActiveFilter: false,

    // 操作面板选项
    actionSheetActions: [
      { name: '查看详情', value: 'view' },
      { name: '编辑记录', value: 'edit' },
      { name: '删除记录', value: 'delete', color: '#FF5247' }
    ]
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    this.initPage();
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    this.refreshData();
  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {
    this.refreshData();
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {
    this.loadMoreRecords();
  },

  /**
   * 初始化页面
   */
  async initPage() {
    try {
      await this.loadClassList();
      await this.loadStudentList();
      await this.loadRecordList();
      await this.loadStatistics();
    } catch (error) {
      console.error('页面初始化失败:', error);
      wx.showToast({
        title: '加载失败',
        icon: 'none'
      });
    }
  },

  /**
   * 刷新数据
   */
  async refreshData() {
    this.setData({
      refreshing: true,
      currentPage: 1,
      hasMore: true
    });

    try {
      await this.loadRecordList(true);
      await this.loadStatistics();
      wx.stopPullDownRefresh();
    } catch (error) {
      console.error('刷新数据失败:', error);
    } finally {
      this.setData({ refreshing: false });
    }
  },

  /**
   * 加载班级列表
   */
  async loadClassList() {
    try {
      const result = await cloudService.getClassList();
      if (result.success) {
        const classList = [
          { id: '', name: '全部班级' },
          ...result.data.map(item => ({
            id: item._id,
            name: item.className
          }))
        ];
        this.setData({ classList });
      }
    } catch (error) {
      console.error('加载班级列表失败:', error);
    }
  },

  /**
   * 加载学生列表
   */
  async loadStudentList() {
    try {
      const result = await cloudService.getStudentList();
      if (result.success) {
        const studentList = [
          { id: '', name: '全部学生' },
          ...result.data.map(item => ({
            id: item._id,
            name: item.name,
            className: item.className
          }))
        ];
        this.setData({ studentList });
      }
    } catch (error) {
      console.error('加载学生列表失败:', error);
    }
  },

  /**
   * 加载记录列表
   */
  async loadRecordList(refresh = false) {
    if (refresh) {
      this.setData({
        recordList: [],
        currentPage: 1,
        hasMore: true
      });
    }

    if (!this.data.hasMore && !refresh) {
      return;
    }

    this.setData({ loading: true });

    try {
      const result = await cloudService.getRecordList({
        page: this.data.currentPage,
        pageSize: this.data.pageSize,
        ...this.data.filterOptions
      });

      if (result.success) {
        const newRecords = result.data.map(record => ({
          ...record,
          id: record._id,
          createTimeText: this.formatTime(record.createTime),
          behaviorTypeText: this.getBehaviorTypeText(record.behaviorType),
          scoreColor: this.getScoreColor(record.score)
        }));

        const recordList = refresh ? newRecords : [...this.data.recordList, ...newRecords];

        this.setData({
          recordList,
          currentPage: this.data.currentPage + 1,
          hasMore: newRecords.length === this.data.pageSize,
          loading: false
        });
      } else {
        throw new Error(result.error || '加载失败');
      }
    } catch (error) {
      console.error('加载记录列表失败:', error);
      this.setData({
        recordList: [],
        loading: false
      });
      wx.showToast({
        title: '加载失败',
        icon: 'none'
      });
    }
  },

  /**
   * 加载统计信息
   */
  async loadStatistics() {
    try {
      const result = await cloudService.getRecordStatistics();
      if (result.success) {
        this.setData({
          statistics: result.data
        });
      }
    } catch (error) {
      console.error('加载统计信息失败:', error);
    }
  },

  /**
   * 加载更多记录
   */
  loadMoreRecords() {
    if (!this.data.loading && this.data.hasMore) {
      this.loadRecordList();
    }
  },

  /**
   * 显示筛选面板
   */
  showFilter() {
    this.setData({ showFilterSheet: true });
  },

  /**
   * 隐藏筛选面板
   */
  hideFilter() {
    this.setData({ showFilterSheet: false });
  },

  /**
   * 筛选条件变化
   */
  onFilterChange(e) {
    const { field } = e.currentTarget.dataset;
    const { value } = e.detail;

    this.setData({
      [`filterOptions.${field}`]: value
    });
  },

  /**
   * 应用筛选
   */
  applyFilter() {
    // 检查是否有活跃的筛选条件
    const { filterOptions } = this.data;
    const hasActiveFilter = !!(
      filterOptions.classId ||
      filterOptions.studentId ||
      filterOptions.behaviorType ||
      filterOptions.dateRange
    );

    this.setData({ hasActiveFilter });
    this.hideFilter();
    this.refreshData();
  },

  /**
   * 重置筛选
   */
  resetFilter() {
    this.setData({
      filterOptions: {
        classId: '',
        studentId: '',
        behaviorType: '',
        dateRange: ''
      }
    });
    this.applyFilter();
  },

  /**
   * 记录项点击
   */
  onRecordTap(e) {
    const { record } = e.currentTarget.dataset;
    this.setData({
      selectedRecord: record,
      showActionSheet: true
    });
  },

  /**
   * 隐藏操作面板
   */
  hideActionSheet() {
    this.setData({
      showActionSheet: false,
      selectedRecord: null
    });
  },

  /**
   * 操作选择
   */
  onActionSelect(e) {
    const { value } = e.detail;

    switch (value) {
      case 'view':
        this.viewRecord();
        break;
      case 'edit':
        this.editRecord();
        break;
      case 'delete':
        this.deleteRecord();
        break;
    }
  },

  /**
   * 查看记录详情
   */
  viewRecord() {
    const record = this.data.selectedRecord;
    if (record) {
      wx.navigateTo({
        url: `/pages/record/detail/detail?id=${record.id}`
      });
    }
    this.hideActionSheet();
  },

  /**
   * 编辑记录
   */
  editRecord() {
    const record = this.data.selectedRecord;
    if (record) {
      wx.navigateTo({
        url: `/pages/record/create/create?mode=edit&id=${record._id || record.id}`
      });
    }
    this.hideActionSheet();
  },

  /**
   * 删除记录
   */
  deleteRecord() {
    const record = this.data.selectedRecord;
    if (!record) return;

    wx.showModal({
      title: '确认删除',
      content: `确定要删除${record.studentName}的这条记录吗？`,
      success: async (res) => {
        if (res.confirm) {
          try {
            const result = await cloudService.deleteRecord(record._id || record.id);
            if (result.success) {
              wx.showToast({
                title: '删除成功',
                icon: 'success'
              });
              this.refreshData();
            } else {
              throw new Error(result.error || '删除失败');
            }
          } catch (error) {
            console.error('删除记录失败:', error);
            wx.showToast({
              title: '删除失败',
              icon: 'none'
            });
          }
        }
      }
    });
    this.hideActionSheet();
  },



  /**
   * 导出记录 - 使用统一分享工具
   */
  async exportRecords() {
    if (!this.data.recordList || this.data.recordList.length === 0) {
      wx.showToast({
        title: '暂无记录数据可导出',
        icon: 'none'
      });
      return;
    }

    // 使用统一的Excel分享工具
    return ExcelShareUtils.shareRecords(this.data.recordList);
  },

  /**
   * 格式化时间
   */
  formatTime(timestamp) {
    const date = new Date(timestamp);
    const now = new Date();
    const diff = now - date;

    if (diff < 60000) { // 1分钟内
      return '刚刚';
    } else if (diff < 3600000) { // 1小时内
      return `${Math.floor(diff / 60000)}分钟前`;
    } else if (diff < 86400000) { // 1天内
      return `${Math.floor(diff / 3600000)}小时前`;
    } else {
      return date.toLocaleDateString();
    }
  },

  /**
   * 获取行为类型文本
   */
  getBehaviorTypeText(type) {
    const typeMap = {
      positive: '积极行为',
      negative: '消极行为',
      neutral: '中性行为',
      academic: '学习表现'
    };
    return typeMap[type] || '未知类型';
  },

  /**
   * 获取分数颜色
   */
  getScoreColor(score) {
    if (score >= 8) return '#52C873';
    if (score >= 6) return '#4080FF';
    if (score >= 4) return '#FF8C00';
    return '#FF4444';
  }
});