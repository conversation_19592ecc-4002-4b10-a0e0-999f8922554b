<!--AI数据库调试页面-->
<view class="container">
  <view class="header">
    <text class="title">AI数据库配置调试</text>
    <text class="desc">检查system_config集合中的AI配置数据</text>
  </view>

  <view class="actions">
    <button 
      class="btn-primary" 
      bind:tap="checkAIConfig" 
      loading="{{isChecking}}"
      disabled="{{isChecking}}"
    >
      {{isChecking ? '检查中...' : '开始检查AI配置'}}
    </button>
    
    <button class="btn-secondary" bind:tap="clearResults">清空结果</button>
    <button class="btn-secondary" bind:tap="copyResults">复制结果</button>
  </view>

  <view class="results-container">
    <text class="results-title">检查结果：</text>
    <scroll-view class="results-scroll" scroll-y>
      <text class="results-text" decode="{{true}}">{{debugResults || '点击"开始检查AI配置"查看数据库中的AI配置信息'}}</text>
    </scroll-view>
  </view>

  <view class="tips">
    <text class="tips-title">使用说明：</text>
    <text class="tips-text">1. 点击"开始检查AI配置"查看数据库配置</text>
    <text class="tips-text">2. 重点关注是否找到 type='ai_config' 且 status='active' 的记录</text>
    <text class="tips-text">3. 确认 hasApiKey 为 true 且 apiKeyLength > 0</text>
    <text class="tips-text">4. 可以复制结果发送给开发者分析</text>
  </view>
</view>