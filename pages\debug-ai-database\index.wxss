/* AI数据库调试页面样式 */
.container {
  padding: 30rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.header {
  text-align: center;
  margin-bottom: 40rpx;
}

.title {
  display: block;
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 16rpx;
}

.desc {
  display: block;
  font-size: 28rpx;
  color: #666;
}

.actions {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
  margin-bottom: 40rpx;
}

.btn-primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 12rpx;
  padding: 24rpx;
  font-size: 32rpx;
  font-weight: bold;
}

.btn-primary[disabled] {
  background: #ccc;
}

.btn-secondary {
  background: white;
  color: #333;
  border: 2rpx solid #ddd;
  border-radius: 12rpx;
  padding: 20rpx;
  font-size: 28rpx;
}

.results-container {
  background: white;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 40rpx;
  box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.1);
}

.results-title {
  display: block;
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}

.results-scroll {
  height: 600rpx;
  border: 2rpx solid #eee;
  border-radius: 8rpx;
  padding: 20rpx;
  background: #fafafa;
}

.results-text {
  font-size: 24rpx;
  line-height: 1.6;
  color: #333;
  white-space: pre-wrap;
  word-break: break-all;
  font-family: monospace;
}

.tips {
  background: #e8f4fd;
  border-radius: 16rpx;
  padding: 30rpx;
  border-left: 6rpx solid #1890ff;
}

.tips-title {
  display: block;
  font-size: 30rpx;
  font-weight: bold;
  color: #1890ff;
  margin-bottom: 16rpx;
}

.tips-text {
  display: block;
  font-size: 26rpx;
  color: #333;
  margin-bottom: 8rpx;
  line-height: 1.5;
}