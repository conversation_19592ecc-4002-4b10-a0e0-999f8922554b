<!--
  班级详情页面
-->
<view class="class-detail-page">
  <!-- 班级信息卡片 -->
  <view class="class-info-card">
    <view class="class-header">
      <view class="class-icon">
        <van-icon name="friends-o" size="24px" color="#4080FF" />
      </view>
      <view class="class-basic-info">
        <view class="class-name">{{classInfo.name}}</view>
        <view class="class-meta">
          <text class="grade-text">{{classInfo.gradeText}}</text>
          <text class="subject-text">{{classInfo.subjectText}}</text>
        </view>
      </view>
      <van-tag
        type="{{classInfo.status === 'active' ? 'primary' : 'default'}}"
        size="medium"
      >
        {{classInfo.statusText}}
      </van-tag>
    </view>

    <view class="class-description" wx:if="{{classInfo.description}}">
      <text>{{classInfo.description}}</text>
    </view>

    <view class="class-stats-grid">
      <view class="stat-card">
        <view class="stat-number">{{classInfo.studentCount}}</view>
        <view class="stat-label">学生总数</view>
      </view>
      <view class="stat-card">
        <view class="stat-number">{{classInfo.todayRecords}}</view>
        <view class="stat-label">今日记录</view>
      </view>
      <view class="stat-card">
        <view class="stat-number">{{classInfo.weeklyAvg}}</view>
        <view class="stat-label">周均分</view>
      </view>
      <view class="stat-card">
        <view class="stat-number">{{classInfo.totalRecords}}</view>
        <view class="stat-label">总记录数</view>
      </view>
    </view>
  </view>

  <!-- 功能菜单 -->
  <view class="function-menu">
    <view class="menu-title">班级管理</view>
    <view class="menu-grid">
      <view class="menu-item" bindtap="goToStudentList">
        <view class="menu-icon student">
          <van-icon name="friends-o" size="20px" color="#fff" />
        </view>
        <view class="menu-text">学生管理</view>
        <view class="menu-desc">{{classInfo.studentCount}}名学生</view>
      </view>

      <view class="menu-item" bindtap="goToRecordList">
        <view class="menu-icon record">
          <van-icon name="edit" size="20px" color="#fff" />
        </view>
        <view class="menu-text">记录管理</view>
        <view class="menu-desc">{{classInfo.totalRecords}}条记录</view>
      </view>

      <view class="menu-item" bindtap="goToCommentList">
        <view class="menu-icon comment">
          <van-icon name="chat-o" size="20px" color="#fff" />
        </view>
        <view class="menu-text">评语管理</view>
        <view class="menu-desc">{{classInfo.commentCount}}条评语</view>
      </view>

      <view class="menu-item" bindtap="goToAnalytics">
        <view class="menu-icon analytics">
          <van-icon name="bar-chart-o" size="20px" color="#fff" />
        </view>
        <view class="menu-text">数据分析</view>
        <view class="menu-desc">查看统计</view>
      </view>
    </view>
  </view>

  <!-- 最近活动 -->
  <view class="recent-activities">
    <view class="section-header">
      <view class="section-title">最近活动</view>
      <view class="section-more" bindtap="goToRecordList">
        <text>查看全部</text>
        <van-icon name="arrow-right" size="12px" color="#999" />
      </view>
    </view>

    <view wx:if="{{recentActivities.length > 0}}" class="activity-list">
      <view
        wx:for="{{recentActivities}}"
        wx:key="id"
        class="activity-item"
      >
        <view class="activity-icon {{item.type}}">
          <van-icon name="{{item.icon}}" size="14px" color="#fff" />
        </view>
        <view class="activity-content">
          <view class="activity-title">{{item.title}}</view>
          <view class="activity-desc">{{item.description}}</view>
        </view>
        <view class="activity-time">{{item.timeText}}</view>
      </view>
    </view>

    <view wx:else class="empty-activities">
      <van-icon name="notes-o" size="32px" color="#ddd" />
      <view class="empty-text">暂无活动记录</view>
    </view>
  </view>

  <!-- 底部操作 -->
  <view class="bottom-actions">
    <van-button
      type="default"
      size="large"
      custom-class="edit-btn"
      bindtap="editClass"
    >
      编辑班级
    </van-button>
    <van-button
      type="primary"
      size="large"
      custom-class="quick-record-btn"
      bindtap="quickRecord"
    >
      快速记录
    </van-button>
  </view>
</view>