/**
 * 成就徽章数据清理工具
 * 专门用于清理徽章系统相关的所有数据
 */

class AchievementCleaner {
  /**
   * 清空所有徽章相关数据
   */
  static clearAllAchievementData() {
    try {
      console.log('🧹 开始清理徽章系统数据...');
      
      // 徽章系统相关的本地存储键
      const achievementKeys = [
        'unlockedAchievements',     // 已解锁的徽章
        'achievementProgress',      // 徽章进度
        'achievementCache',         // 徽章缓存
        'achievementStats',         // 徽章统计
        'lastAchievementCheck',     // 最后检查时间
        'achievementNotifications', // 徽章通知
        'achievementSettings'       // 徽章设置
      ];
      
      let clearedCount = 0;
      
      achievementKeys.forEach(key => {
        try {
          const data = wx.getStorageSync(key);
          if (data) {
            wx.removeStorageSync(key);
            console.log(`✅ 已清理徽章数据: ${key}`);
            clearedCount++;
          }
        } catch (error) {
          console.warn(`⚠️ 清理徽章数据失败: ${key}`, error);
        }
      });
      
      console.log(`🎯 徽章数据清理完成，共清理 ${clearedCount} 项数据`);
      
      return {
        success: true,
        clearedCount,
        message: `成功清理 ${clearedCount} 项徽章数据`
      };
      
    } catch (error) {
      console.error('清理徽章数据失败:', error);
      return {
        success: false,
        error: error.message,
        message: '清理徽章数据失败'
      };
    }
  }
  
  /**
   * 重置徽章系统到初始状态
   */
  static resetAchievementSystem() {
    try {
      console.log('🔄 重置徽章系统...');
      
      // 先清空所有数据
      this.clearAllAchievementData();
      
      // 重新初始化徽章管理器（如果存在）
      try {
        const AchievementManager = require('./achievementManager');
        if (AchievementManager && typeof AchievementManager.init === 'function') {
          AchievementManager.init();
          console.log('✅ 徽章管理器已重新初始化');
        }
      } catch (error) {
        console.warn('重新初始化徽章管理器失败:', error);
      }
      
      return {
        success: true,
        message: '徽章系统已重置到初始状态'
      };
      
    } catch (error) {
      console.error('重置徽章系统失败:', error);
      return {
        success: false,
        error: error.message,
        message: '重置徽章系统失败'
      };
    }
  }
  
  /**
   * 检查徽章数据残留
   */
  static checkAchievementDataResidue() {
    try {
      console.log('🔍 检查徽章数据残留...');
      
      const allKeys = wx.getStorageInfoSync().keys || [];
      const achievementRelatedKeys = allKeys.filter(key => 
        key.includes('achievement') || 
        key.includes('unlock') ||
        key.includes('badge') ||
        key.includes('medal')
      );
      
      if (achievementRelatedKeys.length > 0) {
        console.warn('⚠️ 发现徽章数据残留:', achievementRelatedKeys);
        return {
          hasResidue: true,
          residueKeys: achievementRelatedKeys,
          message: `发现 ${achievementRelatedKeys.length} 项徽章数据残留`
        };
      } else {
        console.log('✅ 未发现徽章数据残留');
        return {
          hasResidue: false,
          residueKeys: [],
          message: '徽章数据清理彻底'
        };
      }
      
    } catch (error) {
      console.error('检查徽章数据残留失败:', error);
      return {
        hasResidue: false,
        error: error.message,
        message: '检查失败'
      };
    }
  }
  
  /**
   * 强制清理所有可能的徽章相关数据
   */
  static forceCleanAllAchievementData() {
    try {
      console.log('💪 强制清理所有徽章相关数据...');
      
      const allKeys = wx.getStorageInfoSync().keys || [];
      const achievementRelatedKeys = allKeys.filter(key => 
        key.includes('achievement') || 
        key.includes('unlock') ||
        key.includes('badge') ||
        key.includes('medal') ||
        key.includes('progress') ||
        key.includes('stats')
      );
      
      let clearedCount = 0;
      
      achievementRelatedKeys.forEach(key => {
        try {
          wx.removeStorageSync(key);
          console.log(`✅ 强制清理: ${key}`);
          clearedCount++;
        } catch (error) {
          console.warn(`⚠️ 强制清理失败: ${key}`, error);
        }
      });
      
      console.log(`💪 强制清理完成，共清理 ${clearedCount} 项数据`);
      
      return {
        success: true,
        clearedCount,
        message: `强制清理完成，共清理 ${clearedCount} 项数据`
      };
      
    } catch (error) {
      console.error('强制清理失败:', error);
      return {
        success: false,
        error: error.message,
        message: '强制清理失败'
      };
    }
  }
}

module.exports = {
  AchievementCleaner
};
