/**
 * 手动运行的实时连通性测试工具
 * 可直接在微信开发者工具控制台执行
 */

class CloudTester {
  constructor() {
    this.testResults = {
      cloudCalls: []
    };
  }

  // 测试云函数
  async testCloudFunctions() {
    console.log('📡 开始云函数连通性测试...');

    const testCases = [
      { name: '健康检查', fn: 'adminAPI', data: { action: 'healthCheck' } },
      { name: '学生列表', fn: 'getStudents', data: { limit: 1 } },
      { name: '记录列表', fn: 'getRecentRecords', data: { limit: 1 } }
    ];

    for (const testCase of testCases) {
      try {
        const startTime = Date.now();
        const result = await wx.cloud.callFunction({
          name: testCase.fn,
          data: testCase.data
        });
        const duration = Date.now() - startTime;

        console.log(`✅ ${testCase.name}: ${duration}ms - `, result.result?.code === 200 ? '成功' : '异常');
        
        this.testResults.cloudCalls.push({
          ...testCase,
          duration,
          success: result.result?.code === 200,
          response: result.result
        });
      } catch (error) {
        console.error(`❌ ${testCase.name}:`, error.message);
        this.testResults.cloudCalls.push({
          ...testCase,
          duration: 0,
          success: false,
          error: error.message
        });
      }
    }
  }

  // 测试数据同步
  async testDataIntegrity() {
    console.log('\n🔄 测试数据完整性...');

    try {
      // 获取学生数据
      const students = await wx.cloud.callFunction({
        name: 'getStudents',
        data: { studentId: null, limit: 3 }
      });

      if (students.result && students.result.success) {
        console.log(`✅ 学生数据可用: ${students.result.data?.length || 0}条`);
        
        // 抽取一个学生测试记录查询
        if (students.result.data && students.result.data[0]) {
          const sampleStudent = students.result.data[0];
          console.log(`📊 测试样本: ${sampleStudent.name} (${sampleStudent._id})`);
          
          const records = await wx.cloud.callFunction({
            name: 'getRecentRecords',
            data: { studentId: sampleStudent._id, limit: 3 }
          });
          
          console.log(`📈 记录查询结果: ${records.result?.data?.length || 0}条`);
        }
      }

    } catch (error) {
      console.error('❌ 数据完整性检查失败:', error);
    }
  }

  // 测试提示词模板
  testPromptTemplates() {
    console.log('\n📝 测试提示词模板...');

    const promptTemplates = require('../config/aiPromptTemplates.js');
    
    if (promptTemplates && promptTemplates.getAllStyles) {
      const styles = promptTemplates.getAllStyles();
      console.log(`📋 可用风格: ${styles.join(', ')}`);
      
      // 测试每个模板
      styles.forEach(style => {
        const template = promptTemplates.getTemplateByStyle(style);
        const fieldCount = (template.match(/\{\{.*?\}\}/g) || []).length;
        console.log(`   - ${style}: 字段完整性 ${fieldCount}/2`);
      });
    }
  }

  // 测试实时事件流
  testRealTimeEvents() {
    console.log('\n📡 测试实时事件监听...');
    
    // 监听数据变化
    const off = wx.cloud.callFunction({
      name: 'adminAPI',
      data: { 
        action: 'realtime.subscribe',
        topics: ['student.created', 'student.updated'] 
      }
    });

    console.log('🎯 已启动实时事件监听');
    
    // 5秒后关闭监听
    setTimeout(() => {
      console.log('📴 关闭实时监听');
    }, 5000);
  }

  // 运行完整测试
  async runFullTest() {
    console.log('🔥 ====== AI评语助手数据连通性测试 ======\n');
    
    try {
      await this.testNetworkStatus();
      await this.testCloudFunctions();
      await this.testDataIntegrity();
      this.testPromptTemplates();
      await this.testRealTimeEvents();
      
      console.log('\n🎉 ====== 测试完成 ======');
      this.generateTestReport();
      
    } catch (error) {
      console.error('💥 测试中断:', error);
    }
  }

  // 检查网络状态
  async testNetworkStatus() {
    return new Promise((resolve) => {
      wx.getNetworkType({
        success: (res) => {
          console.log(`🔌 网络状态: ${res.networkType}${res.networkType === 'none' ? ' (离线)' : ''}`);
          resolve();
        },
        fail: () => {
          console.log('❓ 网络状态未知');
          resolve();
        }
      });
    });
  }

  // 生成测试报告
  generateTestReport() {
    console.log('\n📊 ====== 测试报告摘要 ======');
    
    const successCount = this.testResults.cloudCalls.filter(call => call.success).length;
    const totalCount = this.testResults.cloudCalls.length;
    
    console.log(`✅ 云函数测试: ${successCount}/${totalCount} 通过`);
    
    if (successCount === totalCount) {
      console.log('🌟 所有基础连通测试通过，系统可使用');
    } else {
      console.log('⚠️  部分功能需要检查配置');
    }
  }
}

// 创建可手动调用的测试器
function runConnectivityTest() {
  const tester = new CloudTester();
  tester.runFullTest();
}

// 如果在前端环境中，直接导出
if (typeof window !== 'undefined') {
  window.runConnectivityTest = runConnectivityTest;
}

// 导出测试器
module.exports = {
  CloudTester,
  runConnectivityTest
};

// 当直接运行时执行测试
if (require.main === module) {
  console.log('直接运行测试...');
  runConnectivityTest();
}