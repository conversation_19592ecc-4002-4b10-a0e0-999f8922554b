<!--
  评语详情页面
-->
<view class="comment-detail-page">
  <!-- 加载状态 -->
  <view wx:if="{{loading}}" class="loading-container">
    <van-loading type="spinner" size="24px" color="#5470C6">加载中...</van-loading>
  </view>

  <!-- 评语内容 -->
  <view wx:else class="page-content">
    <!-- 学生信息卡片 -->
    <view class="student-card">
      <view class="student-header">
        <view class="student-avatar">
          <text class="avatar-text">{{commentInfo.studentName.charAt(0)}}</text>
        </view>
        <view class="student-info">
          <view class="student-name">{{commentInfo.studentName}}</view>
          <view class="student-class">{{commentInfo.className}}</view>
        </view>
        <view class="more-btn" bindtap="showActions">
          <van-icon name="ellipsis" size="20px" color="#666" />
        </view>
      </view>
    </view>

    <!-- 评语内容卡片 -->
    <view class="comment-card">
      <view class="comment-header">
        <view class="comment-title">评语内容</view>
        <view class="comment-meta">
          <text class="create-time">创建于 {{commentInfo.createTimeText || formatTime(commentInfo.createTime)}}</text>
        </view>
      </view>
      <view class="comment-content">{{commentInfo.content}}</view>

      <!-- 评语设置信息 -->
      <view class="comment-settings">
        <view class="setting-item">
          <text class="setting-label">风格：</text>
          <text class="setting-value">{{commentInfo.styleText || '温和亲切'}}</text>
        </view>
        <view class="setting-item">
          <text class="setting-label">长度：</text>
          <text class="setting-value">{{commentInfo.lengthText || '标准版'}}</text>
        </view>
        <view class="setting-item">
          <text class="setting-label">字数：</text>
          <text class="setting-value">{{commentInfo.content.length}}字</text>
        </view>
      </view>
    </view>

    <!-- 操作按钮 -->
    <view class="action-buttons">
      <view class="action-btn secondary" bindtap="copyComment">
        <van-icon name="copy" size="16px" />
        <text>复制</text>
      </view>
      <view class="action-btn smart-share" bindtap="smartShare">
        <van-icon name="share" size="16px" />
        <text>智能分享</text>
      </view>
      <view class="action-btn primary" bindtap="editComment">
        <van-icon name="edit" size="16px" />
        <text>编辑</text>
      </view>
    </view>
  </view>

  <!-- 操作菜单 -->
  <van-action-sheet
    show="{{showActionSheet}}"
    title="选择操作"
    actions="{{actionSheetActions}}"
    bind:close="hideActions"
    bind:select="onActionSelect"
  />

  <!-- 删除确认弹窗 -->
  <van-dialog
    show="{{showDeleteDialog}}"
    title="确认删除"
    message="确定要删除这条评语吗？此操作不可撤销。"
    show-cancel-button
    confirm-button-text="删除"
    confirm-button-color="#FF5247"
    bind:confirm="confirmDelete"
    bind:cancel="cancelDelete"
  />

  <!-- 隐藏的canvas用于生成分享图片 -->
  <canvas
    canvas-id="shareCanvas"
    class="share-canvas"
    style="width: 750rpx; height: 1334rpx; position: fixed; top: -3000rpx; left: 0;"
  ></canvas>
</view>