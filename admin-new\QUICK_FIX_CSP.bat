@echo off
echo.
echo ========================================
echo 🚀 快速CSP修复 - 一键启动
echo ========================================
echo.

echo 📋 步骤1：启动开发服务器...
start "Vite Dev Server" cmd /k "npm run dev"

echo ⏳ 等待服务器启动...
timeout /t 5 /nobreak >nul

echo.
echo 📋 步骤2：启动Chrome无安全模式...

REM 创建临时Chrome目录
set CHROME_TEMP=%TEMP%\chrome-no-csp-%RANDOM%
mkdir "%CHROME_TEMP%" 2>nul

echo 🌐 启动Chrome（完全禁用CSP）...

start "Chrome No CSP" chrome.exe ^
  --disable-web-security ^
  --disable-site-isolation-trials ^
  --disable-features=VizDisplayCompositor ^
  --no-sandbox ^
  --allow-running-insecure-content ^
  --disable-extensions ^
  --user-data-dir="%CHROME_TEMP%" ^
  "http://localhost:8080"

echo.
echo ✅ 完成！
echo 📱 Chrome已启动，自动访问: http://localhost:8080
echo ⚠️  此Chrome禁用了所有安全策略（包括CSP）
echo 💡 现在应该可以正常使用所有功能了
echo.
echo 测试功能：
echo   - 登录: admin / admin123
echo   - 全局搜索按钮
echo   - 消息通知按钮  
echo   - 用户菜单功能
echo   - 安全退出
echo.
pause