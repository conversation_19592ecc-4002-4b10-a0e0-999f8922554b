// 最简化的静态服务器，完全避免CSP问题
const http = require('http')
const fs = require('fs')
const path = require('path')
const url = require('url')

const port = 3000
const mimeTypes = {
  '.html': 'text/html',
  '.js': 'text/javascript',
  '.jsx': 'text/javascript', 
  '.ts': 'text/typescript',
  '.tsx': 'text/typescript',
  '.css': 'text/css',
  '.json': 'application/json',
  '.png': 'image/png',
  '.jpg': 'image/jpeg',
  '.gif': 'image/gif',
  '.svg': 'image/svg+xml'
}

const server = http.createServer((req, res) => {
  // 处理CORS
  res.setHeader('Access-Control-Allow-Origin', '*')
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS')
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization')
  
  if (req.method === 'OPTIONS') {
    res.writeHead(200)
    res.end()
    return
  }

  const parsedUrl = url.parse(req.url, true)
  let pathname = parsedUrl.pathname

  // 默认返回index.html
  if (pathname === '/') {
    pathname = '/index.html'
  }

  const filePath = path.join(__dirname, pathname)
  const ext = path.extname(filePath).toLowerCase()
  const contentType = mimeTypes[ext] || 'text/plain'

  fs.readFile(filePath, (err, data) => {
    if (err) {
      if (err.code === 'ENOENT') {
        // 文件不存在，返回index.html（用于SPA路由）
        fs.readFile(path.join(__dirname, 'index.html'), (err2, data2) => {
          if (!err2) {
            res.writeHead(200, { 'Content-Type': 'text/html' })
            res.end(data2)
          } else {
            res.writeHead(404)
            res.end('页面未找到')
          }
        })
      } else {
        res.writeHead(500)
        res.end('服务器错误')
      }
    } else {
      res.writeHead(200, { 'Content-Type': contentType })
      res.end(data)
    }
  })
})

server.listen(port, () => {
  console.log('🚀 简化服务器已启动！')
  console.log(`📱 访问地址: http://localhost:${port}`)
  console.log('👤 登录信息: admin / admin123') 
  console.log('✅ 已完全避免CSP问题')
  console.log('💡 提示：这是一个静态服务器，请确保你的应用已经构建')
})