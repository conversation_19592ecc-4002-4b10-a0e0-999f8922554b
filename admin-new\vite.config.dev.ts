import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'
import path from 'path'

export default defineConfig({
  plugins: [
    react({
      // 开发环境专用配置，完全禁用可能导致eval的功能
      fastRefresh: false,
      babel: false // 完全禁用Babel，使用esbuild
    })
  ],

  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src'),
      '@/components': path.resolve(__dirname, './src/components'),
      '@/utils': path.resolve(__dirname, './src/utils'),
      '@/hooks': path.resolve(__dirname, './src/hooks'),
      '@/services': path.resolve(__dirname, './src/services')
    }
  },

  server: {
    port: 8080,
    open: true,
    host: true,
    strictPort: false,
    // 开发环境宽松的CSP策略
    headers: {
      'Content-Security-Policy': "default-src 'self' data: blob:; script-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob: localhost:* 127.0.0.1:* ws: wss:; style-src 'self' 'unsafe-inline' data: blob:; img-src 'self' data: blob: https:; font-src 'self' data: blob:; connect-src 'self' ws: wss: data: blob: localhost:* 127.0.0.1:* https:; worker-src 'self' blob:; frame-src 'self';"
    },
    hmr: {
      port: 8080,
      overlay: false // 禁用错误覆盖层
    }
  },

  // 开发环境构建配置
  build: {
    outDir: 'dist',
    sourcemap: true, // 开发环境启用sourcemap
    minify: false // 开发环境不压缩
  },

  // 开发环境优化
  esbuild: {
    target: 'es2020',
    format: 'esm',
    keepNames: true // 保持函数名，便于调试
  },

  // 优化依赖预构建
  optimizeDeps: {
    include: ['react', 'react-dom', 'antd', 'dayjs', 'lodash-es'],
    esbuildOptions: {
      target: 'es2020'
    }
  }
})