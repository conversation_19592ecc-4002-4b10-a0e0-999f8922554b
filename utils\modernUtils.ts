/**
 * 现代化工具函数库
 * 2025年企业级标准，支持TypeScript、性能优化、错误处理
 */

import { z } from 'zod'
import dayjs from 'dayjs'
import { monitoring } from './monitoring'

// 通用工具类
export class ModernUtils {
  /**
   * 防抖函数
   */
  static debounce<T extends (...args: any[]) => any>(
    func: T,
    wait: number,
    immediate = false
  ): (...args: Parameters<T>) => void {
    let timeout: number | null = null
    
    return function executedFunction(...args: Parameters<T>) {
      const later = () => {
        timeout = null
        if (!immediate) func(...args)
      }
      
      const callNow = immediate && !timeout
      
      if (timeout) clearTimeout(timeout)
      timeout = setTimeout(later, wait)
      
      if (callNow) func(...args)
    }
  }

  /**
   * 节流函数
   */
  static throttle<T extends (...args: any[]) => any>(
    func: T,
    limit: number
  ): (...args: Parameters<T>) => void {
    let inThrottle: boolean
    
    return function executedFunction(...args: Parameters<T>) {
      if (!inThrottle) {
        func.apply(this, args)
        inThrottle = true
        setTimeout(() => inThrottle = false, limit)
      }
    }
  }

  /**
   * 深拷贝
   */
  static deepClone<T>(obj: T): T {
    if (obj === null || typeof obj !== 'object') return obj
    if (obj instanceof Date) return new Date(obj.getTime()) as unknown as T
    if (obj instanceof Array) return obj.map(item => this.deepClone(item)) as unknown as T
    if (typeof obj === 'object') {
      const clonedObj = {} as T
      for (const key in obj) {
        if (obj.hasOwnProperty(key)) {
          clonedObj[key] = this.deepClone(obj[key])
        }
      }
      return clonedObj
    }
    return obj
  }

  /**
   * 生成唯一ID
   */
  static generateId(prefix = ''): string {
    const timestamp = Date.now().toString(36)
    const random = Math.random().toString(36).substr(2, 9)
    return `${prefix}${timestamp}_${random}`
  }

  /**
   * 格式化文件大小
   */
  static formatFileSize(bytes: number): string {
    if (bytes === 0) return '0 B'
    
    const k = 1024
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  /**
   * 格式化数字
   */
  static formatNumber(num: number, precision = 2): string {
    if (num >= 1e9) {
      return (num / 1e9).toFixed(precision) + 'B'
    }
    if (num >= 1e6) {
      return (num / 1e6).toFixed(precision) + 'M'
    }
    if (num >= 1e3) {
      return (num / 1e3).toFixed(precision) + 'K'
    }
    return num.toString()
  }

  /**
   * 安全的JSON解析
   */
  static safeJsonParse<T>(str: string, defaultValue: T): T {
    try {
      return JSON.parse(str)
    } catch (error) {
      monitoring.captureError({
        message: `JSON解析失败: ${error}`,
        category: 'logic',
        level: 'warning',
        context: { str: str.slice(0, 100) }
      })
      return defaultValue
    }
  }

  /**
   * 安全的JSON字符串化
   */
  static safeJsonStringify(obj: any, defaultValue = '{}'): string {
    try {
      return JSON.stringify(obj)
    } catch (error) {
      monitoring.captureError({
        message: `JSON字符串化失败: ${error}`,
        category: 'logic',
        level: 'warning'
      })
      return defaultValue
    }
  }

  /**
   * 重试函数
   */
  static async retry<T>(
    fn: () => Promise<T>,
    maxAttempts = 3,
    delay = 1000
  ): Promise<T> {
    let lastError: Error
    
    for (let attempt = 1; attempt <= maxAttempts; attempt++) {
      try {
        return await fn()
      } catch (error) {
        lastError = error as Error
        
        if (attempt === maxAttempts) {
          monitoring.captureError({
            message: `重试失败，已达到最大次数: ${error}`,
            category: 'logic',
            level: 'error',
            context: { maxAttempts, attempt }
          })
          throw lastError
        }
        
        // 指数退避
        const waitTime = delay * Math.pow(2, attempt - 1)
        await new Promise(resolve => setTimeout(resolve, waitTime))
      }
    }
    
    throw lastError!
  }

  /**
   * 超时包装器
   */
  static withTimeout<T>(
    promise: Promise<T>,
    timeoutMs: number,
    timeoutMessage = '操作超时'
  ): Promise<T> {
    return Promise.race([
      promise,
      new Promise<never>((_, reject) => {
        setTimeout(() => {
          reject(new Error(timeoutMessage))
        }, timeoutMs)
      })
    ])
  }

  /**
   * 批处理函数
   */
  static async batchProcess<T, R>(
    items: T[],
    processor: (item: T) => Promise<R>,
    batchSize = 5,
    delay = 100
  ): Promise<R[]> {
    const results: R[] = []
    
    for (let i = 0; i < items.length; i += batchSize) {
      const batch = items.slice(i, i + batchSize)
      const batchResults = await Promise.all(
        batch.map(item => processor(item))
      )
      results.push(...batchResults)
      
      // 批次间延迟
      if (i + batchSize < items.length) {
        await new Promise(resolve => setTimeout(resolve, delay))
      }
    }
    
    return results
  }
}

// 日期时间工具类
export class DateUtils {
  /**
   * 格式化日期
   */
  static format(date: Date | string | number, format = 'YYYY-MM-DD HH:mm:ss'): string {
    return dayjs(date).format(format)
  }

  /**
   * 相对时间
   */
  static fromNow(date: Date | string | number): string {
    const now = dayjs()
    const target = dayjs(date)
    const diff = now.diff(target, 'minute')
    
    if (diff < 1) return '刚刚'
    if (diff < 60) return `${diff}分钟前`
    if (diff < 1440) return `${Math.floor(diff / 60)}小时前`
    if (diff < 10080) return `${Math.floor(diff / 1440)}天前`
    
    return target.format('YYYY-MM-DD')
  }

  /**
   * 是否为今天
   */
  static isToday(date: Date | string | number): boolean {
    return dayjs(date).isSame(dayjs(), 'day')
  }

  /**
   * 是否为本周
   */
  static isThisWeek(date: Date | string | number): boolean {
    return dayjs(date).isSame(dayjs(), 'week')
  }

  /**
   * 是否为本月
   */
  static isThisMonth(date: Date | string | number): boolean {
    return dayjs(date).isSame(dayjs(), 'month')
  }

  /**
   * 获取时间段
   */
  static getTimeRange(type: 'today' | 'week' | 'month' | 'year'): { start: Date; end: Date } {
    const now = dayjs()
    
    switch (type) {
      case 'today':
        return {
          start: now.startOf('day').toDate(),
          end: now.endOf('day').toDate()
        }
      case 'week':
        return {
          start: now.startOf('week').toDate(),
          end: now.endOf('week').toDate()
        }
      case 'month':
        return {
          start: now.startOf('month').toDate(),
          end: now.endOf('month').toDate()
        }
      case 'year':
        return {
          start: now.startOf('year').toDate(),
          end: now.endOf('year').toDate()
        }
      default:
        return {
          start: now.startOf('day').toDate(),
          end: now.endOf('day').toDate()
        }
    }
  }
}

// 验证工具类
export class ValidationUtils {
  /**
   * 手机号验证
   */
  static isValidPhone(phone: string): boolean {
    const phoneRegex = /^1[3-9]\d{9}$/
    return phoneRegex.test(phone)
  }

  /**
   * 邮箱验证
   */
  static isValidEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    return emailRegex.test(email)
  }

  /**
   * 身份证验证
   */
  static isValidIdCard(idCard: string): boolean {
    const idCardRegex = /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/
    return idCardRegex.test(idCard)
  }

  /**
   * 中文姓名验证
   */
  static isValidChineseName(name: string): boolean {
    const nameRegex = /^[\u4e00-\u9fa5]{2,10}$/
    return nameRegex.test(name)
  }

  /**
   * 密码强度检查
   */
  static checkPasswordStrength(password: string): {
    score: number
    level: 'weak' | 'medium' | 'strong'
    suggestions: string[]
  } {
    let score = 0
    const suggestions: string[] = []
    
    // 长度检查
    if (password.length >= 8) {
      score += 2
    } else {
      suggestions.push('密码长度至少8位')
    }
    
    // 包含小写字母
    if (/[a-z]/.test(password)) {
      score += 1
    } else {
      suggestions.push('包含小写字母')
    }
    
    // 包含大写字母
    if (/[A-Z]/.test(password)) {
      score += 1
    } else {
      suggestions.push('包含大写字母')
    }
    
    // 包含数字
    if (/\d/.test(password)) {
      score += 1
    } else {
      suggestions.push('包含数字')
    }
    
    // 包含特殊字符
    if (/[!@#$%^&*(),.?":{}|<>]/.test(password)) {
      score += 1
    } else {
      suggestions.push('包含特殊字符')
    }
    
    let level: 'weak' | 'medium' | 'strong'
    if (score <= 2) {
      level = 'weak'
    } else if (score <= 4) {
      level = 'medium'
    } else {
      level = 'strong'
    }
    
    return { score, level, suggestions }
  }
}

// 存储工具类
export class StorageUtils {
  /**
   * 安全的存储设置
   */
  static setItem(key: string, value: any): boolean {
    try {
      wx.setStorageSync(key, value)
      return true
    } catch (error) {
      monitoring.captureError({
        message: `存储设置失败: ${error}`,
        category: 'logic',
        level: 'error',
        context: { key }
      })
      return false
    }
  }

  /**
   * 安全的存储获取
   */
  static getItem<T>(key: string, defaultValue: T): T {
    try {
      const value = wx.getStorageSync(key)
      return value !== '' ? value : defaultValue
    } catch (error) {
      monitoring.captureError({
        message: `存储获取失败: ${error}`,
        category: 'logic',
        level: 'error',
        context: { key }
      })
      return defaultValue
    }
  }

  /**
   * 安全的存储删除
   */
  static removeItem(key: string): boolean {
    try {
      wx.removeStorageSync(key)
      return true
    } catch (error) {
      monitoring.captureError({
        message: `存储删除失败: ${error}`,
        category: 'logic',
        level: 'error',
        context: { key }
      })
      return false
    }
  }

  /**
   * 清理过期存储
   */
  static clearExpiredItems(prefix: string, maxAge: number): number {
    try {
      const info = wx.getStorageInfoSync()
      let clearedCount = 0
      
      info.keys.forEach(key => {
        if (key.startsWith(prefix)) {
          try {
            const item = wx.getStorageSync(key)
            if (item && item.timestamp && Date.now() - item.timestamp > maxAge) {
              wx.removeStorageSync(key)
              clearedCount++
            }
          } catch (error) {
            // 忽略单个项目的错误
          }
        }
      })
      
      return clearedCount
    } catch (error) {
      monitoring.captureError({
        message: `清理过期存储失败: ${error}`,
        category: 'logic',
        level: 'error'
      })
      return 0
    }
  }

  /**
   * 获取存储使用情况
   */
  static getStorageInfo(): {
    keys: string[]
    currentSize: number
    limitSize: number
    usage: number
  } {
    try {
      const info = wx.getStorageInfoSync()
      return {
        keys: info.keys,
        currentSize: info.currentSize,
        limitSize: info.limitSize,
        usage: Math.round((info.currentSize / info.limitSize) * 100)
      }
    } catch (error) {
      monitoring.captureError({
        message: `获取存储信息失败: ${error}`,
        category: 'logic',
        level: 'error'
      })
      return {
        keys: [],
        currentSize: 0,
        limitSize: 10240, // 默认10MB
        usage: 0
      }
    }
  }
}
