# 🚀 微信开发者工具云函数部署指南

## 📋 部署前准备

### 1. 确认环境
- ✅ 微信开发者工具已安装
- ✅ 项目已在微信开发者工具中打开
- ✅ 云开发环境已配置（小程序AppID: wx3de03090b8e8a734）

### 2. 检查新增的云函数
新增的两个云函数位置：
```
cloudfunctions/
├── managePromptTemplates/     # 提示词模板管理
│   ├── index.js              # 主逻辑文件
│   └── package.json          # 依赖配置
└── getUsageStats/            # 使用统计数据
    ├── index.js              # 主逻辑文件
    └── package.json          # 依赖配置
```

## 🔧 部署步骤

### 步骤1：打开微信开发者工具
1. 启动微信开发者工具
2. 打开项目：`D:\G盘（软件）\cursor开发文件\评语灵感君`
3. 确认云开发环境已初始化

### 步骤2：部署 managePromptTemplates 云函数
1. 在微信开发者工具左侧文件树中找到：
   `cloudfunctions → managePromptTemplates`

2. **右键点击** `managePromptTemplates` 文件夹

3. 选择 **"创建并部署：云端安装依赖（不上传node_modules）"**

4. 等待部署完成，控制台会显示：
   ```
   ✅ managePromptTemplates 云函数部署成功
   ```

### 步骤3：部署 getUsageStats 云函数
1. 在微信开发者工具左侧文件树中找到：
   `cloudfunctions → getUsageStats`

2. **右键点击** `getUsageStats` 文件夹

3. 选择 **"创建并部署：云端安装依赖（不上传node_modules）"**

4. 等待部署完成，控制台会显示：
   ```
   ✅ getUsageStats 云函数部署成功
   ```

### 步骤4：验证部署状态
1. 在微信开发者工具中点击 **"云开发"** 按钮
2. 进入云开发控制台
3. 点击 **"云函数"** 标签页
4. 确认看到以下函数：
   - ✅ `managePromptTemplates`
   - ✅ `getUsageStats` 
   - 以及其他已有的云函数

## 🧪 功能测试

### 测试1：初始化默认模板
1. 在微信开发者工具的**控制台**中执行：
```javascript
// 测试managePromptTemplates云函数
wx.cloud.callFunction({
  name: 'managePromptTemplates',
  data: {
    action: 'initDefaults'
  }
}).then(res => {
  console.log('初始化结果:', res)
}).catch(err => {
  console.error('初始化失败:', err)
})
```

### 测试2：获取统计数据
```javascript
// 测试getUsageStats云函数
wx.cloud.callFunction({
  name: 'getUsageStats',
  data: {
    action: 'getStats'
  }
}).then(res => {
  console.log('统计数据:', res)
}).catch(err => {
  console.error('获取统计失败:', err)
})
```

### 测试3：管理后台功能验证
1. 启动管理后台项目：
```bash
cd admin-new
npm run dev
```

2. 访问 http://localhost:5173

3. 测试以下功能：
   - ✅ Dashboard数据大屏显示真实数据
   - ✅ AI配置页面的提示词模板增删改
   - ✅ 教师AI使用统计显示真实数据

## 🔍 问题排查

### 部署失败的常见原因

#### 1. 云开发环境未初始化
**症状**：显示"云开发环境不存在"
**解决**：
1. 点击微信开发者工具中的"云开发"按钮
2. 按照提示开通云开发服务
3. 创建环境ID

#### 2. 权限不足
**症状**：显示"没有操作权限"
**解决**：
1. 确认当前微信账号是小程序管理员
2. 检查云开发环境的权限设置

#### 3. 网络连接问题
**症状**：上传超时或连接失败
**解决**：
1. 检查网络连接
2. 重试部署
3. 考虑使用手机热点

#### 4. node_modules冲突
**症状**：依赖安装失败
**解决**：
1. 删除云函数目录下的node_modules文件夹
2. 重新选择"创建并部署：云端安装依赖"

### 功能测试失败排查

#### 1. 管理后台数据不显示
**检查项**：
- [ ] 云函数是否部署成功
- [ ] 浏览器控制台是否有错误
- [ ] 网络请求是否正常

#### 2. 提示词模板操作失败  
**检查项**：
- [ ] managePromptTemplates是否部署成功
- [ ] 数据库权限是否配置正确
- [ ] 云函数日志是否有错误信息

## 📊 数据库初始化

部署完成后，建议初始化一些测试数据：

### 1. 创建数据库集合
在云开发控制台→数据库中创建以下集合：
- `prompt_templates` - 提示词模板
- `users` - 用户信息  
- `activity_log` - 活动日志
- `generated_comments` - 生成的评语
- `ai_usage_log` - AI使用记录

### 2. 设置数据库权限
将所有集合的权限设置为：
- 读权限：仅创建者可读写
- 写权限：仅创建者可读写

## 🎉 部署完成检查清单

部署完成后，确认以下项目：

- [ ] ✅ managePromptTemplates 云函数部署成功
- [ ] ✅ getUsageStats 云函数部署成功  
- [ ] ✅ 管理后台可以正常访问
- [ ] ✅ Dashboard显示真实统计数据
- [ ] ✅ 提示词模板可以增删改
- [ ] ✅ 教师使用统计显示数据
- [ ] ✅ 小程序可以使用新的提示词模板

---

💡 **提示**：如果遇到问题，请查看微信开发者工具的控制台输出，通常会有详细的错误信息帮助排查问题。