/**
 * 清空所有云端数据脚本
 * 请在微信开发者工具的云函数控制台中运行此脚本
 */

// 在微信开发者工具中打开云开发控制台 → 云函数 → 新建云函数
// 将以下代码复制到 index.js 中，然后部署并测试云函数

const cloud = require('wx-server-sdk');

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV // 使用当前云环境
});

const db = cloud.database();

// 要清理的数据表（完整列表）
const collections = [
  'users',              // 用户信息
  'classes',            // 班级信息
  'students',           // 学生信息
  'records',            // 行为记录
  'comments',           // 评语记录
  'ai_configs',         // AI配置
  'ai_generation_logs', // AI生成日志
  'ai_error_logs',      // AI错误日志
  'ai_usage',           // AI使用统计
  'settings',           // 设置信息
  'admins',             // 管理员信息（保留系统管理员）
  'logs',               // 系统日志
  'files',              // 文件记录
  'notifications',      // 通知信息
  'system_config'       // 系统配置（保留基础配置）
];

// 需要保留的系统数据（不删除）
const preserveData = {
  'system_config': ['system_basic'],  // 保留基础系统配置
  'admins': ['default_super_admin']   // 保留默认管理员
};

/**
 * 云函数入口函数
 */
exports.main = async (event, context) => {
  console.log('开始清理所有云端数据...');
  
  const results = [];
  
  try {
    for (const collectionName of collections) {
      try {
        console.log(`清理集合: ${collectionName}`);
        
        // 先查询所有文档
        const queryResult = await db.collection(collectionName).get();
        const docs = queryResult.data;

        if (docs.length === 0) {
          results.push({
            collection: collectionName,
            success: true,
            deletedCount: 0,
            message: '集合已为空'
          });
          continue;
        }

        // 过滤需要保留的数据
        const preserveList = preserveData[collectionName] || [];
        const docsToDelete = docs.filter(doc => {
          if (preserveList.length === 0) return true;

          // 检查是否需要保留
          return !preserveList.some(preserveId => {
            return doc._id === preserveId ||
                   doc.id === preserveId ||
                   doc.type === preserveId ||
                   doc.username === preserveId;
          });
        });

        // 批量删除文档
        let deletedCount = 0;
        let preservedCount = docs.length - docsToDelete.length;

        for (const doc of docsToDelete) {
          try {
            await db.collection(collectionName).doc(doc._id).remove();
            deletedCount++;
          } catch (error) {
            console.error(`删除文档失败 (${collectionName}/${doc._id}):`, error);
          }
        }
        
        results.push({
          collection: collectionName,
          success: true,
          deletedCount: deletedCount,
          preservedCount: preservedCount,
          totalCount: docs.length,
          message: `成功删除 ${deletedCount}/${docs.length} 条记录${preservedCount > 0 ? `，保留 ${preservedCount} 条系统数据` : ''}`
        });
        
        console.log(`${collectionName}: 删除了 ${deletedCount}/${docs.length} 条记录`);
        
      } catch (error) {
        console.error(`清理集合 ${collectionName} 失败:`, error);
        results.push({
          collection: collectionName,
          success: false,
          error: error.message,
          message: `清理失败: ${error.message}`
        });
      }
    }
    
    // 统计总结果
    const successCount = results.filter(r => r.success).length;
    const totalDeleted = results.reduce((sum, r) => sum + (r.deletedCount || 0), 0);
    
    console.log('数据清理完成！');
    console.log(`成功清理: ${successCount}/${collections.length} 个集合`);
    console.log(`总共删除: ${totalDeleted} 条记录`);
    
    return {
      success: true,
      message: '数据清理完成',
      summary: {
        totalCollections: collections.length,
        successCollections: successCount,
        totalDeleted: totalDeleted
      },
      details: results
    };
    
  } catch (error) {
    console.error('数据清理过程发生错误:', error);
    return {
      success: false,
      error: error.message,
      results: results
    };
  }
};

/**
 * 使用说明：
 * 
 * 1. 在微信开发者工具中打开你的小程序项目
 * 2. 点击 "云开发" 按钮
 * 3. 在云开发控制台中选择 "云函数"
 * 4. 点击 "新建云函数"，命名为 "clearAllData"
 * 5. 将以上代码复制到 index.js 文件中
 * 6. 点击 "保存并安装依赖"
 * 7. 部署完成后，点击 "测试" 按钮运行云函数
 * 8. 查看运行结果，确认数据已清空
 * 
 * 注意事项：
 * - 此操作不可逆，请确认要清空所有数据
 * - 建议在测试环境中先运行
 * - 清空后需要重新录入用户信息和基础数据
 */ 