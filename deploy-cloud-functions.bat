@echo off
chcp 65001 >nul
echo ====================================
echo 评语灵感君 - 云函数部署脚本
echo ====================================
echo.

echo 🚀 开始部署云函数...
echo.

echo 📦 1. 部署 managePromptTemplates 云函数...
cd cloudfunctions\managePromptTemplates
call npm install
if %errorlevel% neq 0 (
    echo ❌ 安装依赖失败
    pause
    exit /b 1
)

echo 正在上传云函数...
call wxcloud functions:deploy managePromptTemplates
if %errorlevel% neq 0 (
    echo ❌ 部署失败
    pause
    exit /b 1
)
echo ✅ managePromptTemplates 部署成功
echo.

cd ..\..

echo 📦 2. 部署 getUsageStats 云函数...
cd cloudfunctions\getUsageStats
call npm install
if %errorlevel% neq 0 (
    echo ❌ 安装依赖失败
    pause
    exit /b 1
)

echo 正在上传云函数...
call wxcloud functions:deploy getUsageStats
if %errorlevel% neq 0 (
    echo ❌ 部署失败
    pause
    exit /b 1
)
echo ✅ getUsageStats 部署成功
echo.

cd ..\..

echo 🎉 所有云函数部署完成！
echo.
echo 📋 部署的云函数列表：
echo   - managePromptTemplates (提示词模板管理)
echo   - getUsageStats (使用统计数据)
echo.
echo 💡 接下来你可以：
echo   1. 在管理后台测试提示词模板的增删改功能
echo   2. 查看实时的使用统计数据
echo   3. 初始化默认提示词模板
echo.
pause