/**
 * AI评语助手 - 简化版首页
 * 专注核心功能，确保稳定运行
 */
const app = getApp();

// 工具函数
function isMobile() {
  try {
    const systemInfo = wx.getSystemInfoSync();
    return systemInfo.platform !== 'devtools';
  } catch (error) {
    return true; // 默认认为是真机
  }
}

function isDevTools() {
  try {
    const systemInfo = wx.getSystemInfoSync();
    return systemInfo.platform === 'devtools';
  } catch (error) {
    return false;
  }
}

Page({
  /**
   * 页面的初始数据
   */
  data: {
    // 用户信息
    userInfo: {},
    displayName: '老师', // 格式化后的显示名称

    // 问候时间
    greetingTime: '',

    // 效率统计
    efficiencyStats: {
      timeSaved: 0,       // 节省时间（分钟）
      commentsGenerated: 0, // 生成评语数
      qualityScore: 0     // 平均质量分
    },

    // 最近评语
    recentComments: []
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    // 执行页面初始化
    this.initPage();
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {
    console.log('[首页] 页面显示事件触发');
    
    // 【修复】分阶段同步，确保真机环境兼容性
    
    // 第一阶段：立即强制同步
    this.forceSyncUserInfo();
    
    // 第二阶段：延迟再次检查（真机环境需要）
    setTimeout(() => {
      console.log('[首页] 延迟检查用户信息...');
      this.getUserInfo();
      
      // 第三阶段：如果还是没有信息，最后一次尝试
      setTimeout(() => {
        const currentUserInfo = this.data.userInfo;
        if (!currentUserInfo || (!currentUserInfo.name && !currentUserInfo.nickName)) {
          console.log('[首页] 执行最后一次用户信息同步...');
          this.forceSyncUserInfo();
        }
      }, 1000);
    }, 500);
    
    // 刷新其他数据
    this.refreshData();
    this.startDataRefreshTimer();
  },

  /**
   * 【修复】强制同步用户信息 - 真机与开发工具兼容
   */
  forceSyncUserInfo() {
    try {
      console.log('[首页] 开始强制同步用户信息...');
      
      // 多层级获取用户信息，确保数据完整性
      const methods = [
        // 方法1：直接从存储获取
        () => wx.getStorageSync('userInfo'),
        // 方法2：从全局数据获取
        () => getApp().globalData.userInfo,
        // 方法3：从页面数据获取
        () => this.data.userInfo
      ];
      
      let userInfo = null;
      let sourceMethod = '';
      
      // 依次尝试各种获取方法
      for (let i = 0; i < methods.length; i++) {
        try {
          const result = methods[i]();
          if (result && (result.name || result.nickName || result.avatarUrl)) {
            userInfo = result;
            sourceMethod = `方法${i + 1}`;
            console.log(`[首页] 通过${sourceMethod}获取到用户信息:`, result);
            break;
          }
        } catch (error) {
          console.warn(`[首页] 方法${i + 1}获取用户信息失败:`, error);
        }
      }
      
      if (userInfo) {
        // 确保数据同步到所有位置
        this.setData({ userInfo });
        getApp().globalData.userInfo = userInfo;
        wx.setStorageSync('userInfo', userInfo);
        
        // 格式化显示名称
        const displayName = this.formatDisplayName(userInfo);
        this.setData({ displayName });
        
        console.log(`[首页] 用户信息同步成功，显示名称: ${displayName}`);
        
        // 手机端静默同步成功
        console.log(`[首页] 用户信息同步成功，显示名称: ${displayName}`);
      } else {
        console.warn('[首页] 未能获取到任何用户信息');
        
        // 设置默认用户信息
        const defaultUserInfo = {
          name: '未设置姓名',
          nickName: '老师',
          avatarUrl: ''
        };
        
        this.setData({ 
          userInfo: defaultUserInfo,
          displayName: '老师'
        });
        
        // 手机端静默处理，不打扰用户
        console.log('[首页] 用户信息为空，使用默认显示');
      }
    } catch (error) {
      console.error('[首页] 强制同步用户信息失败:', error);
      
      // 错误时静默处理
      console.error('[首页] 用户信息同步异常，但不影响使用');
    }
  },

  /**
   * 用户信息更新回调（由设置页面调用）
   */
  onUserInfoUpdate(userInfo) {
    console.log('[首页] 收到用户信息更新通知:', userInfo);

    // 立即更新用户信息到所有位置
    this.setData({ userInfo: userInfo });
    getApp().globalData.userInfo = userInfo;
    
    try {
      wx.setStorageSync('userInfo', userInfo);
      console.log('[首页] 用户信息已同步到存储');
    } catch (error) {
      console.error('[首页] 同步用户信息到存储失败:', error);
    }

    // 格式化并更新显示名称
    const displayName = this.formatDisplayName(userInfo);
    this.setData({ displayName });
    console.log('[首页] 用户名称已更新为:', displayName);
    
    // 手机端静默更新
    console.log(`[首页] 用户信息更新完成: ${displayName}`);
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function () {
    this.stopDataRefreshTimer();
  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function () {
    this.stopDataRefreshTimer();
  },

  /**
   * 初始化页面
   */
  async initPage() {
    try {
      // 设置问候时间
      this.setGreetingTime();
      
      // 获取用户信息
      await this.getUserInfo();
      
      // 加载数据
      await this.refreshEfficiencyStats();
      await this.loadRecentComments();
      
    } catch (error) {
      console.error('页面初始化失败:', error);
    }
  },

  /**
   * 设置问候时间
   */
  setGreetingTime() {
    const hour = new Date().getHours();
    let greeting = '';
    
    if (hour < 6) {
      greeting = '凌晨好';
    } else if (hour < 12) {
      greeting = '早上好';
    } else if (hour < 18) {
      greeting = '下午好';
    } else {
      greeting = '晚上好';
    }
    
    this.setData({ greetingTime: greeting });
  },

  /**
   * 获取用户信息 - 增强版，兼容真机环境
   */
  async getUserInfo() {
    try {
      console.log('[首页] 开始获取用户信息...');
      
      // 【策略1】多源数据获取
      const sources = {
        local: wx.getStorageSync('userInfo'),
        global: app.globalData.userInfo,
        page: this.data.userInfo
      };
      
      console.log('[首页] 各源数据状态:', {
        local: sources.local ? '有数据' : '无数据',
        global: sources.global ? '有数据' : '无数据', 
        page: sources.page ? '有数据' : '无数据'
      });
      
      // 【策略2】智能合并最完整的数据
      let userInfo = {};
      
      // 优先使用存储中的完整数据
      if (sources.local && (sources.local.name || sources.local.nickName)) {
        userInfo = { ...userInfo, ...sources.local };
        console.log('[首页] 使用本地存储数据');
      }
      
      // 补充全局数据
      if (sources.global && (sources.global.name || sources.global.nickName)) {
        userInfo = { ...userInfo, ...sources.global };
        console.log('[首页] 补充全局数据');
      }
      
      // 补充页面数据
      if (sources.page && (sources.page.name || sources.page.nickName)) {
        userInfo = { ...userInfo, ...sources.page };
        console.log('[首页] 补充页面数据');
      }
      
      // 【策略3】真机环境特殊处理
      if (isMobile()) {
        console.log('[首页] 真机环境，执行特殊处理...');
        
        // 真机环境下可能需要延迟获取
        if (!userInfo.name && !userInfo.nickName) {
          console.log('[首页] 真机环境下数据为空，等待100ms再次尝试...');
          await new Promise(resolve => setTimeout(resolve, 100));
          
          // 再次尝试获取
          const retryLocal = wx.getStorageSync('userInfo');
          const retryGlobal = app.globalData.userInfo;
          
          if (retryLocal && (retryLocal.name || retryLocal.nickName)) {
            userInfo = { ...userInfo, ...retryLocal };
            console.log('[首页] 延迟获取成功:', retryLocal);
          } else if (retryGlobal && (retryGlobal.name || retryGlobal.nickName)) {
            userInfo = { ...userInfo, ...retryGlobal };
            console.log('[首页] 延迟从全局获取成功:', retryGlobal);
          }
        }
      }
      
      // 【策略4】数据同步
      if (userInfo && (userInfo.name || userInfo.nickName)) {
        // 确保所有位置数据一致
        this.setData({ userInfo });
        app.globalData.userInfo = userInfo;
        
        try {
          wx.setStorageSync('userInfo', userInfo);
        } catch (syncError) {
          console.error('[首页] 同步用户信息到存储失败:', syncError);
        }
        
        // 格式化显示名称
        const displayName = this.formatDisplayName(userInfo);
        console.log(`[首页] 用户信息获取成功，显示名称: ${displayName}`);
        
      } else {
        console.log('[首页] 未获取到有效用户信息，使用默认值');
        
        // 使用默认信息
        const defaultInfo = {
          name: '未设置姓名',
          nickName: '老师',
          avatarUrl: ''
        };
        
        this.setData({
          userInfo: defaultInfo,
          displayName: '老师'
        });
      }
      
    } catch (error) {
      console.error('[首页] 获取用户信息异常:', error);
      
      // 异常时使用默认信息
      this.setData({
        userInfo: { nickName: '老师', name: '未设置姓名' },
        displayName: '老师'
      });
      
      // 手机端静默处理错误
      console.error('[首页] 获取用户信息异常，使用默认值');
    }
  },

  /**
   * 格式化显示名称 - 简化版
   */
  formatDisplayName(userInfo) {
    console.log('[首页] 开始格式化显示名称，输入数据:', userInfo);

    // 数据验证
    if (!userInfo || typeof userInfo !== 'object') {
      console.log('[首页] 用户信息无效，使用默认称呼');
      return '老师';
    }

    // 获取名称
    let displayName = userInfo.name || userInfo.nickName;
    console.log('[首页] 提取的原始名称:', displayName);

    // 名称验证
    if (!displayName ||
        typeof displayName !== 'string' ||
        displayName.trim() === '' ||
        displayName === '未设置姓名' ||
        displayName === '用户') {
      console.log('[首页] 名称无效，使用默认称呼');
      return '老师';
    }

    // 清理名称
    displayName = displayName.trim();

    // 如果已经包含"老师"，直接返回
    if (displayName.includes('老师')) {
      console.log('[首页] 名称已包含"老师"，直接使用');
      return displayName;
    }

    // 提取姓氏（前1-2个字符）
    let surname = displayName;
    if (displayName.length > 2) {
      // 常见复姓处理
      const compoundSurnames = ['欧阳', '太史', '端木', '上官', '司马', '东方', '独孤', '南宫', '万俟', '闻人', '夏侯', '诸葛', '尉迟', '公羊', '赫连', '澹台', '皇甫', '宗政', '濮阳', '公冶', '太叔', '申屠', '公孙', '慕容', '仲孙', '钟离', '长孙', '宇文', '司徒', '鲜于'];

      let foundCompound = false;
      for (const compound of compoundSurnames) {
        if (displayName.startsWith(compound)) {
          surname = compound;
          foundCompound = true;
          break;
        }
      }

      if (!foundCompound) {
        surname = displayName.charAt(0);
      }
    }

    const formattedName = surname + '老师';
    console.log('[首页] 最终格式化结果:', formattedName);
    return formattedName;
  },

  /**
   * 设置问候时间
   */
  setGreetingTime() {

  /**
   * 从昵称中提取合适的称呼
   */
  extractFromNickname(name, pattern) {
    if (/^小.+/.test(name)) {
      // 小妮子 -> 小妮、小明 -> 小明
      return name.length > 2 ? name.substring(0, 2) : name;
    }

    if (/^老.+/.test(name)) {
      // 老王 -> 老王、老李 -> 老李
      return name.length > 2 ? name.substring(0, 2) : name;
    }

    if (/^阿.+/.test(name)) {
      // 阿强 -> 阿强、阿美 -> 阿美
      return name.length > 2 ? name.substring(0, 2) : name;
    }

    if (/.+子$/.test(name)) {
      // 妮子 -> 妮、胖子 -> 胖
      return name.length > 2 ? name.charAt(0) : name.substring(0, 1);
    }

    if (/.+儿$/.test(name)) {
      // 小儿 -> 小、妞儿 -> 妞
      return name.length > 2 ? name.charAt(0) : name.substring(0, 1);
    }

    if (/^.+哥$/.test(name) || /^.+姐$/.test(name)) {
      // 强哥 -> 强、美姐 -> 美
      return name.substring(0, name.length - 1);
    }

    // 默认返回第一个字符
    return name.charAt(0);
  },

  /**
   * 处理纯英文名
   */
  handleEnglishName(name) {
    if (!name || name.length === 0) {
      return 'User';
    }

    // 将英文名首字母大写，其余小写
    const formatted = name.charAt(0).toUpperCase() + name.slice(1).toLowerCase();

    // 根据长度决定显示策略
    if (formatted.length <= 8) {
      // 8个字符以内直接显示完整名称
      return formatted;
    } else if (formatted.length <= 12) {
      // 8-12个字符显示前8个字符
      return formatted.substring(0, 8);
    } else {
      // 超过12个字符显示前6个字符
      return formatted.substring(0, 6);
    }
  },

  /**
   * 处理混合名称
   */
  handleMixedName(name) {
    if (!name || name.length === 0) {
      return 'User';
    }

    // 如果包含中文，优先提取中文部分
    const chineseChars = name.match(/[\u4e00-\u9fa5]/g);
    if (chineseChars && chineseChars.length > 0) {
      return chineseChars[0];
    }

    // 如果没有中文，提取英文部分
    const englishChars = name.match(/[a-zA-Z]+/g);
    if (englishChars && englishChars.length > 0) {
      const firstEnglish = englishChars[0];
      // 确保返回值不为空
      if (firstEnglish.length === 0) {
        return name.charAt(0) || 'User';
      }

      // 智能截取英文名
      if (firstEnglish.length <= 6) {
        return firstEnglish.charAt(0).toUpperCase() + firstEnglish.slice(1).toLowerCase();
      } else {
        return firstEnglish.charAt(0).toUpperCase() + firstEnglish.slice(1, 4).toLowerCase();
      }
    }

    // 默认返回第一个字符，确保不为空
    return name.charAt(0) || 'User';
  },

  /**
   * 测试姓名解析功能（开发调试用）
   */
  testNameParsing() {
    const testNames = [
      'warmsunchan',      // 应该显示: Warmsunchan老师
      'john',             // 应该显示: John老师
      'alexandersmith',   // 应该显示: Alexander老师 (超过12字符截取前6个)
      'warmheart',        // 应该显示: Warmheart老师
      '小妮子',           // 应该显示: 小妮老师
      '欧阳修',           // 应该显示: 欧阳老师
      '张三',             // 应该显示: 张老师
      '滚滚',             // 应该显示: 滚老师
      '老王',             // 应该显示: 老王老师
      '阿强'              // 应该显示: 阿强老师
    ];

    console.log('=== 姓名解析测试 ===');
    testNames.forEach(name => {
      const result = this.parseNameIntelligently(name);
      console.log(`${name} -> ${result}老师`);
    });
    console.log('=== 测试完成 ===');
  },

  /**
   * 加载最近评语
   */
  async loadRecentComments() {
    try {
      // 从本地存储获取最近评语
      const savedComments = wx.getStorageSync('savedComments') || [];
      const recentComments = wx.getStorageSync('recentComments') || [];
      
      // 合并并去重
      const allComments = [...savedComments, ...recentComments];
      const uniqueComments = allComments.filter((comment, index, arr) => 
        arr.findIndex(c => c.id === comment.id) === index
      );
      
      // 按时间排序（最新的在前）
      const sortedComments = uniqueComments.sort((a, b) => {
        const timeA = a.createTime || a.timestamp || a.saveTime || 0;
        const timeB = b.createTime || b.timestamp || b.saveTime || 0;
        return timeB - timeA;
      });
      
      if (sortedComments.length > 0) {
        // 添加时间显示
        const commentsWithTime = sortedComments.map(comment => {
          const createTime = new Date(comment.createTime || comment.timestamp || comment.saveTime || Date.now());
          const now = new Date();
          const timeDiff = now - createTime;
          
          let timeAgo = '';
          if (timeDiff < 60000) { // 1分钟
            timeAgo = '刚刚';
          } else if (timeDiff < 3600000) { // 1小时
            timeAgo = Math.floor(timeDiff / 60000) + '分钟前';
          } else if (timeDiff < 86400000) { // 1天
            timeAgo = Math.floor(timeDiff / 3600000) + '小时前';
          } else {
            timeAgo = Math.floor(timeDiff / 86400000) + '天前';
          }
          
          return {
            ...comment,
            timeAgo,
            content: this.truncateText(comment.content || comment.comment || '', 30)
          };
        });
        
        this.setData({ 
          recentComments: commentsWithTime.slice(0, 5) // 最多显示5条
        });
      } else {
        // 没有评语时显示空数组
        this.setData({ 
          recentComments: []
        });
      }
        
    } catch (error) {
      console.error('加载最近评语失败:', error);
      this.setData({ 
        recentComments: []
      });
    }
  },

  /**
   * 截取文本
   */
  truncateText(text, maxLength) {
    if (!text) return '';
    if (text.length <= maxLength) return text;
    return text.substring(0, maxLength) + '...';
  },

  /**
   * 刷新效率统计
   */
  async refreshEfficiencyStats() {
    try {
      // 从本地存储获取真实统计数据
      const savedComments = wx.getStorageSync('savedComments') || [];
      const recentComments = wx.getStorageSync('recentComments') || [];
      
      // 合并评语数据并去重
      const allComments = [...savedComments, ...recentComments];
      const uniqueComments = allComments.filter((comment, index, arr) => 
        arr.findIndex(c => c.id === comment.id) === index
      );
      
      // 计算今日数据
      const today = new Date();
      today.setHours(0, 0, 0, 0);
      const todayTimestamp = today.getTime();
      
      const todayComments = uniqueComments.filter(comment => {
        const commentTime = comment.createTime || comment.timestamp || Date.now();
        return commentTime >= todayTimestamp;
      });
      
      // 计算质量分数（基于评语长度和完整性）
      let totalQuality = 0;
      if (todayComments.length > 0) {
        todayComments.forEach(comment => {
          const content = comment.content || comment.comment || '';
          const length = content.length;
          
          // 基于评语长度计算质量分数
          let quality = 0;
          if (length >= 150) quality = 9.5; // 详细评语
          else if (length >= 100) quality = 9.0; // 标准评语  
          else if (length >= 50) quality = 8.5; // 简洁评语
          else quality = 7.0; // 过短评语
          
          totalQuality += quality;
        });
      }
      
      const avgQuality = todayComments.length > 0 ? (totalQuality / todayComments.length).toFixed(1) : '0';
      
      const stats = {
        timeSaved: todayComments.length * 15,  // 每条评语节省15分钟
        commentsGenerated: todayComments.length,
        qualityScore: avgQuality
      };
      
      console.log('首页统计数据:', stats, '今日评语数:', todayComments.length);
      
      this.setData({ efficiencyStats: stats });
    } catch (error) {
      console.error('刷新效率统计失败:', error);
      // 显示实际的空状态
      this.setData({
        efficiencyStats: {
          timeSaved: 0,
          commentsGenerated: 0,
          qualityScore: '0'
        }
      });
    }
  },

  /**
   * 清除所有测试数据（开发调试用）
   */
  clearTestData() {
    try {
      // 清除本地存储中的测试数据
      wx.removeStorageSync('savedComments');
      wx.removeStorageSync('recentComments');

      // 重置页面数据
      this.setData({
        recentComments: [],
        efficiencyStats: {
          timeSaved: 0,
          commentsGenerated: 0,
          qualityScore: '0'
        }
      });

      console.log('所有测试数据已清除');
      // 注释掉toast提示，避免在生产环境中显示
      // wx.showToast({
      //   title: '测试数据已清除',
      //   icon: 'success'
      // });
    } catch (error) {
      console.error('清除测试数据失败:', error);
    }
  },

  /**
   * 刷新数据
   */
  async refreshData() {
    await Promise.all([
      this.getUserInfo(),
      this.refreshEfficiencyStats(),
      this.loadRecentComments()
    ]);
  },

  /**
   * 跳转到AI生成页面
   */
  goToGenerate() {
    wx.navigateTo({
      url: '/pages/comment/generate/generate'
    });
  },

  /**
   * 跳转到快速记录页面
   */
  goToQuickRecord() {
    wx.navigateTo({
      url: '/pages/record/create/create'
    });
  },

  /**
   * 跳转到我的作品
   */
  goToWorks() {
    wx.navigateTo({
      url: '/pages/works/list/list'
    });
  },

  /**
   * 【开发测试】跳转到增长功能测试页面
   */
  goToGrowthTest() {
    console.log('[首页] 长按触发增长功能测试页面');
    wx.navigateTo({
      url: '/pages/growth-test/growth-test'
    });
  },
  
  /**
   * 【手机端调试】用户信息调试工具 - 长按触发
   */
  debugUserInfo() {
    try {
      const systemInfo = wx.getSystemInfoSync();
      
      // 获取各种数据源
      const localData = wx.getStorageSync('userInfo');
      const globalData = getApp().globalData.userInfo;
      const pageData = this.data.userInfo;
      const displayName = this.data.displayName;
      
      const debugInfo = {
        platform: systemInfo.platform || '未知',
        local: localData ? `有数据 (${localData.name || localData.nickName || '无名称'})` : '无数据',
        global: globalData ? `有数据 (${globalData.name || globalData.nickName || '无名称'})` : '无数据',
        page: pageData ? `有数据 (${pageData.name || pageData.nickName || '无名称'})` : '无数据',
        display: displayName || '未设置'
      };
      
      const debugText = `平台: ${debugInfo.platform}\n本地存储: ${debugInfo.local}\n全局数据: ${debugInfo.global}\n页面数据: ${debugInfo.page}\n显示名称: ${debugInfo.display}`;
      
      console.log('[首页] 用户信息调试数据:', debugInfo);
      
      wx.showModal({
        title: '🔍 用户信息调试',
        content: debugText,
        showCancel: true,
        cancelText: '关闭',
        confirmText: '重新同步',
        success: (res) => {
          if (res.confirm) {
            // 用户点击重新同步
            this.forceSyncUserInfo();
            
            setTimeout(() => {
              wx.showToast({
                title: '同步完成，请检查显示',
                icon: 'success',
                duration: 2000
              });
            }, 500);
          }
        }
      });
    } catch (error) {
      console.error('[首页] 调试工具异常:', error);
      wx.showModal({
        title: '调试异常',
        content: `错误: ${error.message}`,
        showCancel: false
      });
    }
  },

  /**
   * 【增长功能】测试增长功能 - 开发模式专用
   */
  testGrowthFeatures() {
    try {
      const { testGrowthFeatures, showGrowthStats } = require('../../utils/growthTest');
      
      wx.showActionSheet({
        itemList: ['测试所有功能', '查看增长统计', '重置增长数据', '模拟用户操作'],
        success: (res) => {
          switch (res.tapIndex) {
            case 0:
              testGrowthFeatures();
              wx.showToast({ title: '测试完成，查看控制台', icon: 'success' });
              break;
            case 1:
              showGrowthStats();
              break;
            case 2:
              wx.showModal({
                title: '确认重置',
                content: '将清除所有增长功能数据，确定继续吗？',
                success: (modalRes) => {
                  if (modalRes.confirm) {
                    const { resetGrowthData } = require('../../utils/growthTest');
                    resetGrowthData();
                    wx.showToast({ title: '数据已重置', icon: 'success' });
                  }
                }
              });
              break;
            case 3:
              const { simulateUserActions } = require('../../utils/growthTest');
              simulateUserActions();
              wx.showToast({ title: '模拟操作已启动', icon: 'success' });
              break;
          }
        }
      });
    } catch (error) {
      console.error('[首页] 增长功能测试失败:', error);
      wx.showToast({ title: '测试功能不可用', icon: 'none' });
    }
  },

  /**
   * 下拉刷新
   */
  onPullDownRefresh() {
    this.refreshData().finally(() => {
      wx.stopPullDownRefresh();
    });
  },

  /**
   * 启动数据刷新定时器
   */
  startDataRefreshTimer() {
    // 清除已存在的定时器
    if (this.refreshTimer) {
      clearInterval(this.refreshTimer);
    }

    // 每30秒刷新一次数据
    this.refreshTimer = setInterval(() => {
      this.refreshEfficiencyStats();
      this.loadRecentComments();
    }, 30000);
  },

  /**
   * 停止数据刷新定时器
   */
  stopDataRefreshTimer() {
    if (this.refreshTimer) {
      clearInterval(this.refreshTimer);
      this.refreshTimer = null;
    }
  },


  /**
   * 页面分享 - 集成增长优化
   */
  onShareAppMessage() {
    try {
      // 【增长功能】使用优化的分享内容
      const optimizedContent = getOptimizedShareContent({
        title: 'AI评语助手 - 3分钟生成专业评语',
        path: '/pages/index/index'
      });
      
      return optimizedContent;
    } catch (error) {
      console.error('[首页] 分享优化失败，使用原有内容:', error);
      // 降级到原有分享内容
      return {
        title: 'AI评语助手 - 3分钟生成专业评语',
        path: '/pages/index/index'
      };
    }
  },
  
  /**
   * 【开发者专用】触摸事件 - 长按触发调试（仅开发者模式）
   */
  onLongPress() {
    // 只有在特定条件下才显示调试工具（比如连续长按3次）
    this.debugPressCount = (this.debugPressCount || 0) + 1;
    
    if (this.debugPressCount >= 3) {
      // 重置计数器
      this.debugPressCount = 0;
      
      // 显示开发者调试工具
      this.debugUserInfo();
      
      // 清除计数器（5秒后重置）
      setTimeout(() => {
        this.debugPressCount = 0;
      }, 5000);
    } else {
      // 给开发者提示
      console.log(`[开发者模式] 长按${this.debugPressCount}/3次，连续长按3次可开启调试工具`);
    }
  },

  /**
   * 【修复】清除用户信息缓存并重新加载
   */
  clearUserInfoCache() {
    try {
      console.log('[首页] 清除用户信息缓存...');
      
      // 清除所有可能的用户信息缓存
      this.setData({ 
        userInfo: {},
        displayName: '老师'
      });
      
      // 清除全局数据
      const app = getApp();
      app.globalData.userInfo = null;
      
      // 重新获取用户信息
      setTimeout(() => {
        this.getUserInfo();
      }, 100);
      
      console.log('[首页] 用户信息缓存已清除，正在重新加载...');
    } catch (error) {
      console.error('[首页] 清除缓存失败:', error);
    }
  }
});