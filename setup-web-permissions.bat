@echo off
chcp 65001 >nul
echo.
echo ========================================
echo   Setup Web Access Permissions
echo ========================================
echo.
echo Step 1: Deploy cloud functions...
echo.

REM Deploy cloud functions with new configs
echo Deploying getUsageStats...
call tcb fn deploy getUsageStats --env cloud1-4g85f8xlb8166ff1

echo.
echo Deploying dataQuery...  
call tcb fn deploy dataQuery --env cloud1-4g85f8xlb8166ff1

echo.
echo ========================================
echo   Manual Steps Required in Tencent Console
echo ========================================
echo.
echo Please follow these steps in Tencent Cloud Console:
echo.
echo 1. Open: https://console.cloud.tencent.com/tcb
echo 2. Select Environment: cloud1-4g85f8xlb8166ff1
echo.
echo 3. Configure Database Security Rules:
echo    - Go to: Security Rules ^> Database Security Rules
echo    - Click: Add Rule
echo    - Collection: * (all collections)
echo    - Operation: read
echo    - Condition: auth.uid != null
echo.
echo 4. Configure Cloud Function Permissions:
echo    - Go to: Cloud Functions ^> Permission Management  
echo    - Enable Anonymous Access for:
echo      * getUsageStats
echo      * dataQuery
echo      * adminAPI
echo.
echo 5. Configure Web Security Domains:
echo    - Go to: Environment ^> Security Config ^> Web Security Domain
echo    - Add domains:
echo      * localhost:5173
echo      * 127.0.0.1:5173
echo      * localhost:3000
echo.
echo ========================================
echo   Configuration Complete!
echo ========================================
echo.
pause