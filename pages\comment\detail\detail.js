/**
 * 评语详情页面
 */
const { cloudService } = require('../../../services/cloudService');
const { notifyHomeDataUpdate, notifyCommentDataUpdate } = require('../../../utils/dataSync');
const { smartShare } = require('../../../utils/smartShare');

Page({
  /**
   * 页面的初始数据
   */
  data: {
    commentId: '',
    commentInfo: {},
    loading: true,

    // 操作菜单
    showActionSheet: false,
    actionSheetActions: [
      { name: '智能分享', value: 'share', icon: 'share' },
      { name: '编辑评语', value: 'edit' },
      { name: '重新生成', value: 'regenerate' },
      { name: '复制内容', value: 'copy' },
      { name: '删除评语', value: 'delete', color: '#FF5247' }
    ],

    // 删除确认
    showDeleteDialog: false
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    const { id } = options;
    if (id) {
      this.setData({ commentId: id });
      this.loadCommentDetail(id);
    } else {
      wx.showToast({
        title: '参数错误',
        icon: 'none'
      });
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
    }
  },

  /**
   * 加载评语详情
   */
  async loadCommentDetail(commentId) {
    try {
      this.setData({ loading: true });

      console.log('开始加载评语详情, commentId:', commentId);

      const result = await cloudService.getCommentDetail(commentId);
      console.log('获取评语详情结果:', result);
      
      if (result.success) {
        const commentInfo = result.data;

        // 数据验证
        if (!commentInfo || !commentInfo._id) {
          throw new Error('评语数据格式错误');
        }

        this.setData({
          commentInfo: commentInfo
        });

        // 设置页面标题
        wx.setNavigationBarTitle({
          title: `${commentInfo.studentName || '未知学生'}的评语`
        });
      } else {
        throw new Error(result.error || '加载失败');
      }
    } catch (error) {
      console.error('加载评语详情失败:', error);
      wx.showToast({
        title: error.message || '加载失败',
        icon: 'none'
      });
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
    } finally {
      this.setData({ loading: false });
    }
  },

  /**
   * 显示操作菜单
   */
  showActions() {
    this.setData({ showActionSheet: true });
  },

  /**
   * 隐藏操作菜单
   */
  hideActions() {
    this.setData({ showActionSheet: false });
  },

  /**
   * 操作选择
   */
  onActionSelect(e) {
    const { value } = e.detail;
    this.hideActions();

    switch (value) {
      case 'share':
        this.smartShare();
        break;
      case 'edit':
        this.editComment();
        break;
      case 'regenerate':
        this.regenerateComment();
        break;
      case 'copy':
        this.copyComment();
        break;
      case 'delete':
        this.showDeleteConfirm();
        break;
    }
  },

  /**
   * 编辑评语
   */
  editComment() {
    const { commentInfo } = this.data;
    wx.navigateTo({
      url: `/pages/comment/edit/edit?id=${commentInfo._id || commentInfo.id}`
    });
  },

  /**
   * 重新生成评语
   */
  regenerateComment() {
    const { commentInfo } = this.data;

    wx.showModal({
      title: '重新生成评语',
      content: `确定要重新生成${commentInfo.studentName}的评语吗？\n\n原评语内容将被覆盖，此操作不可撤销。`,
      confirmText: '重新生成',
      cancelText: '取消',
      success: (res) => {
        if (res.confirm) {
          // 构建跳转参数，包含学生信息和重新生成标识（不使用URLSearchParams）
          const paramStr = [
            `mode=regenerate`,
            `commentId=${commentInfo._id || commentInfo.id}`,
            `studentId=${commentInfo.studentId}`,
            `studentName=${encodeURIComponent(commentInfo.studentName)}`,
            `className=${encodeURIComponent(commentInfo.className || '')}`,
            `originalStyle=${commentInfo.style || 'warm'}`,
            `originalLength=${commentInfo.length || 'medium'}`
          ].join('&');

          wx.navigateTo({
            url: `/pages/comment/generate/generate?${paramStr}`
          });
        }
      }
    });
  },

  /**
   * 复制评语内容
   */
  copyComment() {
    const { commentInfo } = this.data;

    wx.setClipboardData({
      data: commentInfo.content,
      success: () => {
        wx.showToast({
          title: '已复制到剪贴板',
          icon: 'success'
        });
      },
      fail: () => {
        wx.showToast({
          title: '复制失败',
          icon: 'none'
        });
      }
    });
  },

  /**
   * 显示删除确认
   */
  showDeleteConfirm() {
    this.setData({ showDeleteDialog: true });
  },

  /**
   * 确认删除
   */
  async confirmDelete() {
    try {
      const { commentInfo } = this.data;

      wx.showLoading({
        title: '删除中...',
        mask: true
      });

      const result = await cloudService.deleteComment(commentInfo._id || commentInfo.id);

      if (result.success) {
        wx.showToast({
          title: '删除成功',
          icon: 'success'
        });

        // 通知数据更新
        notifyHomeDataUpdate();
        notifyCommentDataUpdate();

        // 返回上一页并刷新数据
        setTimeout(() => {
          wx.navigateBack();
        }, 1500);
      } else {
        throw new Error(result.error || '删除失败');
      }
    } catch (error) {
      console.error('删除评语失败:', error);
      wx.showToast({
        title: '删除失败',
        icon: 'none'
      });
    } finally {
      wx.hideLoading();
      this.setData({ showDeleteDialog: false });
    }
  },

  /**
   * 取消删除
   */
  cancelDelete() {
    this.setData({ showDeleteDialog: false });
  },

  /**
   * 格式化时间
   */
  formatTime(time) {
    if (!time) return '';

    const date = new Date(time);
    const now = new Date();
    const diff = now - date;

    if (diff < 60000) { // 1分钟内
      return '刚刚';
    } else if (diff < 3600000) { // 1小时内
      return Math.floor(diff / 60000) + '分钟前';
    } else if (diff < 86400000) { // 1天内
      return Math.floor(diff / 3600000) + '小时前';
    } else {
      return date.toLocaleDateString();
    }
  },

  /**
   * 智能分享评语
   */
  async smartShare() {
    try {
      const { commentInfo } = this.data;
      
      if (!commentInfo || !commentInfo.content) {
        wx.showToast({
          title: '评语内容为空',
          icon: 'none'
        });
        return;
      }

      // 准备分享数据
      const shareData = {
        studentName: commentInfo.studentName || '学生',
        commentContent: commentInfo.content,
        score: commentInfo.score || 85,
        style: commentInfo.styleText || '温和亲切',
        teacherName: '老师',
        className: commentInfo.className || '班级'
      };

      // 使用智能分享功能
      await smartShare(shareData, 'achievement', {
        canvasId: 'shareCanvas'
      });

    } catch (error) {
      console.error('智能分享失败:', error);
      wx.showToast({
        title: '分享失败，请重试',
        icon: 'none'
      });
    }
  }
})