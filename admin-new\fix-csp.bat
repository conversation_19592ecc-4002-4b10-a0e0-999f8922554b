@echo off
echo.
echo ==========================================
echo CSP Fix - Chrome No Security Mode
echo ==========================================
echo.

echo Step 1: Cleaning port 8080...
echo Killing processes on port 8080...
for /f "tokens=5" %%i in ('netstat -ano ^| findstr :8080') do (
    taskkill /f /pid %%i >nul 2>&1
)
taskkill /f /im node.exe >nul 2>&1
taskkill /f /im chrome.exe >nul 2>&1

echo Waiting for port to be released...
timeout /t 2 /nobreak >nul

echo Step 2: Starting dev server...
start "Dev Server" cmd /k "npm run dev"

echo Waiting for server to start...
timeout /t 3 /nobreak >nul

echo.
echo Step 3: Kill existing Chrome processes...
taskkill /f /im chrome.exe >nul 2>&1
timeout /t 2 /nobreak >nul

echo.
echo Step 4: Create temp directory...
set TEMP_DIR=%TEMP%\chrome-no-csp-%RANDOM%
mkdir "%TEMP_DIR%" >nul 2>&1

echo.
echo Step 5: Launch Chrome with all security disabled...

start "Chrome No Security" chrome.exe ^
  --disable-web-security ^
  --disable-features=VizDisplayCompositor ^
  --disable-site-isolation-trials ^
  --disable-extensions ^
  --no-sandbox ^
  --allow-running-insecure-content ^
  --disable-background-timer-throttling ^
  --disable-backgrounding-occluded-windows ^
  --disable-renderer-backgrounding ^
  --user-data-dir="%TEMP_DIR%" ^
  --ignore-certificate-errors ^
  --ignore-ssl-errors ^
  --disable-dev-shm-usage ^
  "http://localhost:8080"

echo.
echo SUCCESS! Chrome launched with security disabled
echo URL: http://localhost:8080
echo Login: admin / admin123
echo.
echo Test these features:
echo - Global search button
echo - Notification button  
echo - User menu functions
echo - Logout function
echo.
pause