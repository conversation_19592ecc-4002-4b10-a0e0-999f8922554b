<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>全面诊断工具</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        h1 { color: #2c3e50; text-align: center; }
        h2 { color: #34495e; border-bottom: 2px solid #3498db; padding-bottom: 10px; }
        
        .test-section {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
            background: #f9f9f9;
        }
        
        .status {
            padding: 5px 10px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
            display: inline-block;
            margin: 5px 5px 5px 0;
        }
        .status.checking { background: #fff3cd; color: #856404; }
        .status.success { background: #d4edda; color: #155724; }
        .status.error { background: #f8d7da; color: #721c24; }
        .status.warning { background: #d1ecf1; color: #0c5460; }
        
        button {
            background: #3498db;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #2980b9; }
        button:disabled { background: #bdc3c7; cursor: not-allowed; }
        
        .log {
            background: #2c3e50;
            color: #ecf0f1;
            padding: 15px;
            border-radius: 5px;
            font-family: monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
            margin-top: 15px;
        }
        
        .url-test {
            background: #ecf0f1;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 12px;
        }
        
        .instructions {
            background: #e8f4fd;
            border-left: 4px solid #3498db;
            padding: 15px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 全面诊断工具</h1>
        
        <div class="instructions">
            <h3>📋 诊断步骤</h3>
            <ol>
                <li><strong>检查云函数部署状态</strong> - 在微信开发者工具中确认</li>
                <li><strong>测试网络连通性</strong> - 验证域名解析和网络访问</li>
                <li><strong>检查HTTP触发器</strong> - 验证触发器配置</li>
                <li><strong>测试不同的API地址</strong> - 尝试多个可能的地址</li>
            </ol>
        </div>
        
        <div style="text-align: center; margin: 20px 0;">
            <button onclick="runFullDiagnosis()">开始全面诊断</button>
            <button onclick="testNetworkConnectivity()">测试网络连通性</button>
            <button onclick="testAlternativeURLs()">测试备用地址</button>
            <button onclick="clearLogs()">清空日志</button>
        </div>
    </div>

    <div class="container">
        <h2>🌐 网络连通性测试</h2>
        <div id="networkTests"></div>
    </div>

    <div class="container">
        <h2>☁️ 云函数测试</h2>
        <div id="cloudFunctionTests"></div>
    </div>

    <div class="container">
        <h2>🔄 备用地址测试</h2>
        <div id="alternativeTests"></div>
    </div>

    <div class="container">
        <h2>📝 诊断日志</h2>
        <div class="log" id="logContainer">
            等待开始诊断...
        </div>
    </div>

    <script>
        // 所有可能的API地址
        const API_ENDPOINTS = {
            primary: [
                'https://cloud1-4g85f8xlb8166ff1.tcb.qcloud.la/test',
                'https://cloud1-4g85f8xlb8166ff1.tcb.qcloud.la/admin-v2',
                'https://cloud1-4g85f8xlb8166ff1.tcb.qcloud.la/admin'
            ],
            alternative: [
                'https://cloud1-4g85f8xlb8166ff1.ap-beijing.app.tcloudbase.com/test',
                'https://cloud1-4g85f8xlb8166ff1.ap-beijing.app.tcloudbase.com/admin-v2',
                'https://cloud1-4g85f8xlb8166ff1.ap-beijing.app.tcloudbase.com/admin'
            ],
            direct: [
                'https://service-cloud1-4g85f8xlb8166ff1-1234567890.ap-beijing.apigateway.myqcloud.com/release/test',
                'https://service-cloud1-4g85f8xlb8166ff1-1234567890.ap-beijing.apigateway.myqcloud.com/release/admin-v2'
            ]
        }
        
        function log(message, type = 'info') {
            const logContainer = document.getElementById('logContainer')
            const timestamp = new Date().toLocaleTimeString()
            const prefix = type === 'error' ? '❌' : type === 'success' ? '✅' : type === 'warning' ? '⚠️' : 'ℹ️'
            logContainer.textContent += `[${timestamp}] ${prefix} ${message}\n`
            logContainer.scrollTop = logContainer.scrollHeight
        }
        
        function createTestSection(title, containerId) {
            const container = document.getElementById(containerId)
            const section = document.createElement('div')
            section.className = 'test-section'
            section.innerHTML = `
                <h3>${title}</h3>
                <div class="status checking">检查中...</div>
                <div class="details" style="margin-top: 10px;"></div>
            `
            container.appendChild(section)
            return section
        }
        
        async function testURL(url, method = 'GET', body = null) {
            try {
                const options = {
                    method,
                    headers: {
                        'Content-Type': 'application/json'
                    }
                }
                
                if (body) {
                    options.body = JSON.stringify(body)
                }
                
                const response = await fetch(url, options)
                
                return {
                    success: true,
                    status: response.status,
                    statusText: response.statusText,
                    headers: Object.fromEntries(response.headers.entries()),
                    url: url
                }
            } catch (error) {
                return {
                    success: false,
                    error: error.message,
                    url: url
                }
            }
        }
        
        async function testNetworkConnectivity() {
            log('🌐 开始网络连通性测试...')
            const container = document.getElementById('networkTests')
            container.innerHTML = ''
            
            // 测试基础域名解析
            const domains = [
                'https://cloud1-4g85f8xlb8166ff1.tcb.qcloud.la',
                'https://cloud1-4g85f8xlb8166ff1.ap-beijing.app.tcloudbase.com'
            ]
            
            for (const domain of domains) {
                const section = createTestSection(`域名连通性: ${domain}`, 'networkTests')
                const statusEl = section.querySelector('.status')
                const detailsEl = section.querySelector('.details')
                
                try {
                    const result = await testURL(domain, 'GET')
                    
                    if (result.success) {
                        statusEl.className = 'status success'
                        statusEl.textContent = `✅ 连通正常 (${result.status})`
                        detailsEl.innerHTML = `
                            <div style="color: #155724;">
                                状态码: ${result.status}<br>
                                响应时间: 正常<br>
                                域名解析: 成功
                            </div>
                        `
                        log(`域名 ${domain} 连通正常`)
                    } else {
                        statusEl.className = 'status warning'
                        statusEl.textContent = `⚠️ 连接异常`
                        detailsEl.innerHTML = `
                            <div style="color: #856404;">
                                错误: ${result.error}<br>
                                可能原因: 域名解析失败或网络问题
                            </div>
                        `
                        log(`域名 ${domain} 连接异常: ${result.error}`)
                    }
                } catch (error) {
                    statusEl.className = 'status error'
                    statusEl.textContent = '❌ 测试失败'
                    detailsEl.innerHTML = `<div style="color: #721c24;">测试过程出错: ${error.message}</div>`
                    log(`域名 ${domain} 测试失败: ${error.message}`)
                }
            }
        }
        
        async function testCloudFunctions() {
            log('☁️ 开始云函数测试...')
            const container = document.getElementById('cloudFunctionTests')
            container.innerHTML = ''
            
            for (const url of API_ENDPOINTS.primary) {
                const section = createTestSection(`云函数: ${url}`, 'cloudFunctionTests')
                const statusEl = section.querySelector('.status')
                const detailsEl = section.querySelector('.details')
                
                try {
                    // 测试OPTIONS请求
                    const optionsResult = await testURL(url, 'OPTIONS')
                    
                    // 测试POST请求
                    const postResult = await testURL(url, 'POST', {
                        action: 'healthCheck',
                        timestamp: Date.now()
                    })
                    
                    let statusHTML = ''
                    let detailsHTML = ''
                    
                    if (optionsResult.success) {
                        statusHTML += '✅ OPTIONS '
                        detailsHTML += `✅ CORS预检成功 (${optionsResult.status})<br>`
                    } else {
                        statusHTML += '❌ OPTIONS '
                        detailsHTML += `❌ CORS预检失败: ${optionsResult.error}<br>`
                    }
                    
                    if (postResult.success) {
                        statusHTML += '✅ POST'
                        detailsHTML += `✅ POST请求成功 (${postResult.status})<br>`
                        statusEl.className = 'status success'
                    } else {
                        statusHTML += '❌ POST'
                        detailsHTML += `❌ POST请求失败: ${postResult.error}<br>`
                        statusEl.className = 'status error'
                    }
                    
                    statusEl.textContent = statusHTML
                    detailsEl.innerHTML = `<div>${detailsHTML}</div>`
                    
                    log(`云函数 ${url}: ${statusHTML}`)
                    
                } catch (error) {
                    statusEl.className = 'status error'
                    statusEl.textContent = '❌ 测试失败'
                    detailsEl.innerHTML = `<div style="color: #721c24;">测试过程出错: ${error.message}</div>`
                    log(`云函数 ${url} 测试失败: ${error.message}`)
                }
            }
        }
        
        async function testAlternativeURLs() {
            log('🔄 开始备用地址测试...')
            const container = document.getElementById('alternativeTests')
            container.innerHTML = ''
            
            const allUrls = [...API_ENDPOINTS.alternative, ...API_ENDPOINTS.direct]
            
            for (const url of allUrls) {
                const section = createTestSection(`备用地址: ${url}`, 'alternativeTests')
                const statusEl = section.querySelector('.status')
                const detailsEl = section.querySelector('.details')
                
                try {
                    const result = await testURL(url, 'POST', {
                        action: 'healthCheck',
                        timestamp: Date.now()
                    })
                    
                    if (result.success) {
                        statusEl.className = 'status success'
                        statusEl.textContent = `✅ 连通成功 (${result.status})`
                        detailsEl.innerHTML = `
                            <div style="color: #155724;">
                                🎉 找到可用地址！<br>
                                状态码: ${result.status}<br>
                                建议使用此地址进行后续测试
                            </div>
                        `
                        log(`🎉 找到可用地址: ${url}`)
                    } else {
                        statusEl.className = 'status error'
                        statusEl.textContent = '❌ 连接失败'
                        detailsEl.innerHTML = `
                            <div style="color: #721c24;">
                                错误: ${result.error}
                            </div>
                        `
                        log(`备用地址 ${url} 连接失败: ${result.error}`)
                    }
                } catch (error) {
                    statusEl.className = 'status error'
                    statusEl.textContent = '❌ 测试失败'
                    detailsEl.innerHTML = `<div style="color: #721c24;">测试过程出错: ${error.message}</div>`
                    log(`备用地址 ${url} 测试失败: ${error.message}`)
                }
            }
        }
        
        async function runFullDiagnosis() {
            log('🔧 开始全面诊断...')
            
            await testNetworkConnectivity()
            await new Promise(resolve => setTimeout(resolve, 1000))
            
            await testCloudFunctions()
            await new Promise(resolve => setTimeout(resolve, 1000))
            
            await testAlternativeURLs()
            
            log('🎯 全面诊断完成！请查看上述结果。')
        }
        
        function clearLogs() {
            document.getElementById('logContainer').textContent = ''
        }
        
        // 页面加载时自动开始诊断
        window.addEventListener('load', () => {
            setTimeout(() => {
                log('🔧 全面诊断工具已加载')
                log('💡 建议先点击"开始全面诊断"按钮')
            }, 500)
        })
    </script>
</body>
</html>
