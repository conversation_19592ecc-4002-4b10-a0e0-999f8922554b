/**
 * 云数据连通配置 - 管理后台与小程序数据通信
 */

const config = {
  // 云环境配置
  env: 'cloud1-4g85f8xlb8166ff1',
  
  // 数据集合映射
  collections: {
    users: 'users',                    // 用户表
    students: 'students',              // 学生信息
    classes: 'classes',                // 班级信息
    records: 'records',                // 行为记录
    comments: 'comments',              // 评语数据
    settings: 'settings',              // 用户设置
    admins: 'admins',                  // 管理员
    ai_usage: 'ai_usage',              // AI使用统计
    logs: 'logs'                       // 操作日志
  },

  // API端点配置
  apiEndpoints: {
    // 管理后台API
    admin: {
      baseUrl: 'https://service-1j23b4-1251260924.gz.apigw.tencentcs.com/release/admin',
      timeout: 30000,
      retryAttempts: 3
    },
    
    // 小程序云函数API
    miniprogram: {
      // 云函数调用路径映射
      getUserInfo: 'getUserProfile',
      getStudents: 'getStudents',
      getClasses: 'getClasses',
      getRecords: 'getRecords',
      getComments: 'getComments',
      createRecord: 'createRecord',
      updateRecord: 'updateRecord',
      deleteRecord: 'deleteRecord',
      createStudent: 'addStudent',
      updateStudent: 'updateStudent'
    }
  },

  // 实时同步配置
  realTimeSync: {
    enabled: true,
    interval: 5000,               // 5秒同步间隔
    retryDelay: 1000,             // 重试延迟
    maxRetries: 3,
    batchSize: 50,                // 批量大小
    
    // 事件监听
    events: [
      'student.added',
      'student.updated',
      'student.deleted',
      'record.added',
      'record.updated',
      'record.deleted',
      'comment.generated',
      'comment.updated',
      'class.created',
      'class.updated'
    ]
  },

  // 数据验证规则
  validation: {
    student: {
      required: ['name', 'className'],
      validate: (data) => {
        return data.name && data.name.trim().length > 0 &&
               data.className && data.className.trim().length > 0;
      }
    },
    
    record: {
      required: ['studentId', 'content', 'type'],
      validate: (data) => {
        return data.studentId && data.content && 
               ['positive', 'negative', 'neutral'].includes(data.type);
      }
    },
    
    comment: {
      required: ['studentId', 'content'],
      validate: (data) => {
        return data.studentId && data.content && data.content.trim().length > 0;
      }
    }
  },

  // 字段映射配置
  fieldMapping: {
    // 小程序到管理后台的字段映射
    student: {
      _id: 'id',
      name: 'name',
      studentId: 'studentNumber',
      className: 'className',
      classId: 'classId',
      gender: 'gender',
      phone: 'phone',
      createTime: 'createdAt',
      updateTime: 'updatedAt',
      remark: 'notes'
    },
    
    record: {
      _id: 'id',
      studentId: 'studentId',
      studentName: 'studentName',
      classId: 'classId',
      className: 'className',
      type: 'type',
      category: 'category',
      content: 'content',
      images: 'evidence',
      tags: 'tags',
      score: 'score',
      createTime: 'createdAt',
      teacherId: 'teacherId'
    },
    
    comment: {
      _id: 'id',
      studentId: 'studentId',
      studentName: 'studentName',
      classId: 'classId',
      className: 'className',
      content: 'content',
      style: 'style',
      length: 'length',
      aiGenerated: 'aiGenerated',
      recordIds: 'relatedRecords',
      createTime: 'createdAt',
      teacherId: 'teacherId'
    }
  },

  // 安全策略
  security: {
    tokenExpiration: 3600000,     // token过期时间(1小时)
    maxConcurrentRequests: 100,   // 最大并发请求数
    rateLimit: {
      windowMs: 60000,            // 时间窗口
      max: 100,                   // 最大请求数
      skipSuccessful: true       // 跳过成功请求计数
    }
  },

  // 缓存策略
  cache: {
    enabled: true,
    ttl: 300000,                  // 5分钟TTL
    maxSize: 1000,               // 最大缓存数
    collections: ['students', 'classes']
  }
};

// 数据转换工具
const DataTransformer = {
  toAdminFormat: (data, type) => {
    if (!data || !type) return data;
    
    const mapping = config.fieldMapping[type];
    if (!mapping) return data;
    
    const result = {};
    Object.keys(mapping).forEach(sourceKey => {
      if (data[sourceKey] !== undefined) {
        result[mapping[sourceKey]] = data[sourceKey];
      }
    });
    
    return result;
  },
  
  toMiniprogramFormat: (data, type) => {
    if (!data || !type) return data;
    
    const mapping = config.fieldMapping[type];
    if (!mapping) return data;
    
    const result = {};
    Object.keys(mapping).forEach(sourceKey => {
      if (data[mapping[sourceKey]] !== undefined) {
        result[sourceKey] = data[mapping[sourceKey]];
      }
    });
    
    return result;
  }
};

// 连接状态监控
let connectionAlive = true;
let lastPingTime = 0;

const ConnectionMonitor = {
  ping: async () => {
    try {
      const result = await wx.cloud.callFunction({
        name: 'adminAPI',
        data: { action: 'healthCheck', source: 'miniprogram' }
      });
      
      if (result.result && result.result.code === 200) {
        connectionAlive = true;
        lastPingTime = Date.now();
        return true;
      }
      
      connectionAlive = false;
      return false;
    } catch (error) {
      console.error('连接检查失败:', error);
      connectionAlive = false;
      return false;
    }
  },
  
  isConnected: () => connectionAlive && (Date.now() - lastPingTime < 30000),
  
  getStatus: () => ({
    connected: ConnectionMonitor.isConnected(),
    lastPing: lastPingTime
  })
};

module.exports = {
  config,
  DataTransformer,
  ConnectionMonitor
};