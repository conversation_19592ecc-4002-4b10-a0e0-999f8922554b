@echo off
echo 开始调试启动过程...
echo.

echo 1. 检查当前目录...
cd /d "%~dp0"
echo 当前目录: %CD%
echo.

echo 2. 检查package.json是否存在...
if exist package.json (
    echo ✅ package.json存在
) else (
    echo ❌ package.json不存在
    pause
    exit
)

echo.
echo 3. 检查node_modules是否存在...
if exist node_modules (
    echo ✅ node_modules存在
) else (
    echo ❌ node_modules不存在，需要运行 npm install
    pause
    exit
)

echo.
echo 4. 尝试启动开发服务器...
echo 运行命令: npm run dev
echo.

npm run dev

echo.
echo 服务器已退出，按任意键继续...
pause