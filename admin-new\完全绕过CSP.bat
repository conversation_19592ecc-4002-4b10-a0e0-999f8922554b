@echo off
echo.
echo ==========================================
echo 💀 完全绕过CSP - 最后手段
echo ==========================================
echo.

echo 如果所有方法都失败了，使用这个方案：
echo.

echo 方案1：下载便携版Chrome
echo 1. 下载Chrome便携版（不受系统策略限制）
echo 2. 解压到临时目录
echo 3. 用命令行启动，禁用所有安全功能
echo.

echo 方案2：使用Firefox开发者版本
echo Firefox的CSP实现不同，可能可以绕过
echo.

echo 方案3：修改系统HOST文件
echo 127.0.0.1 localhost
echo 确保没有DNS劫持
echo.

echo 方案4：临时禁用杀毒软件
echo 某些杀毒软件会注入CSP策略
echo.

echo 现在尝试最激进的Chrome启动...
echo.

REM 完全清理Chrome
echo 清理Chrome进程和缓存...
taskkill /f /im chrome.exe >nul 2>&1
timeout /t 2 /nobreak >nul

REM 删除Chrome缓存（可选）
set /p clear_cache=是否清理Chrome缓存？[y/N]: 
if /i "%clear_cache%"=="y" (
    rmdir /s /q "%LOCALAPPDATA%\Google\Chrome\User Data\Default\Cache" >nul 2>&1
    echo Chrome缓存已清理
)

REM 创建全新的临时目录
set NEW_TEMP=%TEMP%\chrome-ultimate-%RANDOM%-%RANDOM%
mkdir "%NEW_TEMP%" >nul 2>&1

echo.
echo 启动最激进的Chrome配置...

start "Ultimate Chrome" chrome.exe ^
  --disable-web-security ^
  --disable-features=VizDisplayCompositor,TranslateUI ^
  --disable-site-isolation-trials ^
  --disable-extensions ^
  --disable-plugins ^
  --no-sandbox ^
  --no-zygote ^
  --single-process ^
  --allow-running-insecure-content ^
  --disable-background-timer-throttling ^
  --disable-backgrounding-occluded-windows ^
  --disable-renderer-backgrounding ^
  --disable-hang-monitor ^
  --disable-prompt-on-repost ^
  --no-first-run ^
  --no-default-browser-check ^
  --disable-sync ^
  --disable-translate ^
  --disable-logging ^
  --disable-gpu-sandbox ^
  --user-data-dir="%NEW_TEMP%" ^
  --ignore-certificate-errors ^
  --ignore-ssl-errors ^
  --ignore-certificate-errors-spki-list ^
  --allow-cross-origin-auth-prompt ^
  --disable-dev-shm-usage ^
  "http://localhost:8080"

echo.
echo ✅ 最激进的Chrome已启动！
echo 📍 临时目录: %NEW_TEMP%
echo.
echo 这是最后的解决方案了，如果还不行：
echo   1. 换一台电脑试试
echo   2. 检查是否有企业策略限制
echo   3. 联系网络管理员
echo.
pause