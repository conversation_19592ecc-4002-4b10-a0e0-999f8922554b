import React, { useState } from 'react'
import { Button, Input, Form, Typography, message } from 'antd'
import { useAuthStore } from './stores/authStore'

const { Title } = Typography

const SimpleLogin: React.FC = () => {
  const [loading, setLoading] = useState(false)
  const login = useAuthStore((state) => state.login)
  
  const handleLogin = async (values: { username: string; password: string }) => {
    setLoading(true)
    try {
      await login(values.username, values.password)
      message.success('登录成功！')
    } catch (error) {
      message.error('登录失败，请检查用户名和密码')
    } finally {
      setLoading(false)
    }
  }

  return (
    <div style={{ 
      display: 'flex', 
      justifyContent: 'center', 
      alignItems: 'center', 
      minHeight: '100vh',
      background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)'
    }}>
      <div style={{
        background: 'white',
        padding: '40px',
        borderRadius: '8px',
        boxShadow: '0 4px 20px rgba(0,0,0,0.1)',
        width: '400px'
      }}>
        <Title level={2} style={{ textAlign: 'center', marginBottom: '30px' }}>
          评语灵感君管理后台
        </Title>
        
        <Form onFinish={handleLogin} layout="vertical">
          <Form.Item
            label="用户名"
            name="username"
            rules={[{ required: true, message: '请输入用户名!' }]}
          >
            <Input placeholder="请输入用户名" size="large" />
          </Form.Item>
          
          <Form.Item
            label="密码"
            name="password"
            rules={[{ required: true, message: '请输入密码!' }]}
          >
            <Input.Password placeholder="请输入密码" size="large" />
          </Form.Item>
          
          <Form.Item>
            <Button 
              type="primary" 
              htmlType="submit" 
              loading={loading}
              size="large"
              style={{ width: '100%' }}
            >
              登录
            </Button>
          </Form.Item>
        </Form>
        
        <div style={{ textAlign: 'center', marginTop: '20px', color: '#666' }}>
          <p>测试账号：admin</p>
          <p>测试密码：admin123</p>
        </div>
      </div>
    </div>
  )
}

export default SimpleLogin