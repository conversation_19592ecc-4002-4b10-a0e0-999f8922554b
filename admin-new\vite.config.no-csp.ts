import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'
import path from 'path'

// 开发专用配置 - 无CSP限制
export default defineConfig({
  plugins: [
    react({
      fastRefresh: true,
      babel: {
        presets: [],
        plugins: []
      }
    })
  ],

  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src'),
      '@/components': path.resolve(__dirname, './src/components'),
      '@/utils': path.resolve(__dirname, './src/utils'),
      '@/hooks': path.resolve(__dirname, './src/hooks'),
      '@/services': path.resolve(__dirname, './src/services')
    }
  },

  // 开发服务器配置 - 完全无CSP限制
  server: {
    port: 8080,
    host: '0.0.0.0',
    strictPort: true,
    // 完全移除CSP头部
    headers: {},
    hmr: {
      port: 8080,
      overlay: true
    }
  },

  build: {
    outDir: 'dist',
    sourcemap: true,
    minify: false, // 开发时不压缩
    target: 'esnext'
  },

  esbuild: {
    target: 'esnext',
    format: 'esm',
    keepNames: true
  },

  define: {
    __DEV__: true,
    'process.env.NODE_ENV': JSON.stringify('development'),
    global: 'globalThis'
  },

  optimizeDeps: {
    include: [
      'react', 
      'react-dom', 
      'antd', 
      '@ant-design/icons',
      'dayjs', 
      'lodash-es',
      'axios',
      'react-router-dom'
    ],
    esbuildOptions: {
      target: 'esnext'
    }
  }
})