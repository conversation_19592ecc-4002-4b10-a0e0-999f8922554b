/**
 * 数据同步桥接云函数
 * 实现小程序与管理后台的免费数据连通
 * 
 * 核心功能：
 * 1. 统一数据访问接口
 * 2. 实时数据同步机制
 * 3. 权限验证和数据过滤
 * 4. 缓存管理和性能优化
 */

const cloud = require('wx-server-sdk')

// 初始化云开发
cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()
const _ = db.command

/**
 * 云函数入口函数
 */
exports.main = async (event, context) => {
  console.log('🌉 DataSyncBridge 收到请求:', event)
  
  try {
    const { OPENID } = cloud.getWXContext()
    const { action, source = 'unknown', data = {}, collection, filters = {} } = event
    
    if (!OPENID) {
      throw new Error('无法获取用户身份')
    }
    
    // 记录请求日志
    console.log(`📊 [${source}] ${action} 请求:`, { openid: OPENID, data })
    
    // 根据action分发处理
    let result
    switch (action) {
      case 'getData':
        result = await getData(collection || data.collection, filters, OPENID, source)
        break
      case 'updateData':
        result = await updateData(data, OPENID, source)
        break
      case 'getUpdates':
        result = await getUpdates(data.since, OPENID, source)
        break
      case 'getDashboardStats':
        result = await getDashboardStats(OPENID, source)
        break
      case 'getRealtimeData':
        result = await getRealtimeData(OPENID, source)
        break
      case 'syncNotify':
        result = await syncNotify(data, OPENID, source)
        break
      case 'healthCheck':
        result = await healthCheck()
        break
      default:
        throw new Error(`未知的操作: ${action}`)
    }
    
    // 统一返回格式
    return {
      success: true,
      data: result,
      timestamp: Date.now(),
      source,
      version: '1.0.0'
    }
    
  } catch (error) {
    console.error('❌ DataSyncBridge 处理失败:', error)
    return {
      success: false,
      error: error.message || '处理失败',
      timestamp: Date.now(),
      source: event.source || 'unknown'
    }
  }
}

/**
 * 获取数据 - 统一数据查询接口
 */
async function getData(collection, filters, openid, source) {
  try {
    console.log(`📖 获取数据: ${collection}`, { filters, source })
    
    // 权限验证
    await validateAccess(openid, 'read', collection, source)
    
    let query = db.collection(collection)
    
    // 根据用户权限过滤数据
    if (source === 'miniprogram') {
      // 小程序端只能访问自己的数据
      query = query.where({ teacherId: openid })
    } else if (source === 'admin') {
      // 管理后台可以访问所有数据，但需要管理员权限
      const isAdmin = await checkAdminPermission(openid)
      if (!isAdmin) {
        query = query.where({ teacherId: openid })
      }
    }
    
    // 应用过滤条件
    if (filters && Object.keys(filters).length > 0) {
      query = query.where(filters)
    }
    
    // 执行查询
    const result = await query.orderBy('updateTime', 'desc').limit(100).get()
    
    console.log(`✅ 数据查询成功: ${collection}, 返回 ${result.data.length} 条记录`)
    
    return {
      collection,
      items: result.data,
      total: result.data.length,
      hasMore: result.data.length === 100
    }
    
  } catch (error) {
    console.error(`❌ 获取数据失败: ${collection}`, error)
    throw error
  }
}

/**
 * 更新数据 - 统一数据更新接口
 */
async function updateData(data, openid, source) {
  try {
    const { collection, operation, docId, updateData } = data
    console.log(`✏️ 更新数据: ${collection}.${operation}`, { docId, source })
    
    // 权限验证
    await validateAccess(openid, 'write', collection, source)
    
    let result
    const currentTime = new Date()
    
    switch (operation) {
      case 'create':
        result = await db.collection(collection).add({
          data: {
            ...updateData,
            teacherId: openid,
            createTime: currentTime,
            updateTime: currentTime,
            createTimestamp: Date.now()
          }
        })
        break
        
      case 'update':
        result = await db.collection(collection).doc(docId).update({
          data: {
            ...updateData,
            updateTime: currentTime,
            updateTimestamp: Date.now()
          }
        })
        break
        
      case 'delete':
        result = await db.collection(collection).doc(docId).remove()
        break
        
      default:
        throw new Error(`未知的操作类型: ${operation}`)
    }
    
    // 记录同步事件
    await recordSyncEvent({
      collection,
      operation,
      docId: docId || result._id,
      source,
      openid,
      timestamp: Date.now()
    })
    
    console.log(`✅ 数据更新成功: ${collection}.${operation}`)
    
    return {
      operation,
      docId: docId || result._id,
      success: true
    }
    
  } catch (error) {
    console.error('❌ 更新数据失败:', error)
    throw error
  }
}

/**
 * 获取增量更新 - 实时同步核心
 */
async function getUpdates(since, openid, source) {
  try {
    console.log(`🔄 获取增量更新: since=${since}, source=${source}`)
    
    const sinceTime = new Date(since || Date.now() - 24 * 60 * 60 * 1000) // 默认24小时内
    
    // 查询同步事件记录
    const eventsResult = await db.collection('sync_events')
      .where({
        timestamp: _.gte(since || 0),
        openid: source === 'admin' ? _.exists(true) : openid // 管理后台可以看到所有事件
      })
      .orderBy('timestamp', 'desc')
      .limit(50)
      .get()
    
    const events = eventsResult.data
    const hasChanges = events.length > 0
    
    console.log(`📊 找到 ${events.length} 个更新事件`)
    
    return {
      hasChanges,
      changes: events,
      lastSyncTime: Date.now()
    }
    
  } catch (error) {
    console.error('❌ 获取增量更新失败:', error)
    throw error
  }
}

/**
 * 获取仪表板统计数据
 */
async function getDashboardStats(openid, source) {
  try {
    console.log(`📊 获取仪表板统计: source=${source}`)
    
    const isAdmin = await checkAdminPermission(openid)
    const userFilter = isAdmin ? {} : { teacherId: openid }
    
    // 并行查询各种统计数据
    const [usersResult, studentsResult, commentsResult, recordsResult] = await Promise.all([
      db.collection('users').where(userFilter).count(),
      db.collection('students').where(userFilter).count(),
      db.collection('comments').where(userFilter).count(),
      db.collection('records').where(userFilter).count()
    ])
    
    // 今日数据统计
    const today = new Date()
    today.setHours(0, 0, 0, 0)
    
    const todayCommentsResult = await db.collection('comments')
      .where({
        ...userFilter,
        createTime: _.gte(today)
      })
      .count()
    
    const stats = {
      totalUsers: usersResult.total,
      totalStudents: studentsResult.total,
      totalComments: commentsResult.total,
      totalRecords: recordsResult.total,
      todayComments: todayCommentsResult.total,
      lastUpdated: new Date().toISOString()
    }
    
    console.log('✅ 仪表板统计获取成功:', stats)
    return stats
    
  } catch (error) {
    console.error('❌ 获取仪表板统计失败:', error)
    throw error
  }
}

/**
 * 获取实时数据
 */
async function getRealtimeData(openid, source) {
  try {
    console.log(`⚡ 获取实时数据: source=${source}`)
    
    const isAdmin = await checkAdminPermission(openid)
    const userFilter = isAdmin ? {} : { teacherId: openid }
    
    // 获取最近的活动记录
    const recentActivities = await db.collection('sync_events')
      .where(userFilter)
      .orderBy('timestamp', 'desc')
      .limit(10)
      .get()
    
    // 获取在线用户数（简化实现）
    const activeUsers = await db.collection('users')
      .where({
        ...userFilter,
        lastLoginTime: _.gte(new Date(Date.now() - 30 * 60 * 1000)) // 30分钟内活跃
      })
      .count()
    
    return {
      recentActivities: recentActivities.data,
      activeUsers: activeUsers.total,
      serverTime: Date.now()
    }
    
  } catch (error) {
    console.error('❌ 获取实时数据失败:', error)
    throw error
  }
}

/**
 * 同步通知
 */
async function syncNotify(data, openid, source) {
  try {
    console.log(`📢 同步通知: source=${source}`, data)
    
    // 记录通知事件
    await recordSyncEvent({
      collection: 'notifications',
      operation: 'notify',
      source,
      openid,
      data,
      timestamp: Date.now()
    })
    
    return { notified: true }
    
  } catch (error) {
    console.error('❌ 同步通知失败:', error)
    throw error
  }
}

/**
 * 健康检查
 */
async function healthCheck() {
  try {
    // 测试数据库连接
    await db.collection('users').limit(1).get()
    
    return {
      status: 'healthy',
      timestamp: Date.now(),
      version: '1.0.0'
    }
  } catch (error) {
    throw new Error('健康检查失败: ' + error.message)
  }
}

/**
 * 权限验证
 */
async function validateAccess(openid, action, resource, source) {
  try {
    // 获取用户信息
    const userResult = await db.collection('users').doc(openid).get()
    
    if (!userResult.data) {
      throw new Error('用户不存在')
    }
    
    const user = userResult.data
    
    // 基本权限检查
    if (user.status !== 'active') {
      throw new Error('用户账号已被禁用')
    }
    
    // 根据来源和资源进行权限检查
    if (source === 'admin') {
      const isAdmin = await checkAdminPermission(openid)
      if (!isAdmin && action === 'write') {
        throw new Error('管理操作需要管理员权限')
      }
    }
    
    return true
    
  } catch (error) {
    console.error('❌ 权限验证失败:', error)
    throw error
  }
}

/**
 * 检查管理员权限
 */
async function checkAdminPermission(openid) {
  try {
    const adminResult = await db.collection('admins').doc(openid).get()
    return adminResult.data && adminResult.data.status === 'active'
  } catch (error) {
    return false
  }
}

/**
 * 记录同步事件
 */
async function recordSyncEvent(event) {
  try {
    await db.collection('sync_events').add({
      data: {
        ...event,
        id: `sync_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
      }
    })
  } catch (error) {
    console.warn('⚠️ 记录同步事件失败:', error)
    // 不抛出错误，避免影响主要功能
  }
}
