/* 学生编辑页面样式 - 紧凑布局 */
.student-create-page {
  /* 首页同款渐变背景 */
  background: linear-gradient(135deg, #F0F2F5 0%, #E4E7ED 100%);
  min-height: 100vh;
  padding: 24rpx 32rpx 60rpx 32rpx; /* 减少上下padding */
  display: flex;
  flex-direction: column;
}

/* 页面标题区域 - 紧凑设计 */
.page-header-section {
  margin-bottom: 24rpx; /* 减少底部间距 */
  padding-top: 10rpx; /* 减少顶部padding */
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.page-title {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.title-main {
  font-size: 56rpx;
  font-weight: 700;
  color: #2C3E50;
  margin-bottom: 12rpx;
  letter-spacing: 2rpx;
  text-shadow: 0 2rpx 8rpx rgba(84, 112, 198, 0.1);
}

.title-sub {
  font-size: 28rpx;
  color: #606266;
  font-weight: 500;
  letter-spacing: 1rpx;
  opacity: 0.9;
}

.header-icon {
  position: relative;
}

.icon-container {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  background: linear-gradient(135deg, #5470C6 0%, #73C0DE 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 8rpx 24rpx rgba(84, 112, 198, 0.3);
  position: relative;
  z-index: 2;
}

.icon-container::before {
  content: '';
  position: absolute;
  top: -8rpx;
  left: -8rpx;
  right: -8rpx;
  bottom: -8rpx;
  border-radius: 50%;
  background: linear-gradient(135deg, rgba(84, 112, 198, 0.1) 0%, rgba(115, 192, 222, 0.1) 100%);
  z-index: -1;
}

/* 单个添加模式的特殊布局 */
.single-mode {
  flex: 1;
  display: flex;
  flex-direction: column;
}

/* 模式切换区域 - 首页风格优化 */
.mode-switch-section {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(20rpx);
  border-radius: 24rpx;
  padding: 32rpx;
  margin-bottom: 32rpx;
  box-shadow: 0 12rpx 32rpx rgba(84, 112, 198, 0.1);
  border: 1rpx solid rgba(255, 255, 255, 0.2);
}

.mode-tabs {
  display: flex;
  background: #F5F5F5;
  border-radius: 12rpx;
  padding: 4rpx;
  position: relative;
}

.mode-tab {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 16rpx 24rpx;
  border-radius: 8rpx;
  transition: all 0.3s ease;
  position: relative;
  z-index: 1;
}

.mode-tab.active {
  background: linear-gradient(135deg, #4A90E2 0%, #5BA3F5 100%);
  box-shadow: 0 2rpx 8rpx rgba(74, 144, 226, 0.3);
}

.mode-tab .van-icon {
  color: #999999;
  transition: all 0.3s ease;
  margin-right: 8rpx;
}

.mode-tab.active .van-icon {
  color: #ffffff;
}

.tab-text {
  font-size: 28rpx;
  color: #666666;
  font-weight: 500;
  transition: all 0.3s ease;
}

.mode-tab.active .tab-text {
  color: #ffffff;
  font-weight: 600;
}

/* 通用区域样式 - 紧凑设计 */
.form-section,
.tips-section,
.import-guide,
.template-section,
.upload-section,
.preview-section {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20rpx);
  border-radius: 24rpx;
  margin-bottom: 20rpx; /* 减少底部间距 */
  padding: 24rpx; /* 减少内边距 */
  box-shadow: 0 12rpx 32rpx rgba(84, 112, 198, 0.08);
  border: 1rpx solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.form-section:hover,
.tips-section:hover,
.import-guide:hover,
.template-section:hover,
.upload-section:hover,
.preview-section:hover {
  box-shadow: 0 16rpx 40rpx rgba(84, 112, 198, 0.12);
  transform: translateY(-4rpx);
}

/* 单个添加模式特殊样式 */
.single-mode .form-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  margin-bottom: 120rpx; /* 增加底部间距，避免与按钮太接近 */
}

.section-title {
  font-size: 36rpx;
  font-weight: 700;
  color: #2C3E50;
  margin-bottom: 32rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  letter-spacing: 2rpx;
  text-shadow: 0 2rpx 8rpx rgba(84, 112, 198, 0.1);
}



/* 学生信息表单样式 - 按照设计图 */
.student-form-grid {
  display: flex;
  flex-direction: column;
  gap: 24rpx; /* 减少行间距 */
  margin-bottom: 20rpx; /* 减少底部间距 */
}

.form-row {
  display: flex;
  gap: 24rpx; /* 减少列间距 */
}

.form-field {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 12rpx; /* 减少字段内间距 */
}

/* 字段标签样式 */
.field-label {
  display: flex;
  align-items: center;
  margin-bottom: 12rpx;
}

.label-text {
  font-size: 28rpx;
  color: #333333;
  font-weight: 500;
  letter-spacing: 0.5rpx;
}

.required {
  color: #FF4D4F;
  margin-left: 6rpx;
  font-size: 28rpx;
  font-weight: 600;
}

/* 输入框容器 */
.field-input {
  position: relative;
}

/* 输入框样式 - 圆角设计 */
.input-box {
  width: 100%;
  height: 80rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
  color: #333333;
  background: #ffffff;
  border: 2rpx solid #E5E5E5;
  border-radius: 20rpx;
  box-sizing: border-box;
  transition: all 0.3s ease;
}

.input-box:focus {
  border-color: #4A90E2;
  outline: none;
}

.input-box::placeholder {
  color: #CCCCCC;
}

/* 输入框样式 - 首页卡片风格 */
.form-input {
  background: linear-gradient(135deg, #F8F9FA 0%, #FFFFFF 100%) !important;
  border-radius: 20rpx !important;
  padding: 24rpx !important;
  font-size: 30rpx !important;
  border: 2rpx solid rgba(84, 112, 198, 0.1) !important;
  box-shadow: 0 4rpx 12rpx rgba(84, 112, 198, 0.05) !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
}

/* 确保van-field内部输入框样式正确 */
.form-input .van-field__input {
  background: transparent !important;
  border: none !important;
  font-size: 30rpx !important;
  color: #2C3E50 !important;
}

.form-input:focus {
  border-color: #5470C6 !important;
  background: linear-gradient(135deg, #FFFFFF 0%, #F8F9FA 100%) !important;
  box-shadow: 0 8rpx 24rpx rgba(84, 112, 198, 0.15) !important;
  transform: translateY(-2rpx) !important;
}

/* 班级选择器 - 首页按钮风格 */
.class-selector {
  background: linear-gradient(135deg, #F8F9FA 0%, #FFFFFF 100%);
  border-radius: 20rpx;
  padding: 24rpx;
  font-size: 30rpx;
  border: 2rpx solid rgba(84, 112, 198, 0.1);
  display: flex;
  align-items: center;
  justify-content: space-between;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 4rpx 12rpx rgba(84, 112, 198, 0.05);
}

.class-selector:active {
  background: linear-gradient(135deg, #5470C6 0%, #73C0DE 100%);
  transform: translateY(2rpx) scale(0.98);
  box-shadow: 0 2rpx 8rpx rgba(84, 112, 198, 0.2);
}

.class-selector:active .class-text {
  color: #ffffff;
}

.class-selector:active .van-icon {
  color: #ffffff !important;
}

.class-text {
  color: #2C3E50;
  flex: 1;
  font-weight: 500;
  letter-spacing: 1rpx;
}

.class-text.placeholder {
  color: #909399;
  opacity: 0.8;
}

/* 性别选择框样式 - 圆角设计，与输入框保持一致 */
.select-box {
  width: 100%;
  height: 80rpx;
  padding: 0 20rpx;
  background: #ffffff;
  border: 2rpx solid #E5E5E5;
  border-radius: 20rpx;
  box-sizing: border-box;
  display: flex;
  align-items: center; /* 确保内容和图标都垂直居中 */
  justify-content: space-between;
  transition: all 0.3s ease;
  cursor: pointer;
}

.select-box:active {
  border-color: #4A90E2;
  background: #F8F9FA;
}

/* 文字内容容器 */
.select-content {
  flex: 1;
  display: flex;
  align-items: center; /* 确保文字垂直居中 */
  height: 100%;
}

.select-text {
  color: #333333;
  font-size: 28rpx;
  line-height: 1;
  transform: translateY(-14rpx); /* 向上移动14rpx，继续增加向上距离 */
}

.select-text.placeholder {
  color: #CCCCCC;
}

/* 图标容器 */
.select-icon {
  display: flex;
  align-items: center; /* 确保图标垂直居中 */
  justify-content: center;
  height: 100%;
  width: 32rpx; /* 给图标固定宽度 */
}

.error-message {
  color: #EE6666;
  font-size: 26rpx;
  margin-top: 12rpx;
  font-weight: 500;
}

/* 文本域样式 - 首页风格 */
.form-textarea {
  background: linear-gradient(135deg, #F8F9FA 0%, #FFFFFF 100%) !important;
  border-radius: 20rpx !important;
  padding: 24rpx !important;
  font-size: 30rpx !important;
  min-height: 120rpx !important;
  border: 2rpx solid rgba(84, 112, 198, 0.1) !important;
  box-shadow: 0 4rpx 12rpx rgba(84, 112, 198, 0.05) !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
}

.form-textarea:focus {
  border-color: #5470C6 !important;
  background: linear-gradient(135deg, #FFFFFF 0%, #F8F9FA 100%) !important;
  box-shadow: 0 8rpx 24rpx rgba(84, 112, 198, 0.15) !important;
  transform: translateY(-2rpx) !important;
}

/* 单选按钮容器 */
.radio-container {
  display: flex;
  gap: 40rpx;
  margin-top: 16rpx;
}

.radio-item {
  font-size: 30rpx !important;
  color: #2C3E50 !important;
  font-weight: 500 !important;
}

/* 温馨提示样式 - 首页信息卡片风格 */
.tips-section {
  margin-top: 24rpx;
  background: linear-gradient(135deg, rgba(84, 112, 198, 0.03) 0%, rgba(115, 192, 222, 0.03) 100%);
  border: 2rpx solid rgba(84, 112, 198, 0.1);
  position: relative;
  overflow: hidden;
}

.tips-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 8rpx;
  height: 100%;
  background: linear-gradient(135deg, #5470C6 0%, #73C0DE 100%);
}

.tips-title {
  display: flex;
  align-items: center;
  gap: 12rpx;
  margin-bottom: 24rpx;
}

.tips-title-text {
  font-size: 32rpx;
  font-weight: 700;
  color: #5470C6;
  letter-spacing: 1rpx;
}

.tips-content {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.tip-item {
  display: flex;
  align-items: flex-start;
}

.tip-text {
  font-size: 28rpx;
  color: #606266;
  line-height: 1.6;
  font-weight: 400;
  letter-spacing: 0.5rpx;
}

/* 批量导入样式 */
.import-guide {
  border-left: 4rpx solid #4080FF;
}

.guide-title {
  display: flex;
  align-items: center;
  gap: 12rpx;
  margin-bottom: 16rpx;
}

.title-text {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
}

.guide-content {
  padding-left: 24rpx; /* 减少左边距 */
}

.guide-item {
  font-size: 24rpx; /* 减少字体大小 */
  color: #666;
  line-height: 1.4; /* 减少行高 */
  margin-bottom: 4rpx; /* 减少底部间距 */
  position: relative;
}

.guide-item::before {
  content: '•';
  color: #4080FF;
  position: absolute;
  left: -16rpx;
}



/* 单个模板下载卡片 */
.single-template-card {
  display: flex;
  align-items: center;
  gap: 16rpx;
  padding: 20rpx;
  background: #f8f9fa;
  border-radius: 16rpx;
  border: 2rpx solid transparent;
  transition: all 0.3s ease;
  cursor: pointer;
}

.single-template-card:active {
  border-color: #4080FF;
  background: rgba(64, 128, 255, 0.05);
  transform: scale(0.98);
}

.template-icon {
  width: 64rpx;
  height: 64rpx;
  border-radius: 16rpx;
  background: rgba(64, 128, 255, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
}

.template-info {
  flex: 1;
}

.template-name {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 4rpx;
}

.template-desc {
  font-size: 24rpx;
  color: #666;
}

.download-btn {
  width: 48rpx;
  height: 48rpx;
  border-radius: 50%;
  background: rgba(64, 128, 255, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 文件上传区域 - 紧凑设计 */
.upload-area {
  border: 2rpx dashed #ddd;
  border-radius: 16rpx;
  padding: 32rpx 20rpx; /* 减少上下padding */
  text-align: center;
  transition: all 0.3s ease;
}

.upload-area:active {
  border-color: #4080FF;
  background: rgba(64, 128, 255, 0.02);
}

.upload-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12rpx;
}

.uploaded-file {
  display: flex;
  align-items: center;
  gap: 16rpx;
  padding: 20rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
}

.file-icon {
  width: 56rpx;
  height: 56rpx;
  border-radius: 12rpx;
  background: rgba(64, 128, 255, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
}

.file-info {
  flex: 1;
}

.file-name {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 4rpx;
}

.file-size {
  font-size: 24rpx;
  color: #666;
}

.file-actions {
  display: flex;
  gap: 12rpx;
}

.action-btn {
  width: 48rpx;
  height: 48rpx;
  border-radius: 50%;
  background: white;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

/* 预览表格 */
.preview-count {
  font-size: 24rpx;
  color: #4080FF;
  font-weight: 500;
}

/* 预览表格样式优化 */
.preview-table,
.preview-table-full {
  border: 2rpx solid rgba(84, 112, 198, 0.1);
  border-radius: 16rpx;
  overflow: hidden;
  background: white;
  box-shadow: 0 4rpx 12rpx rgba(84, 112, 198, 0.05);
}

.table-header {
  display: flex;
  background: linear-gradient(135deg, #5470C6 0%, #73C0DE 100%);
  font-weight: 600;
  color: white;
  font-size: 26rpx;
}

.table-body {
  max-height: 400rpx;
  background: white;
}

.table-row {
  display: flex;
  border-bottom: 1rpx solid rgba(84, 112, 198, 0.08);
  transition: all 0.2s ease;
}

.table-row:hover {
  background: rgba(84, 112, 198, 0.02);
}

.table-row.error {
  background: rgba(238, 102, 102, 0.05);
  border-left: 4rpx solid #EE6666;
}

.table-cell {
  flex: 1;
  padding: 12rpx 12rpx; /* 减少padding */
  font-size: 24rpx; /* 减少字体大小 */
  color: #2C3E50;
  text-align: center;
  border-right: 1rpx solid rgba(84, 112, 198, 0.08);
  line-height: 1.3; /* 减少行高 */
}

.table-header .table-cell {
  color: white;
  font-weight: 600;
  padding: 16rpx 12rpx; /* 减少表头padding */
}

.table-cell:last-child {
  border-right: none;
}

.error-summary {
  display: flex;
  align-items: center;
  gap: 12rpx;
  margin-top: 24rpx;
  padding: 20rpx 24rpx;
  background: linear-gradient(135deg, rgba(238, 102, 102, 0.05) 0%, rgba(238, 102, 102, 0.02) 100%);
  border-radius: 16rpx;
  border: 2rpx solid rgba(238, 102, 102, 0.1);
  border-left: 6rpx solid #EE6666;
}

.error-text {
  font-size: 28rpx;
  color: #EE6666;
  font-weight: 500;
}

/* 底部操作栏 - 紧凑布局 */
.bottom-actions {
  display: flex;
  gap: 16rpx;
  padding: 16rpx 0; /* 减少padding */
  margin-top: 20rpx; /* 减少顶部间距 */
}

/* 按钮基础样式 - 圆角设计 */
.btn {
  height: 88rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 44rpx;
  transition: all 0.3s ease;
  border: none;
  font-size: 32rpx;
  font-weight: 600;
  letter-spacing: 1rpx;
}

/* 取消按钮 - 灰色样式 */
.btn-cancel {
  flex: 1;
  background: #F5F5F5;
  color: #666666;
  border: 2rpx solid #E5E5E5;
  margin-right: 16rpx;
}

.btn-cancel:active {
  background: #EEEEEE;
}

/* 保存按钮 - 蓝色渐变 */
.btn-save {
  flex: 1;
  background: linear-gradient(135deg, #4A90E2 0%, #5BA3F5 100%);
  color: white;
  box-shadow: 0 4rpx 12rpx rgba(74, 144, 226, 0.3);
}

.btn-save:active {
  transform: translateY(1rpx);
  box-shadow: 0 2rpx 8rpx rgba(74, 144, 226, 0.4);
}

.btn-save.disabled {
  background: #CCCCCC;
  color: #999999;
  box-shadow: none;
}

/* 弹窗样式 */
.file-preview-popup {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.popup-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.popup-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.preview-content {
  flex: 1;
  padding: 24rpx 32rpx;
  overflow: hidden;
}

.preview-stats {
  display: flex;
  justify-content: space-around;
  margin-bottom: 24rpx;
  padding: 24rpx;
  background: #f8f9fa;
  border-radius: 16rpx;
}

.stat-item {
  text-align: center;
}

.stat-number {
  font-size: 36rpx;
  font-weight: 700;
  color: #4080FF;
  margin-bottom: 8rpx;
}

.stat-label {
  font-size: 24rpx;
  color: #666;
}

.preview-table-full {
  height: calc(100% - 120rpx);
  display: flex;
  flex-direction: column;
}

.preview-table-full .table-body {
  flex: 1;
  overflow-y: auto;
}