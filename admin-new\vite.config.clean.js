import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'
import path from 'path'

// 简洁统一的Vite配置 - 解决所有混乱
export default defineConfig({
  plugins: [
    react({
      fastRefresh: true
    })
  ],

  resolve: {
    alias: {
      '@': path.resolve(process.cwd(), './src')
    }
  },

  // 统一使用8081端口，无CSP限制
  server: {
    port: 8081,
    host: true,
    strictPort: false,
    headers: {},
    cors: true
  },

  build: {
    outDir: 'dist',
    sourcemap: true
  },

  define: {
    'process.env.NODE_ENV': JSON.stringify(process.env.NODE_ENV || 'development')
  }
})