# 手动部署AI调试云函数指南

由于tcb命令不可用，请按以下步骤部署调试版本的callDoubaoAPI云函数：

## 方法1：使用微信开发者工具部署

1. 打开微信开发者工具
2. 打开项目：评语灵感君
3. 在左侧项目目录中找到 `cloudfunctions/callDoubaoAPI`
4. 右键点击 `callDoubaoAPI` 文件夹
5. 选择"创建并部署：云端安装依赖（不上传node_modules）"
6. 等待部署完成

## 方法2：通过腾讯云控制台部署

1. 访问 [腾讯云云开发控制台](https://console.cloud.tencent.com/tcb)
2. 选择你的环境
3. 进入"云函数"页面
4. 找到 callDoubaoAPI 函数
5. 点击"编辑代码"
6. 将 `cloudfunctions/callDoubaoAPI/index.js` 的内容复制粘贴替换
7. 点击"保存并部署"

## 测试步骤

部署完成后：
1. 在小程序中触发AI评语生成功能
2. 查看云函数日志，寻找调试信息：
   - 🔍 system_config总数量
   - 🔍 配置详情
   - 🔍 AI配置查询结果
   - 🔍 找到的AI配置详情

## 预期看到的调试信息

正常情况下应该看到：
```
🔍 调试：查看system_config集合所有数据
🔍 system_config总数量: X
🔍 配置1: { _id: xxx, type: xxx, status: xxx, hasApiKey: true/false, ... }
🔍 AI配置查询结果数量: 1
🔍 找到的AI配置详情: { hasApiKey: true, apiKeyLength: XX, ... }
```

如果看到 `hasApiKey: false` 或 `AI配置查询结果数量: 0`，说明数据库配置有问题。