/* 自定义导航栏 - 渐变紫色高端设计 */
.custom-navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  width: 100%;
  background: linear-gradient(135deg, #712BFF 0%, #9B59B6 50%, #AE8BFF 100%);
  box-shadow: 0 4rpx 20rpx rgba(113, 43, 255, 0.3);
  z-index: 1000;
  backdrop-filter: blur(20rpx);
}

.navbar-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 32rpx;
  height: 88rpx;
  box-sizing: border-box;
}

/* 左侧返回按钮 */
.navbar-left {
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 48rpx;
  height: 48rpx;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  transition: all 0.3s ease;
}

.navbar-left:active {
  transform: scale(0.95);
  background: rgba(255, 255, 255, 0.2);
}

/* 品牌区域 */
.navbar-brand {
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.brand-icon {
  width: 40rpx;
  height: 40rpx;
  border-radius: 8rpx;
}

.brand-name {
  font-size: 34rpx;
  font-weight: 700;
  color: #FFFFFF;
  letter-spacing: 1rpx;
  text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.2);
}

/* 标题区域 */
.navbar-title {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
}

.title-text {
  font-size: 36rpx;
  font-weight: 600;
  color: #FFFFFF;
  letter-spacing: 0.5rpx;
}

/* 右侧操作区域 */
.navbar-right {
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 48rpx;
  height: 48rpx;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  transition: all 0.3s ease;
}

.navbar-right:active {
  transform: scale(0.95);
  background: rgba(255, 255, 255, 0.2);
}

.navbar-right-slot {
  flex-shrink: 0;
  display: flex;
  align-items: center;
}

/* 底部渐变装饰 */
.navbar-decoration {
  height: 2rpx;
  background: linear-gradient(90deg, transparent 0%, rgba(255, 255, 255, 0.3) 50%, transparent 100%);
}

/* 状态栏适配 */
.status-bar-placeholder {
  width: 100%;
  background: transparent;
}

/* 动画效果 */
.navbar-brand .brand-icon {
  animation: float 2s ease-in-out infinite;
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-2rpx);
  }
}

/* 响应式调整 */
@media (max-width: 750rpx) {
  .navbar-content {
    padding: 0 24rpx;
  }
  
  .brand-name {
    font-size: 32rpx;
  }
  
  .title-text {
    font-size: 34rpx;
  }
}