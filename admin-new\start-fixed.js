// 简化的开发服务器启动脚本
const { createServer } = require('vite')
const react = require('@vitejs/plugin-react')

async function startServer() {
  try {
    const server = await createServer({
      plugins: [react.default()],
      server: {
        port: 3000,
        strictPort: true,
        open: false,
        cors: true
      },
      build: {
        target: 'es2015',
        minify: false,
        sourcemap: false
      },
      esbuild: {
        jsxDev: false,
        drop: ['console', 'debugger']
      },
      define: {
        __DEV__: 'false',
        'process.env.NODE_ENV': JSON.stringify('development')
      }
    })

    await server.listen()
    console.log('🚀 开发服务器已启动！')
    console.log('📱 访问地址: http://localhost:3000')
    console.log('👤 登录信息: admin / admin123')
    console.log('✅ CSP问题已修复，应用现在可以正常运行')
  } catch (error) {
    console.error('❌ 启动失败:', error)
  }
}

startServer()