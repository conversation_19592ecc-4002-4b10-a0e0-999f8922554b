/**
 * 紧急分享功能 - 最简化的分享实现
 * 用于在智能分享模块加载失败时的降级方案
 */

/**
 * 紧急文本分享
 */
function emergencyTextShare(content) {
  try {
    const shareText = `🤖 AI评语助手分享\n\n${content.title || '我的教学成果'}\n\n💡 3分钟生成专业评语，推荐给所有老师试试！`;
    
    wx.setClipboardData({
      data: shareText,
      success: () => {
        wx.showModal({
          title: '📋 内容已复制',
          content: '分享内容已复制到剪贴板，您可以粘贴到微信群或朋友圈分享！',
          showCancel: false,
          confirmText: '知道了'
        });
      },
      fail: () => {
        wx.showToast({
          title: '复制失败',
          icon: 'none'
        });
      }
    });
  } catch (error) {
    console.error('[紧急分享] 文本分享失败:', error);
    wx.showToast({
      title: '分享功能暂不可用',
      icon: 'none'
    });
  }
}

/**
 * 紧急Excel分享
 */
function emergencyExcelShare(data) {
  try {
    // 生成简单的CSV内容
    let csvContent = '\uFEFF'; // BOM
    csvContent += '学生姓名,班级,评语内容,评分,创建时间\n';
    
    if (Array.isArray(data)) {
      data.forEach(item => {
        csvContent += `"${item.studentName || ''}","${item.className || ''}","${item.content || ''}","${item.score || ''}","${item.createTime || ''}"\n`;
      });
    }
    
    wx.setClipboardData({
      data: csvContent,
      success: () => {
        wx.showModal({
          title: '📊 数据已复制',
          content: 'CSV格式的数据已复制到剪贴板！\n\n使用方法：\n1. 打开Excel或WPS\n2. 新建工作表\n3. 粘贴数据\n4. 保存文件',
          showCancel: false,
          confirmText: '知道了'
        });
      },
      fail: () => {
        wx.showToast({
          title: '复制失败',
          icon: 'none'
        });
      }
    });
  } catch (error) {
    console.error('[紧急分享] Excel分享失败:', error);
    wx.showToast({
      title: '导出功能暂不可用',
      icon: 'none'
    });
  }
}

/**
 * 紧急自动分享 - 根据内容类型自动选择
 */
function emergencyAutoShare(content) {
  try {
    if (Array.isArray(content) || (content && content.data && Array.isArray(content.data))) {
      // 数据导出类型
      emergencyExcelShare(content.data || content);
    } else {
      // 文本分享类型
      emergencyTextShare(content);
    }
  } catch (error) {
    console.error('[紧急分享] 自动分享失败:', error);
    wx.showToast({
      title: '分享功能故障',
      icon: 'none'
    });
  }
}

module.exports = {
  emergencyTextShare,
  emergencyExcelShare,
  emergencyAutoShare
};