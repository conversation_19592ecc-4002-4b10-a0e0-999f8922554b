/**
 * Excel/CSV 分享通用工具
 * 基于学生管理的分享功能，提供统一的Excel文件分享能力
 */

const ExcelShareUtils = {
  
  /**
   * 通用Excel分享功能
   * @param {Object} options 配置选项
   * @param {Array} options.data 要导出的数据数组
   * @param {Array} options.headers 表头数组
   * @param {string} options.fileName 文件名前缀
   * @param {string} options.title 分享标题
   * @param {string} options.description 分享描述
   */
  async shareExcelFile(options) {
    const {
      data = [],
      headers = [],
      fileName = '数据导出',
      title = '分享Excel文件',
      description = '生成Excel文件并分享到微信'
    } = options;

    if (!data.length || !headers.length) {
      wx.showToast({
        title: '没有可导出的数据',
        icon: 'none'
      });
      return;
    }

    wx.showLoading({
      title: '生成Excel文件...',
      mask: true
    });

    try {
      // 1. 生成CSV内容
      const csvContent = this.createCSVContent(data, headers);
      const fullFileName = this.generateFileName(fileName);
      
      // 2. 创建文件
      const fs = wx.getFileSystemManager();
      const filePath = `${wx.env.USER_DATA_PATH}/${fullFileName}`;
      
      // 添加BOM以支持中文
      const bomContent = '\uFEFF' + csvContent;
      fs.writeFileSync(filePath, bomContent, 'utf8');

      wx.hideLoading();

      // 3. 显示分享确认
      wx.showModal({
        title: title,
        content: `${description}\n\n文件名：${fullFileName}\n记录数：${data.length}条\n\n是否立即分享到微信？`,
        confirmText: '立即分享',
        cancelText: '稍后处理',
        success: (res) => {
          if (res.confirm) {
            this.shareToWeChat(filePath, fullFileName, title);
          } else {
            wx.showToast({
              title: '文件已生成',
              icon: 'success'
            });
          }
        }
      });

    } catch (error) {
      wx.hideLoading();
      console.error('生成Excel文件失败:', error);
      wx.showModal({
        title: '生成失败',
        content: '生成Excel文件时出现错误，请重试。',
        showCancel: false,
        confirmText: '我知道了'
      });
    }
  },

  /**
   * 创建CSV内容
   * @param {Array} data 数据数组
   * @param {Array} headers 表头数组
   */
  createCSVContent(data, headers) {
    const rows = [headers];
    
    data.forEach(item => {
      const row = headers.map(header => {
        let value = '';
        
        // 支持嵌套属性访问，如 'student.name'
        if (header.includes('.')) {
          const keys = header.split('.');
          value = keys.reduce((obj, key) => obj?.[key] || '', item);
        } else {
          value = item[header] || '';
        }
        
        // 处理特殊字符
        return String(value).replace(/"/g, '""');
      });
      rows.push(row);
    });

    // 转换为CSV格式
    return rows.map(row => 
      row.map(cell => `"${cell}"`).join(',')
    ).join('\n');
  },

  /**
   * 生成文件名
   * @param {string} prefix 文件名前缀
   */
  generateFileName(prefix) {
    const now = new Date();
    const dateStr = now.toISOString().slice(0, 10);
    const timeStr = now.toTimeString().slice(0, 5).replace(':', '');
    return `${prefix}_${dateStr}_${timeStr}.csv`;
  },

  /**
   * 分享文件到微信
   * @param {string} filePath 文件路径
   * @param {string} fileName 文件名
   * @param {string} title 分享标题
   */
  shareToWeChat(filePath, fileName, title) {
    // 检查分享功能是否可用
    if (typeof wx.shareFileMessage !== 'function') {
      wx.showModal({
        title: '功能不可用',
        content: '当前环境不支持文件分享\n请在真机微信中使用',
        showCancel: false,
        confirmText: '知道了'
      });
      return;
    }

    // 显示分享指引
    wx.showModal({
      title: `分享${title}`,
      content: `文件：${fileName}\n\n操作步骤：\n1. 选择微信聊天发送文件\n2. 在微信中下载并打开\n3. 用Excel查看或编辑数据\n\n建议发送到"文件传输助手"`,
      confirmText: '开始分享',
      cancelText: '取消',
      success: (res) => {
        if (res.confirm) {
          this.performFileShare(filePath, fileName);
        }
      }
    });
  },

  /**
   * 执行文件分享
   * @param {string} filePath 文件路径
   * @param {string} fileName 文件名
   */
  performFileShare(filePath, fileName) {
    wx.shareFileMessage({
      filePath: filePath,
      fileName: fileName,
      success: () => {
        wx.showModal({
          title: '分享成功',
          content: `文件已发送到微信聊天\n\n您可以在微信中：\n• 下载查看数据\n• 用Excel编辑文件\n• 转发给其他人`,
          showCancel: false,
          confirmText: '知道了'
        });
      },
      fail: (err) => {
        console.error('分享失败:', err);
        wx.showModal({
          title: '分享失败',
          content: '无法分享文件到微信\n\n可能原因：\n• 微信版本过低\n• 系统权限限制\n• 文件路径异常\n\n建议更新微信版本',
          showCancel: false,
          confirmText: '我知道了'
        });
      }
    });
  },

  /**
   * 快速分享评语列表
   * @param {Array} comments 评语数据数组
   */
  async shareComments(comments) {
    const headers = ['学生姓名', '班级', '科目', '评语内容', '创建时间'];
    const data = comments.map(comment => ({
      '学生姓名': comment.studentName || '',
      '班级': comment.className || '',
      '科目': comment.subject || '',
      '评语内容': comment.content || '',
      '创建时间': comment.createTime || ''
    }));

    return this.shareExcelFile({
      data,
      headers,
      fileName: '评语记录',
      title: '分享评语记录',
      description: '将评语记录导出为Excel文件'
    });
  },

  /**
   * 快速分享学生列表
   * @param {Array} students 学生数据数组
   */
  async shareStudents(students) {
    const headers = ['姓名', '学号', '班级', '性别'];
    const data = students.map(student => ({
      '姓名': student.name || '',
      '学号': student.studentId || '',
      '班级': student.className || '',
      '性别': student.genderText || student.gender || ''
    }));

    return this.shareExcelFile({
      data,
      headers,
      fileName: '学生名单',
      title: '分享学生名单',
      description: '将学生名单导出为Excel文件'
    });
  },

  /**
   * 快速分享使用记录
   * @param {Array} records 记录数据数组
   */
  async shareRecords(records) {
    const headers = ['日期', '操作类型', '目标', '详情'];
    const data = records.map(record => ({
      '日期': record.createTime || record.date || '',
      '操作类型': record.type || record.action || '',
      '目标': record.target || record.studentName || '',
      '详情': record.description || record.detail || ''
    }));

    return this.shareExcelFile({
      data,
      headers,
      fileName: '使用记录',
      title: '分享使用记录',
      description: '将使用记录导出为Excel文件'
    });
  }
};

module.exports = ExcelShareUtils;