# 🚀 快速开始指南

## 📋 立即开始

现在所有配置文件都已经创建完成，按照以下步骤即可启动项目：

### 1. 安装依赖
```bash
npm install
```

### 2. 检查环境配置
```bash
# 验证环境变量配置是否正确
node scripts/validate-env.js
```

### 3. 启动开发服务器
```bash
npm run dev
```

浏览器会自动打开 http://localhost:3000

## ⚠️ 重要配置说明

### 当前配置状态
- ✅ `.env.local` 已创建
- ✅ 使用你现有的云开发环境ID: `cloud1-4g85f8xlb8166ff1`
- ✅ API地址已配置: `https://cloud1-4g85f8xlb8166ff1.ap-beijing.app.tcloudbase.com/adminAPI`

### 需要确认的配置

1. **云函数地址**：
   - 当前配置: `https://cloud1-4g85f8xlb8166ff1.ap-beijing.app.tcloudbase.com/adminAPI`
   - 请确认这个地址是否正确对应你的`adminAPI`云函数

2. **环境ID**：
   - 当前配置: `cloud1-4g85f8xlb8166ff1`
   - 这应该和你小程序项目中的环境ID一致

## 🔧 如果需要修改配置

编辑 `.env.local` 文件：

```bash
# 修改API地址（如果不正确）
VITE_API_BASE_URL=https://你的环境ID.ap-beijing.app.tcloudbase.com/adminAPI

# 修改环境ID（如果不正确）
VITE_WECHAT_CLOUD_ENV_ID=你的环境ID
```

## 🐛 常见问题

### Q: 启动时提示环境变量错误？
A: 运行 `node scripts/validate-env.js` 查看具体错误信息

### Q: API连接失败？
A: 确认云函数`adminAPI`已部署且可访问，检查控制台网络错误信息

### Q: 端口被占用？
A: 修改 `.env.local` 中的 `VITE_DEV_SERVER_PORT=3001`

## 📱 Mock数据模式

如果暂时无法连接云函数，可以启用Mock模式：

1. 编辑 `.env.local`：
```bash
VITE_ENABLE_MOCK=true
```

2. 重启开发服务器：
```bash
npm run dev
```

这样就可以使用模拟数据进行开发了。

## 🎯 下一步

项目启动成功后，你可以：

1. 访问登录页面进行登录
2. 查看仪表板数据
3. 配置AI服务商
4. 编辑评语模板
5. 监控使用情况

默认登录账号（如果启用Mock模式）：
- 用户名: admin
- 密码: admin123

开始享受现代化的管理后台开发体验吧！🎉