/**
 * 云函数代理服务器
 * 为管理后台提供HTTP接口来调用云函数
 */

const express = require('express')
const cors = require('cors')
const cloud = require('wx-server-sdk')

const app = express()
const PORT = 3001

// 初始化云开发
cloud.init({
  env: 'cloud1-4g85f8xlb8166ff1-dev'
})

// 中间件
app.use(cors())
app.use(express.json())

// 请求日志
app.use((req, res, next) => {
  console.log(`📥 ${req.method} ${req.path}`, req.body || req.query)
  next()
})

/**
 * 调用云函数的通用处理器
 */
async function callCloudFunction(functionName, action, data = {}) {
  try {
    console.log(`🚀 调用云函数 ${functionName}:`, { action, data })
    
    const result = await cloud.callFunction({
      name: functionName,
      data: {
        action,
        data,
        timestamp: Date.now()
      }
    })
    
    console.log(`✅ 云函数 ${functionName} 响应:`, result.result)
    return result.result
    
  } catch (error) {
    console.error(`❌ 云函数 ${functionName} 调用失败:`, error)
    throw error
  }
}

// API路由

/**
 * 获取仪表板统计数据
 */
app.get('/api/dashboard/stats', async (req, res) => {
  try {
    const result = await callCloudFunction('adminDataQuery', 'getDashboardStats')
    
    if (result.success) {
      res.json({
        success: true,
        data: result.data
      })
    } else {
      res.status(500).json({
        success: false,
        error: result.error || '获取统计数据失败'
      })
    }
  } catch (error) {
    res.status(500).json({
      success: false,
      error: error.message
    })
  }
})

/**
 * 获取最近活动
 */
app.get('/api/dashboard/activities', async (req, res) => {
  try {
    const result = await callCloudFunction('adminDataQuery', 'getRecentActivities')
    
    if (result.success) {
      res.json({
        success: true,
        data: result.data
      })
    } else {
      res.status(500).json({
        success: false,
        error: result.error || '获取活动数据失败'
      })
    }
  } catch (error) {
    res.status(500).json({
      success: false,
      error: error.message
    })
  }
})

/**
 * 获取学生列表
 */
app.get('/api/students', async (req, res) => {
  try {
    const { page = 1, pageSize = 20, search = '' } = req.query
    
    const result = await callCloudFunction('adminDataQuery', 'getStudents', {
      page: parseInt(page),
      pageSize: parseInt(pageSize),
      search
    })
    
    if (result.success) {
      res.json({
        success: true,
        data: result.data
      })
    } else {
      res.status(500).json({
        success: false,
        error: result.error || '获取学生数据失败'
      })
    }
  } catch (error) {
    res.status(500).json({
      success: false,
      error: error.message
    })
  }
})

/**
 * 获取记录列表
 */
app.get('/api/records', async (req, res) => {
  try {
    const { page = 1, pageSize = 20, type = '' } = req.query
    
    const result = await callCloudFunction('adminDataQuery', 'getRecords', {
      page: parseInt(page),
      pageSize: parseInt(pageSize),
      type
    })
    
    if (result.success) {
      res.json({
        success: true,
        data: result.data
      })
    } else {
      res.status(500).json({
        success: false,
        error: result.error || '获取记录数据失败'
      })
    }
  } catch (error) {
    res.status(500).json({
      success: false,
      error: error.message
    })
  }
})

/**
 * 测试数据库连接
 */
app.get('/api/test/connection', async (req, res) => {
  try {
    const result = await callCloudFunction('adminDataQuery', 'testConnection')
    
    if (result.success) {
      res.json({
        success: true,
        data: result.data
      })
    } else {
      res.status(500).json({
        success: false,
        error: result.error || '数据库连接测试失败'
      })
    }
  } catch (error) {
    res.status(500).json({
      success: false,
      error: error.message
    })
  }
})

/**
 * 健康检查
 */
app.get('/api/health', (req, res) => {
  res.json({
    success: true,
    message: '云函数代理服务器运行正常',
    timestamp: new Date().toISOString()
  })
})

// 错误处理
app.use((error, req, res, next) => {
  console.error('❌ 服务器错误:', error)
  res.status(500).json({
    success: false,
    error: error.message || '服务器内部错误'
  })
})

// 404处理
app.use((req, res) => {
  res.status(404).json({
    success: false,
    error: `接口不存在: ${req.method} ${req.path}`
  })
})

// 启动服务器
app.listen(PORT, () => {
  console.log(`🚀 云函数代理服务器启动成功`)
  console.log(`📡 服务地址: http://localhost:${PORT}`)
  console.log(`🔗 云开发环境: cloud1-4g85f8xlb8166ff1-dev`)
  console.log(`📋 可用接口:`)
  console.log(`   GET /api/dashboard/stats - 获取仪表板统计`)
  console.log(`   GET /api/dashboard/activities - 获取最近活动`)
  console.log(`   GET /api/students - 获取学生列表`)
  console.log(`   GET /api/records - 获取记录列表`)
  console.log(`   GET /api/test/connection - 测试数据库连接`)
  console.log(`   GET /api/health - 健康检查`)
})

module.exports = app
