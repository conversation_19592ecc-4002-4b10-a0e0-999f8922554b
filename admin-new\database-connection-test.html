<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据库连接测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }
        .container {
            background: white;
            border-radius: 12px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            margin-bottom: 20px;
        }
        h1 { color: #2c3e50; text-align: center; margin-bottom: 30px; }
        .success-banner {
            background: linear-gradient(135deg, #00b894, #00cec9);
            color: white;
            padding: 20px;
            border-radius: 8px;
            text-align: center;
            margin-bottom: 30px;
            font-weight: bold;
        }
        .test-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .test-card {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 20px;
            background: #f9f9f9;
        }
        .test-card h3 {
            margin-top: 0;
            color: #2c3e50;
        }
        button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
            transition: transform 0.2s;
        }
        button:hover { transform: translateY(-2px); }
        button:disabled { opacity: 0.6; cursor: not-allowed; transform: none; }
        
        .result {
            margin: 10px 0;
            padding: 15px;
            border-radius: 8px;
            font-family: monospace;
            font-size: 14px;
        }
        .success { background: #d4edda; color: #155724; border-left: 4px solid #28a745; }
        .error { background: #f8d7da; color: #721c24; border-left: 4px solid #dc3545; }
        .info { background: #d1ecf1; color: #0c5460; border-left: 4px solid #17a2b8; }
        .warning { background: #fff3cd; color: #856404; border-left: 4px solid #ffc107; }
        
        .data-preview {
            background: #2c3e50;
            color: #ecf0f1;
            padding: 15px;
            border-radius: 6px;
            font-family: monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
            margin-top: 10px;
        }
        
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        .status-success { background: #28a745; }
        .status-error { background: #dc3545; }
        .status-warning { background: #ffc107; }
        
        .instructions {
            background: #e8f4fd;
            border-left: 4px solid #3498db;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="success-banner">
            🔗 数据库连接测试工具
            <br>测试管理后台与小程序数据库的连接状态
        </div>
        
        <h1>📊 小程序数据库连接测试</h1>
        
        <div class="instructions">
            <h3>📋 测试说明</h3>
            <p>此工具将测试本地API服务器与小程序云开发数据库的连接状态。</p>
            <p><strong>测试内容包括：</strong></p>
            <ul>
                <li>数据库连接状态</li>
                <li>各个数据集合的访问权限</li>
                <li>真实数据的获取和显示</li>
                <li>数据格式的正确性</li>
            </ul>
        </div>
        
        <div style="text-align: center; margin: 30px 0;">
            <button onclick="testDatabaseConnection()">测试数据库连接</button>
            <button onclick="testAllRealData()">测试所有真实数据</button>
            <button onclick="switchDataMode()">切换数据模式</button>
            <button onclick="clearResults()">清空结果</button>
        </div>
        
        <div class="test-grid">
            <div class="test-card">
                <h3>🔗 数据库连接状态</h3>
                <p>测试与小程序云开发数据库的基础连接</p>
                <div id="connection-status">
                    <span class="status-indicator status-warning"></span>等待测试
                </div>
                <div id="connection-results"></div>
            </div>
            
            <div class="test-card">
                <h3>📊 仪表板数据</h3>
                <p>测试用户数、评语数、AI调用数等统计数据</p>
                <div id="dashboard-status">
                    <span class="status-indicator status-warning"></span>等待测试
                </div>
                <div id="dashboard-results"></div>
            </div>
            
            <div class="test-card">
                <h3>⚡ 实时活动数据</h3>
                <p>测试最近的用户活动和评语生成记录</p>
                <div id="activity-status">
                    <span class="status-indicator status-warning"></span>等待测试
                </div>
                <div id="activity-results"></div>
            </div>
            
            <div class="test-card">
                <h3>👥 学生和评语数据</h3>
                <p>测试学生信息和评语记录的获取</p>
                <div id="data-status">
                    <span class="status-indicator status-warning"></span>等待测试
                </div>
                <div id="data-results"></div>
            </div>
        </div>
    </div>

    <div class="container">
        <h2>📝 测试日志</h2>
        <div id="logContainer" style="max-height: 400px; overflow-y: auto; background: #f8f9fa; padding: 15px; border-radius: 6px; font-family: monospace; font-size: 12px;">
            等待开始测试...
        </div>
    </div>

    <script>
        const API_URL = 'http://localhost:3000/admin'
        let currentDataMode = 'real' // 'real' 或 'mock'
        
        function log(message, type = 'info') {
            const logContainer = document.getElementById('logContainer')
            const timestamp = new Date().toLocaleTimeString()
            const prefix = type === 'error' ? '❌' : type === 'success' ? '✅' : type === 'warning' ? '⚠️' : 'ℹ️'
            logContainer.textContent += `[${timestamp}] ${prefix} ${message}\n`
            logContainer.scrollTop = logContainer.scrollHeight
        }
        
        function updateStatus(cardId, success, message) {
            const statusEl = document.getElementById(`${cardId}-status`)
            const indicator = statusEl.querySelector('.status-indicator')
            
            if (success === null) {
                indicator.className = 'status-indicator status-warning'
                statusEl.innerHTML = `<span class="status-indicator status-warning"></span>⏳ ${message}`
            } else if (success) {
                indicator.className = 'status-indicator status-success'
                statusEl.innerHTML = `<span class="status-indicator status-success"></span>✅ ${message}`
            } else {
                indicator.className = 'status-indicator status-error'
                statusEl.innerHTML = `<span class="status-indicator status-error"></span>❌ ${message}`
            }
        }
        
        function addResult(cardId, message, type = 'info', data = null) {
            const resultsEl = document.getElementById(`${cardId}-results`)
            const div = document.createElement('div')
            div.className = `result ${type}`
            
            let content = message
            if (data) {
                content += `<div class="data-preview">${JSON.stringify(data, null, 2)}</div>`
            }
            
            div.innerHTML = content
            resultsEl.appendChild(div)
        }
        
        async function callAPI(action, data = {}) {
            try {
                const response = await fetch(API_URL, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        action,
                        ...data,
                        timestamp: Date.now(),
                        requestId: `db_test_${Date.now()}`
                    })
                })
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`)
                }
                
                const result = await response.json()
                
                if (result.code === 200) {
                    return { success: true, data: result.data }
                } else {
                    return { success: false, error: result.message }
                }
                
            } catch (error) {
                return { success: false, error: error.message }
            }
        }
        
        async function testDatabaseConnection() {
            log('🔗 开始测试数据库连接...')
            updateStatus('connection', null, '正在连接数据库...')
            
            try {
                const result = await callAPI('database.testConnection')
                
                if (result.success && result.data.success) {
                    updateStatus('connection', true, '数据库连接正常')
                    addResult('connection', '✅ 数据库连接成功', 'success', result.data)
                    log(`✅ 数据库连接成功，可访问集合: ${result.data.collections.join(', ')}`)
                } else {
                    updateStatus('connection', false, '数据库连接失败')
                    addResult('connection', `❌ 连接失败: ${result.data?.message || result.error}`, 'error')
                    log(`❌ 数据库连接失败: ${result.data?.message || result.error}`)
                }
            } catch (error) {
                updateStatus('connection', false, '连接异常')
                addResult('connection', `❌ 连接异常: ${error.message}`, 'error')
                log(`❌ 数据库连接异常: ${error.message}`)
            }
        }
        
        async function testDashboardData() {
            log('📊 开始测试仪表板数据...')
            updateStatus('dashboard', null, '正在获取统计数据...')
            
            try {
                const result = await callAPI('data.getDashboardStats')
                
                if (result.success) {
                    updateStatus('dashboard', true, '统计数据获取成功')
                    addResult('dashboard', `✅ 统计数据: 用户${result.data.totalUsers}个, 评语${result.data.todayComments}条, AI调用${result.data.aiCalls}次`, 'success', result.data)
                    log(`✅ 仪表板数据获取成功`)
                } else {
                    updateStatus('dashboard', false, '统计数据获取失败')
                    addResult('dashboard', `❌ 获取失败: ${result.error}`, 'error')
                    log(`❌ 仪表板数据获取失败: ${result.error}`)
                }
            } catch (error) {
                updateStatus('dashboard', false, '数据获取异常')
                addResult('dashboard', `❌ 获取异常: ${error.message}`, 'error')
                log(`❌ 仪表板数据获取异常: ${error.message}`)
            }
        }
        
        async function testActivityData() {
            log('⚡ 开始测试实时活动数据...')
            updateStatus('activity', null, '正在获取活动记录...')
            
            try {
                const result = await callAPI('data.getRecentActivities', { limit: 5 })
                
                if (result.success) {
                    updateStatus('activity', true, '活动数据获取成功')
                    addResult('activity', `✅ 活动记录: 获取${result.data.length}条记录`, 'success', result.data)
                    log(`✅ 实时活动数据获取成功，共${result.data.length}条记录`)
                } else {
                    updateStatus('activity', false, '活动数据获取失败')
                    addResult('activity', `❌ 获取失败: ${result.error}`, 'error')
                    log(`❌ 实时活动数据获取失败: ${result.error}`)
                }
            } catch (error) {
                updateStatus('activity', false, '数据获取异常')
                addResult('activity', `❌ 获取异常: ${error.message}`, 'error')
                log(`❌ 实时活动数据获取异常: ${error.message}`)
            }
        }
        
        async function testStudentData() {
            log('👥 开始测试学生和评语数据...')
            updateStatus('data', null, '正在获取学生和评语数据...')
            
            try {
                const [studentsResult, commentsResult] = await Promise.all([
                    callAPI('data.getStudents', { params: { limit: 3 } }),
                    callAPI('data.getRecords', { params: { limit: 3 } })
                ])
                
                if (studentsResult.success && commentsResult.success) {
                    updateStatus('data', true, '学生和评语数据获取成功')
                    addResult('data', `✅ 学生数据: ${studentsResult.data.total}条记录`, 'success')
                    addResult('data', `✅ 评语数据: ${commentsResult.data.total}条记录`, 'success')
                    log(`✅ 学生和评语数据获取成功`)
                } else {
                    updateStatus('data', false, '数据获取失败')
                    addResult('data', `❌ 获取失败`, 'error')
                    log(`❌ 学生和评语数据获取失败`)
                }
            } catch (error) {
                updateStatus('data', false, '数据获取异常')
                addResult('data', `❌ 获取异常: ${error.message}`, 'error')
                log(`❌ 学生和评语数据获取异常: ${error.message}`)
            }
        }
        
        async function testAllRealData() {
            log('🚀 开始测试所有真实数据连接...')
            
            // 清空之前的结果
            document.querySelectorAll('[id$="-results"]').forEach(el => el.innerHTML = '')
            
            // 依次测试所有数据
            await testDatabaseConnection()
            await new Promise(resolve => setTimeout(resolve, 1000))
            
            await testDashboardData()
            await new Promise(resolve => setTimeout(resolve, 1000))
            
            await testActivityData()
            await new Promise(resolve => setTimeout(resolve, 1000))
            
            await testStudentData()
            
            log('🎯 所有真实数据测试完成！')
        }
        
        function switchDataMode() {
            currentDataMode = currentDataMode === 'real' ? 'mock' : 'real'
            log(`🔄 数据模式切换为: ${currentDataMode === 'real' ? '真实数据' : '模拟数据'}`)
            
            const modeInfo = currentDataMode === 'real' 
                ? '当前使用真实数据模式，将从小程序数据库获取数据'
                : '当前使用模拟数据模式，将使用预设的测试数据'
            
            addResult('connection', modeInfo, 'info')
        }
        
        function clearResults() {
            document.getElementById('logContainer').textContent = '等待开始测试...'
            document.querySelectorAll('[id$="-results"]').forEach(el => el.innerHTML = '')
            document.querySelectorAll('[id$="-status"]').forEach(el => {
                el.innerHTML = '<span class="status-indicator status-warning"></span>等待测试'
            })
        }
        
        // 页面加载时自动开始测试
        window.addEventListener('load', () => {
            log('🔗 数据库连接测试工具已加载')
            log('💡 点击"测试所有真实数据"开始全面测试')
            
            // 自动测试数据库连接
            setTimeout(testDatabaseConnection, 1000)
        })
    </script>
</body>
</html>
