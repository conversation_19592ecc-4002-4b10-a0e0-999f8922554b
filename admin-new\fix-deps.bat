@echo off
echo.
echo =============================================
echo   修复依赖问题
echo =============================================
echo.

echo 🧹 Cleaning up old dependencies...
if exist "node_modules" (
    echo Removing node_modules directory...
    rmdir /s /q node_modules
)

if exist "package-lock.json" (
    echo Removing package-lock.json...
    del package-lock.json
)

echo ✅ Cleanup completed
echo.

echo 📦 Installing fresh dependencies...
echo This may take a few minutes...
echo.

npm install

if errorlevel 1 (
    echo.
    echo ❌ Installation failed. Trying alternative method...
    echo.
    echo 🔄 Trying with npm cache clean...
    npm cache clean --force
    npm install
    
    if errorlevel 1 (
        echo.
        echo ❌ Still failing. Trying with yarn...
        echo First installing yarn...
        npm install -g yarn
        yarn install
    )
)

echo.
echo ✅ Dependencies installation completed!
echo.
echo =============================================
echo   Press any key to continue...
echo =============================================
pause >nul