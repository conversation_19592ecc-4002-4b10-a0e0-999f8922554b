@echo off
echo.
echo ==========================================
echo   启动Express服务器（完全无CSP限制）
echo ==========================================
echo.

echo 正在安装Express依赖...
call npm install express http-proxy-middleware --save

if errorlevel 1 (
    echo 依赖安装失败，尝试使用现有依赖启动...
) else (
    echo 依赖安装完成！
)

echo.
echo 启动Express服务器...
echo 访问地址: http://localhost:3000
echo.

call npm run dev:express

if errorlevel 1 (
    echo.
    echo Express启动失败，尝试直接运行...
    node pure-dev-server.js
)

echo.
pause