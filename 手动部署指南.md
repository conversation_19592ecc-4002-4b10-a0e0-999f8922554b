# 云函数手动部署指南

## 方案一：微信开发者工具手动部署（推荐）

1. **打开微信开发者工具**
2. **打开你的小程序项目**  
3. **在左侧文件树中找到 `cloudfunctions` 目录**
4. **部署 saveUserProfile:**
   - 右键点击 `cloudfunctions/saveUserProfile` 文件夹
   - 选择 "上传并部署：云端安装依赖"
   - 等待上传完成

5. **部署 getUserProfile:**
   - 右键点击 `cloudfunctions/getUserProfile` 文件夹  
   - 选择 "上传并部署：云端安装依赖"
   - 等待上传完成

## 方案二：使用开发者工具终端

1. **在微信开发者工具中打开终端**
2. **确保在项目根目录**
3. **执行以下命令:**

```bash
# 进入 saveUserProfile 目录并安装依赖
cd cloudfunctions/saveUserProfile
npm install
cd ../..

# 进入 getUserProfile 目录并安装依赖  
cd cloudfunctions/getUserProfile
npm install
cd ../..
```

4. **然后在开发者工具中手动右键上传**

## 方案三：创建数据库集合

**在开发者工具的云开发控制台中:**

1. 点击 "数据库"
2. 点击 "添加集合"
3. 输入集合名称: `user_profiles`
4. 点击确定创建

## 验证部署是否成功

部署完成后，检查:
1. 云开发控制台 > 云函数 > 应该能看到两个新函数
2. 云开发控制台 > 数据库 > 应该能看到 `user_profiles` 集合
3. 在小程序中测试用户信息同步功能

## 如果还有问题

1. 检查云环境ID是否正确: `cloud1-4g85f8xlb8166ff1`
2. 确保开发者工具已登录正确的微信账号
3. 确保该账号有云开发权限