/* 优化后的登录页面样式 */

.login-page {
  min-height: 100vh;
  position: relative;
  overflow: hidden;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

/* 背景装饰 - 更现代的设计 */
.bg-decoration {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1;
}

.bg-gradient {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, 
    rgba(102, 126, 234, 0.9) 0%, 
    rgba(118, 75, 162, 0.9) 100%);
}

.bg-pattern {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  opacity: 0.1;
}

.pattern-dot {
  position: absolute;
  width: 4px;
  height: 4px;
  background: white;
  border-radius: 50%;
  animation: float 6s ease-in-out infinite;
}

.pattern-dot:nth-child(1) { top: 10%; left: 10%; animation-delay: 0s; }
.pattern-dot:nth-child(2) { top: 20%; left: 80%; animation-delay: 1s; }
.pattern-dot:nth-child(3) { top: 30%; left: 20%; animation-delay: 2s; }
.pattern-dot:nth-child(4) { top: 40%; left: 90%; animation-delay: 3s; }
.pattern-dot:nth-child(5) { top: 50%; left: 15%; animation-delay: 4s; }
.pattern-dot:nth-child(6) { top: 60%; left: 75%; animation-delay: 5s; }

@keyframes float {
  0%, 100% { transform: translateY(0px) scale(1); opacity: 0.3; }
  50% { transform: translateY(-20px) scale(1.2); opacity: 0.8; }
}

/* 返回按钮 */
.back-button {
  position: absolute;
  top: 60rpx;
  left: 32rpx;
  z-index: 10;
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 12rpx 16rpx;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 24rpx;
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
}

.back-button:active {
  transform: scale(0.95);
  background: rgba(255, 255, 255, 0.3);
}

.back-text {
  color: white;
  font-size: 28rpx;
  font-weight: 500;
}

/* 主要内容 */
.main-content {
  position: relative;
  z-index: 2;
  padding: 120rpx 48rpx 80rpx;
  display: flex;
  flex-direction: column;
  min-height: 100vh;
}

/* 品牌区域 */
.brand-section {
  text-align: center;
  margin-bottom: 80rpx;
}

.app-logo {
  margin-bottom: 32rpx;
}

.logo-container {
  width: 120rpx;
  height: 120rpx;
  margin: 0 auto;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 32rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(10px);
  border: 2px solid rgba(255, 255, 255, 0.3);
}

.logo-image {
  width: 80rpx;
  height: 80rpx;
}

.brand-info {
  margin-bottom: 48rpx;
}

.app-title {
  font-size: 56rpx;
  font-weight: bold;
  color: white;
  margin-bottom: 16rpx;
  text-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

.app-subtitle {
  font-size: 32rpx;
  color: rgba(255, 255, 255, 0.9);
  font-weight: 500;
}

/* 核心价值展示 */
.value-proposition {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
  margin-top: 48rpx;
}

.value-item {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 16rpx;
  padding: 20rpx 32rpx;
  background: rgba(255, 255, 255, 0.15);
  border-radius: 16rpx;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.value-text {
  color: white;
  font-size: 28rpx;
  font-weight: 500;
}

/* 登录激励区域 */
.login-incentive {
  text-align: center;
  margin-bottom: 60rpx;
  padding: 40rpx 32rpx;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 24rpx;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.incentive-title {
  font-size: 36rpx;
  font-weight: bold;
  color: white;
  margin-bottom: 32rpx;
}

.incentive-benefits {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.benefit-item {
  display: flex;
  align-items: center;
  gap: 16rpx;
  padding: 16rpx 24rpx;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 12rpx;
}

.benefit-text {
  color: rgba(255, 255, 255, 0.9);
  font-size: 28rpx;
  flex: 1;
  text-align: left;
}

/* 登录按钮区域 */
.login-section {
  margin-top: auto;
  padding-bottom: 40rpx;
}

/* 主登录按钮 */
.primary-login-button {
  background: linear-gradient(135deg, #4080FF 0%, #5470C6 100%);
  border-radius: 20rpx;
  padding: 32rpx 48rpx;
  margin-bottom: 32rpx;
  box-shadow: 0 8px 32px rgba(64, 128, 255, 0.4);
  transition: all 0.3s ease;
}

.primary-login-button:active {
  transform: translateY(2px);
  box-shadow: 0 4px 16px rgba(64, 128, 255, 0.3);
}

.button-content {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 16rpx;
  margin-bottom: 8rpx;
}

.login-text {
  color: white;
  font-size: 32rpx;
  font-weight: bold;
}

.button-subtitle {
  text-align: center;
  color: rgba(255, 255, 255, 0.8);
  font-size: 24rpx;
}

/* 游客模式按钮 */
.guest-mode-button {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12rpx;
  padding: 24rpx;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 16rpx;
  margin-bottom: 40rpx;
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
}

.guest-mode-button:active {
  background: rgba(255, 255, 255, 0.2);
  transform: scale(0.98);
}

.guest-text {
  color: rgba(255, 255, 255, 0.9);
  font-size: 28rpx;
  font-weight: 500;
}

/* 法律条款 */
.legal-terms {
  text-align: center;
  line-height: 1.6;
}

.terms-text {
  color: rgba(255, 255, 255, 0.7);
  font-size: 24rpx;
}

.link-text {
  color: #4080FF;
  font-size: 24rpx;
  text-decoration: underline;
}

/* 底部信任标识 */
.trust-indicators {
  position: absolute;
  bottom: 40rpx;
  left: 48rpx;
  right: 48rpx;
  display: flex;
  justify-content: space-around;
  z-index: 2;
}

.trust-item {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 16rpx 24rpx;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 12rpx;
  backdrop-filter: blur(10px);
}

.trust-text {
  color: rgba(255, 255, 255, 0.8);
  font-size: 24rpx;
}

/* 弹窗样式优化 */
.agreement-popup {
  background: white;
  border-radius: 20rpx 20rpx 0 0;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.popup-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 32rpx 48rpx;
  border-bottom: 1px solid #f0f0f0;
}

.popup-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.agreement-content {
  flex: 1;
  padding: 32rpx 48rpx;
}

.agreement-text {
  line-height: 1.8;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin: 32rpx 0 16rpx;
}

.section-content {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 24rpx;
}

.popup-footer {
  padding: 32rpx 48rpx;
  border-top: 1px solid #f0f0f0;
}

/* 响应式适配 */
@media (max-width: 375px) {
  .main-content {
    padding: 100rpx 32rpx 60rpx;
  }
  
  .app-title {
    font-size: 48rpx;
  }
  
  .app-subtitle {
    font-size: 28rpx;
  }
}
