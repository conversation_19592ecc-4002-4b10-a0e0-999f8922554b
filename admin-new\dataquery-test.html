<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DataQuery云函数测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; }
        .section { 
            background: white; 
            margin: 20px 0; 
            padding: 20px; 
            border-radius: 8px; 
            box-shadow: 0 2px 8px rgba(0,0,0,0.1); 
        }
        .success { border-left: 4px solid #52c41a; }
        .error { border-left: 4px solid #ff4d4f; }
        .loading { border-left: 4px solid #1890ff; }
        pre { 
            background: #f8f9fa; 
            padding: 15px; 
            border-radius: 4px; 
            overflow-x: auto;
            white-space: pre-wrap;
            word-wrap: break-word;
        }
        button { 
            margin: 5px 10px 5px 0; 
            padding: 10px 20px; 
            cursor: pointer;
            background: #1890ff;
            color: white;
            border: none;
            border-radius: 4px;
            font-size: 14px;
        }
        button:hover { background: #40a9ff; }
        button:disabled { background: #d9d9d9; cursor: not-allowed; }
        .stats { 
            display: grid; 
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); 
            gap: 15px; 
            margin: 15px 0; 
        }
        .stat-card { 
            background: #f0f9ff; 
            padding: 15px; 
            border-radius: 6px; 
            text-align: center; 
            border: 1px solid #e6f4ff;
        }
        .stat-number { font-size: 24px; font-weight: bold; color: #1890ff; }
        .stat-label { color: #666; margin-top: 5px; }
        .header { text-align: center; margin-bottom: 30px; }
        .activity-item {
            border-bottom: 1px solid #f0f0f0;
            padding: 10px 0;
        }
        .activity-item:last-child { border-bottom: none; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎯 DataQuery云函数测试</h1>
            <p>直接测试dataQuery云函数，验证与小程序数据库的连接</p>
            <p><strong>环境ID:</strong> cloud1-4g85f8xlb8166ff1</p>
            <p><strong>测试时间:</strong> <span id="currentTime"></span></p>
        </div>

        <div class="section">
            <h2>🧪 测试操作</h2>
            <button onclick="testDashboardStats()" id="btn-dashboard">测试仪表板数据</button>
            <button onclick="testRecentActivities()" id="btn-activities">测试活动记录</button>
            <button onclick="testStudentData()" id="btn-students">测试学生数据</button>
            <button onclick="testCommentData()" id="btn-comments">测试评语数据</button>
            <button onclick="testConnection()" id="btn-connection">测试数据库连接</button>
            <button onclick="clearResults()" style="background: #ff7875;">清空结果</button>
        </div>

        <div class="section">
            <h2>📊 实时统计</h2>
            <div class="stats" id="statsContainer">
                <div class="stat-card">
                    <div class="stat-number" id="stat-users">-</div>
                    <div class="stat-label">活跃用户</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="stat-comments">-</div>
                    <div class="stat-label">今日评语</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="stat-ai">-</div>
                    <div class="stat-label">AI调用</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="stat-satisfaction">-</div>
                    <div class="stat-label">满意度</div>
                </div>
            </div>
        </div>

        <div class="section">
            <h2>📋 测试结果</h2>
            <div id="testResults">等待测试...</div>
        </div>
    </div>

    <script src="https://unpkg.com/@cloudbase/js-sdk/dist/index.umd.js"></script>
    <script>
        // 显示当前时间
        document.getElementById('currentTime').textContent = new Date().toLocaleString();

        // 测试结果容器
        const resultsContainer = document.getElementById('testResults');

        function addResult(title, status, data, timestamp = true) {
            const resultDiv = document.createElement('div');
            resultDiv.className = `section ${status}`;
            resultDiv.innerHTML = `
                <h3>${title}</h3>
                <pre>${typeof data === 'object' ? JSON.stringify(data, null, 2) : data}</pre>
                ${timestamp ? `<p><small>⏰ ${new Date().toLocaleString()}</small></p>` : ''}
            `;
            resultsContainer.appendChild(resultDiv);
            
            // 滚动到最新结果
            resultDiv.scrollIntoView({ behavior: 'smooth', block: 'end' });
        }

        function clearResults() {
            resultsContainer.innerHTML = '等待测试...';
            // 清空统计数据
            document.getElementById('stat-users').textContent = '-';
            document.getElementById('stat-comments').textContent = '-';
            document.getElementById('stat-ai').textContent = '-';
            document.getElementById('stat-satisfaction').textContent = '-';
        }

        function setButtonLoading(buttonId, loading) {
            const btn = document.getElementById(buttonId);
            btn.disabled = loading;
            if (loading) {
                btn.textContent = btn.textContent.replace(/^(测试|获取)/, '加载中...');
            } else {
                btn.textContent = btn.textContent.replace('加载中...', '测试');
            }
        }

        // 初始化CloudBase
        const cloudbase = window.cloudbase;
        const app = cloudbase.init({
            env: 'cloud1-4g85f8xlb8166ff1'
        });

        async function testDashboardStats() {
            setButtonLoading('btn-dashboard', true);
            addResult('📊 仪表板数据测试', 'loading', '正在调用dataQuery云函数...');
            
            try {
                const result = await app.callFunction({
                    name: 'dataQuery',
                    data: {
                        action: 'getDashboardStats'
                    }
                });

                if (result.result.code === 200) {
                    const stats = result.result.data;
                    
                    // 更新统计显示
                    document.getElementById('stat-users').textContent = stats.totalUsers || 0;
                    document.getElementById('stat-comments').textContent = stats.todayComments || 0;
                    document.getElementById('stat-ai').textContent = stats.aiCalls || 0;
                    document.getElementById('stat-satisfaction').textContent = (stats.satisfaction || 0).toFixed(1);
                    
                    addResult('✅ 仪表板数据获取成功', 'success', {
                        message: '成功获取仪表板统计数据',
                        data: stats
                    });
                } else {
                    throw new Error(result.result.message || '数据获取失败');
                }
            } catch (error) {
                addResult('❌ 仪表板数据获取失败', 'error', {
                    error: error.message,
                    details: error.errMsg || error.toString()
                });
            } finally {
                setButtonLoading('btn-dashboard', false);
            }
        }

        async function testRecentActivities() {
            setButtonLoading('btn-activities', true);
            addResult('📈 活动记录测试', 'loading', '正在获取最近活动记录...');
            
            try {
                const result = await app.callFunction({
                    name: 'dataQuery',
                    data: {
                        action: 'getRecentActivities',
                        params: { limit: 10 }
                    }
                });

                if (result.result.code === 200) {
                    const activities = result.result.data;
                    
                    let activityHtml = '<h4>最近活动记录:</h4>';
                    if (activities && activities.length > 0) {
                        activities.forEach((activity, index) => {
                            activityHtml += `
                                <div class="activity-item">
                                    <strong>${activity.userName || '未知用户'}</strong> 
                                    ${activity.action}
                                    <small style="color: #999; float: right;">
                                        ${new Date(activity.timestamp).toLocaleString()}
                                    </small>
                                </div>
                            `;
                        });
                    } else {
                        activityHtml += '<p style="color: #999;">暂无活动记录</p>';
                    }
                    
                    addResult('✅ 活动记录获取成功', 'success', activityHtml, false);
                    addResult('📝 活动记录详细数据', 'success', activities);
                } else {
                    throw new Error(result.result.message || '活动记录获取失败');
                }
            } catch (error) {
                addResult('❌ 活动记录获取失败', 'error', {
                    error: error.message,
                    details: error.errMsg || error.toString()
                });
            } finally {
                setButtonLoading('btn-activities', false);
            }
        }

        async function testStudentData() {
            setButtonLoading('btn-students', true);
            addResult('👥 学生数据测试', 'loading', '正在获取学生数据...');
            
            try {
                const result = await app.callFunction({
                    name: 'dataQuery',
                    data: {
                        action: 'getStudents',
                        params: { limit: 10 }
                    }
                });

                if (result.result.code === 200) {
                    const studentData = result.result.data;
                    addResult('✅ 学生数据获取成功', 'success', {
                        message: `总共 ${studentData.total} 个学生，显示前 ${studentData.list.length} 个`,
                        students: studentData.list.map(s => ({
                            name: s.name,
                            class: s.class,
                            teacher: s.teacher,
                            status: s.status
                        }))
                    });
                } else {
                    throw new Error(result.result.message || '学生数据获取失败');
                }
            } catch (error) {
                addResult('❌ 学生数据获取失败', 'error', {
                    error: error.message,
                    details: error.errMsg || error.toString()
                });
            } finally {
                setButtonLoading('btn-students', false);
            }
        }

        async function testCommentData() {
            setButtonLoading('btn-comments', true);
            addResult('💬 评语数据测试', 'loading', '正在获取评语数据...');
            
            try {
                const result = await app.callFunction({
                    name: 'dataQuery',
                    data: {
                        action: 'getComments',
                        params: { limit: 5 }
                    }
                });

                if (result.result.code === 200) {
                    const commentData = result.result.data;
                    addResult('✅ 评语数据获取成功', 'success', {
                        message: `总共 ${commentData.total} 条评语，显示前 ${commentData.list.length} 条`,
                        comments: commentData.list.map(c => ({
                            studentName: c.studentName,
                            teacherName: c.teacherName,
                            content: c.content ? c.content.substring(0, 50) + '...' : '无内容',
                            aiModel: c.aiModel,
                            tokensUsed: c.tokensUsed,
                            createTime: c.createTime
                        }))
                    });
                } else {
                    throw new Error(result.result.message || '评语数据获取失败');
                }
            } catch (error) {
                addResult('❌ 评语数据获取失败', 'error', {
                    error: error.message,
                    details: error.errMsg || error.toString()
                });
            } finally {
                setButtonLoading('btn-comments', false);
            }
        }

        async function testConnection() {
            setButtonLoading('btn-connection', true);
            addResult('🔧 数据库连接测试', 'loading', '正在测试数据库连接...');
            
            try {
                const result = await app.callFunction({
                    name: 'dataQuery',
                    data: {
                        action: 'testConnection'
                    }
                });

                if (result.result.code === 200) {
                    const connectionData = result.result.data;
                    addResult('✅ 数据库连接测试成功', 'success', connectionData);
                } else {
                    throw new Error(result.result.message || '连接测试失败');
                }
            } catch (error) {
                addResult('❌ 数据库连接测试失败', 'error', {
                    error: error.message,
                    details: error.errMsg || error.toString()
                });
            } finally {
                setButtonLoading('btn-connection', false);
            }
        }

        // 页面加载完成后自动运行基础测试
        window.addEventListener('load', () => {
            console.log('页面加载完成，准备进行dataQuery云函数测试');
            setTimeout(() => {
                testConnection();
                setTimeout(() => testDashboardStats(), 2000);
            }, 1000);
        });
    </script>
</body>
</html>