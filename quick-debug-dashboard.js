// 🔧 Dashboard数据问题快速调试脚本
// 在浏览器控制台中运行

async function debugDashboardData() {
  console.log('🔧 开始调试Dashboard数据问题...')
  
  try {
    // 1. 检查realDataService
    console.log('\n1️⃣ 测试realDataService...')
    const { realDataService } = await import('./admin-new/src/services/realDataService.ts')
    const dashboardStats = await realDataService.getDashboardStats()
    console.log('📊 realDataService结果:', dashboardStats)
    
    // 2. 检查云函数直接调用
    console.log('\n2️⃣ 测试云函数直接调用...')
    const { cloudbaseService } = await import('./admin-new/src/services/cloudWebSDK.ts')
    const cloudResult = await cloudbaseService.callFunction('dataQuery', {
      action: 'getDashboardStats'
    })
    console.log('☁️ 云函数直接调用结果:', cloudResult)
    
    // 3. 数据库诊断
    console.log('\n3️⃣ 运行数据库诊断...')
    const diagnosisResult = await cloudbaseService.callFunction('dataQuery', {
      action: 'diagnoseDatabaseStructure'
    })
    console.log('🔍 数据库诊断结果:', diagnosisResult)
    
    // 4. 分析问题原因
    console.log('\n🔍 问题分析:')
    
    if (dashboardStats.totalUsers === 0) {
      console.log('❌ 活跃教师数为0 - 可能原因:')
      console.log('   - users集合不存在或无数据')
      console.log('   - 字段名不匹配 (lastLoginTime等)')
    }
    
    if (dashboardStats.totalStudents === 0) {
      console.log('❌ 学生总数为0 - 可能原因:')
      console.log('   - students集合不存在')
      console.log('   - 应用使用了student、studentInfo等其他集合名')
    }
    
    if (dashboardStats.aiCalls === 0) {
      console.log('❌ AI调用次数为0 - 可能原因:')
      console.log('   - ai_usage集合不存在')
      console.log('   - 从comments集合统计失败')
    }
    
    // 5. 提供解决方案
    console.log('\n💡 解决方案建议:')
    console.log('1. 检查小程序是否有真实数据')
    console.log('2. 确认数据库集合命名 (users/user, students/student等)')
    console.log('3. 检查字段名匹配 (lastLoginTime, studentName等)')
    console.log('4. 重新部署dataQuery云函数')
    
  } catch (error) {
    console.error('❌ 调试过程中出错:', error)
  }
}

// 简化版调试 - 只检查关键数据
async function quickDebug() {
  console.log('🚀 快速调试...')
  
  try {
    const { cloudbaseService } = await import('./admin-new/src/services/cloudWebSDK.ts')
    
    // 测试每个关键API
    const tests = [
      { action: 'getDashboardStats', description: '仪表板统计' },
      { action: 'diagnoseDatabaseStructure', description: '数据库诊断' }
    ]
    
    for (const test of tests) {
      console.log(`\n🧪 测试: ${test.description}`)
      try {
        const result = await cloudbaseService.callFunction('dataQuery', {
          action: test.action
        })
        console.log(`✅ ${test.description} 成功:`, result.data)
      } catch (error) {
        console.log(`❌ ${test.description} 失败:`, error.message)
      }
    }
    
  } catch (error) {
    console.error('❌ 快速调试失败:', error)
  }
}

// 导出到全局供控制台使用
window.debugDashboardData = debugDashboardData
window.quickDebug = quickDebug

console.log('🔧 Dashboard调试工具已加载')
console.log('使用方法:')
console.log('- debugDashboardData() - 完整调试')
console.log('- quickDebug() - 快速调试')