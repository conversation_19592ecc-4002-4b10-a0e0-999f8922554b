<!--
  评语编辑页面
-->
<view class="edit-page">
  <!-- 加载状态 -->
  <van-loading wx:if="{{loading}}" type="spinner" />

  <!-- 内容区域 -->
  <scroll-view wx:else class="content-container" scroll-y="{{true}}">
    <!-- 学生信息 -->
    <view class="student-info-section">
      <view class="section-title">学生信息</view>
      <view class="student-card">
        <view class="student-avatar">
          <text class="avatar-text">{{commentInfo.surname}}</text>
        </view>
        <view class="student-details">
          <view class="student-name">{{commentInfo.studentName}}</view>
          <view class="student-class">{{commentInfo.className}}</view>
        </view>
      </view>
    </view>

    <!-- 评语内容 -->
    <view class="content-section">
      <view class="section-title">
        <text>评语内容</text>
        <text class="word-count {{wordCount > maxWords ? 'error' : ''}}">
          {{wordCount}}/{{maxWords}}
        </text>
      </view>

      <textarea
        class="content-textarea"
        placeholder="请输入评语内容..."
        value="{{formData.content}}"
        bindinput="onContentChange"
        maxlength="{{maxWords}}"
        auto-height
        show-confirm-bar="{{false}}"
        cursor-spacing="20"
      />
    </view>

    <!-- 评语设置 -->
    <view class="settings-section">
      <view class="section-title">评语设置</view>

      <!-- 风格选择 -->
      <view class="setting-item">
        <view class="setting-label">评语风格</view>
        <picker
          mode="selector"
          range="{{styleOptions}}"
          range-key="name"
          value="{{currentStyleIndex}}"
          bindchange="onStyleChange"
        >
          <view class="picker-display">
            <text class="picker-text">{{currentStyleText}}</text>
            <van-icon name="arrow" />
          </view>
        </picker>
      </view>

      <!-- 长度选择 -->
      <view class="setting-item">
        <view class="setting-label">评语长度</view>
        <picker
          mode="selector"
          range="{{lengthOptions}}"
          range-key="name"
          value="{{currentLengthIndex}}"
          bindchange="onLengthChange"
        >
          <view class="picker-display">
            <text class="picker-text">{{currentLengthText}}</text>
            <van-icon name="arrow" />
          </view>
        </picker>
      </view>
    </view>

    <!-- 操作历史 -->
    <view wx:if="{{commentInfo.editTime}}" class="history-section">
      <view class="section-title">编辑历史</view>
      <view class="history-item">
        <view class="history-info">
          <text class="history-action">最后编辑</text>
          <text class="history-time">{{commentInfo.editTime}}</text>
        </view>
      </view>
    </view>

    <!-- 底部操作栏 -->
    <view class="bottom-actions">
      <van-button
        type="default"
        size="large"
        custom-class="action-btn cancel-btn"
        bindtap="onCancel"
      >
        取消
      </van-button>

      <van-button
        type="info"
        size="large"
        custom-class="action-btn regenerate-btn"
        loading="{{regenerating}}"
        bindtap="regenerateComment"
      >
        AI重新生成
      </van-button>

      <van-button
        type="primary"
        size="large"
        custom-class="action-btn save-btn"
        loading="{{saving}}"
        bindtap="saveComment"
      >
        保存
      </van-button>
    </view>

    <!-- 底部间距 -->
    <view class="bottom-spacing"></view>
  </scroll-view>
</view> 