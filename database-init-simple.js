/**
 * 云数据库初始化脚本（简化版）
 * 解决变量冲突问题，在微信开发者工具的云开发控制台中运行
 */

console.log('🚀 开始初始化云数据库（简化版）...')

// 使用立即执行函数避免变量冲突
;(async () => {
  try {
    // 1. 创建数据库表
    console.log('📋 创建数据库表...')
    
    const collections = ['admin_users', 'system_settings', 'user_preferences', 'admin_logs']
    
    for (const collectionName of collections) {
      try {
        await db.createCollection(collectionName)
        console.log(`✅ ${collectionName} 表创建成功`)
      } catch (err) {
        console.log(`⚠️ ${collectionName} 表可能已存在`)
      }
    }

    // 2. 密码哈希函数
    const hashPassword = (password) => {
      let hash = 0
      const salt = 'admin_salt_2024'
      const str = password + salt
      for (let i = 0; i < str.length; i++) {
        const char = str.charCodeAt(i)
        hash = ((hash << 5) - hash) + char
        hash = hash & hash
      }
      return hash.toString(16)
    }

    // 3. 创建默认管理员账户
    console.log('👤 创建默认管理员账户...')
    
    const adminCheck = await db.collection('admin_users').where({
      username: 'admin'
    }).get()
    
    if (adminCheck.data.length === 0) {
      await db.collection('admin_users').add({
        data: {
          username: 'admin',
          password_hash: hashPassword('admin123'),
          role: 'super_admin',
          status: 'active',
          created_at: new Date(),
          updated_at: new Date()
        }
      })
      console.log('✅ 默认管理员账户创建成功')
    } else {
      console.log('⚠️ 管理员账户已存在')
    }

    // 4. 创建默认系统设置
    console.log('⚙️ 创建默认系统设置...')
    
    const settings = [
      ['systemName', '评语灵感君管理后台', 'string'],
      ['version', 'v2.0.0', 'string'],
      ['maxUsers', 1000, 'number'],
      ['sessionTimeout', 30, 'number'],
      ['autoRefresh', false, 'boolean'],
      ['darkMode', false, 'boolean'],
      ['debugMode', false, 'boolean'],
      ['enableCache', true, 'boolean'],
      ['logLevel', 'info', 'string'],
      ['backupFrequency', 'daily', 'string']
    ]

    for (const [key, value, type] of settings) {
      const existing = await db.collection('system_settings').where({
        setting_key: key
      }).get()
      
      if (existing.data.length === 0) {
        await db.collection('system_settings').add({
          data: {
            setting_key: key,
            setting_value: value,
            setting_type: type,
            updated_by: 'system',
            updated_at: new Date(),
            created_at: new Date()
          }
        })
        console.log(`✅ 系统设置 ${key} 创建成功`)
      } else {
        console.log(`⚠️ 系统设置 ${key} 已存在`)
      }
    }

    // 5. 完成提示
    console.log('')
    console.log('🎉 数据库初始化完成！')
    console.log('')
    console.log('📋 已创建的表:')
    console.log('  - admin_users: 管理员用户表')
    console.log('  - system_settings: 系统设置表')
    console.log('  - user_preferences: 用户偏好表')
    console.log('  - admin_logs: 管理员操作日志表')
    console.log('')
    console.log('👤 默认管理员账户:')
    console.log('  - 用户名: admin')
    console.log('  - 密码: admin123')
    console.log('  - 角色: super_admin')
    console.log('')
    console.log('🔧 下一步:')
    console.log('  1. 部署 settingsAPI 云函数')
    console.log('  2. 测试登录功能')
    console.log('')
    console.log('✅ 初始化完成！')

  } catch (error) {
    console.error('❌ 初始化失败:', error)
    console.log('')
    console.log('🔧 解决方案:')
    console.log('  1. 检查云开发环境是否正确')
    console.log('  2. 确认数据库权限设置')
    console.log('  3. 重新运行脚本')
  }
})()

console.log('📝 脚本已启动，请等待执行完成...')
