/**
 * React Hook for Real-time Data
 * 基于云函数调用的免费实时数据更新
 *
 * 核心功能：
 * 1. 自动数据获取和刷新
 * 2. 加载状态管理
 * 3. 错误处理
 * 4. 缓存优化
 */

import { useState, useEffect, useCallback, useRef } from 'react'
import realTimeDataService, { DataResponse, DataItem, DashboardStats, RealtimeData } from '../services/realTimeDataService'

interface UseRealTimeDataOptions {
  autoRefresh?: boolean
  refreshInterval?: number
  forceRefresh?: boolean
  onError?: (error: Error) => void
  onDataChange?: (data: any) => void
}

interface UseRealTimeDataResult<T> {
  data: T | null
  loading: boolean
  error: Error | null
  refresh: () => Promise<void>
  updateData: (operation: any) => Promise<void>
}

/**
 * 通用实时数据Hook
 */
export function useRealTimeData<T = DataItem[]>(
  collection: string,
  filters: Record<string, any> = {},
  options: UseRealTimeDataOptions = {}
): UseRealTimeDataResult<T[]> {
  const [data, setData] = useState<T[] | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<Error | null>(null)

  const {
    autoRefresh = true,
    refreshInterval = 30000,
    forceRefresh = false,
    onError,
    onDataChange
  } = options

  const refreshIntervalRef = useRef<NodeJS.Timeout | null>(null)
  const mountedRef = useRef(true)

  // 获取数据
  const fetchData = useCallback(async (force = false) => {
    try {
      setLoading(true)
      setError(null)

      const result = await realTimeDataService.getData<T>(collection, filters, force)

      if (mountedRef.current) {
        setData(result.data)
        setLoading(false)

        if (onDataChange) {
          onDataChange(result.data)
        }
      }
    } catch (err) {
      const error = err as Error
      console.error(`❌ 获取数据失败: ${collection}`, error)

      if (mountedRef.current) {
        setError(error)
        setLoading(false)

        if (onError) {
          onError(error)
        }
      }
    }
  }, [collection, JSON.stringify(filters), onError, onDataChange])

  // 刷新数据
  const refresh = useCallback(async () => {
    await fetchData(true)
  }, [fetchData])

  // 更新数据
  const updateData = useCallback(async (operation: any) => {
    try {
      await realTimeDataService.updateData({
        collection,
        ...operation
      })

      // 更新成功后刷新数据
      await fetchData(true)
    } catch (err) {
      const error = err as Error
      console.error(`❌ 更新数据失败: ${collection}`, error)

      if (onError) {
        onError(error)
      }

      throw error
    }
  }, [collection, fetchData, onError])

  // 初始化和自动刷新
  useEffect(() => {
    // 初始加载
    fetchData(forceRefresh)

    // 设置自动刷新
    if (autoRefresh) {
      refreshIntervalRef.current = setInterval(() => {
        fetchData(false)
      }, refreshInterval)
    }

    // 监听数据变更事件
    const handleDataChange = (changeEvent: any) => {
      if (changeEvent.collection === collection) {
        fetchData(true)
      }
    }

    realTimeDataService.addEventListener(`dataChange:${collection}`, handleDataChange)

    // 清理函数
    return () => {
      if (refreshIntervalRef.current) {
        clearInterval(refreshIntervalRef.current)
      }

      realTimeDataService.removeEventListener(`dataChange:${collection}`, handleDataChange)
    }
  }, [collection, JSON.stringify(filters), autoRefresh, refreshInterval, forceRefresh, fetchData])

  // 组件卸载时清理
  useEffect(() => {
    return () => {
      mountedRef.current = false
    }
  }, [])

  return {
    data,
    loading,
    error,
    refresh,
    updateData
  }
}

// 专门用于系统统计的实时数据Hook
export const useRealTimeStats = () => {
  const [stats, setStats] = useState({
    totalUsers: 1234,
    todayComments: 110,
    aiCalls: 2456,
    systemScore: 987
  })
  
  const [tokensData, setTokensData] = useState({
    hourly: [],
    daily: [],
    totalToday: 112960,
    totalWeek: 772260,
    costToday: 0.23,
    costWeek: 1.54
  })
  
  const [activities, setActivities] = useState([
    { user: '张老师', action: '生成了5条学生评语', time: '2分钟前', icon: '📝', color: '#51cf66' },
    { user: '李老师', action: '导入了新的学生数据', time: '15分钟前', icon: '📁', color: '#667eea' },
    { user: '王老师', action: '修改了AI配置参数', time: '1小时前', icon: '⚙️', color: '#ff6b6b' },
    { user: '赵老师', action: '导出了月度报告', time: '2小时前', icon: '📈', color: '#ffd43b' }
  ])
  
  // 处理用户活动更新
  const handleUserActivity = useCallback((data: any) => {
    setActivities(prev => [data, ...prev.slice(0, 9)]) // 保持最新10条记录
  }, [])
  
  // 处理tokens更新
  const handleTokensUpdate = useCallback((data: any) => {
    setTokensData(prev => ({
      ...prev,
      ...data
    }))
  }, [])
  
  // 处理评语生成事件
  const handleCommentGenerated = useCallback((data: any) => {
    setStats(prev => ({
      ...prev,
      todayComments: prev.todayComments + 1,
      aiCalls: prev.aiCalls + 1
    }))
  }, [])
  
  // 处理系统状态更新
  const handleSystemStatus = useCallback((data: any) => {
    setStats(prev => ({
      ...prev,
      ...data
    }))
  }, [])
  
  // 使用新的仪表板统计Hook
  const { data: dashboardData, loading, error, refresh } = useDashboardStats()

  // 使用新的实时数据Hook
  const { data: realtimeData } = useRealtimeData()

  // 合并数据以保持向后兼容
  useEffect(() => {
    if (dashboardData) {
      setStats({
        totalUsers: dashboardData.totalUsers,
        todayComments: dashboardData.todayComments,
        aiCalls: dashboardData.totalComments,
        systemScore: 987 // 保持原有字段
      })
    }
  }, [dashboardData])

  useEffect(() => {
    if (realtimeData && realtimeData.recentActivities) {
      const formattedActivities = realtimeData.recentActivities.slice(0, 4).map((activity: any, index: number) => ({
        user: activity.user || '用户',
        action: activity.action || '执行了操作',
        time: activity.time || `${index * 5 + 2}分钟前`,
        icon: activity.icon || '📝',
        color: activity.color || '#51cf66'
      }))
      setActivities(formattedActivities)
    }
  }, [realtimeData])

  return {
    stats,
    tokensData,
    activities,
    isConnected: !loading && !error,
    connectionError: error?.message || null,
    sendMessage: () => console.log('使用云函数调用，无需发送消息'),
    refresh
  }
}

/**
 * 仪表板统计数据Hook
 */
export function useDashboardStats(options: UseRealTimeDataOptions = {}): UseRealTimeDataResult<DashboardStats> {
  const [data, setData] = useState<DashboardStats | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<Error | null>(null)

  const {
    autoRefresh = true,
    refreshInterval = 60000, // 仪表板数据1分钟刷新一次
    forceRefresh = false,
    onError,
    onDataChange
  } = options

  const refreshIntervalRef = useRef<NodeJS.Timeout | null>(null)
  const mountedRef = useRef(true)

  // 获取统计数据
  const fetchStats = useCallback(async (force = false) => {
    try {
      setLoading(true)
      setError(null)

      const result = await realTimeDataService.getDashboardStats(force)

      if (mountedRef.current) {
        setData(result.data)
        setLoading(false)

        if (onDataChange) {
          onDataChange(result.data)
        }
      }
    } catch (err) {
      const error = err as Error
      console.error('❌ 获取仪表板统计失败:', error)

      if (mountedRef.current) {
        setError(error)
        setLoading(false)

        if (onError) {
          onError(error)
        }
      }
    }
  }, [onError, onDataChange])

  // 刷新统计数据
  const refresh = useCallback(async () => {
    await fetchStats(true)
  }, [fetchStats])

  // 更新数据（仪表板统计不支持直接更新）
  const updateData = useCallback(async () => {
    throw new Error('仪表板统计数据不支持直接更新')
  }, [])

  // 初始化和自动刷新
  useEffect(() => {
    // 初始加载
    fetchStats(forceRefresh)

    // 设置自动刷新
    if (autoRefresh) {
      refreshIntervalRef.current = setInterval(() => {
        fetchStats(false)
      }, refreshInterval)
    }

    // 清理函数
    return () => {
      if (refreshIntervalRef.current) {
        clearInterval(refreshIntervalRef.current)
      }
    }
  }, [autoRefresh, refreshInterval, forceRefresh, fetchStats])

  // 组件卸载时清理
  useEffect(() => {
    return () => {
      mountedRef.current = false
    }
  }, [])

  return {
    data,
    loading,
    error,
    refresh,
    updateData
  }
}

/**
 * 实时数据Hook
 */
export function useRealtimeData(options: UseRealTimeDataOptions = {}): UseRealTimeDataResult<RealtimeData> {
  const [data, setData] = useState<RealtimeData | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<Error | null>(null)

  const {
    autoRefresh = true,
    refreshInterval = 10000, // 实时数据10秒刷新一次
    onError,
    onDataChange
  } = options

  const refreshIntervalRef = useRef<NodeJS.Timeout | null>(null)
  const mountedRef = useRef(true)

  // 获取实时数据
  const fetchRealtimeData = useCallback(async () => {
    try {
      setLoading(true)
      setError(null)

      const result = await realTimeDataService.getRealtimeData()

      if (mountedRef.current) {
        setData(result.data)
        setLoading(false)

        if (onDataChange) {
          onDataChange(result.data)
        }
      }
    } catch (err) {
      const error = err as Error
      console.error('❌ 获取实时数据失败:', error)

      if (mountedRef.current) {
        setError(error)
        setLoading(false)

        if (onError) {
          onError(error)
        }
      }
    }
  }, [onError, onDataChange])

  // 刷新实时数据
  const refresh = useCallback(async () => {
    await fetchRealtimeData()
  }, [fetchRealtimeData])

  // 更新数据（实时数据不支持直接更新）
  const updateData = useCallback(async () => {
    throw new Error('实时数据不支持直接更新')
  }, [])

  // 初始化和自动刷新
  useEffect(() => {
    // 初始加载
    fetchRealtimeData()

    // 设置自动刷新
    if (autoRefresh) {
      refreshIntervalRef.current = setInterval(() => {
        fetchRealtimeData()
      }, refreshInterval)
    }

    // 清理函数
    return () => {
      if (refreshIntervalRef.current) {
        clearInterval(refreshIntervalRef.current)
      }
    }
  }, [autoRefresh, refreshInterval, fetchRealtimeData])

  // 组件卸载时清理
  useEffect(() => {
    return () => {
      mountedRef.current = false
    }
  }, [])

  return {
    data,
    loading,
    error,
    refresh,
    updateData
  }
}

/**
 * 学生数据Hook
 */
export function useStudents(filters: Record<string, any> = {}, options: UseRealTimeDataOptions = {}) {
  return useRealTimeData('students', filters, options)
}

/**
 * 用户数据Hook
 */
export function useUsers(filters: Record<string, any> = {}, options: UseRealTimeDataOptions = {}) {
  return useRealTimeData('users', filters, options)
}

/**
 * 评语数据Hook
 */
export function useComments(filters: Record<string, any> = {}, options: UseRealTimeDataOptions = {}) {
  return useRealTimeData('comments', filters, options)
}

/**
 * 班级数据Hook
 */
export function useClasses(filters: Record<string, any> = {}, options: UseRealTimeDataOptions = {}) {
  return useRealTimeData('classes', filters, options)
}

// 向后兼容的导出
export const useRealTimeActivities = useRealtimeData