import React from 'react'
import { ConfigProvider } from 'antd'
import { useAuthStore } from './stores/authStore'
import SimpleLogin from './SimpleLogin'
import zhCN from 'antd/locale/zh_CN'

const TestApp: React.FC = () => {
  const { isAuthenticated } = useAuthStore()
  
  if (!isAuthenticated) {
    return (
      <ConfigProvider locale={zhCN}>
        <SimpleLogin />
      </ConfigProvider>
    )
  }

  return (
    <ConfigProvider locale={zhCN}>
      <div style={{ 
        padding: '40px', 
        textAlign: 'center',
        background: '#f0f2f5',
        minHeight: '100vh'
      }}>
        <h1>🎉 登录成功！欢迎使用管理后台</h1>
        <p>这里将显示主要的管理界面</p>
        <button 
          onClick={() => useAuthStore.getState().logout()}
          style={{
            background: '#ff4d4f',
            color: 'white',
            border: 'none',
            padding: '10px 20px',
            borderRadius: '4px',
            cursor: 'pointer',
            marginTop: '20px'
          }}
        >
          退出登录
        </button>
      </div>
    </ConfigProvider>
  )
}

export default TestApp