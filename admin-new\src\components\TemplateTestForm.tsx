import React, { useState } from 'react'
import { Form, Input, Button, TextArea, Select, Card, message } from 'antd'
import { createPromptTemplate } from '../services/authApi'

const { TextArea } = Input

interface TemplateFormData {
  templateName: string
  templateType: string
  templateDescription: string
  templateContent: string
  enabled: boolean
}

const TemplateTestForm: React.FC = () => {
  const [form] = Form.useForm()
  const [loading, setLoading] = useState(false)
  
  const handleSubmit = async (values: TemplateFormData) => {
    setLoading(true)
    try {
      console.log('提交的模板数据:', values)
      
      const result = await createPromptTemplate({
        name: values.templateName,
        type: values.templateType,
        content: values.templateContent,
        description: values.templateDescription,
        enabled: values.enabled
      })
      
      if (result.success) {
        message.success('模板创建成功！')
        form.resetFields()
      } else {
        message.error('模板创建失败：' + result.error)
      }
    } catch (error) {
      console.error('创建模板失败:', error)
      message.error('创建失败，请检查网络连接')
    } finally {
      setLoading(false)
    }
  }
  
  const testTemplate = `你是一位特别有亲和力、善于用温暖话语鼓励学生的班主任。你的目光总能捕捉到每个孩子身上独特的闪光点。你的任务是根据提供的学生日常表现素材，为学生撰写一份充满善意与温情的学期综合评语。

学生姓名：
<student_name>
{{学生姓名}}
</student_name>

学生本学期的日常表现素材如下：
<performance_material>
{{行为记录}}
</performance_material>

请严格遵循以下要求撰写评语：
1. **评语结构**：采用"如沐春风的总体印象 + 令人欣喜的闪光点 + 温馨提示与小期待 + 美好的祝愿"的结构。
2. **内容要求**：
   * **闪光点部分**：从素材中提炼2-3个最暖心的优点，并结合具体的小事来描述，让表扬听起来格外真诚、亲切。
   * **温馨提示部分**：如果素材中有相关记录，请用"如果……就更好了"的句式，像聊天一样温柔地提出小建议。如果没有，则可以写一些对未来生活的美好期盼。
3. **语气风格**：语言要像邻家大姐姐/大哥哥一样，充满生活气息和真挚的情感。多用微笑、拥抱等词语，营造亲密的氛围。称呼以学生可爱的昵称或名字（不带姓，如小宇同学）开头。
4. **个性化**：评语必须紧密围绕素材，发掘学生独特的可爱之处，严禁使用生硬的套话，让学生感觉这份评语是专门为他/她写的"悄悄话"。
5. **日常行为记录缺失处理要求**：当表现素材为空，则只输出"老师还不太了解你，无法写出对你的心里话！"

请直接生成完整的评语内容，100-150字之间。`

  return (
    <Card title="提示词模板测试表单" style={{ margin: '20px' }}>
      <Form
        form={form}
        layout="vertical"
        onFinish={handleSubmit}
        initialValues={{
          templateName: '🤗 温暖亲切测试',
          templateType: 'warm',
          templateDescription: '语气温和，拉近师生距离，充满关爱的评语',
          templateContent: testTemplate,
          enabled: true
        }}
      >
        <Form.Item
          label="模板名称"
          name="templateName"
          rules={[{ required: true, message: '请输入模板名称' }]}
        >
          <Input placeholder="例如：积极鼓励型评语" />
        </Form.Item>

        <Form.Item
          label="模板类型"
          name="templateType"
          rules={[{ required: true, message: '请选择模板类型' }]}
        >
          <Select placeholder="请选择模板类型">
            <Select.Option value="gentle">温和亲切型</Select.Option>
            <Select.Option value="encouraging">鼓励激励型</Select.Option>
            <Select.Option value="detailed">详细具体型</Select.Option>
            <Select.Option value="comprehensive">综合发展型</Select.Option>
            <Select.Option value="formal">正式规范型</Select.Option>
          </Select>
        </Form.Item>

        <Form.Item
          label="模板描述"
          name="templateDescription"
          rules={[{ required: true, message: '请输入模板描述' }]}
        >
          <Input placeholder="请简要描述此模板的用途和特点" />
        </Form.Item>

        <Form.Item
          label="模板内容 (关键测试项)"
          name="templateContent"
          rules={[{ required: true, message: '请输入模板内容' }]}
        >
          <TextArea
            rows={12}
            placeholder="请输入提示词模板内容，可以使用 {{变量名}} 的形式定义变量..."
            style={{ fontFamily: 'monospace', fontSize: '13px' }}
          />
        </Form.Item>

        <Form.Item>
          <Button type="primary" htmlType="submit" loading={loading} size="large">
            测试创建模板
          </Button>
        </Form.Item>
      </Form>
    </Card>
  )
}

export default TemplateTestForm