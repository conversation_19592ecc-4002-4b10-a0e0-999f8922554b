<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据连通性测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }
        .container {
            background: white;
            border-radius: 12px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            margin-bottom: 20px;
        }
        h1 {
            color: #2c3e50;
            text-align: center;
            margin-bottom: 30px;
        }
        .success-banner {
            background: linear-gradient(135deg, #00b894, #00cec9);
            color: white;
            padding: 20px;
            border-radius: 8px;
            text-align: center;
            margin-bottom: 30px;
            font-weight: bold;
        }
        .test-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .test-card {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 20px;
            background: #f9f9f9;
        }
        .test-card h3 {
            margin-top: 0;
            color: #2c3e50;
        }
        button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
            transition: transform 0.2s;
        }
        button:hover { transform: translateY(-2px); }
        button:disabled { opacity: 0.6; cursor: not-allowed; transform: none; }
        
        .result {
            margin: 10px 0;
            padding: 15px;
            border-radius: 8px;
            font-family: monospace;
            font-size: 14px;
        }
        .success { background: #d4edda; color: #155724; border-left: 4px solid #28a745; }
        .error { background: #f8d7da; color: #721c24; border-left: 4px solid #dc3545; }
        .info { background: #d1ecf1; color: #0c5460; border-left: 4px solid #17a2b8; }
        
        .data-preview {
            background: #2c3e50;
            color: #ecf0f1;
            padding: 15px;
            border-radius: 6px;
            font-family: monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
            margin-top: 10px;
        }
        
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        .status-success { background: #28a745; }
        .status-error { background: #dc3545; }
        .status-warning { background: #ffc107; }
    </style>
</head>
<body>
    <div class="container">
        <div class="success-banner">
            🎉 数据连通性测试工具
            <br>测试管理后台与小程序的数据连通情况
        </div>
        
        <h1>📊 智能数据大屏连通测试</h1>
        
        <div style="text-align: center; margin: 30px 0;">
            <button onclick="testAllConnections()">测试所有连通点</button>
            <button onclick="testDashboardData()">测试仪表板数据</button>
            <button onclick="testRealtimeData()">测试实时动态</button>
            <button onclick="clearResults()">清空结果</button>
        </div>
        
        <div class="test-grid">
            <div class="test-card">
                <h3>📈 智能数据大屏</h3>
                <p>测试活跃教师用户、评语总数、tokens消耗等核心统计数据</p>
                <div id="dashboard-status">
                    <span class="status-indicator status-warning"></span>等待测试
                </div>
                <div id="dashboard-results"></div>
            </div>
            
            <div class="test-card">
                <h3>⚡ 实时动态数据</h3>
                <p>测试用户活动记录、评语生成记录、AI调用记录等实时数据</p>
                <div id="realtime-status">
                    <span class="status-indicator status-warning"></span>等待测试
                </div>
                <div id="realtime-results"></div>
            </div>
            
            <div class="test-card">
                <h3>🤖 AI配置连通</h3>
                <p>测试AI模型配置、提示词模板等配置数据的双向同步</p>
                <div id="ai-status">
                    <span class="status-indicator status-warning"></span>等待测试
                </div>
                <div id="ai-results"></div>
            </div>
            
            <div class="test-card">
                <h3>👥 数据管理连通</h3>
                <p>测试学生详情数据和评语详情数据的展示</p>
                <div id="data-status">
                    <span class="status-indicator status-warning"></span>等待测试
                </div>
                <div id="data-results"></div>
            </div>
        </div>
    </div>

    <div class="container">
        <h2>📝 测试日志</h2>
        <div id="logContainer" style="max-height: 400px; overflow-y: auto; background: #f8f9fa; padding: 15px; border-radius: 6px; font-family: monospace; font-size: 12px;">
            等待开始测试...
        </div>
    </div>

    <script>
        const API_URL = 'http://localhost:3000/admin'
        
        function log(message, type = 'info') {
            const logContainer = document.getElementById('logContainer')
            const timestamp = new Date().toLocaleTimeString()
            const prefix = type === 'error' ? '❌' : type === 'success' ? '✅' : type === 'warning' ? '⚠️' : 'ℹ️'
            logContainer.textContent += `[${timestamp}] ${prefix} ${message}\n`
            logContainer.scrollTop = logContainer.scrollHeight
        }
        
        function updateStatus(cardId, success, message) {
            const statusEl = document.getElementById(`${cardId}-status`)
            const indicator = statusEl.querySelector('.status-indicator')
            
            if (success) {
                indicator.className = 'status-indicator status-success'
                statusEl.innerHTML = `<span class="status-indicator status-success"></span>✅ ${message}`
            } else {
                indicator.className = 'status-indicator status-error'
                statusEl.innerHTML = `<span class="status-indicator status-error"></span>❌ ${message}`
            }
        }
        
        function addResult(cardId, message, type = 'info', data = null) {
            const resultsEl = document.getElementById(`${cardId}-results`)
            const div = document.createElement('div')
            div.className = `result ${type}`
            
            let content = message
            if (data) {
                content += `<div class="data-preview">${JSON.stringify(data, null, 2)}</div>`
            }
            
            div.innerHTML = content
            resultsEl.appendChild(div)
        }
        
        async function callAPI(action, data = {}) {
            try {
                const response = await fetch(API_URL, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        action,
                        ...data,
                        timestamp: Date.now(),
                        requestId: `test_${Date.now()}`
                    })
                })
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`)
                }
                
                const result = await response.json()
                
                if (result.code === 200) {
                    return { success: true, data: result.data }
                } else {
                    return { success: false, error: result.message }
                }
                
            } catch (error) {
                return { success: false, error: error.message }
            }
        }
        
        async function testDashboardData() {
            log('📈 开始测试智能数据大屏连通性...')
            
            try {
                const result = await callAPI('data.getDashboardStats')
                
                if (result.success) {
                    updateStatus('dashboard', true, '数据连通正常')
                    addResult('dashboard', '✅ 仪表板数据获取成功', 'success', result.data)
                    log(`✅ 仪表板数据: 用户${result.data.totalUsers}个, 评语${result.data.todayComments}条, AI调用${result.data.aiCalls}次`)
                } else {
                    updateStatus('dashboard', false, '数据连通失败')
                    addResult('dashboard', `❌ 获取失败: ${result.error}`, 'error')
                    log(`❌ 仪表板数据获取失败: ${result.error}`)
                }
            } catch (error) {
                updateStatus('dashboard', false, '连接异常')
                addResult('dashboard', `❌ 连接异常: ${error.message}`, 'error')
                log(`❌ 仪表板数据连接异常: ${error.message}`)
            }
        }
        
        async function testRealtimeData() {
            log('⚡ 开始测试实时动态数据连通性...')
            
            try {
                const result = await callAPI('data.getRecentActivities', { limit: 5 })
                
                if (result.success) {
                    updateStatus('realtime', true, '实时数据连通正常')
                    addResult('realtime', `✅ 实时动态获取成功，共${result.data.length}条记录`, 'success', result.data)
                    log(`✅ 实时动态数据: 获取${result.data.length}条活动记录`)
                } else {
                    updateStatus('realtime', false, '实时数据连通失败')
                    addResult('realtime', `❌ 获取失败: ${result.error}`, 'error')
                    log(`❌ 实时动态数据获取失败: ${result.error}`)
                }
            } catch (error) {
                updateStatus('realtime', false, '连接异常')
                addResult('realtime', `❌ 连接异常: ${error.message}`, 'error')
                log(`❌ 实时动态数据连接异常: ${error.message}`)
            }
        }
        
        async function testAIConfig() {
            log('🤖 开始测试AI配置连通性...')
            
            try {
                const result = await callAPI('ai.getModels')
                
                if (result.success) {
                    updateStatus('ai', true, 'AI配置连通正常')
                    addResult('ai', `✅ AI模型配置获取成功，共${result.data.length}个模型`, 'success', result.data)
                    log(`✅ AI配置数据: 获取${result.data.length}个AI模型配置`)
                } else {
                    updateStatus('ai', false, 'AI配置连通失败')
                    addResult('ai', `❌ 获取失败: ${result.error}`, 'error')
                    log(`❌ AI配置数据获取失败: ${result.error}`)
                }
            } catch (error) {
                updateStatus('ai', false, '连接异常')
                addResult('ai', `❌ 连接异常: ${error.message}`, 'error')
                log(`❌ AI配置数据连接异常: ${error.message}`)
            }
        }
        
        async function testDataManagement() {
            log('👥 开始测试数据管理连通性...')
            
            try {
                const [studentsResult, commentsResult] = await Promise.all([
                    callAPI('data.getStudents', { params: { limit: 5 } }),
                    callAPI('data.getRecords', { params: { limit: 5 } })
                ])
                
                if (studentsResult.success && commentsResult.success) {
                    updateStatus('data', true, '数据管理连通正常')
                    addResult('data', `✅ 学生数据: ${studentsResult.data.total}条记录`, 'success')
                    addResult('data', `✅ 评语数据: ${commentsResult.data.total}条记录`, 'success')
                    log(`✅ 数据管理: 学生${studentsResult.data.total}条, 评语${commentsResult.data.total}条`)
                } else {
                    updateStatus('data', false, '数据管理连通失败')
                    addResult('data', `❌ 获取失败`, 'error')
                    log(`❌ 数据管理连通失败`)
                }
            } catch (error) {
                updateStatus('data', false, '连接异常')
                addResult('data', `❌ 连接异常: ${error.message}`, 'error')
                log(`❌ 数据管理连接异常: ${error.message}`)
            }
        }
        
        async function testAllConnections() {
            log('🚀 开始测试所有数据连通点...')
            
            // 清空之前的结果
            document.querySelectorAll('[id$="-results"]').forEach(el => el.innerHTML = '')
            
            // 并行测试所有连通点
            await Promise.all([
                testDashboardData(),
                testRealtimeData(),
                testAIConfig(),
                testDataManagement()
            ])
            
            log('🎯 所有连通性测试完成！')
        }
        
        function clearResults() {
            document.getElementById('logContainer').textContent = '等待开始测试...'
            document.querySelectorAll('[id$="-results"]').forEach(el => el.innerHTML = '')
            document.querySelectorAll('[id$="-status"]').forEach(el => {
                const cardId = el.id.replace('-status', '')
                updateStatus(cardId.replace('-status', ''), null, '等待测试')
                el.innerHTML = '<span class="status-indicator status-warning"></span>等待测试'
            })
        }
        
        // 页面加载时自动开始测试
        window.addEventListener('load', () => {
            log('🔗 数据连通性测试工具已加载')
            log('💡 点击"测试所有连通点"开始全面测试')
        })
    </script>
</body>
</html>
