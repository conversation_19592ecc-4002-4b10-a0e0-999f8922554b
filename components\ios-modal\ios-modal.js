/**
 * iOS风格弹窗组件 - 高级简约设计
 */
Component({
  properties: {
    // 是否显示弹窗
    show: {
      type: Boolean,
      value: false
    },
    // 弹窗标题
    title: {
      type: String,
      value: ''
    },
    // 弹窗图标
    icon: {
      type: String,
      value: ''
    },
    // 描述文字
    description: {
      type: String,
      value: ''
    },
    // 列表项目
    items: {
      type: Array,
      value: []
    },
    // 是否显示复选标记
    showCheckmarks: {
      type: Boolean,
      value: false
    },
    // 特性说明
    features: {
      type: Array,
      value: []
    },
    // 是否显示取消按钮
    showCancel: {
      type: Boolean,
      value: true
    },
    // 取消按钮文字
    cancelText: {
      type: String,
      value: '取消'
    },
    // 确认按钮文字
    confirmText: {
      type: String,
      value: '确定'
    },
    // 点击遮罩是否关闭
    closeOnOverlay: {
      type: Boolean,
      value: true
    }
  },

  data: {},

  methods: {
    /**
     * 阻止事件冒泡
     */
    stopPropagation() {
      // 阻止点击弹窗内容时关闭弹窗
    },

    /**
     * 点击遮罩层
     */
    onOverlayTap() {
      if (this.properties.closeOnOverlay) {
        this.triggerEvent('cancel');
      }
    },

    /**
     * 点击列表项
     */
    onItemTap(e) {
      const { index } = e.currentTarget.dataset;
      this.triggerEvent('itemtap', { index, item: this.properties.items[index] });
    },

    /**
     * 点击取消按钮
     */
    onCancel() {
      this.triggerEvent('cancel');
    },

    /**
     * 点击确认按钮
     */
    onConfirm() {
      this.triggerEvent('confirm');
    }
  }
});
