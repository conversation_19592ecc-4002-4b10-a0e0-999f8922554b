/**
 * 统一模板系统类型定义
 * Ultra-Think架构重构 - 阶段1：数据模型统一
 */

export type TemplateType = 'formal' | 'warm' | 'encouraging' | 'detailed'

export type TemplateCategory = 'daily' | 'weekly' | 'monthly' | 'term'

export interface TemplateVariable {
  name: string          // 变量名：如 'studentName'
  type: 'string' | 'text' | 'number'
  required: boolean     // 是否必填
  description: string   // 变量说明
  placeholder?: string  // 占位符文本
}

export interface TemplateMetadata {
  author: string        // 创建者
  description: string   // 模板描述
  tags: string[]       // 标签分类
  usageCount: number   // 使用次数
  lastUsedAt?: string  // 最后使用时间
}

/**
 * 统一模板数据结构
 * 兼容现有的四套不同格式
 */
export interface UnifiedTemplate {
  id: string                    // 统一ID格式，如 'formal_v1'
  name: string                 // 显示名称：如 '正式评语模板'
  type: TemplateType          // 标准化类型枚举
  category: TemplateCategory  // 分类：daily/weekly/monthly/term
  content: string             // 完整模板内容
  variables: TemplateVariable[] // 变量定义
  metadata: TemplateMetadata  // 元数据
  version: number             // 版本控制
  checksum: string           // 内容校验和
  enabled: boolean           // 启用状态
  timestamps: {              // 时间戳
    createdAt: string        // ISO格式
    updatedAt: string        // ISO格式
    lastUsedAt?: string      // 最后使用时间
  }
}

/**
 * 模板使用统计
 */
export interface TemplateUsageStats {
  templateId: string
  usageCount: number
  successRate: number
  averageRating: number
  lastMonth: {
    usageCount: number
    successCount: number
  }
}

/**
 * 模板版本信息
 */
export interface TemplateVersion {
  id: string
  templateId: string
  version: number
  content: string
  checksum: string
  changeLog: string
  createdAt: string
  createdBy: string
}

/**
 * 兼容性映射：现有格式 → 统一格式
 */
export interface LegacyCompatibility {
  // 小程序硬编码格式
  legacyType: string
  unifiedId: string
  migrationRules: {
    contentKey: string      // 内容字段映射
    variableFormat: string  // 变量格式转换
  }
}

/**
 * 模板缓存策略
 */
export interface TemplateCacheConfig {
  level1: 'memory'      // 内存缓存
  level2: 'localStorage' // 本地存储
  level3: 'cloudCache'  // 云端缓存
  fallback: 'hardcoded' // 硬编码兜底
  duration: number      // 缓存时长（毫秒）
  maxSize: number      // 最大缓存条目
}

/**
 * 模板验证规则
 */
export interface TemplateValidation {
  requiredFields: string[]
  requiredVariables: string[]
  contentMinLength: number
  contentMaxLength: number
  forbiddenPatterns: RegExp[]
}

/**
 * 模板API响应格式
 */
export interface TemplateAPIResponse<T = UnifiedTemplate[]> {
  success: boolean
  data: T
  message?: string
  error?: string
  metadata?: {
    total: number
    page: number
    pageSize: number
    hasMore: boolean
  }
}

/**
 * 模板更新请求
 */
export interface TemplateUpdateRequest {
  id: string
  content?: string
  name?: string
  enabled?: boolean
  metadata?: Partial<TemplateMetadata>
  changeLog: string
}

/**
 * 模板创建请求
 */
export interface TemplateCreateRequest {
  name: string
  type: TemplateType
  category: TemplateCategory
  content: string
  variables: TemplateVariable[]
  metadata: TemplateMetadata
}

/**
 * 默认配置常量
 */
export const TEMPLATE_CONSTANTS = {
  DEFAULT_CACHE_DURATION: 30 * 60 * 1000, // 30分钟
  MAX_TEMPLATE_SIZE: 10 * 1024, // 10KB
  MIN_TEMPLATE_SIZE: 100, // 100字符
  SUPPORTED_VARIABLES: [
    'studentName',
    'performanceMaterial',
    'className',
    'subjectName',
    'timeRange'
  ],
  VERSION_INCREMENT: 1,
  DEFAULT_CATEGORY: 'term' as TemplateCategory
}

/**
 * 兼容性映射表
 */
export const LEGACY_MAPPING: Record<string, LegacyCompatibility> = {
  'formal': {
    legacyType: 'formal',
    unifiedId: 'formal_v1',
    migrationRules: {
      contentKey: 'content',
      variableFormat: '{{{variable}}}'
    }
  },
  'warm': {
    legacyType: 'warm',
    unifiedId: 'warm_v1',
    migrationRules: {
      contentKey: 'content',
      variableFormat: '{{{variable}}}'
    }
  },
  'encouraging': {
    legacyType: 'encouraging',
    unifiedId: 'encouraging_v1',
    migrationRules: {
      contentKey: 'content',
      variableFormat: '{{{variable}}}'
    }
  },
  'detailed': {
    legacyType: 'detailed',
    unifiedId: 'detailed_v1',
    migrationRules: {
      contentKey: 'content',
      variableFormat: '{{{variable}}}'
    }
  }
}

/**
 * 模板验证规则
 */
export const TEMPLATE_VALIDATION: TemplateValidation = {
  requiredFields: ['id', 'name', 'type', 'content'],
  requiredVariables: ['studentName', 'performanceMaterial'],
  contentMinLength: 100,
  contentMaxLength: 5000,
  forbiddenPatterns: [
    /<script.*?>.*?<\/script>/gi,  // 防止脚本注入
    /javascript:/gi,               // 防止javascript伪协议
    /on\w+\s*=/gi                 // 防止事件处理器
  ]
}