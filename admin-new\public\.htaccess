# CSP安全策略配置
<IfModule mod_headers.c>
    # 开发环境CSP策略（更宽松，支持HMR）
    Header always set Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-eval' 'unsafe-inline' http://localhost:* ws://localhost:*; object-src 'none'; style-src 'self' 'unsafe-inline'; img-src 'self' data: blob:; font-src 'self' data:; connect-src 'self' http://localhost:* ws://localhost:* https://*.tcb.qcloud.la https://*.tencentcloudapi.com; frame-ancestors 'none'; base-uri 'self'; form-action 'self';"
</IfModule>