/**
 * AI智能评语系统 - 四种精确匹配的提示词模板
 * 完全对应小程序的四种评语风格
 */

// 积极鼓励型模板 - 基于提供的原始模板，确保字段完全匹配
const positiveEncouragingTemplate = `你是一位拥有15年教龄、经验丰富、充满爱心且善于观察的中职班主任。你的任务是根据提供的学生日常表现素材，为学生撰写一份全面、客观、个性化、充满关怀的学期综合评语。

学生姓名：<student_name>{{studentName}}</student_name>

学生本学期的日常表现素材如下：<performance_material>{{behaviorTags}}</performance_material>

请严格遵循以下要求撰写评语：

1. **评语结构**：采用"总体评价 + 优点详述 + 待改进点与期望 + 结尾祝福"的结构。

2. **内容要求**：
    - **优点详述部分**：必须从素材中提炼2 - 3个最突出的优点，并引用具体事例来支撑，让表扬不空洞。
    - **待改进点部分**：如果素材中有相关记录，请用委婉、鼓励的语气指出，并提出具体、可行的建议。如果没有，则可以写一些普遍性的鼓励和期望。

3. **语气风格**：语言要真诚、平实、温暖，既要体现中职老师的专业性，又要充满人文关怀，让学生和家长感受到被尊重和关爱。称呼以学生名字（不带姓，如小华同学）开头，文字风格要符合高中阶段学生的年龄阶段。

4. **个性化**：评语必须尊重、保留且紧密结合提供的素材，体现出每个学生的独特性，严禁使用千篇一律的套话，严禁捏造素材中不存在的内容。

5. **日常行为记录缺失处理要求**：当表现素材为空，则只输出"无行为依据，无法生评语！"

请直接生成完整的评语内容，100-150字之间。`

// 严肃权威型模板
const strictAuthorityTemplate = `您是一位拥有15年教龄、严格规范、深孚众望的中职班主任。请根据本学期该学生行为记录，撰写权威教育性评语。

学生姓名：<student_name><tag1>{{studentName}}</tag1></student_name>

本学期学生行为记录：<performance_material><tag2>{{behaviorTags}}</tag2></performance_material>

编写标准：
1. 大纲结构：事实陈述→纪律重申→改正要求→未来规范
2. 内容标准：
   - 错误事实：根据记录准确描述问题
   - 纪律依据：说明违反何规定，要求何行为
   - 改正措施：明确改正方法和时限
   - 行为规范：确立未来行为预期
3. 用语要求：断言语气、严肃措辞、定义明确、不带商量
4. 个性要求：严格基于记录，决不虚构内容
5. 数据缺失：如<b0><?>{{behaviorTags}}</b0>为空，输出"无记录无需评价""

请产生权威评语，字数100-150字
`

// 幽默亲切型模板
const humorousTemplate = `您是一位拥有15年教龄、幽默风趣、深受学生喜爱的"班姐姐"班主任。请用年轻人的语言风格，为学生写一份欢乐评语。

学生姓名：<student_name><i>{{studentName}}</i></student_name>

学生趣事记录：<performance_material><b>{{behaviorTags}}</b></performance_material>

写作要点：
1. 文章结构：开场互动→趣事分享→姜辣提醒→暖心祝福
2. 内容要点：
   - 网络对话：用网络热梗描述有趣行为
   - 真实趣事：使用记录原事，引发共鸣
   - 强调建议：温馨提醒用意，用"表情包"式语言
   - 温暖鼓励：营造轻松氛围，给予正能量
3. 语言风格：网络热梗、口语表达、朋友语气、亲密不沉重
4. 个性要求：紧贴实际、用幽默包装真实、体现学生特点
5. 记录缺失：<b1>{{behaviorTags}}</b1>为空时输入"缺少欢乐素材，生成失败[旺财狗头]"

生成风趣友好的评语内容，字数100-150字
`

请产生客观中性评语，字数100-150字

// 风格映射
const templates = {
  positiveEncouraging: positiveEncouragingTemplate,
  strictAuthority: strictAuthorityTemplate,
  humorousFriendly: humorousTemplate,
  objectiveNeutral: neutralTemplate
};

// 中文风格映射
const styleMapping = {
  '积极鼓励型': 'positiveEncouraging',
  '严肃权威型': 'strictAuthority',
  '幽默亲切型': 'humorousFriendly',
  '客观中性型': 'objectiveNeutral'
};

// 实用工具函数
module.exports = {
  templates,
  styleMapping,
  
  // 根据风格名称获取模板
  getTemplateByStyle: (styleName) => {
    const mappedStyle = styleMapping[styleName];
    if (mappedStyle && templates[mappedStyle]) {
      return templates[mappedStyle];
    }
    // 默认返回积极鼓励型
    return templates.positiveEncouraging;
  },
  
  // 获取所有支持的风格
  getAllStyles: () => Object.keys(styleMapping),
  
  // 获取模板详细信息
  getTemplateInfo: (styleName) => {
    const mappedStyle = styleMapping[styleName];
    if (mappedStyle && templates[mappedStyle]) {
      return {
        style: styleName,
        templateKey: mappedStyle,
        prompt: templates[mappedStyle]
      };
    }
    return null;
  },
  
  // 将学生数据注入模板
  injectDataIntoTemplate: (template, data) => {
    let processedTemplate = template;
    
    // 替换学生姓名
    if (data.studentName) {
      processedTemplate = processedTemplate.replace(/\{\{studentName\}\}/g, data.studentName);
    }
    
    // 替换行为素材
    const behaviorContent = data.behaviorTags || data.records || data.performance || '';
    processedTemplate = processedTemplate.replace(/\{\{behaviorTags\}\}/g, behaviorContent);
    
    // 处理节日礼貌
    const baseEnd = processedTemplate.split('\n').pop();
    if (!baseEnd?.includes('节日') && data.studentName) {
      const currentMonth = new Date().getMonth() + 1;
      let season = '';
      if ([9,10,11].includes(currentMonth)) season = '秋季';
      else if ([12,1,2].includes(currentMonth)) season = '冬季';
      else if ([3,4,5].includes(currentMonth)) season = '春季';
      else season = '夏季';
      
      processedTemplate += `\n祝${data.studentName}.${season}平安健康，学业进步！`;
    }
    
    return processedTemplate;
  }
};