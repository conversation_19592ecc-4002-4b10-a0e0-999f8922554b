<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>云函数直接调用测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }
        .container {
            background: white;
            border-radius: 12px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            margin-bottom: 20px;
        }
        h1 { color: #2c3e50; text-align: center; margin-bottom: 30px; }
        .warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }
        .info {
            background: #e8f4fd;
            border: 1px solid #bee5eb;
            color: #0c5460;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }
        .success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }
        button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
            transition: transform 0.2s;
        }
        button:hover { transform: translateY(-2px); }
        button:disabled { opacity: 0.6; cursor: not-allowed; transform: none; }
        .result {
            margin: 10px 0;
            padding: 15px;
            border-radius: 8px;
            font-family: monospace;
            font-size: 14px;
        }
        .result.success { background: #d4edda; color: #155724; border-left: 4px solid #28a745; }
        .result.error { background: #f8d7da; color: #721c24; border-left: 4px solid #dc3545; }
        .result.info { background: #d1ecf1; color: #0c5460; border-left: 4px solid #17a2b8; }
        .data-preview {
            background: #2c3e50;
            color: #ecf0f1;
            padding: 15px;
            border-radius: 6px;
            font-family: monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
            margin-top: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>☁️ 云函数直接调用测试</h1>
        
        <div class="warning">
            <h3>⚠️ 重要说明</h3>
            <p>由于HTTP触发器配置问题，我们改用云函数直接调用的方式。</p>
            <p><strong>此方法需要在小程序环境中运行，或者需要配置云开发SDK。</strong></p>
        </div>

        <div class="info">
            <h3>📋 解决方案选择</h3>
            <p>请选择以下解决方案之一：</p>
            <ol>
                <li><strong>方案A：</strong>升级微信开发者工具到最新版本</li>
                <li><strong>方案B：</strong>在腾讯云控制台手动配置HTTP触发器</li>
                <li><strong>方案C：</strong>继续使用本地API服务器</li>
                <li><strong>方案D：</strong>部署到其他云服务平台</li>
            </ol>
        </div>

        <div style="text-align: center; margin: 30px 0;">
            <button onclick="testLocalAPI()">测试本地API</button>
            <button onclick="checkCloudSDK()">检查云开发SDK</button>
            <button onclick="showSolutions()">显示详细解决方案</button>
        </div>
        
        <div id="results"></div>
    </div>

    <div class="container">
        <h2>📝 操作日志</h2>
        <div id="logContainer" style="max-height: 300px; overflow-y: auto; background: #f8f9fa; padding: 15px; border-radius: 6px; font-family: monospace; font-size: 12px;">
            等待开始测试...
        </div>
    </div>

    <script>
        function log(message, type = 'info') {
            const logContainer = document.getElementById('logContainer')
            const timestamp = new Date().toLocaleTimeString()
            const prefix = type === 'error' ? '❌' : type === 'success' ? '✅' : type === 'warning' ? '⚠️' : 'ℹ️'
            logContainer.textContent += `[${timestamp}] ${prefix} ${message}\n`
            logContainer.scrollTop = logContainer.scrollHeight
        }

        function addResult(message, type = 'info', data = null) {
            const results = document.getElementById('results')
            const div = document.createElement('div')
            div.className = `result ${type}`
            
            let content = message
            if (data) {
                content += `<div class="data-preview">${JSON.stringify(data, null, 2)}</div>`
            }
            
            div.innerHTML = content
            results.appendChild(div)
        }

        async function testLocalAPI() {
            log('🔍 测试本地API服务器连通性...')
            
            try {
                const response = await fetch('http://localhost:3000/admin', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        action: 'healthCheck',
                        timestamp: Date.now()
                    })
                })

                if (response.ok) {
                    const result = await response.json()
                    log('✅ 本地API服务器连通正常')
                    addResult('✅ 本地API服务器工作正常，建议继续使用', 'success', result)
                } else {
                    log('❌ 本地API服务器响应异常')
                    addResult('❌ 本地API服务器响应异常，请检查服务器状态', 'error')
                }
            } catch (error) {
                log('❌ 本地API服务器连接失败')
                addResult('❌ 本地API服务器未启动，请先启动本地服务器', 'error')
            }
        }

        function checkCloudSDK() {
            log('🔍 检查云开发SDK环境...')
            
            if (typeof wx !== 'undefined' && wx.cloud) {
                log('✅ 检测到微信小程序环境')
                addResult('✅ 检测到微信小程序环境，可以使用云函数直接调用', 'success')
            } else if (typeof window !== 'undefined' && window.location.protocol === 'file:') {
                log('⚠️ 当前在本地文件环境中')
                addResult('⚠️ 当前在本地文件环境中，无法使用云函数直接调用', 'info')
            } else {
                log('❌ 非小程序环境，无法使用云开发SDK')
                addResult('❌ 非小程序环境，无法使用云开发SDK直接调用云函数', 'error')
            }
        }

        function showSolutions() {
            log('📋 显示详细解决方案...')
            
            const solutions = `
<h3>🛠️ 详细解决方案</h3>

<h4>方案A：升级微信开发者工具</h4>
<ul>
    <li>下载最新版微信开发者工具</li>
    <li>重新打开项目</li>
    <li>查看云开发控制台是否有HTTP触发器选项</li>
</ul>

<h4>方案B：腾讯云控制台配置</h4>
<ul>
    <li>访问：https://console.cloud.tencent.com/</li>
    <li>进入云开发 → 环境：cloud1-4g85f8xlb8166ff1</li>
    <li>云函数 → adminAPI_v2 → 触发器管理</li>
    <li>添加HTTP触发器，路径：/admin-v2</li>
</ul>

<h4>方案C：继续使用本地服务器（推荐）</h4>
<ul>
    <li>保持本地API服务器运行</li>
    <li>管理后台继续使用 http://localhost:3000/admin</li>
    <li>后续可以将本地服务器部署到云服务器</li>
</ul>

<h4>方案D：部署到其他平台</h4>
<ul>
    <li>腾讯云函数SCF</li>
    <li>阿里云函数计算</li>
    <li>华为云FunctionGraph</li>
    <li>Vercel、Netlify等</li>
</ul>
            `
            
            addResult(solutions, 'info')
            log('📋 解决方案已显示')
        }

        // 页面加载时的检查
        window.addEventListener('load', () => {
            log('☁️ 云函数测试工具已加载')
            log('💡 由于HTTP触发器配置问题，建议继续使用本地API服务器')
            
            // 自动检查环境
            setTimeout(() => {
                checkCloudSDK()
                testLocalAPI()
            }, 1000)
        })
    </script>
</body>
</html>
