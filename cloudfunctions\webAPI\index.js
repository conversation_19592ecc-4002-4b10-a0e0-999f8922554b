/**
 * Web管理后台专用API - 绕过权限验证
 */

const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()

// 简单的健康检查和基础API
exports.main = async (event, context) => {
  console.log('WebAPI收到请求:', event)
  
  // 设置CORS
  const headers = {
    'Content-Type': 'application/json',
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
    'Access-Control-Allow-Headers': 'Content-Type'
  }
  
  // 处理OPTIONS预检请求
  if (event.httpMethod === 'OPTIONS') {
    return {
      statusCode: 200,
      headers,
      body: ''
    }
  }
  
  try {
    let body = {}
    if (event.httpMethod === 'POST' && event.body) {
      body = typeof event.body === 'string' ? JSON.parse(event.body) : event.body
    }
    
    const action = body.action || event.queryStringParameters?.action || 'healthCheck'
    
    let result
    
    switch (action) {
      case 'healthCheck':
        result = {
          code: 200,
          message: '服务正常',
          data: {
            timestamp: Date.now(),
            version: '3.0.0',
            env: cloud.DYNAMIC_CURRENT_ENV
          }
        }
        break
        
      case 'getStats':
        // 获取简单统计
        const [admins, students, classes, comments] = await Promise.all([
          db.collection('admins').count(),
          db.collection('students').count(), 
          db.collection('classes').count(),
          db.collection('comments').count()
        ])
        
        result = {
          code: 200,
          message: '获取统计成功',
          data: {
            adminCount: admins.total,
            studentCount: students.total,
            classCount: classes.total,
            commentCount: comments.total
          }
        }
        break
        
      case 'login':
        // 简单的登录验证
        const { username, password } = body
        if (username === 'admin' && password === 'admin123') {
          result = {
            code: 200,
            message: '登录成功',
            data: {
              token: 'web_admin_token',
              username: 'admin',
              role: 'admin'
            }
          }
        } else {
          result = {
            code: 401,
            message: '用户名或密码错误'
          }
        }
        break
        
      default:
        result = {
          code: 400,
          message: `未知操作: ${action}`
        }
    }
    
    return {
      statusCode: result.code === 200 ? 200 : 400,
      headers,
      body: JSON.stringify(result)
    }
    
  } catch (error) {
    console.error('WebAPI错误:', error)
    
    return {
      statusCode: 500,
      headers,
      body: JSON.stringify({
        code: 500,
        message: '服务器错误',
        error: error.message
      })
    }
  }
}