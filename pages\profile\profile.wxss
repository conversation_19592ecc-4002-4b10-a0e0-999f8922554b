/* 个人中心页面样式 - 莫兰迪科技风格 */
.profile-page {
  background: linear-gradient(135deg, #F0F2F5 0%, #E4E7ED 100%);
  min-height: 100vh;
}

/* 用户信息区域 */
.user-section {
  background: linear-gradient(135deg, #5470C6 0%, #73C0DE 100%);
  padding: 80rpx 32rpx 60rpx;
  display: flex;
  align-items: center;
  gap: 32rpx;
  box-shadow: 0 8rpx 32rpx rgba(84, 112, 198, 0.2);
}

.user-avatar {
  width: 140rpx;
  height: 140rpx;
  position: relative;
}

.avatar-edit {
  position: absolute;
  bottom: 4rpx;
  right: 4rpx;
  width: 36rpx;
  height: 36rpx;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.avatar-image {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  border: 4rpx solid rgba(255, 255, 255, 0.8);
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.1);
}

.avatar-placeholder {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  border: 4rpx solid rgba(255, 255, 255, 0.8);
  font-size: 48rpx;
  color: white;
  font-weight: 600;
}

.user-info {
  flex: 1;
}

.user-name {
  font-size: 40rpx;
  font-weight: 700;
  color: white;
  margin-bottom: 12rpx;
  text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.user-subtitle {
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.9);
  font-weight: 500;
}

.quick-edit {
  display: flex;
  align-items: center;
  gap: 12rpx;
  padding: 16rpx 20rpx;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 24rpx;
  transition: all 0.3s ease;
  backdrop-filter: blur(10rpx);
}

.quick-edit:active {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(0.95);
}

.edit-text {
  font-size: 26rpx;
  color: white;
  font-weight: 600;
}

/* 数据概览 */
.overview-section {
  margin: 32rpx;
  margin-bottom: 40rpx;
}

.section-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #2C3E50;
  margin-bottom: 32rpx;
  text-align: left;
  letter-spacing: 1rpx;
}

.overview-grid {
  background: rgba(255, 255, 255, 0.85);
  border-radius: 32rpx;
  padding: 48rpx 32rpx;
  display: flex;
  justify-content: space-between;
  box-shadow: 0 4rpx 20rpx rgba(84, 112, 198, 0.1);
  border: 1rpx solid rgba(84, 112, 198, 0.08);
  backdrop-filter: blur(10rpx);
}

.overview-item {
  text-align: center;
  flex: 1;
}

.overview-number {
  font-size: 64rpx;
  font-weight: 600;
  color: #5470C6;
  margin-bottom: 16rpx;
  line-height: 1;
  text-shadow: 0 2rpx 8rpx rgba(84, 112, 198, 0.15);
}

.overview-label {
  font-size: 26rpx;
  color: #606266;
  font-weight: 500;
  letter-spacing: 1rpx;
}

/* 功能导航 */
.function-section {
  margin: 32rpx;
  margin-bottom: 40rpx;
}

.function-grid {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

.function-card {
  background: rgba(255, 255, 255, 0.85);
  border-radius: 24rpx;
  padding: 32rpx;
  display: flex;
  align-items: center;
  gap: 24rpx;
  box-shadow: 0 4rpx 20rpx rgba(84, 112, 198, 0.1);
  border: 1rpx solid rgba(84, 112, 198, 0.08);
  backdrop-filter: blur(10rpx);
  transition: all 0.3s ease;
}

.function-card:active {
  transform: translateY(-2rpx);
  box-shadow: 0 6rpx 24rpx rgba(84, 112, 198, 0.15);
}

.function-icon {
  width: 88rpx;
  height: 88rpx;
  border-radius: 24rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 40rpx;
  color: white;
  font-weight: 600;
}

.class-icon {
  background: linear-gradient(135deg, #5470C6 0%, #73C0DE 100%);
  box-shadow: 0 6rpx 16rpx rgba(84, 112, 198, 0.3);
}

.student-icon {
  background: linear-gradient(135deg, #91CC75 0%, #73C0DE 100%);
  box-shadow: 0 6rpx 16rpx rgba(145, 204, 117, 0.3);
}

.record-icon {
  background: linear-gradient(135deg, #FAC858 0%, #FC8452 100%);
  box-shadow: 0 6rpx 16rpx rgba(250, 200, 88, 0.3);
}

.comment-icon {
  background: linear-gradient(135deg, #EE6666 0%, #FC8452 100%);
  box-shadow: 0 6rpx 16rpx rgba(238, 102, 102, 0.3);
}

.analytics-icon {
  background: linear-gradient(135deg, #9C27B0 0%, #BA68C8 100%);
  box-shadow: 0 6rpx 16rpx rgba(156, 39, 176, 0.3);
}

.function-info {
  flex: 1;
}

.function-name {
  font-size: 34rpx;
  font-weight: 600;
  color: #2C3E50;
  margin-bottom: 12rpx;
}

.function-desc {
  font-size: 28rpx;
  color: #606266;
  font-weight: 400;
}

/* 关于信息 */
.about-section {
  margin: 32rpx;
  background: rgba(255, 255, 255, 0.85);
  border-radius: 32rpx;
  padding: 48rpx 32rpx;
  box-shadow: 0 4rpx 20rpx rgba(84, 112, 198, 0.1);
  border: 1rpx solid rgba(84, 112, 198, 0.08);
  backdrop-filter: blur(10rpx);
}

.about-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid rgba(84, 112, 198, 0.1);
}

.about-item:last-child {
  border-bottom: none;
}

.about-label {
  font-size: 30rpx;
  color: #2C3E50;
  font-weight: 500;
}

.about-value {
  font-size: 28rpx;
  color: #606266;
  font-weight: 400;
}

/* 退出登录 */
.logout-section {
  margin: 48rpx 32rpx 32rpx;
}

.logout-button {
  background: rgba(255, 255, 255, 0.85);
  border-radius: 24rpx;
  padding: 32rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 16rpx;
  box-shadow: 0 4rpx 20rpx rgba(238, 102, 102, 0.15);
  border: 1rpx solid rgba(238, 102, 102, 0.2);
  backdrop-filter: blur(10rpx);
  transition: all 0.3s ease;
}

.logout-button:active {
  transform: scale(0.98);
  background: rgba(238, 102, 102, 0.05);
  border-color: #EE6666;
}

.logout-text {
  font-size: 30rpx;
  color: #EE6666;
  font-weight: 600;
}

/* 工具箱 */
.tools-section {
  margin: 32rpx;
  margin-bottom: 60rpx;
}

.tools-list {
  background: rgba(255, 255, 255, 0.85);
  border-radius: 32rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 20rpx rgba(84, 112, 198, 0.1);
  border: 1rpx solid rgba(84, 112, 198, 0.08);
  backdrop-filter: blur(10rpx);
}

.tool-item {
  display: flex;
  align-items: center;
  padding: 32rpx;
  gap: 20rpx;
  border-bottom: 1rpx solid rgba(84, 112, 198, 0.1);
  transition: all 0.3s ease;
}

.tool-item:last-child {
  border-bottom: none;
}

.tool-item:active {
  background: rgba(84, 112, 198, 0.05);
}

.tool-name {
  font-size: 30rpx;
  color: #2C3E50;
  font-weight: 600;
  flex: 1;
}

.tool-desc {
  font-size: 26rpx;
  color: #909399;
  margin-right: 12rpx;
  font-weight: 400;
}

/* 快速编辑弹窗 */
.quick-edit-popup {
  padding: 32rpx;
  height: 100%;
  display: flex;
  flex-direction: column;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20rpx);
}

.popup-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 40rpx;
  padding-bottom: 32rpx;
  border-bottom: 1rpx solid rgba(84, 112, 198, 0.1);
}

.popup-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #2C3E50;
}

.quick-edit-form {
  flex: 1;
  margin-bottom: 40rpx;
}

.popup-actions {
  display: flex;
  gap: 20rpx;
  padding-top: 32rpx;
  border-top: 1rpx solid rgba(84, 112, 198, 0.1);
}

.cancel-btn {
  flex: 1;
  background: rgba(84, 112, 198, 0.1) !important;
  color: #5470C6 !important;
}

.save-btn {
  flex: 2;
  background: linear-gradient(135deg, #5470C6 0%, #73C0DE 100%) !important;
  color: white !important;
}