<!--
  数据分析概览页面
-->
<view class="analytics-overview-page">
  <!-- 时间筛选器 -->
  <view class="time-filter-section">
    <view class="filter-tabs">
      <view
        wx:for="{{timeFilters}}"
        wx:key="value"
        class="filter-tab {{selectedTimeFilter === item.value ? 'active' : ''}}"
        data-value="{{item.value}}"
        bindtap="selectTimeFilter"
      >
        {{item.name}}
      </view>
    </view>
  </view>

  <!-- 核心指标卡片 -->
  <view class="metrics-section">
    <view class="section-title">核心指标</view>
    <view class="metrics-grid">
      <view class="metric-card">
        <view class="metric-icon total">
          <van-icon name="friends-o" size="20px" color="#fff" />
        </view>
        <view class="metric-content">
          <view class="metric-number">{{metrics.totalStudents}}</view>
          <view class="metric-label">学生总数</view>
          <view class="metric-change positive">
            <van-icon name="arrow-up" size="12px" color="#52C873" />
            <text>+{{metrics.studentGrowth}}%</text>
          </view>
        </view>
      </view>

      <view class="metric-card">
        <view class="metric-icon records">
          <van-icon name="edit" size="20px" color="#fff" />
        </view>
        <view class="metric-content">
          <view class="metric-number">{{metrics.totalRecords}}</view>
          <view class="metric-label">记录总数</view>
          <view class="metric-change positive">
            <van-icon name="arrow-up" size="12px" color="#52C873" />
            <text>+{{metrics.recordGrowth}}%</text>
          </view>
        </view>
      </view>

      <view class="metric-card">
        <view class="metric-icon score">
          <van-icon name="star-o" size="20px" color="#fff" />
        </view>
        <view class="metric-content">
          <view class="metric-number">{{metrics.avgScore}}</view>
          <view class="metric-label">平均评分</view>
          <view class="metric-change {{metrics.scoreChange >= 0 ? 'positive' : 'negative'}}">
            <van-icon name="{{metrics.scoreChange >= 0 ? 'arrow-up' : 'arrow-down'}}" size="12px" color="{{metrics.scoreChange >= 0 ? '#52C873' : '#FF5247'}}" />
            <text>{{metrics.scoreChange >= 0 ? '+' : ''}}{{metrics.scoreChange}}%</text>
          </view>
        </view>
      </view>

      <view class="metric-card">
        <view class="metric-icon comments">
          <van-icon name="chat-o" size="20px" color="#fff" />
        </view>
        <view class="metric-content">
          <view class="metric-number">{{metrics.totalComments}}</view>
          <view class="metric-label">生成评语</view>
          <view class="metric-change positive">
            <van-icon name="arrow-up" size="12px" color="#52C873" />
            <text>+{{metrics.commentGrowth}}%</text>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 趋势图表 -->
  <view class="chart-section">
    <view class="section-title">记录趋势</view>
    <view class="chart-container">
      <view wx:if="{{showChart}}" class="trend-chart">
        <!-- 图表标题 -->
        <view class="chart-header">
          <view class="chart-title">{{selectedTimeFilter === 'week' ? '最近7天' : selectedTimeFilter === 'month' ? '最近30天' : selectedTimeFilter === 'semester' ? '最近6个月' : '最近12个月'}}记录数量</view>
          <view class="chart-max">最高: {{recordTrend.maxValue}}条</view>
        </view>

        <!-- 简易柱状图 -->
        <view class="chart-bars">
          <view
            wx:for="{{recordTrend.data}}"
            wx:key="index"
            class="chart-bar"
            style="height: {{item / recordTrend.maxValue * 100}}%"
          >
            <view class="bar-value">{{item}}</view>
          </view>
        </view>

        <!-- X轴标签 -->
        <view class="chart-labels">
          <view
            wx:for="{{recordTrend.labels}}"
            wx:key="index"
            class="chart-label"
          >{{item}}</view>
        </view>
      </view>

      <view wx:else class="chart-placeholder">
        <van-icon name="bar-chart-o" size="48px" color="#ddd" />
        <view class="chart-text">加载中...</view>
      </view>
    </view>
  </view>

  <!-- 行为分布 -->
  <view class="behavior-section">
    <view class="section-title">行为分布</view>
    <view class="behavior-stats">
      <view
        wx:for="{{behaviorStats}}"
        wx:key="type"
        class="behavior-item"
      >
        <view class="behavior-icon {{item.type}}">
          <van-icon name="{{item.icon}}" size="16px" color="#fff" />
        </view>
        <view class="behavior-content">
          <view class="behavior-name">{{item.name}}</view>
          <view class="behavior-count">{{item.count}}次</view>
        </view>
        <view class="behavior-percentage">{{item.percentage}}%</view>
      </view>
    </view>
  </view>

  <!-- 班级排行 -->
  <view class="ranking-section">
    <view class="section-title">班级表现排行</view>
    <view class="ranking-list">
      <view
        wx:for="{{classRanking}}"
        wx:key="id"
        class="ranking-item"
        data-class="{{item}}"
        bindtap="goToClassDetail"
      >
        <view class="ranking-number {{index < 3 ? 'top' : ''}}">
          {{index + 1}}
        </view>
        <view class="ranking-content">
          <view class="class-name">{{item.name}}</view>
          <view class="class-stats">
            <text class="stat-item">{{item.studentCount}}人</text>
            <text class="stat-item">{{item.recordCount}}记录</text>
          </view>
        </view>
        <view class="ranking-score">{{item.avgScore}}</view>
      </view>
    </view>
  </view>
</view>