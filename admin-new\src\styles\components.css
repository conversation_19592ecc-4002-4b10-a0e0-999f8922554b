/**
 * 组件样式库
 * 基于设计系统的组件级样式
 */

/* ==========================================================================
   按钮组件样式
   ========================================================================== */

.btn {
  @apply inline-flex items-center justify-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium transition-all duration-200;
  cursor: pointer;
  user-select: none;
}

.btn:disabled {
  @apply opacity-50 cursor-not-allowed;
}

.btn-primary {
  @apply bg-primary-600 text-white hover:bg-primary-700 focus:ring-2 focus:ring-primary-500 focus:ring-offset-2;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.btn-primary:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4);
}

.btn-secondary {
  @apply bg-white text-gray-700 border-gray-300 hover:bg-gray-50 hover:border-gray-400 focus:ring-2 focus:ring-primary-500 focus:ring-offset-2;
}

.btn-ghost {
  @apply bg-transparent text-primary-600 hover:bg-primary-50 focus:ring-2 focus:ring-primary-500 focus:ring-offset-2;
}

.btn-danger {
  @apply bg-red-600 text-white hover:bg-red-700 focus:ring-2 focus:ring-red-500 focus:ring-offset-2;
}

.btn-success {
  @apply bg-green-600 text-white hover:bg-green-700 focus:ring-2 focus:ring-green-500 focus:ring-offset-2;
}

/* 按钮尺寸 */
.btn-xs {
  @apply px-2 py-1 text-xs;
}

.btn-sm {
  @apply px-3 py-2 text-sm;
}

.btn-lg {
  @apply px-6 py-3 text-lg;
}

.btn-xl {
  @apply px-8 py-4 text-xl;
}

/* ==========================================================================
   表单组件样式
   ========================================================================== */

.form-input {
  @apply w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-all duration-200;
}

.form-input:invalid {
  @apply border-red-300 focus:ring-red-500 focus:border-red-500;
  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

.form-textarea {
  @apply w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-all duration-200 resize-y min-h-[80px];
}

.form-select {
  @apply w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-all duration-200 bg-white;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
  background-position: right 0.5rem center;
  background-repeat: no-repeat;
  background-size: 1.5em 1.5em;
  padding-right: 2.5rem;
}

.form-checkbox {
  @apply h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded;
}

.form-radio {
  @apply h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300;
}

.form-label {
  @apply block text-sm font-medium text-gray-700 mb-1;
}

.form-help {
  @apply mt-1 text-sm text-gray-500;
}

.form-error {
  @apply mt-1 text-sm text-red-600;
}

/* ==========================================================================
   卡片组件样式
   ========================================================================== */

.card {
  @apply bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden;
}

.card-header {
  @apply px-6 py-4 border-b border-gray-200 bg-gray-50;
}

.card-body {
  @apply px-6 py-4;
}

.card-footer {
  @apply px-6 py-4 border-t border-gray-200 bg-gray-50;
}

.card-title {
  @apply text-lg font-semibold text-gray-900;
}

.card-subtitle {
  @apply text-sm text-gray-600 mt-1;
}

/* 卡片变体 */
.card-elevated {
  @apply shadow-lg;
}

.card-interactive {
  @apply transition-all duration-200 hover:shadow-md hover:-translate-y-1 cursor-pointer;
}

/* ==========================================================================
   模态框组件样式
   ========================================================================== */

.modal-overlay {
  @apply fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity z-40;
}

.modal-container {
  @apply fixed inset-0 z-50 overflow-y-auto;
}

.modal-wrapper {
  @apply flex min-h-full items-end justify-center p-4 text-center sm:items-center sm:p-0;
}

.modal-panel {
  @apply relative transform overflow-hidden rounded-lg bg-white text-left shadow-xl transition-all sm:my-8 sm:w-full sm:max-w-lg;
  animation: scaleIn 0.2s cubic-bezier(0.33, 1, 0.68, 1);
}

.modal-header {
  @apply px-6 py-4 border-b border-gray-200;
}

.modal-body {
  @apply px-6 py-4;
}

.modal-footer {
  @apply px-6 py-4 border-t border-gray-200 flex justify-end space-x-3;
}

.modal-title {
  @apply text-lg font-semibold text-gray-900;
}

.modal-close {
  @apply absolute top-4 right-4 text-gray-400 hover:text-gray-600 transition-colors;
}

/* ==========================================================================
   表格组件样式
   ========================================================================== */

.table {
  @apply min-w-full divide-y divide-gray-200;
}

.table-header {
  @apply bg-gray-50;
}

.table-header-cell {
  @apply px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider;
}

.table-body {
  @apply bg-white divide-y divide-gray-200;
}

.table-row {
  @apply hover:bg-gray-50 transition-colors;
}

.table-cell {
  @apply px-6 py-4 whitespace-nowrap text-sm text-gray-900;
}

.table-cell-secondary {
  @apply text-gray-500;
}

/* 表格变体 */
.table-striped .table-row:nth-child(even) {
  @apply bg-gray-50;
}

.table-borderless {
  @apply divide-y-0;
}

.table-borderless .table-body {
  @apply divide-y-0;
}

/* ==========================================================================
   徽章组件样式
   ========================================================================== */

.badge {
  @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
}

.badge-primary {
  @apply bg-primary-100 text-primary-800;
}

.badge-secondary {
  @apply bg-gray-100 text-gray-800;
}

.badge-success {
  @apply bg-green-100 text-green-800;
}

.badge-warning {
  @apply bg-yellow-100 text-yellow-800;
}

.badge-danger {
  @apply bg-red-100 text-red-800;
}

.badge-info {
  @apply bg-blue-100 text-blue-800;
}

/* 徽章尺寸 */
.badge-sm {
  @apply px-2 py-0.5 text-xs;
}

.badge-lg {
  @apply px-3 py-1 text-sm;
}

/* ==========================================================================
   导航组件样式
   ========================================================================== */

.nav {
  @apply flex space-x-8;
}

.nav-item {
  @apply text-sm font-medium text-gray-500 hover:text-gray-700 transition-colors;
}

.nav-item.active {
  @apply text-primary-600;
}

.nav-vertical {
  @apply flex-col space-x-0 space-y-1;
}

.nav-vertical .nav-item {
  @apply block px-3 py-2 rounded-md;
}

.nav-vertical .nav-item:hover {
  @apply bg-gray-100;
}

.nav-vertical .nav-item.active {
  @apply bg-primary-100 text-primary-700;
}

/* ==========================================================================
   面包屑组件样式
   ========================================================================== */

.breadcrumb {
  @apply flex items-center space-x-2 text-sm text-gray-500;
}

.breadcrumb-item {
  @apply hover:text-gray-700 transition-colors;
}

.breadcrumb-item.active {
  @apply text-gray-900 font-medium;
}

.breadcrumb-separator {
  @apply text-gray-400;
}

/* ==========================================================================
   提示组件样式
   ========================================================================== */

.alert {
  @apply p-4 rounded-md border-l-4;
}

.alert-success {
  @apply bg-green-50 border-green-400 text-green-700;
}

.alert-warning {
  @apply bg-yellow-50 border-yellow-400 text-yellow-700;
}

.alert-danger {
  @apply bg-red-50 border-red-400 text-red-700;
}

.alert-info {
  @apply bg-blue-50 border-blue-400 text-blue-700;
}

.alert-title {
  @apply font-medium mb-2;
}

.alert-description {
  @apply text-sm;
}

/* ==========================================================================
   加载状态组件样式
   ========================================================================== */

.loading-spinner {
  @apply animate-spin h-5 w-5 border-2 border-primary-600 border-t-transparent rounded-full;
}

.loading-skeleton {
  @apply bg-gray-200 rounded;
  background: linear-gradient(90deg, var(--gray-200) 25%, var(--gray-100) 50%, var(--gray-200) 75%);
  background-size: 200px 100%;
  animation: shimmer 1.5s infinite;
}

.loading-skeleton-text {
  @apply loading-skeleton h-4 mb-2;
}

.loading-skeleton-title {
  @apply loading-skeleton h-6 mb-4;
}

.loading-skeleton-avatar {
  @apply loading-skeleton w-10 h-10 rounded-full;
}

/* ==========================================================================
   工具提示组件样式
   ========================================================================== */

.tooltip {
  @apply relative inline-block;
}

.tooltip-content {
  @apply invisible absolute z-10 px-3 py-2 text-sm font-medium text-white bg-gray-900 rounded-lg shadow-sm opacity-0 transition-opacity duration-300;
  bottom: 125%;
  left: 50%;
  margin-left: -60px;
}

.tooltip-content::after {
  content: "";
  @apply absolute border-4 border-transparent border-t-gray-900;
  top: 100%;
  left: 50%;
  margin-left: -4px;
}

.tooltip:hover .tooltip-content {
  @apply visible opacity-100;
}

/* ==========================================================================
   分页组件样式
   ========================================================================== */

.pagination {
  @apply flex items-center justify-center space-x-1;
}

.pagination-item {
  @apply px-3 py-2 text-sm leading-tight text-gray-500 bg-white border border-gray-300 hover:bg-gray-100 hover:text-gray-700 transition-colors;
}

.pagination-item:first-child {
  @apply rounded-l-lg;
}

.pagination-item:last-child {
  @apply rounded-r-lg;
}

.pagination-item.active {
  @apply z-10 bg-primary-50 border-primary-500 text-primary-600;
}

.pagination-item:disabled {
  @apply opacity-50 cursor-not-allowed;
}

/* ==========================================================================
   暗色模式适配
   ========================================================================== */

[data-theme="dark"] {
  .card {
    @apply bg-gray-800 border-gray-700;
  }
  
  .card-header,
  .card-footer {
    @apply bg-gray-700 border-gray-600;
  }
  
  .form-input {
    @apply bg-gray-700 border-gray-600 text-white placeholder-gray-400;
  }
  
  .table-header {
    @apply bg-gray-700;
  }
  
  .table-body {
    @apply bg-gray-800;
  }
  
  .table-row:hover {
    @apply bg-gray-700;
  }
  
  .modal-panel {
    @apply bg-gray-800;
  }
  
  .nav-item {
    @apply text-gray-300 hover:text-white;
  }
  
  .nav-vertical .nav-item:hover {
    @apply bg-gray-700;
  }
}