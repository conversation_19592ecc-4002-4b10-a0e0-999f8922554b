@echo off
REM Simple English-only batch file to avoid encoding issues
setlocal enabledelayedexpansion

echo Starting Pingyu Admin Dev Server...
echo Current Directory: %CD%
echo.

REM Check if node exists
where node >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Node.js not found in PATH
    echo Please install Node.js from https://nodejs.org/
    pause
    exit /b 1
)

REM Check if npm exists  
where npm >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: npm not found in PATH
    pause
    exit /b 1
)

echo Node.js version:
node --version
echo.

echo npm version:
npm --version
echo.

REM Try to install dependencies if node_modules doesn't exist
if not exist "node_modules" (
    echo Installing dependencies...
    call npm install
)

echo Starting development server...
echo Executing: npm run dev:no-csp
start cmd /k "npm run dev:no-csp"
echo.
echo Server should start in a new window...
echo If it doesn't work, check the new command window for errors
pause