// 🔥 JavaScript执行效率优化工具集

/**
 * 1. 防抖函数 - 优化频繁触发的事件
 */
export function debounce<T extends (...args: any[]) => void>(
  func: T,
  wait: number,
  immediate = false
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout | null = null
  
  return function executedFunction(...args: Parameters<T>) {
    const later = () => {
      timeout = null
      if (!immediate) func(...args)
    }
    
    const callNow = immediate && !timeout
    
    if (timeout) clearTimeout(timeout)
    timeout = setTimeout(later, wait)
    
    if (callNow) func(...args)
  }
}

/**
 * 2. 节流函数 - 限制函数执行频率
 */
export function throttle<T extends (...args: any[]) => void>(
  func: T,
  limit: number
): (...args: Parameters<T>) => void {
  let inThrottle: boolean = false
  
  return function executedFunction(...args: Parameters<T>) {
    if (!inThrottle) {
      func(...args)
      inThrottle = true
      setTimeout(() => inThrottle = false, limit)
    }
  }
}

/**
 * 3. 高效的数组处理 - 避免多次遍历
 */
export class ArrayProcessor<T = any> {
  private data: T[]
  
  constructor(data: T[]) {
    this.data = data
  }
  
  // 🔥 一次遍历完成多个操作
  multiProcess(operations: {
    filter?: (item: T) => boolean
    map?: (item: T) => any
    sort?: (a: T, b: T) => number
    groupBy?: (item: T) => string
  }) {
    let result = this.data
    
    // 过滤和映射一次完成
    if (operations.filter || operations.map) {
      result = this.data.reduce((acc, item) => {
        if (!operations.filter || operations.filter(item)) {
          acc.push(operations.map ? operations.map(item) : item)
        }
        return acc
      }, [] as any[])
    }
    
    // 排序
    if (operations.sort) {
      result.sort(operations.sort)
    }
    
    // 分组
    if (operations.groupBy) {
      return result.reduce((acc, item) => {
        const key = operations.groupBy!(item)
        if (!acc[key]) acc[key] = []
        acc[key].push(item)
        return acc
      }, {} as Record<string, T[]>)
    }
    
    return result
  }
  
  // 🔥 分页处理 - 避免渲染大量数据
  paginate(pageSize: number, currentPage: number = 1) {
    const startIndex = (currentPage - 1) * pageSize
    const endIndex = startIndex + pageSize
    
    return {
      data: this.data.slice(startIndex, endIndex),
      totalPages: Math.ceil(this.data.length / pageSize),
      currentPage,
      totalItems: this.data.length,
      hasMore: endIndex < this.data.length
    }
  }
}

/**
 * 4. Web Worker 封装 - 将重计算任务移到后台
 */
export class WorkerManager {
  private static instance: WorkerManager
  private workers: Map<string, Worker> = new Map()
  
  static getInstance() {
    if (!WorkerManager.instance) {
      WorkerManager.instance = new WorkerManager()
    }
    return WorkerManager.instance
  }
  
  // 🔥 创建并管理Web Worker
  async runInWorker<T = any>(
    workerScript: string,
    data: any,
    workerId = 'default'
  ): Promise<T> {
    return new Promise((resolve, reject) => {
      try {
        // 检查是否已有worker
        let worker = this.workers.get(workerId)
        
        if (!worker) {
          // 创建inline worker
          const blob = new Blob([workerScript], { type: 'application/javascript' })
          const workerUrl = URL.createObjectURL(blob)
          worker = new Worker(workerUrl)
          this.workers.set(workerId, worker)
          
          // 清理URL对象
          worker.addEventListener('message', () => {
            URL.revokeObjectURL(workerUrl)
          }, { once: true })
        }
        
        // 设置消息处理
        const handleMessage = (e: MessageEvent) => {
          worker!.removeEventListener('message', handleMessage)
          worker!.removeEventListener('error', handleError)
          resolve(e.data)
        }
        
        const handleError = (error: ErrorEvent) => {
          worker!.removeEventListener('message', handleMessage)
          worker!.removeEventListener('error', handleError)
          reject(error)
        }
        
        worker.addEventListener('message', handleMessage)
        worker.addEventListener('error', handleError)
        
        // 发送数据
        worker.postMessage(data)
        
      } catch (error) {
        reject(error)
      }
    })
  }
  
  // 🔥 清理所有worker
  cleanup() {
    this.workers.forEach(worker => {
      worker.terminate()
    })
    this.workers.clear()
  }
}

/**
 * 5. 内存优化 - 对象池模式
 */
export class ObjectPool<T> {
  private pool: T[] = []
  private createFn: () => T
  private resetFn: (obj: T) => void
  
  constructor(createFn: () => T, resetFn: (obj: T) => void, initialSize = 10) {
    this.createFn = createFn
    this.resetFn = resetFn
    
    // 预创建对象
    for (let i = 0; i < initialSize; i++) {
      this.pool.push(this.createFn())
    }
  }
  
  acquire(): T {
    return this.pool.pop() || this.createFn()
  }
  
  release(obj: T) {
    this.resetFn(obj)
    this.pool.push(obj)
  }
  
  clear() {
    this.pool.length = 0
  }
}

/**
 * 6. 高效的DOM操作
 */
export class DOMOptimizer {
  // 🔥 批量DOM更新
  static batchDOMUpdates(updates: (() => void)[]) {
    // 使用DocumentFragment减少重排
    const fragment = document.createDocumentFragment()
    
    // 批量执行更新
    requestAnimationFrame(() => {
      updates.forEach(update => update())
    })
  }
  
  // 🔥 虚拟滚动计算
  static calculateVisibleRange(
    scrollTop: number,
    containerHeight: number,
    itemHeight: number,
    totalItems: number,
    overscan = 5
  ) {
    const startIndex = Math.max(0, Math.floor(scrollTop / itemHeight) - overscan)
    const endIndex = Math.min(
      totalItems - 1,
      Math.ceil((scrollTop + containerHeight) / itemHeight) + overscan
    )
    
    return { startIndex, endIndex, visibleCount: endIndex - startIndex + 1 }
  }
}

/**
 * 7. 性能监控和分析
 */
export class PerformanceMonitor {
  private static metrics: Map<string, number[]> = new Map()
  
  // 🔥 性能标记
  static mark(name: string) {
    performance.mark(`${name}-start`)
  }
  
  static measure(name: string) {
    performance.mark(`${name}-end`)
    performance.measure(name, `${name}-start`, `${name}-end`)
    
    const measure = performance.getEntriesByName(name)[0]
    const duration = measure.duration
    
    // 记录性能数据
    if (!this.metrics.has(name)) {
      this.metrics.set(name, [])
    }
    this.metrics.get(name)!.push(duration)
    
    return duration
  }
  
  // 🔥 获取性能统计
  static getStats(name: string) {
    const durations = this.metrics.get(name) || []
    if (durations.length === 0) return null
    
    const sum = durations.reduce((a, b) => a + b, 0)
    const avg = sum / durations.length
    const min = Math.min(...durations)
    const max = Math.max(...durations)
    
    return { avg, min, max, count: durations.length, total: sum }
  }
  
  // 🔥 清理性能数据
  static clear() {
    this.metrics.clear()
    performance.clearMarks()
    performance.clearMeasures()
  }
}

/**
 * 8. 数据处理优化示例脚本 (用于Web Worker)
 */
export const dataProcessingWorkerScript = `
  // Web Worker脚本：处理大量数据
  self.onmessage = function(e) {
    const { data, operation } = e.data;
    
    try {
      let result;
      
      switch(operation) {
        case 'sort':
          result = data.sort((a, b) => {
            // 自定义排序逻辑
            return a.value - b.value;
          });
          break;
          
        case 'filter':
          result = data.filter(item => {
            // 复杂过滤逻辑
            return item.active && item.score > 0.5;
          });
          break;
          
        case 'aggregate':
          result = data.reduce((acc, item) => {
            const key = item.category;
            if (!acc[key]) {
              acc[key] = { count: 0, total: 0 };
            }
            acc[key].count++;
            acc[key].total += item.value;
            return acc;
          }, {});
          break;
          
        case 'search':
          const searchTerm = e.data.searchTerm.toLowerCase();
          result = data.filter(item => 
            item.title.toLowerCase().includes(searchTerm) ||
            item.content.toLowerCase().includes(searchTerm)
          );
          break;
          
        default:
          result = data;
      }
      
      self.postMessage({ success: true, result });
    } catch (error) {
      self.postMessage({ success: false, error: error.message });
    }
  };
`

// 🔥 使用示例和工具函数
export const optimizationUtils = {
  // 创建优化的事件处理器
  createOptimizedHandler: <T extends (...args: any[]) => void>(
    handler: T,
    optimization: 'debounce' | 'throttle' = 'debounce',
    delay = 300
  ) => {
    return optimization === 'debounce' 
      ? debounce(handler, delay)
      : throttle(handler, delay)
  },
  
  // 创建数组处理器
  createArrayProcessor: <T>(data: T[]) => new ArrayProcessor(data),
  
  // 获取Worker管理器
  getWorkerManager: () => WorkerManager.getInstance(),
  
  // 创建对象池
  createObjectPool: <T>(
    createFn: () => T,
    resetFn: (obj: T) => void,
    initialSize?: number
  ) => new ObjectPool(createFn, resetFn, initialSize),
  
  // 性能监控
  perf: PerformanceMonitor
}

export default optimizationUtils