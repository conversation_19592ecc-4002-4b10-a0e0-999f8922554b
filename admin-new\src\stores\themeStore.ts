import { create } from 'zustand'
import { persist } from 'zustand/middleware'

export type ThemeMode = 'light' | 'dark'

interface ThemeState {
  mode: ThemeMode
  isDark: boolean
  toggleTheme: () => void
  setTheme: (mode: ThemeMode) => void
}

export const useThemeStore = create<ThemeState>()(
  persist(
    (set, get) => ({
      mode: 'light',
      isDark: false,
      
      toggleTheme: () => {
        const currentMode = get().mode
        const newMode = currentMode === 'light' ? 'dark' : 'light'
        set({ 
          mode: newMode, 
          isDark: newMode === 'dark' 
        })
        
        // 更新HTML类名和数据属性
        document.documentElement.classList.toggle('dark', newMode === 'dark')
        document.documentElement.setAttribute('data-theme', newMode)
      },
      
      setTheme: (mode: ThemeMode) => {
        set({ 
          mode, 
          isDark: mode === 'dark' 
        })
        
        // 更新HTML类名和数据属性
        document.documentElement.classList.toggle('dark', mode === 'dark')
        document.documentElement.setAttribute('data-theme', mode)
      }
    }),
    {
      name: 'theme-storage',
      onRehydrateStorage: () => (state) => {
        if (state) {
          // 确保页面刷新后主题保持一致
          document.documentElement.classList.toggle('dark', state.isDark)
          document.documentElement.setAttribute('data-theme', state.mode)
        }
      }
    }
  )
)

// 初始化主题 - 页面加载时调用
export const initializeTheme = () => {
  const { mode, isDark } = useThemeStore.getState()
  
  // 确保DOM元素状态与store状态一致
  document.documentElement.classList.toggle('dark', isDark)
  document.documentElement.setAttribute('data-theme', mode)
  
  console.log('🎨 主题初始化完成:', { mode, isDark })
}