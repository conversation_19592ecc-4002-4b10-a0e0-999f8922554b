<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>真实数据库连通测试 - 基于你的数据库集合</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }
        .container {
            background: white;
            border-radius: 12px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            margin-bottom: 20px;
        }
        h1 { color: #2c3e50; text-align: center; margin-bottom: 30px; }
        .success-banner {
            background: linear-gradient(135deg, #00b894, #00cec9);
            color: white;
            padding: 20px;
            border-radius: 8px;
            text-align: center;
            margin-bottom: 30px;
            font-weight: bold;
        }
        .collections-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 10px;
            margin: 20px 0;
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
        }
        .collection-tag {
            background: linear-gradient(135deg, #74b9ff, #0984e3);
            color: white;
            padding: 8px 12px;
            border-radius: 6px;
            text-align: center;
            font-size: 12px;
            font-weight: bold;
        }
        .test-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .test-card {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 20px;
            background: #f9f9f9;
        }
        .test-card h3 {
            margin-top: 0;
            color: #2c3e50;
        }
        button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
            transition: transform 0.2s;
        }
        button:hover { transform: translateY(-2px); }
        button:disabled { opacity: 0.6; cursor: not-allowed; transform: none; }
        
        .result {
            margin: 10px 0;
            padding: 15px;
            border-radius: 8px;
            font-family: monospace;
            font-size: 14px;
        }
        .success { background: #d4edda; color: #155724; border-left: 4px solid #28a745; }
        .error { background: #f8d7da; color: #721c24; border-left: 4px solid #dc3545; }
        .info { background: #d1ecf1; color: #0c5460; border-left: 4px solid #17a2b8; }
        .warning { background: #fff3cd; color: #856404; border-left: 4px solid #ffc107; }
        
        .data-preview {
            background: #2c3e50;
            color: #ecf0f1;
            padding: 15px;
            border-radius: 6px;
            font-family: monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
            margin-top: 10px;
        }
        
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        .status-success { background: #28a745; }
        .status-error { background: #dc3545; }
        .status-warning { background: #ffc107; }
        
        .stats-summary {
            background: linear-gradient(135deg, #74b9ff, #0984e3);
            color: white;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            text-align: center;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
            margin-top: 15px;
        }
        
        .stat-item {
            background: rgba(255,255,255,0.2);
            padding: 15px;
            border-radius: 6px;
            text-align: center;
        }
        
        .stat-number {
            font-size: 24px;
            font-weight: bold;
            display: block;
        }
        
        .stat-label {
            font-size: 12px;
            opacity: 0.9;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="success-banner">
            🎉 真实数据库连通测试
            <br>现在连接到你的小程序云开发数据库！
            <br>环境ID: cloud1-4g85f8xlb8166ff1
        </div>
        
        <h1>📊 基于你的真实数据库集合的连通测试</h1>
        
        <div style="background: #e8f4fd; border-left: 4px solid #3498db; padding: 20px; margin: 20px 0; border-radius: 8px;">
            <h3>🔗 你的数据库集合</h3>
            <p>检测到以下真实数据库集合：</p>
            <div class="collections-grid">
                <div class="collection-tag">users</div>
                <div class="collection-tag">teachers</div>
                <div class="collection-tag">students</div>
                <div class="collection-tag">classes</div>
                <div class="collection-tag">comments</div>
                <div class="collection-tag">records</div>
                <div class="collection-tag">ai_configs</div>
                <div class="collection-tag">ai_usage</div>
                <div class="collection-tag">ai_generation_logs</div>
                <div class="collection-tag">ai_error_logs</div>
                <div class="collection-tag">admins</div>
                <div class="collection-tag">system_config</div>
                <div class="collection-tag">settings</div>
                <div class="collection-tag">logs</div>
                <div class="collection-tag">notifications</div>
                <div class="collection-tag">teams</div>
                <div class="collection-tag">tags</div>
                <div class="collection-tag">prompt_templates</div>
                <div class="collection-tag">files</div>
            </div>
        </div>
        
        <div style="text-align: center; margin: 30px 0;">
            <button onclick="testRealDatabase()">测试真实数据库连接</button>
            <button onclick="testAllRealData()">测试所有真实数据</button>
            <button onclick="showDataSummary()">显示数据概览</button>
            <button onclick="clearResults()">清空结果</button>
        </div>
        
        <div id="dataSummary"></div>
        
        <div class="test-grid">
            <div class="test-card">
                <h3>🔗 真实数据库连接测试</h3>
                <p>测试与你的小程序云开发数据库的连接状态</p>
                <div id="connection-status">
                    <span class="status-indicator status-warning"></span>等待测试
                </div>
                <div id="connection-results"></div>
            </div>
            
            <div class="test-card">
                <h3>📊 真实仪表板数据</h3>
                <p>从你的数据库获取用户数、评语数、AI调用数</p>
                <div id="dashboard-status">
                    <span class="status-indicator status-warning"></span>等待测试
                </div>
                <div id="dashboard-results"></div>
            </div>
            
            <div class="test-card">
                <h3>⚡ 真实活动数据</h3>
                <p>获取最近的用户活动和系统日志</p>
                <div id="activity-status">
                    <span class="status-indicator status-warning"></span>等待测试
                </div>
                <div id="activity-results"></div>
            </div>
            
            <div class="test-card">
                <h3>👥 真实学生和评语数据</h3>
                <p>获取学生信息和评语记录</p>
                <div id="data-status">
                    <span class="status-indicator status-warning"></span>等待测试
                </div>
                <div id="data-results"></div>
            </div>
        </div>
    </div>

    <div class="container">
        <h2>📝 测试日志</h2>
        <div id="logContainer" style="max-height: 400px; overflow-y: auto; background: #f8f9fa; padding: 15px; border-radius: 6px; font-family: monospace; font-size: 12px;">
            等待开始测试真实数据库...
        </div>
    </div>

    <script>
        const API_URL = 'http://localhost:3000/admin'
        let realDataStats = null
        
        function log(message, type = 'info') {
            const logContainer = document.getElementById('logContainer')
            const timestamp = new Date().toLocaleTimeString()
            const prefix = type === 'error' ? '❌' : type === 'success' ? '✅' : type === 'warning' ? '⚠️' : 'ℹ️'
            logContainer.textContent += `[${timestamp}] ${prefix} ${message}\n`
            logContainer.scrollTop = logContainer.scrollHeight
        }
        
        function updateStatus(cardId, success, message) {
            const statusEl = document.getElementById(`${cardId}-status`)
            const indicator = statusEl.querySelector('.status-indicator')
            
            if (success === null) {
                indicator.className = 'status-indicator status-warning'
                statusEl.innerHTML = `<span class="status-indicator status-warning"></span>⏳ ${message}`
            } else if (success) {
                indicator.className = 'status-indicator status-success'
                statusEl.innerHTML = `<span class="status-indicator status-success"></span>✅ ${message}`
            } else {
                indicator.className = 'status-indicator status-error'
                statusEl.innerHTML = `<span class="status-indicator status-error"></span>❌ ${message}`
            }
        }
        
        function addResult(cardId, message, type = 'info', data = null) {
            const resultsEl = document.getElementById(`${cardId}-results`)
            const div = document.createElement('div')
            div.className = `result ${type}`
            
            let content = message
            if (data) {
                content += `<div class="data-preview">${JSON.stringify(data, null, 2)}</div>`
            }
            
            div.innerHTML = content
            resultsEl.appendChild(div)
        }
        
        async function callAPI(action, data = {}) {
            try {
                const response = await fetch(API_URL, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        action,
                        ...data,
                        timestamp: Date.now(),
                        requestId: `real_db_test_${Date.now()}`
                    })
                })
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`)
                }
                
                const result = await response.json()
                
                if (result.code === 200) {
                    return { success: true, data: result.data }
                } else {
                    return { success: false, error: result.message }
                }
                
            } catch (error) {
                return { success: false, error: error.message }
            }
        }
        
        async function testRealDatabase() {
            log('🔗 开始测试真实数据库连接...')
            updateStatus('connection', null, '正在连接真实数据库...')
            
            try {
                const result = await callAPI('database.testConnection')
                
                if (result.success && result.data.success) {
                    updateStatus('connection', true, '真实数据库连接成功')
                    addResult('connection', `✅ 真实数据库连接成功，可访问集合: ${result.data.collections.join(', ')}`, 'success', result.data)
                    log(`✅ 真实数据库连接成功，可访问${result.data.collections.length}个集合`)
                } else {
                    updateStatus('connection', false, '真实数据库连接失败')
                    addResult('connection', `❌ 连接失败: ${result.data?.message || result.error}`, 'error')
                    log(`❌ 真实数据库连接失败: ${result.data?.message || result.error}`)
                }
            } catch (error) {
                updateStatus('connection', false, '连接异常')
                addResult('connection', `❌ 连接异常: ${error.message}`, 'error')
                log(`❌ 真实数据库连接异常: ${error.message}`)
            }
        }
        
        async function testDashboardData() {
            log('📊 开始测试真实仪表板数据...')
            updateStatus('dashboard', null, '正在获取真实统计数据...')
            
            try {
                const result = await callAPI('data.getDashboardStats')
                
                if (result.success) {
                    realDataStats = result.data
                    updateStatus('dashboard', true, '真实统计数据获取成功')
                    addResult('dashboard', `✅ 真实数据: 用户${result.data.totalUsers}个, 今日评语${result.data.todayComments}条, AI调用${result.data.aiCalls}次`, 'success', result.data)
                    log(`✅ 真实仪表板数据获取成功`)
                } else {
                    updateStatus('dashboard', false, '统计数据获取失败')
                    addResult('dashboard', `❌ 获取失败: ${result.error}`, 'error')
                    log(`❌ 真实仪表板数据获取失败: ${result.error}`)
                }
            } catch (error) {
                updateStatus('dashboard', false, '数据获取异常')
                addResult('dashboard', `❌ 获取异常: ${error.message}`, 'error')
                log(`❌ 真实仪表板数据获取异常: ${error.message}`)
            }
        }
        
        async function testActivityData() {
            log('⚡ 开始测试真实活动数据...')
            updateStatus('activity', null, '正在获取真实活动记录...')
            
            try {
                const result = await callAPI('data.getRecentActivities', { limit: 5 })
                
                if (result.success) {
                    updateStatus('activity', true, '真实活动数据获取成功')
                    addResult('activity', `✅ 真实活动记录: 获取${result.data.length}条记录`, 'success', result.data)
                    log(`✅ 真实活动数据获取成功，共${result.data.length}条记录`)
                } else {
                    updateStatus('activity', false, '活动数据获取失败')
                    addResult('activity', `❌ 获取失败: ${result.error}`, 'error')
                    log(`❌ 真实活动数据获取失败: ${result.error}`)
                }
            } catch (error) {
                updateStatus('activity', false, '数据获取异常')
                addResult('activity', `❌ 获取异常: ${error.message}`, 'error')
                log(`❌ 真实活动数据获取异常: ${error.message}`)
            }
        }
        
        async function testStudentData() {
            log('👥 开始测试真实学生和评语数据...')
            updateStatus('data', null, '正在获取真实学生和评语数据...')
            
            try {
                const [studentsResult, commentsResult] = await Promise.all([
                    callAPI('data.getStudents', { params: { limit: 3 } }),
                    callAPI('data.getRecords', { params: { limit: 3 } })
                ])
                
                if (studentsResult.success && commentsResult.success) {
                    updateStatus('data', true, '真实学生和评语数据获取成功')
                    addResult('data', `✅ 真实学生数据: ${studentsResult.data.total}条记录`, 'success')
                    addResult('data', `✅ 真实评语数据: ${commentsResult.data.total}条记录`, 'success')
                    log(`✅ 真实学生和评语数据获取成功`)
                } else {
                    updateStatus('data', false, '数据获取失败')
                    addResult('data', `❌ 获取失败`, 'error')
                    log(`❌ 真实学生和评语数据获取失败`)
                }
            } catch (error) {
                updateStatus('data', false, '数据获取异常')
                addResult('data', `❌ 获取异常: ${error.message}`, 'error')
                log(`❌ 真实学生和评语数据获取异常: ${error.message}`)
            }
        }
        
        async function testAllRealData() {
            log('🚀 开始测试所有真实数据连接...')
            
            // 清空之前的结果
            document.querySelectorAll('[id$="-results"]').forEach(el => el.innerHTML = '')
            
            // 依次测试所有数据
            await testRealDatabase()
            await new Promise(resolve => setTimeout(resolve, 1000))
            
            await testDashboardData()
            await new Promise(resolve => setTimeout(resolve, 1000))
            
            await testActivityData()
            await new Promise(resolve => setTimeout(resolve, 1000))
            
            await testStudentData()
            
            log('🎯 所有真实数据测试完成！')
            
            // 显示数据概览
            if (realDataStats) {
                showDataSummary()
            }
        }
        
        function showDataSummary() {
            if (!realDataStats) {
                log('⚠️ 请先测试仪表板数据')
                return
            }
            
            const summaryEl = document.getElementById('dataSummary')
            summaryEl.innerHTML = `
                <div class="stats-summary">
                    <h3>📊 你的小程序真实数据概览</h3>
                    <div class="stats-grid">
                        <div class="stat-item">
                            <span class="stat-number">${realDataStats.totalUsers}</span>
                            <span class="stat-label">活跃用户</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-number">${realDataStats.todayComments}</span>
                            <span class="stat-label">今日评语</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-number">${realDataStats.aiCalls}</span>
                            <span class="stat-label">AI调用数</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-number">${realDataStats.satisfaction}</span>
                            <span class="stat-label">满意度</span>
                        </div>
                    </div>
                    <p style="margin-top: 15px; opacity: 0.9;">
                        数据更新时间: ${new Date(realDataStats.lastUpdated).toLocaleString()}
                    </p>
                </div>
            `
            
            log('📊 真实数据概览已显示')
        }
        
        function clearResults() {
            document.getElementById('logContainer').textContent = '等待开始测试真实数据库...'
            document.getElementById('dataSummary').innerHTML = ''
            document.querySelectorAll('[id$="-results"]').forEach(el => el.innerHTML = '')
            document.querySelectorAll('[id$="-status"]').forEach(el => {
                el.innerHTML = '<span class="status-indicator status-warning"></span>等待测试'
            })
            realDataStats = null
        }
        
        // 页面加载时自动开始测试
        window.addEventListener('load', () => {
            log('🔗 真实数据库测试工具已加载')
            log('💡 点击"测试所有真实数据"开始全面测试')
            
            // 自动测试数据库连接
            setTimeout(testRealDatabase, 1000)
        })
    </script>
</body>
</html>
