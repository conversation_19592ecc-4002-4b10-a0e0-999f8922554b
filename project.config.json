{"setting": {"es6": true, "postcss": true, "minified": true, "uglifyFileName": true, "enhance": true, "packNpmRelationList": [], "packNpmIgnoreList": ["backend_temp/**", "admin-pro/**", "admin/**", "prototype/**", "scripts/**", "docs/**", "参考原型图/**", "cloudfunctions/**/node_modules/**", "test-*.js", "*.md", "*.txt", "jest.config.js", "tsconfig.json", "package-lock.json", ".eslintrc.*", ".prettierrc.*", "node_modules/**"], "babelSetting": {"ignore": ["backend_temp/**", "admin-pro/**", "admin/**", "node_modules/**", "cloudfunctions/**/node_modules/**", "prototype/**", "scripts/**", "docs/**", "参考原型图/**"], "disablePlugins": [], "outputPath": ""}, "useCompilerPlugins": false, "minifyWXML": true, "compileWorklet": false, "uploadWithSourceMap": false, "packNpmManually": false, "minifyWXSS": true, "localPlugins": false, "disableUseStrict": false, "condition": false, "swc": false, "disableSWC": true}, "compileType": "miniprogram", "simulatorPluginLibVersion": {}, "packOptions": {"ignore": [{"value": "node_modules", "type": "folder"}, {"value": "backend_temp", "type": "folder"}, {"value": "admin-pro", "type": "folder"}, {"value": "admin", "type": "folder"}, {"value": "prototype", "type": "folder"}, {"value": "scripts", "type": "folder"}, {"value": "docs", "type": "folder"}, {"value": "参考原型图", "type": "folder"}, {"value": "database", "type": "folder"}, {"value": "cloudfunctions/adminAPI/node_modules", "type": "folder"}, {"value": "cloudfunctions/getGrowthStats/node_modules", "type": "folder"}, {"value": "cloudfunctions/initDatabase/node_modules", "type": "folder"}, {"value": "test-*.js", "type": "file"}, {"value": "*.md", "type": "file"}, {"value": "*.txt", "type": "file"}, {"value": "*.json.bak", "type": "file"}, {"value": "app_backup.json", "type": "file"}, {"value": "app_v2.json", "type": "file"}, {"value": "手动添加模板数据.json", "type": "file"}, {"value": "fix-*.js", "type": "file"}, {"value": "clear-*.js", "type": "file"}, {"value": "build-*.js", "type": "file"}, {"value": "cleanup-*.js", "type": "file"}, {"value": "cleanup-*.ps1", "type": "file"}, {"value": "修复小程序AI.js", "type": "file"}, {"value": "纯HTTP服务器.js", "type": "file"}, {"value": "终极服务器.js", "type": "file"}, {"value": "超简单后端.js", "type": "file"}, {"value": "jest.config.js", "type": "file"}, {"value": "tsconfig.json", "type": "file"}, {"value": "package-lock.json", "type": "file"}, {"value": ".eslintrc.*", "type": "file"}, {"value": ".prettierrc.*", "type": "file"}, {"value": "pages/test*", "type": "folder"}, {"value": "pages/debug*", "type": "folder"}, {"value": "pages/crud-test", "type": "folder"}, {"value": "pages/form-validation-test", "type": "folder"}, {"value": "pages/integration-test", "type": "folder"}, {"value": "pages/init-test", "type": "folder"}, {"value": "pages/realtime-test", "type": "folder"}, {"value": "pages/wxml-test", "type": "folder"}, {"value": "pages/ai-test", "type": "folder"}, {"value": "pages/cloud-check", "type": "folder"}, {"value": "pages/data-clean", "type": "folder"}, {"value": "pages/growth-test", "type": "folder"}, {"value": "pages/prompt-test", "type": "folder"}, {"value": "pages/test-ai", "type": "folder"}, {"value": "pages/test-login", "type": "folder"}, {"value": "pages/test-syntax", "type": "folder"}], "include": []}, "appid": "wx3de03090b8e8a734", "editorSetting": {}, "cloudfunctionRoot": "cloudfunctions/", "cloudbaseRoot": "cloudbase/", "libVersion": "3.8.11", "cloudfunctionConfig": {"timeout": 60}, "cloudfunctionTemplateRoot": "cloudfunctionTemplate/"}