/**
 * 简单的开发服务器
 * 解决模块加载失败问题
 */

import express from 'express'
import { createServer as createViteServer } from 'vite'
import path from 'path'

const app = express()
const PORT = process.env.PORT || 8080

async function createServer() {
  // 创建Vite开发服务器
  const vite = await createViteServer({
    server: { middlewareMode: true },
    configFile: path.resolve(__dirname, 'vite.config.fixed.ts'),
    root: __dirname
  })

  // 使用Vite的中间件
  app.use(vite.middlewares)

  // 设置静态文件目录
  if (process.env.NODE_ENV === 'production') {
    app.use(express.static('dist'))
    app.get('*', (req, res) => {
      res.sendFile(path.resolve(__dirname, 'dist', 'index.html'))
    })
  }

  app.listen(PORT, () => {
    console.log(`🚀 管理后台已启动`)
    console.log(`📱 地址: http://localhost:${PORT}`)
    console.log(`🔗 使用修正后的Vite配置`)
    console.log(`🎯 热重载: 已修复冲突`)
  })
}

createServer().catch((err) => {
  console.error('❌ 服务器启动失败:', err)
  process.exit(1)
})