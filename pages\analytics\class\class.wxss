/* 班级数据分析页面样式 */
.analytics-page {
  min-height: 100vh;
  background: #f8f9fa;
  padding: 24rpx;
  padding-bottom: 120rpx;
}

/* 页面头部 */
.page-header {
  background: white;
  border-radius: 20rpx;
  padding: 32rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.06);
}

.class-info {
  margin-bottom: 24rpx;
}

.class-name {
  font-size: 40rpx;
  font-weight: 700;
  color: #333;
  margin-bottom: 8rpx;
}

.class-desc {
  font-size: 26rpx;
  color: #666;
}

.time-range-selector {
  
}

/* 数据概览卡片 */
.overview-cards {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 16rpx;
  margin-bottom: 24rpx;
}

.overview-card {
  background: white;
  border-radius: 16rpx;
  padding: 24rpx;
  display: flex;
  align-items: center;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.06);
}

.card-icon {
  width: 56rpx;
  height: 56rpx;
  border-radius: 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16rpx;
}

.student-icon {
  background: linear-gradient(135deg, #4080FF, #6B9FFF);
}

.record-icon {
  background: linear-gradient(135deg, #52C873, #7ED893);
}

.score-icon {
  background: linear-gradient(135deg, #FFB800, #FFC940);
}

.card-content {
  flex: 1;
  text-align: center;
}

.card-number {
  font-size: 32rpx;
  font-weight: 700;
  color: #333;
  margin-bottom: 4rpx;
}

.card-label {
  font-size: 22rpx;
  color: #666;
}

/* 行为分布统计 */
.behavior-stats-section {
  background: white;
  border-radius: 20rpx;
  padding: 32rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.06);
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 24rpx;
}

.behavior-stats {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20rpx;
}

.behavior-stat {
  display: flex;
  align-items: center;
  padding: 20rpx;
  background: #f8f9fa;
  border-radius: 16rpx;
}

.stat-icon {
  width: 48rpx;
  height: 48rpx;
  border-radius: 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16rpx;
}

.positive .stat-icon {
  background: rgba(82, 200, 115, 0.1);
}

.negative .stat-icon {
  background: rgba(255, 82, 71, 0.1);
}

.neutral .stat-icon {
  background: rgba(255, 184, 0, 0.1);
}

.academic .stat-icon {
  background: rgba(64, 128, 255, 0.1);
}

.stat-content {
  flex: 1;
}

.stat-percentage {
  font-size: 28rpx;
  font-weight: 700;
  color: #333;
  margin-bottom: 4rpx;
}

.stat-label {
  font-size: 22rpx;
  color: #666;
}

/* 图表区域 */
.charts-section {
  margin-bottom: 24rpx;
}

.chart-card {
  background: white;
  border-radius: 20rpx;
  padding: 32rpx;
  margin-bottom: 16rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.06);
}

.chart-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 24rpx;
}

.chart-content {
  
}

/* 分数分布图 */
.score-distribution {
  
}

.score-bar {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
}

.bar-label {
  width: 80rpx;
  font-size: 24rpx;
  color: #666;
  text-align: right;
  margin-right: 16rpx;
}

.bar-container {
  flex: 1;
  height: 24rpx;
  background: #f0f0f0;
  border-radius: 12rpx;
  margin-right: 16rpx;
  position: relative;
  overflow: hidden;
}

.bar-fill {
  height: 100%;
  background: linear-gradient(90deg, #4080FF, #6B9FFF);
  border-radius: 12rpx;
  transition: width 0.3s ease;
}

.bar-value {
  width: 40rpx;
  font-size: 24rpx;
  color: #333;
  font-weight: 500;
  text-align: left;
}

/* 周活跃度 */
.weekly-trend {
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
  height: 200rpx;
  padding: 0 20rpx;
}

.trend-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  margin: 0 8rpx;
}

.trend-bar-container {
  flex: 1;
  width: 24rpx;
  position: relative;
  display: flex;
  align-items: flex-end;
  margin-bottom: 12rpx;
}

.trend-bar {
  width: 100%;
  background: linear-gradient(180deg, #52C873, #7ED893);
  border-radius: 12rpx 12rpx 0 0;
  min-height: 8rpx;
  transition: height 0.3s ease;
}

.trend-label {
  font-size: 20rpx;
  color: #666;
  margin-bottom: 4rpx;
}

.trend-value {
  font-size: 22rpx;
  color: #333;
  font-weight: 500;
}

/* 学生排行榜 */
.ranking-section {
  background: white;
  border-radius: 20rpx;
  padding: 32rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.06);
}

.section-header {
  margin-bottom: 24rpx;
}

.ranking-subtitle {
  font-size: 24rpx;
  color: #999;
  margin-top: 8rpx;
}

.ranking-list {
  
}

.ranking-item {
  display: flex;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
  transition: background-color 0.2s ease;
}

.ranking-item:last-child {
  border-bottom: none;
}

.ranking-item:active {
  background-color: #f8f9fa;
}

.ranking-number {
  width: 48rpx;
  height: 48rpx;
  border-radius: 24rpx;
  background: #f0f0f0;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16rpx;
}

.ranking-item:nth-child(1) .ranking-number {
  background: linear-gradient(135deg, #FFD700, #FFA500);
}

.ranking-item:nth-child(2) .ranking-number {
  background: linear-gradient(135deg, #C0C0C0, #A9A9A9);
}

.ranking-item:nth-child(3) .ranking-number {
  background: linear-gradient(135deg, #CD7F32, #B8860B);
}

.rank {
  font-size: 22rpx;
  font-weight: 700;
  color: #333;
}

.ranking-item:nth-child(1) .rank,
.ranking-item:nth-child(2) .rank,
.ranking-item:nth-child(3) .rank {
  color: white;
}

.student-info {
  flex: 1;
  margin-right: 16rpx;
}

.student-name {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 4rpx;
}

.student-stats {
  display: flex;
  gap: 16rpx;
}

.record-count,
.positive-rate {
  font-size: 22rpx;
  color: #666;
}

.student-score {
  text-align: center;
  margin-right: 16rpx;
}

.avg-score {
  font-size: 28rpx;
  font-weight: 700;
  color: #4080FF;
  margin-bottom: 4rpx;
}

.score-label {
  font-size: 20rpx;
  color: #999;
}

.arrow {
  
}

/* 最近记录 */
.recent-records-section {
  background: white;
  border-radius: 20rpx;
  padding: 32rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.06);
}

.records-list {
  
}

.record-item {
  display: flex;
  align-items: flex-start;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.record-item:last-child {
  border-bottom: none;
}

.record-icon {
  width: 40rpx;
  height: 40rpx;
  border-radius: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16rpx;
  margin-top: 4rpx;
}

.record-icon.positive {
  background: #52C873;
}

.record-icon.negative {
  background: #FF5247;
}

.record-icon.neutral {
  background: #FFB800;
}

.record-icon.academic {
  background: #4080FF;
}

.record-content {
  flex: 1;
  margin-right: 16rpx;
}

.record-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 8rpx;
}

.student-name {
  font-size: 26rpx;
  font-weight: 600;
  color: #333;
}

.record-time {
  font-size: 22rpx;
  color: #999;
}

.record-action {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 4rpx;
}

.record-desc {
  font-size: 22rpx;
  color: #999;
  line-height: 1.4;
}

.record-score {
  margin-top: 4rpx;
}

/* 空状态 */
.empty-records {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80rpx 40rpx;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
  margin-top: 16rpx;
}

/* 底部操作 */
.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  padding: 24rpx;
  border-top: 1rpx solid #f0f0f0;
  z-index: 10;
}

.export-btn {
  width: 100%;
}