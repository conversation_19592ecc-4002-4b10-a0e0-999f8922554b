<!--
  我的作品 - 专注评语管理
-->
<view class="morandi-page">
  <!-- 顶部标题 -->
  <view class="header-section">
    <view class="title-container">
      <view class="title-left">
        <view class="page-title">我的作品</view>
        <view class="page-subtitle">管理和编辑已生成的评语</view>
      </view>
      <view class="title-right">
        <view class="action-buttons">
          <view class="clear-btn" bindtap="clearAllComments">
            <van-icon name="delete-o" size="18px" color="#e74c3c" />
            <text class="clear-text">清空</text>
          </view>
          <view class="export-btn" bindtap="exportComments">
            <van-icon name="upgrade" size="18px" color="#5470C6" />
            <text class="export-text">导出</text>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 搜索和筛选 -->
  <view class="search-section">
    <van-search
      value="{{searchKeyword}}"
      placeholder="搜索学生姓名或评语内容"
      bind:change="onSearchChange"
      bind:search="onSearch"
      background="rgba(255,255,255,0.8)"
      shape="round"
    />
  </view>

  <!-- 评语列表 -->
  <view class="comment-list-container">
    <view wx:if="{{commentList.length > 0}}" class="comment-list">
      <view
        wx:for="{{commentList}}"
        wx:key="id"
        class="comment-item"
        data-comment="{{item}}"
        bindtap="goToCommentDetail"
      >
        <view class="comment-header">
          <view class="student-info">
            <view class="student-avatar">
              <text class="avatar-text">{{item.surname}}</text>
            </view>
            <view class="student-details">
              <view class="student-name">{{item.studentName}}</view>
              <view class="student-class">{{item.className}}</view>
            </view>
          </view>

          <view class="comment-meta">
            <view class="comment-style">
              <van-tag size="small" type="{{item.style === 'formal' ? 'primary' : 'default'}}">
                {{item.styleText}}
              </van-tag>
            </view>
            <view class="comment-length">{{item.content.length}}字</view>
          </view>
        </view>

        <view class="comment-content">
          <view class="content-preview">{{item.content}}</view>
          <view wx:if="{{item.content.length > 100}}" class="content-more">
            <text class="more-text">展开全文</text>
            <van-icon name="arrow-down" size="12px" color="#4080FF" />
          </view>
        </view>

        <view class="comment-footer">
          <view class="comment-time">
            <van-icon name="clock-o" size="12px" color="#999" />
            <text class="time-text">{{item.createTimeText}}</text>
          </view>

          <view class="comment-actions">
            <view
              class="action-btn copy-btn"
              data-comment="{{item}}"
              catchtap="copyComment"
            >
              <van-icon name="copy" size="14px" color="#4080FF" />
            </view>
            <view
              class="action-btn edit-btn"
              data-comment="{{item}}"
              catchtap="editComment"
            >
              <van-icon name="edit" size="14px" color="#4080FF" />
            </view>
            <view
              class="action-btn regenerate-btn"
              data-comment="{{item}}"
              catchtap="regenerateComment"
            >
              <van-icon name="replay" size="14px" color="#52C873" />
            </view>
            <view
              class="action-btn delete-btn"
              data-comment="{{item}}"
              catchtap="deleteComment"
            >
              <van-icon name="delete-o" size="14px" color="#FF5247" />
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 空状态 -->
    <view wx:else class="empty-state">
      <view class="empty-icon">
        <van-icon name="chat-o" size="64px" color="#ddd" />
      </view>
      <view class="empty-text">还没有评语</view>
      <view class="empty-desc">开始为学生生成个性化评语吧</view>

      <view class="empty-actions">
        <view class="empty-action-btn primary" bindtap="goToGenerate">
          <van-icon name="plus" size="16px" color="#fff" />
          <text class="action-text">AI生成评语</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 悬浮操作按钮 -->
  <view class="fab-container">
    <view class="fab-button" bindtap="goToGenerate">
      <van-icon name="plus" size="24px" color="#fff" />
    </view>
  </view>

  <!-- 日期筛选弹窗 -->
  <van-action-sheet
    show="{{showDateSheet}}"
    title="选择时间范围"
    actions="{{dateOptions}}"
    bind:close="hideDateFilter"
    bind:select="onDateSelect"
  />

  <!-- 风格筛选弹窗 -->
  <van-action-sheet
    show="{{showStyleSheet}}"
    title="选择评语风格"
    actions="{{styleOptions}}"
    bind:close="hideStyleFilter"
    bind:select="onStyleSelect"
  />

  <!-- 删除确认弹窗 -->
  <van-dialog
    show="{{showDeleteDialog}}"
    title="确认删除"
    message="{{deleteMessage}}"
    show-cancel-button
    confirm-button-text="删除"
    confirm-button-color="#FF5247"
    bind:confirm="confirmDelete"
    bind:cancel="cancelDelete"
  />

  <!-- 重新生成确认弹窗 -->
  <van-dialog
    show="{{showRegenerateDialog}}"
    title="重新生成评语"
    message="确定要重新生成这条评语吗？原内容将被覆盖。"
    show-cancel-button
    confirm-button-text="重新生成"
    bind:confirm="confirmRegenerate"
    bind:cancel="cancelRegenerate"
  />

  <!-- 隐藏的canvas用于生成分享图片 -->
  <canvas
    canvas-id="shareCanvas"
    class="share-canvas"
    style="width: 750rpx; height: 1334rpx; position: fixed; top: -3000rpx; left: 0;"
  ></canvas>
</view>