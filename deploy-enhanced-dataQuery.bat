@echo off
chcp 65001 >nul 2>&1
echo.
echo ========================================
echo   Deploy Enhanced DataQuery Function
echo ========================================
echo.
echo Deploy dataQuery cloud function...
echo Fix Contents:
echo    - Dashboard data display issues (active teachers, today AI calls, total students)
echo    - AI tokens statistics function (based on comment content estimation)
echo    - Teacher AI usage statistics data retrieval
echo    - Field name mapping fixes
echo.

call tcb fn deploy dataQuery --env cloud1-4g85f8xlb8166ff1

echo.
echo ========================================
echo   Deployment Complete!
echo ========================================
echo.
echo Fixed Features:
echo - Active teachers data display normal
echo - Today AI calls statistics fixed
echo - Student total field mapping fixed
echo - AI tokens consumption based on comment content estimation
echo - Teacher usage statistics with aggregation queries
echo - Data fallback mechanism optimized
echo.
echo Now refresh the admin dashboard, data should display normally!
echo.
pause