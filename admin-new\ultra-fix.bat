@echo off
echo ============================================
echo           ULTRA FIX - 彻底修复错误
echo ============================================

echo 第1步：强制删除旧的errorHandler.ts文件...
if exist "src\utils\errorHandler.ts" (
    del /f /q "src\utils\errorHandler.ts"
    echo ✅ 旧文件已删除
) else (
    echo ⚠️  旧文件不存在
)

echo.
echo 第2步：清理所有缓存...
if exist "node_modules\.vite" (
    rmdir /s /q "node_modules\.vite"
    echo ✅ Vite缓存已清理
)

if exist "dist" (
    rmdir /s /q "dist"
    echo ✅ 构建目录已清理
)

echo.
echo 第3步：创建favicon.svg文件...
echo ^<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"^>^<circle cx="50" cy="50" r="40" fill="#1677ff"/^>^<text x="50" y="60" text-anchor="middle" fill="white" font-size="40"^>评^</text^>^</svg^> > public\favicon.svg
echo ✅ favicon.svg已创建

echo.
echo 第4步：重新安装依赖（可选但推荐）...
npm install

echo.
echo 第5步：启动开发服务器...
echo ============================================
echo           准备启动，请稍候...
echo ============================================
npm run dev