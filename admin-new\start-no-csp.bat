@echo off
echo ====================================
echo 评语灵感君管理后台 - 无CSP限制启动
echo ====================================
echo.

echo 正在清理缓存...
if exist node_modules\.vite rmdir /s /q node_modules\.vite
if exist dist rmdir /s /q dist

echo.
echo 正在启动开发服务器（无CSP限制）...
echo 请在浏览器中访问 http://localhost:8080
echo.

set NODE_ENV=development
set VITE_ENABLE_MOCK=true

vite --config vite.config.no-csp.ts --port 8080 --host --force --clearScreen false

pause