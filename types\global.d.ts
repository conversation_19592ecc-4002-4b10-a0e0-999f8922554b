/**
 * 全局类型定义
 * 2025年现代化版本
 */

// 微信小程序全局类型扩展
declare global {
  // 微信小程序API扩展
  namespace WechatMiniprogram {
    interface Wx {
      cloud: WxCloud
    }
  }

  // 云开发类型
  interface WxCloud {
    init(options: CloudInitOptions): void
    database(): CloudDatabase
    callFunction(options: CloudCallFunctionOptions): Promise<CloudCallFunctionResult>
    uploadFile(options: CloudUploadFileOptions): Promise<CloudUploadFileResult>
    downloadFile(options: CloudDownloadFileOptions): Promise<CloudDownloadFileResult>
  }

  interface CloudInitOptions {
    env: string
    traceUser?: boolean
  }

  interface CloudDatabase {
    collection(name: string): CloudCollection
    command: CloudCommand
  }

  interface CloudCollection {
    add(options: { data: any }): Promise<CloudAddResult>
    get(): Promise<CloudGetResult>
    where(condition: any): CloudCollection
    orderBy(field: string, order: 'asc' | 'desc'): CloudCollection
    limit(max: number): CloudCollection
    skip(offset: number): CloudCollection
    count(): Promise<CloudCountResult>
    update(options: { data: any }): Promise<CloudUpdateResult>
    remove(): Promise<CloudRemoveResult>
  }

  interface CloudCommand {
    eq(value: any): any
    neq(value: any): any
    gt(value: any): any
    gte(value: any): any
    lt(value: any): any
    lte(value: any): any
    in(values: any[]): any
    nin(values: any[]): any
    and(...expressions: any[]): any
    or(...expressions: any[]): any
  }

  // 应用全局数据类型
  interface AppGlobalData {
    userInfo: UserInfo | null
    userInfoUpdateTime: number
    token: string | null
    isLogin: boolean
    cloudEnv: string
    db: CloudDatabase | null
    stateManager: any
    errorHandler: any
    globalUtils: any
    cloudService?: any
  }

  // 用户信息类型
  interface UserInfo {
    _id: string
    openid: string
    nickName: string
    school?: string
    role?: string
    phone?: string
    email?: string
    avatar?: string
    createTime: Date
    updateTime: Date
  }

  // 学生信息类型
  interface Student {
    _id: string
    name: string
    studentId: string
    classId: string
    className: string
    teacherId: string
    gender: 'male' | 'female'
    avatar?: string
    phone?: string
    parentPhone?: string
    address?: string
    notes?: string
    createTime: Date
    updateTime: Date
  }

  // 班级信息类型
  interface ClassInfo {
    _id: string
    className: string
    grade: string
    subject: string
    description?: string
    teacherId: string
    studentCount: number
    createTime: Date
    updateTime: Date
  }

  // 行为记录类型
  interface BehaviorRecord {
    _id: string
    studentId: string
    studentName: string
    classId: string
    teacherId: string
    type: 'positive' | 'negative' | 'neutral'
    tags: string[]
    description: string
    images?: string[]
    location?: string
    createTime: Date
  }

  // 评语类型
  interface Comment {
    _id: string
    studentId: string
    studentName: string
    classId: string
    teacherId: string
    content: string
    style: 'formal' | 'warm' | 'encouraging' | 'neutral'
    type: 'daily' | 'weekly' | 'monthly' | 'term'
    aiGenerated: boolean
    quality?: number
    tags?: string[]
    createTime: Date
    updateTime: Date
  }

  // AI配置类型
  interface AIConfig {
    _id: string
    teacherId: string
    model: string
    apiKey: string
    apiUrl: string
    temperature: number
    maxTokens: number
    templates: AITemplate[]
    createTime: Date
    updateTime: Date
  }

  interface AITemplate {
    id: string
    name: string
    type: 'daily' | 'weekly' | 'monthly' | 'term'
    style: 'formal' | 'warm' | 'encouraging' | 'neutral'
    prompt: string
    enabled: boolean
  }

  // API响应类型
  interface ApiResponse<T = any> {
    success: boolean
    data?: T
    message?: string
    error?: string
    code?: number
  }

  // 云函数调用结果类型
  interface CloudCallFunctionResult {
    result: any
    requestID: string
    errMsg: string
  }

  interface CloudCallFunctionOptions {
    name: string
    data?: any
  }

  // 其他云开发类型
  interface CloudAddResult {
    _id: string
    errMsg: string
  }

  interface CloudGetResult {
    data: any[]
    errMsg: string
  }

  interface CloudCountResult {
    total: number
    errMsg: string
  }

  interface CloudUpdateResult {
    stats: {
      updated: number
      created: number
    }
    errMsg: string
  }

  interface CloudRemoveResult {
    stats: {
      removed: number
    }
    errMsg: string
  }

  interface CloudUploadFileOptions {
    cloudPath: string
    filePath: string
  }

  interface CloudUploadFileResult {
    fileID: string
    statusCode: number
    errMsg: string
  }

  interface CloudDownloadFileOptions {
    fileID: string
  }

  interface CloudDownloadFileResult {
    tempFilePath: string
    statusCode: number
    errMsg: string
  }
}

export {}
