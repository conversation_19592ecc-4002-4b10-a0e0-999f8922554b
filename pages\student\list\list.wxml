<!--
  学生管理页面 - 莫兰迪设计风格
-->
<view class="morandi-page" bindtap="closeAllSwipeMenus">
  <!-- 页面标题区域 -->
  <view class="page-header">
    <view class="header-left">
      <view class="page-title">学生管理</view>
      <view class="page-subtitle">管理班级学生信息</view>
    </view>
    <view class="header-actions" wx:if="{{totalStudents > 0}}">
      <view class="header-action-btn clear-btn" bindtap="onClearAllStudents">
        <text class="action-btn-text">清空</text>
      </view>
    </view>
  </view>

  <!-- 搜索区域 -->
  <view class="search-section">
    <view class="search-card">
      <view class="search-input-wrapper">
        <view class="search-icon">🔍</view>
        <input
          class="search-input"
          placeholder="搜索学生姓名或班级"
          value="{{searchKeyword}}"
          bindinput="onSearchChange"
        />
        <view class="search-clear {{searchKeyword ? 'show' : ''}}" bindtap="clearSearch">
          <text class="clear-icon">×</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 统计信息卡片 -->
  <view class="stats-section-clean">
    <view class="stats-title-clean">学生统计</view>
    <view class="stats-row-clean">
      <view class="stat-item-clean">
        <text class="stat-number-clean">{{totalStudents}}</text>
        <text class="stat-label-clean">总学生</text>
      </view>
      <view class="stat-divider"></view>
      <view class="stat-item-clean">
        <text class="stat-number-clean">{{activeStudents}}</text>
        <text class="stat-label-clean">活跃学生</text>
      </view>
      <view class="stat-divider"></view>
      <view class="stat-item-clean">
        <text class="stat-number-clean">{{todayRecords}}</text>
        <text class="stat-label-clean">今日记录</text>
      </view>
    </view>
  </view>

  <!-- 学生列表 -->
  <view class="student-list-container">
    <view wx:if="{{studentList.length > 0}}" class="student-list">
      <view
        wx:for="{{studentList}}"
        wx:key="id"
        class="student-swipe-item"
      >
        <!-- 左滑操作按钮 -->
        <view class="swipe-actions" style="transform: translateX({{(item.translateX || 0) + 320}}rpx); transition: transform {{item.touching ? '0s' : '0.3s'}} ease;">
          <view
            class="swipe-action-btn edit-btn"
            data-student="{{item}}"
            catchtap="editStudent"
          >
            <text class="swipe-action-text">编辑</text>
          </view>
          <view
            class="swipe-action-btn delete-btn"
            data-student="{{item}}"
            catchtap="deleteStudent"
          >
            <text class="swipe-action-text">删除</text>
          </view>
        </view>

        <!-- 学生卡片内容 -->
        <view
          class="student-card"
          data-student="{{item}}"
          bindtouchstart="onTouchStart"
          bindtouchmove="onTouchMove"
          bindtouchend="onTouchEnd"
          catchtap="onCardTap"
          data-index="{{index}}"
          style="transform: translateX({{item.translateX || 0}}rpx); transition: transform {{item.touching ? '0s' : '0.3s'}} ease;"
        >
          <view class="student-card-header">
            <view class="student-avatar-wrapper">
              <view class="student-avatar-compact">
                <image
                  wx:if="{{item.avatar}}"
                  src="{{item.avatar}}"
                  class="avatar-image-compact"
                  mode="aspectFill"
                />
                <view wx:else class="avatar-placeholder-compact">
                  <text class="avatar-text-compact">{{item.surname}}</text>
                </view>
              </view>
            </view>

            <view class="student-main-info">
              <view class="student-name-compact">{{item.name}}</view>
              <view class="student-class-number-row">
                <text class="student-class-compact">{{item.className}}</text>
                <text class="student-number-compact">学号: {{item.studentNumber}}</text>
              </view>
            </view>
          </view>

          <view class="student-card-stats">
            <view class="mini-stat-item">
              <text class="mini-stat-number">{{item.recordCount || 0}}</text>
              <text class="mini-stat-label">记录数</text>
            </view>
            <view class="mini-stat-item">
              <text class="mini-stat-number">{{item.avgScore || '0.0'}}</text>
              <text class="mini-stat-label">平均分</text>
            </view>
            <view class="mini-stat-item">
              <text class="mini-stat-number">{{item.commentCount || 0}}</text>
              <text class="mini-stat-label">评语数</text>
            </view>
            <view class="mini-stat-item">
              <text class="mini-stat-time">{{item.lastRecordTime || '暂无'}}</text>
              <text class="mini-stat-label">最近记录</text>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 空状态 -->
    <view wx:else class="empty-state-clean">
      <view class="empty-icon-clean">👥</view>
      <text class="empty-title-clean">还没有学生信息</text>
      <text class="empty-desc-clean">点击右下角加号开始添加学生</text>
    </view>
  </view>

  <!-- 悬浮操作按钮 - 统一样式 -->
  <view class="create-fab" bindtap="goToAddStudent">
    <text class="fab-icon">+</text>
  </view>

  <!-- 班级筛选弹窗 -->
  <van-action-sheet
    show="{{showClassSheet}}"
    title="选择班级"
    actions="{{classOptions}}"
    bind:close="hideClassFilter"
    bind:select="onClassSelect"
  />

  <!-- 学生操作菜单 -->
  <van-action-sheet
    show="{{showActionSheet}}"
    title="学生操作"
    actions="{{actionSheetActions}}"
    bind:close="hideStudentActions"
    bind:select="onActionSelect"
  />

  <!-- 导出选项弹窗 -->
  <van-action-sheet
    show="{{showExportSheet}}"
    title="选择导出格式"
    actions="{{exportOptions}}"
    bind:close="hideExportOptions"
    bind:select="onExportSelect"
  />

  <!-- 导出进度弹窗 -->
  <van-dialog
    show="{{showExportProgress}}"
    title="导出进度"
    message="{{exportProgressMessage}}"
    show-cancel-button="{{false}}"
    show-confirm-button="{{!exporting}}"
    confirm-button-text="确定"
    bind:confirm="hideExportProgress"
  >
    <view wx:if="{{exporting}}" class="export-progress">
      <van-loading size="24px" color="#4080FF" />
      <text class="progress-text">{{exportProgressMessage}}</text>
    </view>
  </van-dialog>

  <!-- 加载状态 -->
  <van-loading wx:if="{{loading}}" type="spinner" />

  <!-- 隐藏的canvas用于生成分享图片 -->
  <canvas
    canvas-id="shareCanvas"
    class="share-canvas"
    style="width: 375px; height: 667px; position: fixed; top: -2000px; left: -1000px; z-index: -999;"
  ></canvas>
</view>