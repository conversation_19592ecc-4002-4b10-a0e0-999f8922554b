/**
 * 现代化云服务层
 * 2025年企业级标准，支持TypeScript、错误处理、性能监控
 */

import { z } from 'zod'
import { monitoring } from '../utils/monitoring'

// 数据验证Schema
const StudentSchema = z.object({
  name: z.string().min(1).max(50),
  studentId: z.string().min(1).max(20),
  classId: z.string().min(1),
  className: z.string().min(1),
  teacherId: z.string().min(1),
  gender: z.enum(['male', 'female']),
  avatar: z.string().optional(),
  phone: z.string().optional(),
  parentPhone: z.string().optional(),
  address: z.string().optional(),
  notes: z.string().optional()
})

const CommentSchema = z.object({
  studentId: z.string().min(1),
  studentName: z.string().min(1),
  classId: z.string().min(1),
  teacherId: z.string().min(1),
  content: z.string().min(1).max(1000),
  style: z.enum(['formal', 'warm', 'encouraging', 'neutral']),
  type: z.enum(['daily', 'weekly', 'monthly', 'term']),
  aiGenerated: z.boolean(),
  quality: z.number().min(0).max(10).optional(),
  tags: z.array(z.string()).optional()
})

const BehaviorRecordSchema = z.object({
  studentId: z.string().min(1),
  studentName: z.string().min(1),
  classId: z.string().min(1),
  teacherId: z.string().min(1),
  type: z.enum(['positive', 'negative', 'neutral']),
  tags: z.array(z.string()).min(1),
  description: z.string().min(1).max(500),
  images: z.array(z.string()).optional(),
  location: z.string().optional()
})

// 查询选项类型
interface QueryOptions {
  page?: number
  limit?: number
  orderBy?: string
  order?: 'asc' | 'desc'
  where?: Record<string, any>
}

// API响应类型
interface ServiceResponse<T = any> {
  success: boolean
  data?: T
  message?: string
  error?: string
  total?: number
}

class ModernCloudService {
  private db: CloudDatabase | null = null
  private initialized = false
  private retryCount = 0
  private maxRetries = 3

  constructor() {
    this.init()
  }

  /**
   * 初始化云数据库
   */
  async init(): Promise<void> {
    try {
      if (!wx.cloud) {
        throw new Error('微信云开发未初始化')
      }

      this.db = wx.cloud.database()
      this.initialized = true
      
      monitoring.trackEvent('cloud_service_initialized', {
        success: true,
        retryCount: this.retryCount
      })

      console.log('🚀 Modern Cloud Service initialized')
    } catch (error) {
      this.retryCount++
      monitoring.captureError({
        message: `云服务初始化失败: ${error}`,
        category: 'api',
        level: 'error',
        context: { retryCount: this.retryCount }
      })

      if (this.retryCount < this.maxRetries) {
        console.warn(`云服务初始化失败，${this.retryCount}/${this.maxRetries} 次重试...`)
        setTimeout(() => this.init(), 1000 * this.retryCount)
      } else {
        throw new Error('云服务初始化失败，已达到最大重试次数')
      }
    }
  }

  /**
   * 确保服务已初始化
   */
  private ensureInitialized(): void {
    if (!this.initialized || !this.db) {
      throw new Error('云服务未初始化')
    }
  }

  /**
   * 通用查询方法
   */
  private async query<T>(
    collectionName: string,
    options: QueryOptions = {}
  ): Promise<ServiceResponse<T[]>> {
    const startTime = Date.now()
    
    try {
      this.ensureInitialized()
      
      const {
        page = 1,
        limit = 20,
        orderBy = 'createTime',
        order = 'desc',
        where = {}
      } = options

      let query = this.db!.collection(collectionName)

      // 添加查询条件
      if (Object.keys(where).length > 0) {
        query = query.where(where)
      }

      // 添加排序
      query = query.orderBy(orderBy, order)

      // 分页
      const skip = (page - 1) * limit
      query = query.skip(skip).limit(limit)

      // 执行查询
      const [countResult, dataResult] = await Promise.all([
        this.db!.collection(collectionName).where(where).count(),
        query.get()
      ])

      const duration = Date.now() - startTime
      monitoring.trackApiCall(`query_${collectionName}`, duration, true, {
        page,
        limit,
        total: countResult.total
      })

      return {
        success: true,
        data: dataResult.data as T[],
        total: countResult.total
      }
    } catch (error) {
      const duration = Date.now() - startTime
      monitoring.trackApiCall(`query_${collectionName}`, duration, false)
      monitoring.captureError({
        message: `查询${collectionName}失败: ${error}`,
        category: 'api',
        level: 'error',
        context: { collectionName, options }
      })

      return {
        success: false,
        error: error instanceof Error ? error.message : '查询失败'
      }
    }
  }

  /**
   * 通用添加方法
   */
  private async add<T>(
    collectionName: string,
    data: T,
    schema?: z.ZodSchema
  ): Promise<ServiceResponse<{ _id: string }>> {
    const startTime = Date.now()
    
    try {
      this.ensureInitialized()

      // 数据验证
      if (schema) {
        schema.parse(data)
      }

      // 添加时间戳
      const dataWithTimestamp = {
        ...data,
        createTime: new Date(),
        updateTime: new Date()
      }

      const result = await this.db!.collection(collectionName).add({
        data: dataWithTimestamp
      })

      const duration = Date.now() - startTime
      monitoring.trackApiCall(`add_${collectionName}`, duration, true)

      return {
        success: true,
        data: { _id: result._id }
      }
    } catch (error) {
      const duration = Date.now() - startTime
      monitoring.trackApiCall(`add_${collectionName}`, duration, false)
      monitoring.captureError({
        message: `添加${collectionName}失败: ${error}`,
        category: 'api',
        level: 'error',
        context: { collectionName, data }
      })

      return {
        success: false,
        error: error instanceof Error ? error.message : '添加失败'
      }
    }
  }

  /**
   * 通用更新方法
   */
  private async update<T>(
    collectionName: string,
    id: string,
    data: Partial<T>
  ): Promise<ServiceResponse<void>> {
    const startTime = Date.now()
    
    try {
      this.ensureInitialized()

      const updateData = {
        ...data,
        updateTime: new Date()
      }

      await this.db!.collection(collectionName).doc(id).update({
        data: updateData
      })

      const duration = Date.now() - startTime
      monitoring.trackApiCall(`update_${collectionName}`, duration, true)

      return {
        success: true,
        message: '更新成功'
      }
    } catch (error) {
      const duration = Date.now() - startTime
      monitoring.trackApiCall(`update_${collectionName}`, duration, false)
      monitoring.captureError({
        message: `更新${collectionName}失败: ${error}`,
        category: 'api',
        level: 'error',
        context: { collectionName, id, data }
      })

      return {
        success: false,
        error: error instanceof Error ? error.message : '更新失败'
      }
    }
  }

  /**
   * 通用删除方法
   */
  private async remove(
    collectionName: string,
    id: string
  ): Promise<ServiceResponse<void>> {
    const startTime = Date.now()
    
    try {
      this.ensureInitialized()

      await this.db!.collection(collectionName).doc(id).remove()

      const duration = Date.now() - startTime
      monitoring.trackApiCall(`remove_${collectionName}`, duration, true)

      return {
        success: true,
        message: '删除成功'
      }
    } catch (error) {
      const duration = Date.now() - startTime
      monitoring.trackApiCall(`remove_${collectionName}`, duration, false)
      monitoring.captureError({
        message: `删除${collectionName}失败: ${error}`,
        category: 'api',
        level: 'error',
        context: { collectionName, id }
      })

      return {
        success: false,
        error: error instanceof Error ? error.message : '删除失败'
      }
    }
  }

  // 学生管理方法
  async getStudents(options: QueryOptions = {}): Promise<ServiceResponse<Student[]>> {
    return this.query<Student>('students', options)
  }

  async addStudent(student: Omit<Student, '_id' | 'createTime' | 'updateTime'>): Promise<ServiceResponse<{ _id: string }>> {
    return this.add('students', student, StudentSchema)
  }

  async updateStudent(id: string, student: Partial<Student>): Promise<ServiceResponse<void>> {
    return this.update('students', id, student)
  }

  async removeStudent(id: string): Promise<ServiceResponse<void>> {
    return this.remove('students', id)
  }

  // 评语管理方法
  async getComments(options: QueryOptions = {}): Promise<ServiceResponse<Comment[]>> {
    return this.query<Comment>('comments', options)
  }

  async addComment(comment: Omit<Comment, '_id' | 'createTime' | 'updateTime'>): Promise<ServiceResponse<{ _id: string }>> {
    return this.add('comments', comment, CommentSchema)
  }

  async updateComment(id: string, comment: Partial<Comment>): Promise<ServiceResponse<void>> {
    return this.update('comments', id, comment)
  }

  async removeComment(id: string): Promise<ServiceResponse<void>> {
    return this.remove('comments', id)
  }

  // 行为记录管理方法
  async getBehaviorRecords(options: QueryOptions = {}): Promise<ServiceResponse<BehaviorRecord[]>> {
    return this.query<BehaviorRecord>('records', options)
  }

  async addBehaviorRecord(record: Omit<BehaviorRecord, '_id' | 'createTime'>): Promise<ServiceResponse<{ _id: string }>> {
    return this.add('records', record, BehaviorRecordSchema)
  }

  async updateBehaviorRecord(id: string, record: Partial<BehaviorRecord>): Promise<ServiceResponse<void>> {
    return this.update('records', id, record)
  }

  async removeBehaviorRecord(id: string): Promise<ServiceResponse<void>> {
    return this.remove('records', id)
  }

  // 班级管理方法
  async getClasses(options: QueryOptions = {}): Promise<ServiceResponse<ClassInfo[]>> {
    return this.query<ClassInfo>('classes', options)
  }

  async addClass(classInfo: Omit<ClassInfo, '_id' | 'createTime' | 'updateTime'>): Promise<ServiceResponse<{ _id: string }>> {
    return this.add('classes', classInfo)
  }

  async updateClass(id: string, classInfo: Partial<ClassInfo>): Promise<ServiceResponse<void>> {
    return this.update('classes', id, classInfo)
  }

  async removeClass(id: string): Promise<ServiceResponse<void>> {
    return this.remove('classes', id)
  }
}

// 创建全局实例
const modernCloudService = new ModernCloudService()

export { ModernCloudService, modernCloudService }
export type { QueryOptions, ServiceResponse }
