/**
 * 一键清理测试数据工具
 * 提供完整的测试数据清理解决方案
 */

const { performFullCleanup } = require('./dataCleanup');
const { performProductionCheck, validateDatabaseConnection } = require('./productionDataValidator');

/**
 * 一键清理所有测试数据
 */
async function oneClickCleanup(options = {}) {
  const {
    showProgress = true,
    cleanLocal = true,
    cleanDatabase = true,
    validate = true
  } = options;
  
  console.log('🚀 开始一键清理测试数据...');
  
  const results = {
    startTime: new Date().toISOString(),
    steps: [],
    success: false,
    summary: {}
  };
  
  try {
    // 步骤1：清理本地数据
    if (cleanLocal) {
      if (showProgress) {
        wx.showLoading({ title: '清理本地数据...', mask: true });
      }
      
      console.log('📱 步骤1：清理本地测试数据');
      const localResult = performFullCleanup(null, { showToast: false });
      
      results.steps.push({
        step: 1,
        name: '本地数据清理',
        success: localResult.localCleanup,
        details: localResult
      });
      
      if (showProgress) {
        wx.hideLoading();
      }
    }
    
    // 步骤2：清理云端数据库
    if (cleanDatabase) {
      if (showProgress) {
        wx.showLoading({ title: '清理云端数据...', mask: true });
      }
      
      console.log('☁️ 步骤2：清理云端数据库');
      const databaseResult = await cleanCloudDatabase();
      
      results.steps.push({
        step: 2,
        name: '云端数据清理',
        success: databaseResult.success,
        details: databaseResult
      });
      
      if (showProgress) {
        wx.hideLoading();
      }
    }
    
    // 步骤3：验证清理结果
    if (validate) {
      if (showProgress) {
        wx.showLoading({ title: '验证清理结果...', mask: true });
      }
      
      console.log('🔍 步骤3：验证清理结果');
      const validationResult = await validateCleanupResult();
      
      results.steps.push({
        step: 3,
        name: '清理结果验证',
        success: validationResult.isClean,
        details: validationResult
      });
      
      if (showProgress) {
        wx.hideLoading();
      }
    }
    
    // 生成清理报告
    results.success = results.steps.every(step => step.success);
    results.endTime = new Date().toISOString();
    results.summary = generateCleanupSummary(results);
    
    // 显示结果
    if (showProgress) {
      showCleanupResult(results);
    }
    
    console.log('✅ 一键清理完成:', results);
    return results;
    
  } catch (error) {
    console.error('❌ 一键清理失败:', error);
    
    if (showProgress) {
      wx.hideLoading();
      wx.showModal({
        title: '清理失败',
        content: `清理过程中发生错误：${error.message}`,
        showCancel: false
      });
    }
    
    results.success = false;
    results.error = error.message;
    return results;
  }
}

/**
 * 清理云端数据库
 */
async function cleanCloudDatabase() {
  try {
    console.log('调用云函数清理数据库...');
    
    const result = await new Promise((resolve, reject) => {
      wx.cloud.callFunction({
        name: 'clearTestData',
        success: res => resolve(res.result),
        fail: err => reject(err)
      });
    });
    
    return result;
  } catch (error) {
    console.error('云端数据清理失败:', error);
    return {
      success: false,
      error: error.message,
      message: '云端数据清理失败，请检查云函数是否正确部署'
    };
  }
}

/**
 * 验证清理结果
 */
async function validateCleanupResult() {
  try {
    console.log('验证清理结果...');
    
    // 执行生产环境检查
    const productionCheck = performProductionCheck();
    
    // 验证数据库连接
    const dbConnection = await validateDatabaseConnection();
    
    return {
      isClean: productionCheck.isClean && dbConnection.isConnected,
      productionCheck,
      dbConnection,
      message: productionCheck.isClean && dbConnection.isConnected 
        ? '✅ 清理验证通过，环境已净化'
        : '❌ 清理验证发现问题，请检查详细信息'
    };
  } catch (error) {
    console.error('清理结果验证失败:', error);
    return {
      isClean: false,
      error: error.message,
      message: '验证过程中发生错误'
    };
  }
}

/**
 * 生成清理报告摘要
 */
function generateCleanupSummary(results) {
  const summary = {
    totalSteps: results.steps.length,
    successSteps: results.steps.filter(step => step.success).length,
    failedSteps: results.steps.filter(step => !step.success).length,
    overallSuccess: results.success
  };
  
  // 统计清理数据
  let totalDeleted = 0;
  results.steps.forEach(step => {
    if (step.details && step.details.totalDeleted) {
      totalDeleted += step.details.totalDeleted;
    }
  });
  
  summary.totalDeleted = totalDeleted;
  summary.message = results.success 
    ? `🎉 清理成功！共删除 ${totalDeleted} 条测试数据`
    : `⚠️ 清理过程中遇到问题，请查看详细日志`;
  
  return summary;
}

/**
 * 显示清理结果
 */
function showCleanupResult(results) {
  const { summary } = results;
  
  if (results.success) {
    wx.showModal({
      title: '🎉 清理完成',
      content: `${summary.message}\n\n✅ 成功步骤：${summary.successSteps}/${summary.totalSteps}\n📊 删除数据：${summary.totalDeleted}条\n\n您的小程序现在只包含真实数据！`,
      showCancel: false,
      confirmText: '太好了'
    });
  } else {
    wx.showModal({
      title: '⚠️ 清理完成（有问题）',
      content: `${summary.message}\n\n✅ 成功步骤：${summary.successSteps}/${summary.totalSteps}\n❌ 失败步骤：${summary.failedSteps}/${summary.totalSteps}\n\n请查看控制台了解详细信息`,
      showCancel: false,
      confirmText: '知道了'
    });
  }
}

/**
 * 快速清理（仅本地）
 */
function quickCleanup() {
  return oneClickCleanup({
    cleanLocal: true,
    cleanDatabase: false,
    validate: false
  });
}

/**
 * 完整清理（本地+云端+验证）
 */
function fullCleanup() {
  return oneClickCleanup({
    cleanLocal: true,
    cleanDatabase: true,
    validate: true
  });
}

/**
 * 静默清理（不显示进度）
 */
function silentCleanup() {
  return oneClickCleanup({
    showProgress: false,
    cleanLocal: true,
    cleanDatabase: false,
    validate: true
  });
}

module.exports = {
  oneClickCleanup,
  quickCleanup,
  fullCleanup,
  silentCleanup,
  cleanCloudDatabase,
  validateCleanupResult
};
