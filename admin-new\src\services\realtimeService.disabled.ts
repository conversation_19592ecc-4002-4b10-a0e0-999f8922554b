/**
 * 禁用的实时通信服务
 * 提供兼容接口但不进行实际连接
 */

export interface RealtimeMessage {
  type: 'user_activity' | 'tokens_update' | 'comment_generated' | 'system_status' | 'config_update' | 'user_limit_update'
  payload: any
  timestamp: number
  source: 'miniprogram' | 'admin'
  id: string
}

export interface ConnectionConfig {
  wsUrl: string
  apiUrl: string
  token: string
  reconnectInterval: number
  maxReconnectAttempts: number
}

export interface DataSyncConfig {
  enableUserSync: boolean
  enableTokensSync: boolean
  enableCommentsSync: boolean
  enableActivitiesSync: boolean
  syncInterval: number
}

export class RealtimeService {
  private config: ConnectionConfig
  private syncConfig: DataSyncConfig
  private listeners: Map<string, ((message: RealtimeMessage) => void)[]> = new Map()
  private isConnected = false

  constructor(config: ConnectionConfig, syncConfig: DataSyncConfig) {
    this.config = config
    this.syncConfig = syncConfig
    console.log('🚫 实时服务已禁用 - 使用HTTP API替代')
  }

  /**
   * 禁用的连接方法
   */
  async connect(): Promise<void> {
    console.log('🚫 WebSocket连接已禁用，使用HTTP API')
    this.isConnected = false
    return Promise.resolve()
  }

  /**
   * 禁用的断开连接方法
   */
  disconnect(): void {
    console.log('🚫 实时服务断开连接（已禁用）')
    this.isConnected = false
  }

  /**
   * 禁用的消息发送
   */
  sendMessage(message: RealtimeMessage): void {
    console.log('🚫 消息发送已禁用:', message.type)
  }

  /**
   * 添加消息监听器
   */
  addListener(type: string, callback: (message: RealtimeMessage) => void): void {
    if (!this.listeners.has(type)) {
      this.listeners.set(type, [])
    }
    this.listeners.get(type)!.push(callback)
  }

  /**
   * 移除消息监听器
   */
  removeListener(type: string, callback: (message: RealtimeMessage) => void): void {
    const callbacks = this.listeners.get(type)
    if (callbacks) {
      const index = callbacks.indexOf(callback)
      if (index > -1) {
        callbacks.splice(index, 1)
      }
    }
  }

  /**
   * 获取连接状态
   */
  isConnectedState(): boolean {
    return false // 始终返回未连接状态
  }

  /**
   * 获取连接统计
   */
  getStats() {
    return {
      connected: false,
      reconnectAttempts: 0,
      messagesSent: 0,
      messagesReceived: 0,
      lastActivity: null
    }
  }
}

// 默认配置（禁用状态）
const defaultConfig: ConnectionConfig = {
  wsUrl: '',
  apiUrl: '',
  token: '',
  reconnectInterval: 0,
  maxReconnectAttempts: 0
}

const defaultSyncConfig: DataSyncConfig = {
  enableUserSync: false,
  enableTokensSync: false,
  enableCommentsSync: false,
  enableActivitiesSync: false,
  syncInterval: 0
}

// 导出禁用的实例
export const realtimeService = new RealtimeService(defaultConfig, defaultSyncConfig)
export default realtimeService