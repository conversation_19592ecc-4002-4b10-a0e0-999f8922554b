// CSP兼容性检测工具
console.log('🔍 CSP兼容性检测开始...\n');

// 检测1: eval支持
console.log('📋 检测项目:');
console.log('1. eval() 函数支持');
try {
  eval('console.log("  ✅ eval() 可以正常使用")');
} catch (e) {
  console.log('  ❌ eval() 被CSP阻止:', e.message);
}

// 检测2: new Function支持
console.log('2. new Function() 支持');
try {
  const fn = new Function('return "  ✅ new Function() 可以正常使用"');
  console.log(fn());
} catch (e) {
  console.log('  ❌ new Function() 被CSP阻止:', e.message);
}

// 检测3: setTimeout字符串支持
console.log('3. setTimeout(string) 支持');
try {
  setTimeout('console.log("  ✅ setTimeout(string) 可以正常使用")', 0);
} catch (e) {
  console.log('  ❌ setTimeout(string) 被CSP阻止:', e.message);
}

// 检测4: 浏览器环境检测
console.log('4. 浏览器环境');
if (typeof window !== 'undefined') {
  console.log('  ✅ 浏览器环境');
  
  // 检测CSP头
  if (typeof document !== 'undefined') {
    console.log('5. CSP策略检测');
    const meta = document.querySelector('meta[http-equiv="Content-Security-Policy"]');
    if (meta) {
      console.log('  📋 发现CSP meta标签:', meta.content);
    } else {
      console.log('  ✅ 未发现CSP meta标签');
    }
  }
} else {
  console.log('  ✅ Node.js环境');
}

// 检测5: React DevTools兼容性
console.log('6. React DevTools检测');
if (typeof window !== 'undefined' && window.__REACT_DEVTOOLS_GLOBAL_HOOK__) {
  console.log('  ✅ React DevTools已安装');
} else {
  console.log('  ℹ️  React DevTools未检测到');
}

console.log('\n🎯 建议解决方案:');
console.log('1. 使用 start-no-eval.bat 启动（推荐）');
console.log('2. 清除浏览器缓存和数据');
console.log('3. 使用无痕模式测试');
console.log('4. 检查浏览器扩展是否强制CSP');
console.log('5. 尝试不同的浏览器');

console.log('\n✅ CSP检测完成!');