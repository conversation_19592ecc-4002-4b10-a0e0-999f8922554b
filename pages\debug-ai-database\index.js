// 调试AI数据库配置页面
Page({
  data: {
    debugResults: '',
    isChecking: false
  },

  onLoad() {
    console.log('AI数据库调试页面加载完成')
  },

  // 检查AI配置数据
  async checkAIConfig() {
    try {
      this.setData({ 
        isChecking: true,
        debugResults: '正在检查AI配置数据...\n'
      })

      // 初始化云开发
      if (!wx.cloud) {
        throw new Error('云开发未初始化')
      }

      const db = wx.cloud.database()
      
      // 1. 查看system_config集合的所有数据
      console.log('🔍 开始查看system_config集合数据')
      this.appendResult('1. 查看system_config集合所有数据：')
      
      const allConfigs = await db.collection('system_config').get()
      this.appendResult(`总配置数量: ${allConfigs.data.length}`)
      
      allConfigs.data.forEach((config, index) => {
        this.appendResult(`配置${index + 1}: {`)
        this.appendResult(`  _id: ${config._id}`)
        this.appendResult(`  type: ${config.type}`)
        this.appendResult(`  status: ${config.status}`)
        this.appendResult(`  hasApiKey: ${!!config.apiKey}`)
        this.appendResult(`  apiKeyLength: ${config.apiKey ? config.apiKey.length : 0}`)
        this.appendResult(`  updateTime: ${config.updateTime}`)
        this.appendResult(`  所有字段: [${Object.keys(config).join(', ')}]`)
        this.appendResult(`}`)
      })

      // 2. 查看type为ai_config的数据
      this.appendResult('\n2. 查看type=ai_config的数据：')
      const aiConfigs = await db.collection('system_config')
        .where({
          type: 'ai_config'
        })
        .get()
      
      this.appendResult(`AI配置数量: ${aiConfigs.data.length}`)
      aiConfigs.data.forEach((config, index) => {
        this.appendResult(`AI配置${index + 1}: ${JSON.stringify(config, null, 2)}`)
      })

      // 3. 查看status为active的数据
      this.appendResult('\n3. 查看status=active的数据：')
      const activeConfigs = await db.collection('system_config')
        .where({
          status: 'active'
        })
        .get()
      
      this.appendResult(`活跃配置数量: ${activeConfigs.data.length}`)
      activeConfigs.data.forEach((config, index) => {
        this.appendResult(`活跃配置${index + 1}: ${JSON.stringify(config, null, 2)}`)
      })

      // 4. 使用云函数相同的查询条件
      this.appendResult('\n4. 使用云函数相同的查询条件：')
      const configResult = await db.collection('system_config')
        .where({
          type: 'ai_config',
          status: 'active'
        })
        .orderBy('updateTime', 'desc')
        .limit(1)
        .get()
      
      this.appendResult(`查询结果数量: ${configResult.data.length}`)
      
      if (configResult.data && configResult.data.length > 0) {
        const dbConfig = configResult.data[0]
        this.appendResult('找到的AI配置详情：')
        this.appendResult(`- model: ${dbConfig.model}`)
        this.appendResult(`- apiUrl: ${dbConfig.apiUrl}`)
        this.appendResult(`- apiKey存在: ${!!dbConfig.apiKey}`)
        this.appendResult(`- apiKey长度: ${dbConfig.apiKey ? dbConfig.apiKey.length : 0}`)
        this.appendResult(`- apiKey前10位: ${dbConfig.apiKey ? dbConfig.apiKey.substring(0, 10) + '...' : '无'}`)
        this.appendResult(`- updateTime: ${dbConfig.updateTime}`)
        this.appendResult(`- 所有字段: [${Object.keys(dbConfig).join(', ')}]`)
      } else {
        this.appendResult('❌ 未找到匹配的AI配置!')
        this.appendResult('这就是问题所在：云函数找不到有效的AI配置')
      }

      this.appendResult('\n✅ 检查完成!')
      
    } catch (error) {
      console.error('检查AI配置失败:', error)
      this.appendResult(`❌ 检查过程中出错: ${error.message}`)
    } finally {
      this.setData({ isChecking: false })
    }
  },

  // 添加结果到显示区域
  appendResult(text) {
    this.setData({
      debugResults: this.data.debugResults + text + '\n'
    })
  },

  // 清空结果
  clearResults() {
    this.setData({ debugResults: '' })
  },

  // 复制结果到剪贴板
  copyResults() {
    wx.setClipboardData({
      data: this.data.debugResults,
      success: () => {
        wx.showToast({ title: '已复制到剪贴板' })
      }
    })
  }
})