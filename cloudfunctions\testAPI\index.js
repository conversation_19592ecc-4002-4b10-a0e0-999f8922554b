// 简单的测试API云函数
// 专门用于测试管理后台连通性

const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

exports.main = async (event, context) => {
  console.log('🔗 TestAPI 收到请求:', event)
  
  // 处理HTTP触发器请求
  if (event.httpMethod) {
    // 处理CORS预检请求
    if (event.httpMethod === 'OPTIONS') {
      return {
        statusCode: 200,
        headers: {
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
          'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-WX-SOURCE, X-Requested-With',
          'Access-Control-Max-Age': '86400'
        },
        body: ''
      }
    }
    
    try {
      let requestData = {}
      
      // 解析请求数据
      if (event.httpMethod === 'POST' && event.body) {
        requestData = typeof event.body === 'string' ? JSON.parse(event.body) : event.body
      } else if (event.httpMethod === 'GET') {
        requestData = event.queryStringParameters || {}
      }
      
      // 简单的响应数据
      const responseData = {
        status: 'success',
        message: '测试API连通成功！',
        timestamp: new Date().toISOString(),
        requestMethod: event.httpMethod,
        requestData: requestData,
        environment: cloud.DYNAMIC_CURRENT_ENV,
        version: '1.0.0'
      }
      
      console.log('✅ TestAPI 响应:', responseData)
      
      return {
        statusCode: 200,
        headers: {
          'Content-Type': 'application/json',
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
          'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-WX-SOURCE, X-Requested-With'
        },
        body: JSON.stringify({
          code: 200,
          message: 'success',
          data: responseData,
          timestamp: Date.now()
        })
      }
      
    } catch (error) {
      console.error('❌ TestAPI 处理失败:', error)
      
      return {
        statusCode: 500,
        headers: {
          'Content-Type': 'application/json',
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
          'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-WX-SOURCE, X-Requested-With'
        },
        body: JSON.stringify({
          code: 500,
          message: error.message,
          data: null,
          timestamp: Date.now()
        })
      }
    }
  }
  
  // 普通云函数调用
  return {
    code: 200,
    message: 'success',
    data: {
      status: 'success',
      message: '测试API连通成功！',
      timestamp: new Date().toISOString(),
      environment: cloud.DYNAMIC_CURRENT_ENV,
      version: '1.0.0'
    },
    timestamp: Date.now()
  }
}
