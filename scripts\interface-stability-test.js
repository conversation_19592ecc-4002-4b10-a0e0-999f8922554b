/**
 * 接口稳定性验证脚本
 * 验证小程序和管理后台之间的核心接口稳定性
 */

const axios = require('axios')

// 配置
const CONFIG = {
  // 管理后台API
  ADMIN_API: {
    BASE_URL: process.env.ADMIN_API_URL || 'http://localhost:3000/api/admin',
    TIMEOUT: 30000
  },
  
  // 小程序API
  MINIPROGRAM_API: {
    BASE_URL: process.env.MINIPROGRAM_API_URL || 'http://localhost:8080/api',
    TIMEOUT: 10000
  },
  
  // 测试配置
  TEST: {
    RETRY_COUNT: 3,
    RETRY_DELAY: 1000,
    CONCURRENT_REQUESTS: 5,
    TEST_DURATION: 30000 // 30秒
  }
}

/**
 * 接口测试结果
 */
class InterfaceTestResult {
  constructor(name, type, endpoint) {
    this.name = name
    this.type = type
    this.endpoint = endpoint
    this.totalRequests = 0
    this.successfulRequests = 0
    this.failedRequests = 0
    this.totalTime = 0
    this.minResponseTime = Infinity
    this.maxResponseTime = 0
    this.errorDetails = []
    this.timeStamps = []
  }

  addRequest(duration, success, error = null) {
    this.totalRequests++
    
    if (success) {
      this.successfulRequests++
    } else {
      this.failedRequests++
      if (error) {
        this.errorDetails.push({
          error: error.message || String(error),
          timestamp: new Date().toISOString()
        })
        // 只保留最近10个错误
        if (this.errorDetails.length > 10) {
          this.errorDetails.shift()
        }
      }
    }
    
    this.totalTime += duration
    this.minResponseTime = Math.min(this.minResponseTime, duration)
    this.maxResponseTime = Math.max(this.maxResponseTime, duration)
    this.timeStamps.push({
      timestamp: Date.now(),
      duration,
      success
    })
  }

  getAverageResponseTime() {
    return this.totalRequests > 0 ? this.totalTime / this.totalRequests : 0
  }

  getSuccessRate() {
    return this.totalRequests > 0 ? (this.successfulRequests / this.totalRequests) * 100 : 0
  }

  getMetrics() {
    return {
      name: this.name,
      type: this.type,
      endpoint: this.endpoint,
      totalRequests: this.totalRequests,
      successfulRequests: this.successfulRequests,
      failedRequests: this.failedRequests,
      successRate: this.getSuccessRate(),
      averageResponseTime: this.getAverageResponseTime(),
      minResponseTime: this.minResponseTime === Infinity ? 0 : this.minResponseTime,
      maxResponseTime: this.maxResponseTime,
      errorDetails: this.errorDetails
    }
  }
}

/**
 * 接口稳定性测试类
 */
class InterfaceStabilityTest {
  constructor() {
    this.results = new Map()
    this.httpClient = axios.create({
      timeout: CONFIG.ADMIN_API.TIMEOUT
    })
    
    // 配置请求拦截器
    this.httpClient.interceptors.request.use(config => {
      config.metadata = { startTime: Date.now() }
      return config
    })
    
    // 配置响应拦截器
    this.httpClient.interceptors.response.use(
      response => {
        const duration = Date.now() - response.config.metadata.startTime
        return { ...response, duration }
      },
      error => {
        const duration = Date.now() - error.config?.metadata?.startTime || 0
        return Promise.reject({ ...error, duration })
      }
    )
  }

  /**
   * 初始化测试接口
   */
  initializeTests() {
    const tests = [
      // 管理后台接口
      {
        name: '管理后台健康检查',
        type: 'admin',
        endpoint: '/health',
        method: 'GET'
      },
      {
        name: '用户认证接口',
        type: 'admin',
        endpoint: '/auth/check',
        method: 'POST',
        data: { username: 'test', password: 'test' }
      },
      {
        name: '数据统计接口',
        type: 'admin',
        endpoint: '/data/stats',
        method: 'GET'
      },
      {
        name: '实时数据接口',
        type: 'admin',
        endpoint: '/realtime/getStats',
        method: 'GET'
      },
      {
        name: '用户列表接口',
        type: 'admin',
        endpoint: '/user/getUserList',
        method: 'GET'
      },
      {
        name: 'AI配置接口',
        type: 'admin',
        endpoint: '/ai/getConfig',
        method: 'GET'
      },
      
      // 小程序接口
      {
        name: '小程序登录接口',
        type: 'miniprogram',
        endpoint: '/login',
        method: 'POST',
        data: { code: 'test_code' }
      },
      {
        name: '学生数据接口',
        type: 'miniprogram',
        endpoint: '/students',
        method: 'GET'
      },
      {
        name: '评语生成接口',
        type: 'miniprogram',
        endpoint: '/generate',
        method: 'POST',
        data: { studentId: 'test_student', type: 'math' }
      },
      {
        name: '数据同步接口',
        type: 'miniprogram',
        endpoint: '/sync',
        method: 'POST',
        data: { type: 'students', action: 'sync' }
      }
    ]

    tests.forEach(test => {
      const key = `${test.type}_${test.name}`
      this.results.set(key, new InterfaceTestResult(test.name, test.type, test.endpoint))
    })

    return tests
  }

  /**
   * 单次请求测试
   */
  async testSingleEndpoint(test) {
    const result = this.results.get(`${test.type}_${test.name}`)
    const baseUrl = test.type === 'admin' ? CONFIG.ADMIN_API.BASE_URL : CONFIG.MINIPROGRAM_API.BASE_URL
    const url = baseUrl + test.endpoint

    try {
      const response = await this.httpClient.request({
        url,
        method: test.method,
        data: test.data
      })

      result.addRequest(response.duration, true)
      return { success: true, duration: response.duration }

    } catch (error) {
      result.addRequest(error.duration || 0, false, error)
      return { success: false, duration: error.duration || 0, error }
    }
  }

  /**
   * 重试机制测试
   */
  async testWithRetry(test) {
    let lastError = null
    
    for (let i = 0; i < CONFIG.TEST.RETRY_COUNT; i++) {
      try {
        const result = await this.testSingleEndpoint(test)
        if (result.success) {
          return result
        }
      } catch (error) {
        lastError = error
        if (i < CONFIG.TEST.RETRY_COUNT - 1) {
          await new Promise(resolve => setTimeout(resolve, CONFIG.TEST.RETRY_DELAY))
        }
      }
    }
    
    throw lastError
  }

  /**
   * 并发测试
   */
  async testConcurrency(tests, concurrency = CONFIG.TEST.CONCURRENT_REQUESTS) {
    const promises = []
    
    for (let i = 0; i < concurrency; i++) {
      const test = tests[Math.floor(Math.random() * tests.length)]
      promises.push(this.testWithRetry(test))
    }
    
    return Promise.allSettled(promises)
  }

  /**
   * 负载测试
   */
  async testLoad(tests, duration = CONFIG.TEST.TEST_DURATION) {
    const startTime = Date.now()
    let requestCount = 0
    
    while (Date.now() - startTime < duration) {
      const test = tests[Math.floor(Math.random() * tests.length)]
      await this.testWithRetry(test)
      requestCount++
      
      // 控制请求频率
      await new Promise(resolve => setTimeout(resolve, 100 + Math.random() * 200))
    }
    
    return {
      duration,
      requestCount,
      requestsPerSecond: requestCount / (duration / 1000)
    }
  }

  /**
   * 生成测试报告
   */
  generateReport() {
    const report = {
      testTime: new Date().toISOString(),
      summary: {
        totalEndpoints: this.results.size,
        totalRequests: 0,
        totalSuccessful: 0,
        totalFailed: 0,
        overallSuccessRate: 0,
        averageResponseTime: 0
      },
      endpoints: [],
      performance: {
        fastestEndpoint: null,
        slowestEndpoint: null,
        mostReliable: null,
        leastReliable: null
      }
    }

    let totalResponseTime = 0
    let totalSuccessfulRequests = 0

    // 收集所有端点数据
    for (const result of this.results.values()) {
      const metrics = result.getMetrics()
      report.endpoints.push(metrics)
      
      report.summary.totalRequests += metrics.totalRequests
      report.summary.totalSuccessful += metrics.successfulRequests
      report.summary.totalFailed += metrics.failedRequests
      totalResponseTime += metrics.totalTime
      
      if (metrics.successfulRequests > 0) {
        totalSuccessfulRequests += metrics.successfulRequests
      }
    }

    // 计算汇总指标
    report.summary.overallSuccessRate = report.summary.totalRequests > 0 
      ? (report.summary.totalSuccessful / report.summary.totalRequests) * 100 
      : 0
    
    report.summary.averageResponseTime = totalSuccessfulRequests > 0 
      ? totalResponseTime / totalSuccessfulRequests 
      : 0

    // 性能分析
    if (report.endpoints.length > 0) {
      report.performance.fastestEndpoint = report.endpoints.reduce((min, current) => 
        current.averageResponseTime < min.averageResponseTime ? current : min
      )
      
      report.performance.slowestEndpoint = report.endpoints.reduce((max, current) => 
        current.averageResponseTime > max.averageResponseTime ? current : max
      )
      
      report.performance.mostReliable = report.endpoints.reduce((max, current) => 
        current.successRate > max.successRate ? current : max
      )
      
      report.performance.leastReliable = report.endpoints.reduce((min, current) => 
        current.successRate < min.successRate ? current : min
      )
    }

    return report
  }

  /**
   * 显示测试报告
   */
  displayReport(report) {
    console.log('\n📊 接口稳定性测试报告')
    console.log('='.repeat(60))
    console.log(`🕒 测试时间: ${report.testTime}`)
    console.log(`📈 测试端点数: ${report.summary.totalEndpoints}`)
    console.log(`📊 总请求数: ${report.summary.totalRequests}`)
    console.log(`✅ 成功请求数: ${report.summary.totalSuccessful}`)
    console.log(`❌ 失败请求数: ${report.summary.totalFailed}`)
    console.log(`🎯 整体成功率: ${report.summary.overallSuccessRate.toFixed(2)}%`)
    console.log(`⏱️  平均响应时间: ${report.summary.averageResponseTime.toFixed(2)}ms`)
    
    console.log('\n🔍 各端点详细信息:')
    console.log('-'.repeat(60))
    
    report.endpoints.forEach(endpoint => {
      const statusIcon = endpoint.successRate >= 99 ? '🟢' : 
                         endpoint.successRate >= 95 ? '🟡' : '🔴'
      
      console.log(`${statusIcon} ${endpoint.name}`)
      console.log(`   📍 端点: ${endpoint.endpoint}`)
      console.log(`   📊 请求: ${endpoint.totalRequests} | 成功: ${endpoint.successfulRequests} | 失败: ${endpoint.failedRequests}`)
      console.log(`   🎯 成功率: ${endpoint.successRate.toFixed(2)}%`)
      console.log(`   ⏱️  响应时间: 平均${endpoint.averageResponseTime.toFixed(2)}ms | 最快${endpoint.minResponseTime}ms | 最慢${endpoint.maxResponseTime}ms`)
      
      if (endpoint.errorDetails.length > 0) {
        console.log(`   ⚠️  错误示例: ${endpoint.errorDetails[0].error}`)
      }
      console.log()
    })
    
    console.log('🏆 性能分析:')
    console.log('-'.repeat(40))
    console.log(`🚀 最快端点: ${report.performance.fastestEndpoint.name} (${report.performance.fastestEndpoint.averageResponseTime.toFixed(2)}ms)`)
    console.log(`🐌 最慢端点: ${report.performance.slowestEndpoint.name} (${report.performance.slowestEndpoint.averageResponseTime.toFixed(2)}ms)`)
    console.log(`💪 最可靠: ${report.performance.mostReliable.name} (${report.performance.mostReliable.successRate.toFixed(2)}%)`)
    console.log(`⚠️  最不稳定: ${report.performance.leastReliable.name} (${report.performance.leastReliable.successRate.toFixed(2)}%)`)
    
    // 建议
    console.log('\n💡 优化建议:')
    const suggestions = this.generateSuggestions(report)
    suggestions.forEach(suggestion => {
      console.log(`• ${suggestion}`)
    })
  }

  /**
   * 生成优化建议
   */
  generateSuggestions(report) {
    const suggestions = []
    
    if (report.summary.overallSuccessRate < 99) {
      suggestions.push('整体成功率低于99%，建议检查服务器稳定性和网络连接')
    }
    
    if (report.summary.averageResponseTime > 1000) {
      suggestions.push('平均响应时间超过1秒，建议优化数据库查询和接口性能')
    }
    
    const unreliableEndpoints = report.endpoints.filter(e => e.successRate < 95)
    if (unreliableEndpoints.length > 0) {
      suggestions.push(`发现${unreliableEndpoints.length}个不稳定端点，建议优先排查错误`)
    }
    
    const slowEndpoints = report.endpoints.filter(e => e.averageResponseTime > 2000)
    if (slowEndpoints.length > 0) {
      suggestions.push(`发现${slowEndpoints.length}个慢响应端点，建议进行性能优化`)
    }
    
    if (suggestions.length === 0) {
      suggestions.push('所有接口运行良好，继续保持')
    }
    
    return suggestions
  }

  /**
   * 运行完整测试
   */
  async runFullTest() {
    console.log('🚀 开始接口稳定性测试...')
    
    const tests = this.initializeTests()
    
    console.log('📋 基础连通性测试...')
    for (const test of tests) {
      try {
        await this.testWithRetry(test)
        console.log(`  ✅ ${test.name}`)
      } catch (error) {
        console.log(`  ❌ ${test.name}: ${error.message || '连接失败'}`)
      }
    }
    
    console.log('\n🔄 并发测试...')
    const concurrencyResults = await this.testConcurrency(tests)
    const concurrencySuccess = concurrencyResults.filter(r => r.status === 'fulfilled').length
    console.log(`  并发测试完成: ${concurrencySuccess}/${concurrencyResults.length} 成功`)
    
    console.log('\n⚡ 负载测试...')
    const loadResult = await this.testLoad(tests)
    console.log(`  负载测试完成: ${loadResult.requestCount}次请求，${loadResult.requestsPerSecond.toFixed(2)} QPS`)
    
    console.log('\n📊 生成测试报告...')
    const report = this.generateReport()
    this.displayReport(report)
    
    return report
  }
}

// 主函数
async function main() {
  try {
    const test = new InterfaceStabilityTest()
    const report = await test.runFullTest()
    
    // 输出结果到文件
    const fs = require('fs')
    const reportPath = `./stability-report-${Date.now()}.json`
    fs.writeFileSync(reportPath, JSON.stringify(report, null, 2))
    console.log(`\n📄 详细报告已保存至: ${reportPath}`)
    
    // 返回测试结果
    return report
    
  } catch (error) {
    console.error('❌ 测试执行失败:', error)
    process.exit(1)
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  main()
}

module.exports = { InterfaceStabilityTest, InterfaceTestResult }