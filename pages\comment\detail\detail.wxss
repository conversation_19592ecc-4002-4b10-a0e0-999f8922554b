/* 评语详情页面样式 */
.comment-detail-page {
  background: linear-gradient(135deg, #F0F2F5 0%, #E4E7ED 100%);
  min-height: 100vh;
  padding: 32rpx;
}

/* 加载状态 */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 400rpx;
}

/* 学生信息卡片 */
.student-card {
  background: rgba(255, 255, 255, 0.9);
  border-radius: 32rpx;
  padding: 40rpx;
  margin-bottom: 32rpx;
  box-shadow: 0 8rpx 32rpx rgba(84, 112, 198, 0.12);
  border: 1rpx solid rgba(84, 112, 198, 0.08);
  backdrop-filter: blur(10rpx);
}

.student-header {
  display: flex;
  align-items: center;
}

.student-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  background: linear-gradient(135deg, #5470C6 0%, #91CC75 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 24rpx;
}

.avatar-text {
  color: white;
  font-size: 32rpx;
  font-weight: 600;
}

.student-info {
  flex: 1;
}

.student-name {
  font-size: 36rpx;
  font-weight: 600;
  color: #2C3E50;
  margin-bottom: 8rpx;
}

.student-class {
  font-size: 28rpx;
  color: #7F8C8D;
}

.more-btn {
  padding: 16rpx;
  border-radius: 50%;
  background: rgba(84, 112, 198, 0.1);
}

/* 评语内容卡片 */
.comment-card {
  background: rgba(255, 255, 255, 0.9);
  border-radius: 32rpx;
  padding: 40rpx;
  margin-bottom: 32rpx;
  box-shadow: 0 8rpx 32rpx rgba(84, 112, 198, 0.12);
  border: 1rpx solid rgba(84, 112, 198, 0.08);
  backdrop-filter: blur(10rpx);
}

.comment-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32rpx;
  padding-bottom: 24rpx;
  border-bottom: 1rpx solid rgba(84, 112, 198, 0.1);
}

.comment-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #2C3E50;
}

.comment-meta {
  font-size: 24rpx;
  color: #95A5A6;
}

.comment-content {
  font-size: 30rpx;
  line-height: 1.8;
  color: #34495E;
  margin-bottom: 32rpx;
  padding: 32rpx;
  background: rgba(84, 112, 198, 0.05);
  border-radius: 20rpx;
  border-left: 6rpx solid #5470C6;
}

/* 评语设置信息 */
.comment-settings {
  display: flex;
  flex-wrap: wrap;
  gap: 24rpx;
}

.setting-item {
  display: flex;
  align-items: center;
  padding: 16rpx 24rpx;
  background: rgba(84, 112, 198, 0.08);
  border-radius: 16rpx;
}

.setting-label {
  font-size: 26rpx;
  color: #7F8C8D;
  margin-right: 8rpx;
}

.setting-value {
  font-size: 26rpx;
  color: #5470C6;
  font-weight: 500;
}

/* 操作按钮 */
.action-buttons {
  display: flex;
  gap: 24rpx;
}

.action-btn {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12rpx;
  padding: 24rpx;
  border-radius: 24rpx;
  font-size: 28rpx;
  font-weight: 500;
  transition: all 0.3s ease;
}

.action-btn.primary {
  background: linear-gradient(135deg, #5470C6 0%, #91CC75 100%);
  color: white;
  box-shadow: 0 8rpx 24rpx rgba(84, 112, 198, 0.3);
}

.action-btn.secondary {
  background: rgba(255, 255, 255, 0.9);
  color: #5470C6;
  border: 2rpx solid rgba(84, 112, 198, 0.2);
  box-shadow: 0 4rpx 16rpx rgba(84, 112, 198, 0.1);
}

.action-btn.smart-share {
  background: linear-gradient(135deg, #FAC858 0%, #EE6666 100%);
  color: white;
  border: none;
  box-shadow: 0 8rpx 24rpx rgba(250, 200, 88, 0.3);
}

.action-btn:active {
  transform: translateY(2rpx);
  box-shadow: 0 4rpx 12rpx rgba(84, 112, 198, 0.2);
}

/* 隐藏的Canvas样式 */
.share-canvas {
  visibility: hidden;
}