/**
 * 增长功能适配器 - 零侵入式集成现有页面
 * 只需要在页面中调用一行代码即可启用所有增长功能
 */

const { growthEngine } = require('./growthEngine')

// 页面增长功能混入 - 为现有页面无缝添加增长功能
const GrowthMixin = {
  // 页面加载时自动初始化增长功能
  onLoad(options) {
    try {
      // 获取当前页面路径
      const currentPage = getCurrentPages().slice(-1)[0]?.route || 'unknown'
      
      // 初始化增长引擎
      growthEngine.init(currentPage)
      
      // 处理分享参数（如果是通过分享进入的）
      if (options.from && options.from.startsWith('share_')) {
        growthEngine.track('share_click', { 
          from: options.from,
          page: currentPage 
        })
      }
      
      console.log('[GrowthAdapter] 页面增长功能已启用:', currentPage)
    } catch (error) {
      console.error('[GrowthAdapter] 初始化失败:', error)
    }
  },

  // 优化后的分享函数 - 直接替换现有的onShareAppMessage
  onShareAppMessage(shareContext = {}) {
    try {
      const currentPage = getCurrentPages().slice(-1)[0]?.route || 'unknown'
      const shareContent = growthEngine.getShareContent(currentPage, shareContext)
      
      console.log('[GrowthAdapter] 分享内容已优化:', shareContent)
      return shareContent
    } catch (error) {
      console.error('[GrowthAdapter] 分享优化失败:', error)
      // 降级到原有分享内容
      return {
        title: '评语灵感君 - AI智能评语生成',
        path: '/pages/index/index'
      }
    }
  },

  // 分享到朋友圈（如果支持）
  onShareTimeline() {
    try {
      const currentPage = getCurrentPages().slice(-1)[0]?.route || 'unknown'
      growthEngine.track('share_timeline', { page: currentPage })
      
      return {
        title: '评语灵感君 - 教师专用AI评语助手，让教学更轻松',
        imageUrl: '/images/share/timeline-share.jpg'
      }
    } catch (error) {
      console.error('[GrowthAdapter] 朋友圈分享失败:', error)
      return {}
    }
  },

  // 页面显示时的增长逻辑
  onShow() {
    try {
      const currentPage = getCurrentPages().slice(-1)[0]?.route || 'unknown'
      growthEngine.track('page_show', { page: currentPage })
    } catch (error) {
      console.error('[GrowthAdapter] 页面显示跟踪失败:', error)
    }
  }
}

// 简化版本 - 只需要在页面中添加一行代码
const enableGrowthFeatures = (pageInstance) => {
  if (!pageInstance) return

  try {
    // 保存原有的生命周期函数
    const originalOnLoad = pageInstance.onLoad
    const originalOnShow = pageInstance.onShow
    const originalOnShareAppMessage = pageInstance.onShareAppMessage

    // 混入增长功能
    pageInstance.onLoad = function(options) {
      // 先执行原有逻辑
      if (originalOnLoad) {
        originalOnLoad.call(this, options)
      }
      // 再执行增长逻辑
      GrowthMixin.onLoad.call(this, options)
    }

    pageInstance.onShow = function() {
      // 先执行原有逻辑
      if (originalOnShow) {
        originalOnShow.call(this)
      }
      // 再执行增长逻辑
      GrowthMixin.onShow.call(this)
    }

    pageInstance.onShareAppMessage = function(shareContext) {
      // 优先使用增长优化的分享内容
      const optimizedShare = GrowthMixin.onShareAppMessage.call(this, shareContext)
      
      // 如果有原有分享逻辑，进行合并
      if (originalOnShareAppMessage) {
        const originalShare = originalOnShareAppMessage.call(this, shareContext)
        return { ...originalShare, ...optimizedShare }
      }
      
      return optimizedShare
    }

    // 添加朋友圈分享（如果原来没有）
    if (!pageInstance.onShareTimeline) {
      pageInstance.onShareTimeline = GrowthMixin.onShareTimeline
    }

    console.log('[GrowthAdapter] 页面增长功能集成完成')
  } catch (error) {
    console.error('[GrowthAdapter] 集成失败:', error)
  }
}

// 专门的分享优化函数 - 可以在现有页面中直接使用
const getOptimizedShareContent = (customContent = {}) => {
  try {
    const currentPage = getCurrentPages().slice(-1)[0]?.route || 'unknown'
    const defaultContent = growthEngine.getShareContent(currentPage)
    
    // 合并自定义内容和优化内容
    return { ...defaultContent, ...customContent }
  } catch (error) {
    console.error('[GrowthAdapter] 分享内容优化失败:', error)
    return customContent
  }
}

// 用户行为跟踪简化函数
const trackUserAction = (actionName, actionData = {}) => {
  try {
    growthEngine.track(actionName, actionData)
  } catch (error) {
    console.error('[GrowthAdapter] 行为跟踪失败:', error)
  }
}

// 检查并显示用户福利（新用户奖励、连续访问奖励等）
const checkUserRewards = () => {
  try {
    // 检查免费次数
    const freeCount = wx.getStorageSync('free_ai_count') || 0
    if (freeCount > 0) {
      console.log('[GrowthAdapter] 用户剩余免费次数:', freeCount)
    }

    // 显示福利提示
    const lastRewardCheck = wx.getStorageSync('last_reward_check') || 0
    const now = Date.now()
    
    // 每24小时检查一次福利
    if (now - lastRewardCheck > 24 * 60 * 60 * 1000) {
      wx.setStorageSync('last_reward_check', now)
      
      // 这里可以添加其他福利检查逻辑
      console.log('[GrowthAdapter] 福利检查完成')
    }
  } catch (error) {
    console.error('[GrowthAdapter] 福利检查失败:', error)
  }
}

module.exports = {
  GrowthMixin,
  enableGrowthFeatures,
  getOptimizedShareContent,
  trackUserAction,
  checkUserRewards
}