import React from 'react'
import { Card, Typography } from 'antd'
import * as echarts from 'echarts'
import { useEffect, useRef } from 'react'

const { Title } = Typography

interface ChartProps {
  title: string
  type: 'line' | 'bar' | 'pie' | 'gauge' | 'radar'
  data: any
  height?: number
  className?: string
}

const ModernChart: React.FC<ChartProps> = ({ 
  title, 
  type, 
  data, 
  height = 300,
  className = ''
}) => {
  const chartRef = useRef<HTMLDivElement>(null)
  const chartInstance = useRef<echarts.ECharts>()

  useEffect(() => {
    if (!chartRef.current) return

    // 初始化图表
    chartInstance.current = echarts.init(chartRef.current, 'dark')
    
    // 根据类型设置不同的配置
    let option: any = {}
    
    switch (type) {
      case 'line':
        option = getLineChartOption(data)
        break
      case 'bar':
        option = getBarChartOption(data)
        break
      case 'pie':
        option = getPieChartOption(data)
        break
      case 'gauge':
        option = getGaugeChartOption(data)
        break
      case 'radar':
        option = getRadarChartOption(data)
        break
      default:
        option = getLineChartOption(data)
    }

    chartInstance.current.setOption(option)

    // 响应式调整
    const handleResize = () => {
      chartInstance.current?.resize()
    }
    
    window.addEventListener('resize', handleResize)

    return () => {
      window.removeEventListener('resize', handleResize)
      chartInstance.current?.dispose()
    }
  }, [type, data])

  return (
    <Card 
      className={`modern-chart-card ${className}`}
      bodyStyle={{ padding: '20px' }}
      bordered={false}
    >
      <div className="flex items-center justify-between mb-4">
        <Title level={4} className="!mb-0 !text-gray-100">
          {title}
        </Title>
      </div>
      <div 
        ref={chartRef} 
        style={{ height: `${height}px`, width: '100%' }}
        className="chart-container"
      />
    </Card>
  )
}

// 折线图配置
const getLineChartOption = (data: any) => ({
  backgroundColor: 'transparent',
  grid: {
    left: '3%',
    right: '4%',
    bottom: '3%',
    containLabel: true
  },
  xAxis: {
    type: 'category',
    data: data.xAxis || ['周一', '周二', '周三', '周四', '周五', '周六', '周日'],
    axisLine: {
      lineStyle: { color: '#4a5568' }
    },
    axisLabel: {
      color: '#a0aec0'
    }
  },
  yAxis: {
    type: 'value',
    axisLine: {
      lineStyle: { color: '#4a5568' }
    },
    axisLabel: {
      color: '#a0aec0'
    },
    splitLine: {
      lineStyle: { color: '#2d3748' }
    }
  },
  series: [{
    data: data.series || [120, 200, 150, 80, 70, 110, 130],
    type: 'line',
    smooth: true,
    lineStyle: {
      color: '#667eea',
      width: 3
    },
    itemStyle: {
      color: '#667eea'
    },
    areaStyle: {
      color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
        offset: 0,
        color: 'rgba(102, 126, 234, 0.8)'
      }, {
        offset: 1,
        color: 'rgba(102, 126, 234, 0.1)'
      }])
    }
  }],
  tooltip: {
    trigger: 'axis',
    backgroundColor: 'rgba(0, 0, 0, 0.8)',
    borderColor: 'transparent',
    textStyle: {
      color: '#fff'
    }
  }
})

// 柱状图配置
const getBarChartOption = (data: any) => ({
  backgroundColor: 'transparent',
  grid: {
    left: '3%',
    right: '4%',
    bottom: '3%',
    containLabel: true
  },
  xAxis: {
    type: 'category',
    data: data.xAxis || ['语文', '数学', '英语', '物理', '化学', '生物'],
    axisLine: {
      lineStyle: { color: '#4a5568' }
    },
    axisLabel: {
      color: '#a0aec0'
    }
  },
  yAxis: {
    type: 'value',
    axisLine: {
      lineStyle: { color: '#4a5568' }
    },
    axisLabel: {
      color: '#a0aec0'
    },
    splitLine: {
      lineStyle: { color: '#2d3748' }
    }
  },
  series: [{
    data: data.series || [320, 280, 200, 150, 100, 80],
    type: 'bar',
    itemStyle: {
      color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
        offset: 0,
        color: '#ff6b6b'
      }, {
        offset: 1,
        color: '#ee5a52'
      }]),
      borderRadius: [4, 4, 0, 0]
    }
  }],
  tooltip: {
    trigger: 'axis',
    backgroundColor: 'rgba(0, 0, 0, 0.8)',
    borderColor: 'transparent',
    textStyle: {
      color: '#fff'
    }
  }
})

// 饼图配置
const getPieChartOption = (data: any) => ({
  backgroundColor: 'transparent',
  series: [{
    name: data.name || '数据分布',
    type: 'pie',
    radius: ['40%', '70%'],
    center: ['50%', '50%'],
    data: data.series || [
      { value: 335, name: '正面评语' },
      { value: 310, name: '中性评语' },
      { value: 234, name: '建议改进' },
      { value: 135, name: '表扬激励' }
    ],
    itemStyle: {
      borderRadius: 8,
      borderColor: '#1a202c',
      borderWidth: 2
    },
    label: {
      color: '#a0aec0'
    },
    labelLine: {
      lineStyle: {
        color: '#4a5568'
      }
    }
  }],
  color: ['#667eea', '#f093fb', '#4facfe', '#43e97b'],
  tooltip: {
    trigger: 'item',
    backgroundColor: 'rgba(0, 0, 0, 0.8)',
    borderColor: 'transparent',
    textStyle: {
      color: '#fff'
    }
  }
})

// 仪表盘配置
const getGaugeChartOption = (data: any) => ({
  backgroundColor: 'transparent',
  series: [{
    name: data.name || '系统性能',
    type: 'gauge',
    center: ['50%', '60%'],
    startAngle: 200,
    endAngle: -40,
    min: 0,
    max: 100,
    splitNumber: 10,
    itemStyle: {
      color: '#667eea'
    },
    progress: {
      show: true,
      roundCap: true,
      width: 18
    },
    pointer: {
      icon: 'path://M2090.36389,615.30999 L2090.36389,615.30999 C2091.48372,615.30999 2092.40383,616.194028 2092.44859,617.312956 L2096.90698,728.755929 C2097.05155,732.369577 2094.2393,735.416212 2090.62566,735.56078 C2090.53845,735.564269 2090.45117,735.566014 2090.36389,735.566014 L2090.36389,735.566014 C2086.74736,735.566014 2083.81557,732.63423 2083.81557,729.017692 C2083.81557,728.930412 2083.81732,728.84314 2083.82081,728.755929 L2088.2792,617.312956 C2088.32396,616.194028 2089.24407,615.30999 2090.36389,615.30999 Z',
      length: '75%',
      width: 16,
      offsetCenter: [0, '5%']
    },
    axisLine: {
      roundCap: true,
      lineStyle: {
        width: 18,
        color: [[1, '#2d3748']]
      }
    },
    axisTick: {
      distance: -45,
      splitNumber: 5,
      lineStyle: {
        width: 2,
        color: '#4a5568'
      }
    },
    splitLine: {
      distance: -52,
      length: 14,
      lineStyle: {
        width: 3,
        color: '#4a5568'
      }
    },
    axisLabel: {
      distance: -20,
      color: '#a0aec0',
      fontSize: 12
    },
    title: {
      show: false
    },
    detail: {
      backgroundColor: '#1a202c',
      borderColor: '#667eea',
      borderWidth: 2,
      width: '60%',
      lineHeight: 40,
      height: 40,
      borderRadius: 8,
      offsetCenter: [0, '35%'],
      valueAnimation: true,
      formatter: function (value: number) {
        return '{value|' + value.toFixed(0) + '}{unit|%}'
      },
      rich: {
        value: {
          fontSize: 24,
          fontWeight: 'bolder',
          color: '#667eea'
        },
        unit: {
          fontSize: 16,
          color: '#a0aec0',
          padding: [0, 0, -20, 10]
        }
      }
    },
    data: [{
      value: data.value || 75
    }]
  }]
})

// 雷达图配置
const getRadarChartOption = (data: any) => ({
  backgroundColor: 'transparent',
  radar: {
    indicator: data.indicator || [
      { name: '用户活跃度', max: 100 },
      { name: '生成质量', max: 100 },
      { name: '响应速度', max: 100 },
      { name: '系统稳定性', max: 100 },
      { name: '用户满意度', max: 100 }
    ],
    shape: 'polygon',
    splitNumber: 5,
    name: {
      color: '#a0aec0'
    },
    splitLine: {
      lineStyle: {
        color: '#4a5568'
      }
    },
    splitArea: {
      show: false
    },
    axisLine: {
      lineStyle: {
        color: '#4a5568'
      }
    }
  },
  series: [{
    name: data.name || '系统指标',
    type: 'radar',
    data: [{
      value: data.series || [80, 90, 85, 95, 88],
      name: '当前指标',
      itemStyle: {
        color: '#667eea'
      },
      areaStyle: {
        color: 'rgba(102, 126, 234, 0.3)'
      }
    }]
  }],
  tooltip: {
    trigger: 'item',
    backgroundColor: 'rgba(0, 0, 0, 0.8)',
    borderColor: 'transparent',
    textStyle: {
      color: '#fff'
    }
  }
})

export default ModernChart