<view class="custom-navbar" style="padding-top: {{statusBarHeight}}px;">
  <view class="navbar-content">
    <!-- 左侧返回按钮 -->
    <view class="navbar-left" wx:if="{{showBack}}" bindtap="handleBack">
      <van-icon name="arrow-left" size="18px" color="white" />
    </view>
    
    <!-- 品牌区域 -->
    <view class="navbar-brand" wx:if="{{showBrand}}">
      <image class="brand-icon" src="/images/logo.svg" mode="aspectFit" />
      <text class="brand-name">{{brandName}}</text>
    </view>
    
    <!-- 中间标题 -->
    <view class="navbar-title" wx:else>
      <text class="title-text">{{title}}</text>
    </view>
    
    <!-- 右侧操作按钮 -->
    <view class="navbar-right" wx:if="{{showMenu}}">
      <view class="menu-item" bindtap="handleMenu">
        <van-icon name="ellipsis" size="18px" color="white" />
      </view>
    </view>
    
    <!-- 自定义右插槽 -->
    <view class="navbar-right-slot" wx:if="{{showRightSlot}}">
      <slot name="right"></slot>
    </view>
  </view>
  
  <!-- 底部渐变装饰 -->
  <view class="navbar-decoration"></view>
</view>