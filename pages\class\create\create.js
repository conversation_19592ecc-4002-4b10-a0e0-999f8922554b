/**
 * 班级创建/编辑页面
 * 基于微信云开发
 */
const app = getApp();
const { cloudService } = require('../../../services/cloudService');

Page({
  /**
   * 页面的初始数据
   */
  data: {
    // 是否编辑模式
    isEdit: false,

    // 表单数据
    formData: {
      name: '',
      description: '',
      grade: '',
      subject: '',
      status: 'active'
    },

    // 表单错误
    errors: {
      name: '',
      grade: '',
      subject: '',
      description: ''
    },

    // 提交状态
    submitting: false
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    const { mode, id } = options;

    if (mode === 'edit' && id) {
      this.setData({
        isEdit: true,
        classId: id
      });
    }
  },

  /**
   * 字段值变化
   */
  onFieldChange(e) {
    const { field } = e.currentTarget.dataset;
    const { value } = e.detail;

    this.setData({
      [`formData.${field}`]: value,
      [`errors.${field}`]: ''
    });
  },

  /**
   * 班级名称输入
   */
  onNameInput(e) {
    const value = e.detail?.value || e.detail || '';
    this.setData({
      'formData.name': value,
      'errors.name': ''
    });
  },

  /**
   * 年级输入
   */
  onGradeInput(e) {
    const value = e.detail?.value || e.detail || '';
    this.setData({
      'formData.grade': value,
      'errors.grade': ''
    });
  },

  /**
   * 学科输入
   */
  onSubjectInput(e) {
    const value = e.detail?.value || e.detail || '';
    this.setData({
      'formData.subject': value,
      'errors.subject': ''
    });
  },

  /**
   * 描述输入
   */
  onDescriptionInput(e) {
    const value = e.detail?.value || e.detail || '';
    this.setData({
      'formData.description': value
    });
  },



  /**
   * 状态变化
   */
  onStatusChange(e) {
    this.setData({
      'formData.status': e.detail
    });
  },



  /**
   * 提交表单
   */
  async onSubmit() {
    const { formData, isEdit, classId } = this.data;

    // 表单验证
    const errors = {};
    if (!formData.name) errors.name = '请输入班级名称';
    if (!formData.grade) errors.grade = '请选择年级';
    if (!formData.subject) errors.subject = '请选择学科';

    if (Object.keys(errors).length > 0) {
      this.setData({ errors });
      wx.showToast({
        title: '请检查表单信息',
        icon: 'none'
      });
      return;
    }

    try {
      this.setData({ submitting: true });

      // 准备班级数据
      const classData = {
        className: formData.name,
        grade: formData.grade,
        subject: formData.subject,
        description: formData.description || `${formData.grade}${formData.subject}班级`,
        studentCount: 0 // 初始学生数量为0
      };

      let result;
      if (isEdit && classId) {
        // 编辑模式：更新班级
        result = await cloudService.updateClass(classId, classData);
      } else {
        // 创建模式：新建班级
        result = await cloudService.createClass(classData);
      }

      if (result.success) {
        wx.showToast({
          title: isEdit ? '保存成功' : '创建成功',
          icon: 'success'
        });

        setTimeout(() => {
          wx.navigateBack();
        }, 1500);
      } else {
        throw new Error(result.error || '操作失败');
      }

    } catch (error) {
      console.error('班级操作失败:', error);
      wx.showToast({
        title: isEdit ? '保存失败' : '创建失败',
        icon: 'none'
      });
    } finally {
      this.setData({ submitting: false });
    }
  },

  /**
   * 取消操作
   */
  onCancel() {
    wx.navigateBack();
  }
});