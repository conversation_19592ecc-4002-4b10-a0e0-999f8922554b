@echo off
chcp 65001 >nul
echo ====================================
echo 部署 dataQuery 云函数
echo ====================================
echo.

echo 🚀 开始部署 dataQuery 云函数...
echo.

cd cloudfunctions\dataQuery

echo 📦 安装依赖...
call npm install
if %errorlevel% neq 0 (
    echo ❌ 安装依赖失败
    pause
    exit /b 1
)

echo 🚀 部署到云端...
call wxcloud functions:deploy dataQuery
if %errorlevel% neq 0 (
    echo ❌ 部署失败
    echo.
    echo 💡 请确认：
    echo   1. 微信开发者工具已登录
    echo   2. 已安装微信云开发CLI (npm install -g @cloudbase/cli)
    echo   3. 云开发环境已配置
    echo.
    pause
    exit /b 1
)

cd ..\..

echo.
echo ✅ dataQuery 云函数部署成功！
echo.
echo 💡 现在可以测试 getClasses 功能了
echo.
pause