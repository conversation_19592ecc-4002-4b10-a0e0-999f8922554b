/**
 * 初始化提示词模板脚本
 * 调用 managePromptTemplates 云函数的 initDefaults 操作
 */

const cloud = require('wx-server-sdk')

// 初始化云开发环境
cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

async function initPromptTemplates() {
  try {
    console.log('🚀 开始初始化提示词模板...')
    
    // 调用云函数初始化默认模板
    const result = await cloud.callFunction({
      name: 'managePromptTemplates',
      data: {
        action: 'initDefaults'
      }
    })
    
    if (result.result.success) {
      console.log('✅ 提示词模板初始化成功！')
      console.log(`📊 已初始化 ${result.result.count} 个模板`)
      console.log('📋 模板类型：')
      console.log('  - gentle (温和亲切型)')
      console.log('  - encouraging (鼓励激励型)')
      console.log('  - detailed (详细具体型)')
      console.log('  - comprehensive (综合发展型)')
      console.log('  - formal (正式规范型)')
    } else {
      console.error('❌ 初始化失败:', result.result.error)
    }
    
  } catch (error) {
    console.error('❌ 执行失败:', error)
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  initPromptTemplates()
}

module.exports = { initPromptTemplates }