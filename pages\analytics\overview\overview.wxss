/* 数据分析概览页面样式 */
.analytics-overview-page {
  min-height: 100vh;
  background: #f8f9fa;
  padding-bottom: 40rpx;
}

/* 时间筛选器 */
.time-filter-section {
  background: white;
  padding: 24rpx;
  margin-bottom: 16rpx;
}

.filter-tabs {
  display: flex;
  background: #f0f0f0;
  border-radius: 12rpx;
  padding: 6rpx;
}

.filter-tab {
  flex: 1;
  text-align: center;
  padding: 16rpx 12rpx;
  font-size: 26rpx;
  color: #666;
  border-radius: 8rpx;
  transition: all 0.3s ease;
}

.filter-tab.active {
  background: #4080FF;
  color: white;
  font-weight: 500;
}

/* 核心指标 */
.metrics-section {
  background: white;
  margin: 16rpx 24rpx;
  border-radius: 20rpx;
  padding: 32rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.06);
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 24rpx;
}

.metrics-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20rpx;
}

.metric-card {
  display: flex;
  align-items: center;
  padding: 24rpx;
  background: #f8f9fa;
  border-radius: 16rpx;
}

.metric-icon {
  width: 56rpx;
  height: 56rpx;
  border-radius: 14rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16rpx;
  flex-shrink: 0;
}

.metric-icon.total { background: linear-gradient(135deg, #4080FF, #6B9FFF); }
.metric-icon.records { background: linear-gradient(135deg, #52C873, #7DD87F); }
.metric-icon.score { background: linear-gradient(135deg, #FFAA33, #FFB84D); }
.metric-icon.comments { background: linear-gradient(135deg, #8B5CF6, #A78BFA); }

.metric-content {
  flex: 1;
}

.metric-number {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 4rpx;
}

.metric-label {
  font-size: 22rpx;
  color: #999;
  margin-bottom: 8rpx;
}

.metric-change {
  display: flex;
  align-items: center;
  gap: 4rpx;
  font-size: 20rpx;
  font-weight: 500;
}

.metric-change.positive {
  color: #52C873;
}

.metric-change.negative {
  color: #FF5247;
}

/* 图表区域 */
.chart-section {
  background: white;
  margin: 16rpx 24rpx;
  border-radius: 20rpx;
  padding: 32rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.06);
}

.chart-container {
  height: 400rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f8f9fa;
  border-radius: 16rpx;
}

.chart-placeholder {
  text-align: center;
}

.chart-text {
  font-size: 28rpx;
  color: #999;
  margin: 16rpx 0 8rpx;
}

.chart-desc {
  font-size: 22rpx;
  color: #ccc;
}

/* 行为分布 */
.behavior-section {
  background: white;
  margin: 16rpx 24rpx;
  border-radius: 20rpx;
  padding: 32rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.06);
}

.behavior-stats {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.behavior-item {
  display: flex;
  align-items: center;
  padding: 20rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
}

.behavior-icon {
  width: 40rpx;
  height: 40rpx;
  border-radius: 10rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16rpx;
  flex-shrink: 0;
}

.behavior-icon.positive { background: #52C873; }
.behavior-icon.academic { background: #4080FF; }
.behavior-icon.social { background: #FFAA33; }
.behavior-icon.negative { background: #FF5247; }

.behavior-content {
  flex: 1;
}

.behavior-name {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 4rpx;
}

.behavior-count {
  font-size: 22rpx;
  color: #666;
}

.behavior-percentage {
  font-size: 32rpx;
  font-weight: 600;
  color: #4080FF;
  flex-shrink: 0;
}

/* 班级排行 */
.ranking-section {
  background: white;
  margin: 16rpx 24rpx;
  border-radius: 20rpx;
  padding: 32rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.06);
}

.ranking-list {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

.ranking-item {
  display: flex;
  align-items: center;
  padding: 20rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
  transition: all 0.3s ease;
}

.ranking-item:active {
  transform: scale(0.98);
  background: #f0f0f0;
}

.ranking-number {
  width: 48rpx;
  height: 48rpx;
  border-radius: 12rpx;
  background: #ddd;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  font-weight: 600;
  margin-right: 16rpx;
  flex-shrink: 0;
}

.ranking-number.top {
  background: linear-gradient(135deg, #FFD700, #FFA500);
}

.ranking-content {
  flex: 1;
}

.class-name {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 6rpx;
}

.class-stats {
  display: flex;
  gap: 16rpx;
}

.stat-item {
  font-size: 22rpx;
  color: #666;
}

.ranking-score {
  font-size: 32rpx;
  font-weight: 600;
  color: #4080FF;
  flex-shrink: 0;
}

/* 趋势图表样式 */
.trend-chart {
  padding: 20rpx 0;
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32rpx;
  padding: 0 16rpx;
}

.chart-title {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
}

.chart-max {
  font-size: 24rpx;
  color: #666;
}

.chart-bars {
  display: flex;
  align-items: flex-end;
  justify-content: space-between;
  height: 200rpx;
  padding: 0 16rpx;
  margin-bottom: 16rpx;
  position: relative;
}

.chart-bar {
  flex: 1;
  margin: 0 2rpx;
  background: linear-gradient(180deg, #4080FF 0%, #6BA3FF 100%);
  border-radius: 6rpx 6rpx 0 0;
  min-height: 20rpx;
  position: relative;
  display: flex;
  align-items: flex-start;
  justify-content: center;
  transition: all 0.3s ease;
}

.chart-bar:hover {
  background: linear-gradient(180deg, #2C5CE6 0%, #4080FF 100%);
}

.bar-value {
  position: absolute;
  top: -32rpx;
  font-size: 20rpx;
  color: #333;
  font-weight: 500;
  white-space: nowrap;
}

.chart-labels {
  display: flex;
  justify-content: space-between;
  padding: 0 16rpx;
}

.chart-label {
  flex: 1;
  text-align: center;
  font-size: 22rpx;
  color: #666;
  margin: 0 2rpx;
}