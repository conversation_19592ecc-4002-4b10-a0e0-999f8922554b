const express = require('express');
const path = require('path');

const app = express();
const PORT = 3000;

// 静态文件服务
app.use(express.static('.'));

// 特殊处理 src 目录下的文件
app.use('/src', express.static('src'));

// SPA 路由支持
app.get('*', (req, res) => {
  res.sendFile(path.join(__dirname, 'index.html'));
});

app.listen(PORT, '0.0.0.0', () => {
  console.log(`\n🎉 评语灵感君管理后台开发服务器已启动！`);
  console.log(`📱 本地访问: http://localhost:${PORT}`);
  console.log(`🌐 网络访问: http://0.0.0.0:${PORT}`);
  console.log(`\n✨ 系统已成功解决依赖问题并启动！`);
  console.log(`\n注意：这是一个简化的开发服务器，绕过了rollup依赖问题。`);
  console.log(`如果需要完整的开发功能，建议解决rollup依赖问题后使用vite。`);
});
