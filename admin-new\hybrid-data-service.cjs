/**
 * 混合数据服务
 * 使用真实的数据结构，但填充合理的模拟数据
 * 避免wx-server-sdk的认证问题
 */

class HybridDataService {
  constructor() {
    console.log('🔗 HybridDataService 初始化 - 混合数据模式')
    
    // 基于真实数据库集合结构的模拟数据
    this.mockData = {
      users: [
        { _id: 'user001', openid: 'openid001', nickName: '张老师', avatarUrl: 'https://example.com/avatar1.jpg', lastLoginTime: new Date(), createTime: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), loginCount: 45 },
        { _id: 'user002', openid: 'openid002', nickName: '李老师', avatarUrl: 'https://example.com/avatar2.jpg', lastLoginTime: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000), createTime: new Date(Date.now() - 25 * 24 * 60 * 60 * 1000), loginCount: 32 },
        { _id: 'user003', openid: 'openid003', nickName: '王老师', avatarUrl: 'https://example.com/avatar3.jpg', lastLoginTime: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000), createTime: new Date(Date.now() - 20 * 24 * 60 * 60 * 1000), loginCount: 28 },
        { _id: 'user004', openid: 'openid004', nickName: '赵老师', avatarUrl: 'https://example.com/avatar4.jpg', lastLoginTime: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000), createTime: new Date(Date.now() - 15 * 24 * 60 * 60 * 1000), loginCount: 38 },
        { _id: 'user005', openid: 'openid005', nickName: '刘老师', avatarUrl: 'https://example.com/avatar5.jpg', lastLoginTime: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000), createTime: new Date(Date.now() - 10 * 24 * 60 * 60 * 1000), loginCount: 22 }
      ],
      teachers: [
        { _id: 'teacher001', userId: 'user001', name: '张老师', subject: '数学', classIds: ['class001', 'class002'], school: '实验小学', phone: '138****1234', email: '<EMAIL>', status: 'active' },
        { _id: 'teacher002', userId: 'user002', name: '李老师', subject: '语文', classIds: ['class003'], school: '实验小学', phone: '139****5678', email: '<EMAIL>', status: 'active' },
        { _id: 'teacher003', userId: 'user003', name: '王老师', subject: '英语', classIds: ['class004'], school: '实验小学', phone: '137****9012', email: '<EMAIL>', status: 'active' }
      ],
      classes: [
        { _id: 'class001', name: '三年级一班', grade: 3, teacherId: 'teacher001', studentCount: 35, school: '实验小学', createTime: new Date(Date.now() - 60 * 24 * 60 * 60 * 1000) },
        { _id: 'class002', name: '三年级二班', grade: 3, teacherId: 'teacher001', studentCount: 33, school: '实验小学', createTime: new Date(Date.now() - 60 * 24 * 60 * 60 * 1000) },
        { _id: 'class003', name: '四年级一班', grade: 4, teacherId: 'teacher002', studentCount: 32, school: '实验小学', createTime: new Date(Date.now() - 60 * 24 * 60 * 60 * 1000) },
        { _id: 'class004', name: '五年级一班', grade: 5, teacherId: 'teacher003', studentCount: 30, school: '实验小学', createTime: new Date(Date.now() - 60 * 24 * 60 * 60 * 1000) }
      ],
      comments: [
        { _id: 'comment001', studentId: 'student001', studentName: '小明', teacherId: 'teacher001', teacherName: '张老师', openid: 'openid001', content: '该学生在数学方面表现优秀，思维敏捷，能够快速理解新概念，课堂参与度高，作业完成质量好。', createTime: new Date(), subject: '数学', aiModel: 'doubao', tokensUsed: 150, promptId: 'prompt001', generationTime: 2.3 },
        { _id: 'comment002', studentId: 'student002', studentName: '小红', teacherId: 'teacher002', teacherName: '李老师', openid: 'openid002', content: '语文基础扎实，阅读理解能力强，作文表达清晰有条理，字迹工整，学习态度认真。', createTime: new Date(Date.now() - 2 * 60 * 60 * 1000), subject: '语文', aiModel: 'doubao', tokensUsed: 180, promptId: 'prompt002', generationTime: 1.8 },
        { _id: 'comment003', studentId: 'student003', studentName: '小刚', teacherId: 'teacher003', teacherName: '王老师', openid: 'openid003', content: '英语口语表达流利，词汇量丰富，学习态度积极主动，能够主动参与课堂讨论。', createTime: new Date(Date.now() - 4 * 60 * 60 * 1000), subject: '英语', aiModel: 'doubao', tokensUsed: 160, promptId: 'prompt003', generationTime: 2.1 },
        { _id: 'comment004', studentId: 'student004', studentName: '小丽', teacherId: 'teacher001', teacherName: '张老师', openid: 'openid001', content: '科学实验操作规范，观察细致，能够准确记录实验现象，具有良好的科学素养。', createTime: new Date(Date.now() - 6 * 60 * 60 * 1000), subject: '科学', aiModel: 'doubao', tokensUsed: 170, promptId: 'prompt004', generationTime: 2.5 },
        { _id: 'comment005', studentId: 'student005', studentName: '小华', teacherId: 'teacher001', teacherName: '张老师', openid: 'openid001', content: '体育运动能力强，团队合作意识好，能够带动其他同学积极参与体育活动。', createTime: new Date(Date.now() - 8 * 60 * 60 * 1000), subject: '体育', aiModel: 'doubao', tokensUsed: 140, promptId: 'prompt005', generationTime: 1.9 }
      ],
      students: [
        { _id: 'student001', name: '小明', classId: 'class001', className: '三年级一班', teacherId: 'teacher001', teacherName: '张老师', studentNumber: '2024001', gender: '男', age: 9, commentsCount: 2, lastCommentTime: new Date(), updateTime: new Date(), status: 'active' },
        { _id: 'student002', name: '小红', classId: 'class003', className: '四年级一班', teacherId: 'teacher002', teacherName: '李老师', studentNumber: '2024002', gender: '女', age: 10, commentsCount: 1, lastCommentTime: new Date(Date.now() - 2 * 60 * 60 * 1000), updateTime: new Date(), status: 'active' },
        { _id: 'student003', name: '小刚', classId: 'class004', className: '五年级一班', teacherId: 'teacher003', teacherName: '王老师', studentNumber: '2024003', gender: '男', age: 11, commentsCount: 1, lastCommentTime: new Date(Date.now() - 4 * 60 * 60 * 1000), updateTime: new Date(), status: 'active' },
        { _id: 'student004', name: '小丽', classId: 'class001', className: '三年级一班', teacherId: 'teacher001', teacherName: '张老师', studentNumber: '2024004', gender: '女', age: 9, commentsCount: 1, lastCommentTime: new Date(Date.now() - 6 * 60 * 60 * 1000), updateTime: new Date(), status: 'active' },
        { _id: 'student005', name: '小华', classId: 'class002', className: '三年级二班', teacherId: 'teacher001', teacherName: '张老师', studentNumber: '2024005', gender: '男', age: 9, commentsCount: 1, lastCommentTime: new Date(Date.now() - 8 * 60 * 60 * 1000), updateTime: new Date(), status: 'active' }
      ],
      ai_usage: [
        { _id: 'usage001', userId: 'user001', openid: 'openid001', action: 'generate_comment', model: 'doubao', tokensUsed: 150, cost: 0.003, createTime: new Date(), success: true },
        { _id: 'usage002', userId: 'user002', openid: 'openid002', action: 'generate_comment', model: 'doubao', tokensUsed: 180, cost: 0.0036, createTime: new Date(Date.now() - 2 * 60 * 60 * 1000), success: true },
        { _id: 'usage003', userId: 'user003', openid: 'openid003', action: 'generate_comment', model: 'doubao', tokensUsed: 160, cost: 0.0032, createTime: new Date(Date.now() - 4 * 60 * 60 * 1000), success: true }
      ],
      ai_generation_logs: [
        { _id: 'log001', userId: 'user001', studentId: 'student001', prompt: '请为数学表现优秀的学生生成评语', response: '该学生在数学方面表现优秀...', tokensUsed: 150, generationTime: 2.3, createTime: new Date(), success: true },
        { _id: 'log002', userId: 'user002', studentId: 'student002', prompt: '请为语文基础扎实的学生生成评语', response: '语文基础扎实，阅读理解能力强...', tokensUsed: 180, generationTime: 1.8, createTime: new Date(Date.now() - 2 * 60 * 60 * 1000), success: true }
      ],
      system_config: [
        { _id: 'config001', key: 'ai_model_default', value: 'doubao', description: '默认AI模型', updateTime: new Date() },
        { _id: 'config002', key: 'max_tokens_per_request', value: 2000, description: '单次请求最大token数', updateTime: new Date() },
        { _id: 'config003', key: 'daily_usage_limit', value: 10000, description: '每日使用限制', updateTime: new Date() }
      ]
    }
  }

  /**
   * 获取总用户数
   */
  async getTotalUsers() {
    try {
      console.log('📊 查询总用户数...')
      const total = this.mockData.users.length
      console.log('✅ 总用户数查询成功:', total)
      return total
    } catch (error) {
      console.error('❌ 查询总用户数失败:', error)
      return 0
    }
  }

  /**
   * 获取活跃教师用户数（最近30天有活动）
   */
  async getActiveTeachers() {
    try {
      console.log('📊 查询活跃教师用户数...')
      
      const thirtyDaysAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)
      const activeUsers = this.mockData.users.filter(user => 
        new Date(user.lastLoginTime) >= thirtyDaysAgo
      )
      
      console.log('✅ 活跃教师用户数查询成功:', activeUsers.length)
      return activeUsers.length
    } catch (error) {
      console.error('❌ 查询活跃教师用户数失败:', error)
      return 0
    }
  }

  /**
   * 获取评语总数
   */
  async getTotalComments() {
    try {
      console.log('📊 查询评语总数...')
      const total = this.mockData.comments.length
      console.log('✅ 评语总数查询成功:', total)
      return total
    } catch (error) {
      console.error('❌ 查询评语总数失败:', error)
      return 0
    }
  }

  /**
   * 获取今日评语数
   */
  async getTodayComments() {
    try {
      console.log('📊 查询今日评语数...')
      
      const today = new Date()
      today.setHours(0, 0, 0, 0)
      
      const todayComments = this.mockData.comments.filter(comment => 
        new Date(comment.createTime) >= today
      )
      
      console.log('✅ 今日评语数查询成功:', todayComments.length)
      return todayComments.length
    } catch (error) {
      console.error('❌ 查询今日评语数失败:', error)
      return 0
    }
  }

  /**
   * 获取AI调用总数
   */
  async getTotalAICalls() {
    try {
      console.log('📊 查询AI调用总数...')

      // 从ai_usage集合统计总token使用量
      const totalTokens = this.mockData.ai_usage.reduce((sum, usage) => sum + (usage.tokensUsed || 0), 0)

      console.log('✅ AI调用总数查询成功:', totalTokens)
      return totalTokens
    } catch (error) {
      console.error('❌ 查询AI调用总数失败:', error)
      return 0
    }
  }

  /**
   * 获取最近活动记录
   */
  async getRecentActivities(limit = 10) {
    try {
      console.log(`📊 查询最近${limit}条活动记录...`)
      
      const activities = this.mockData.comments
        .sort((a, b) => new Date(b.createTime) - new Date(a.createTime))
        .slice(0, limit)
        .map(comment => ({
          id: comment._id,
          userId: comment.openid,
          userName: comment.teacherName,
          action: `为学生"${comment.studentName}"生成${comment.subject}评语`,
          actionType: 'comment_generate',
          timestamp: comment.createTime,
          metadata: {
            studentName: comment.studentName,
            subject: comment.subject,
            contentLength: comment.content.length,
            tokensUsed: comment.tokensUsed
          }
        }))
      
      console.log('✅ 最近活动记录查询成功:', activities.length)
      return activities
    } catch (error) {
      console.error('❌ 查询最近活动记录失败:', error)
      return []
    }
  }

  /**
   * 获取学生数据
   */
  async getStudents(params = {}) {
    try {
      const { page = 1, limit = 20, keyword = '' } = params
      
      console.log(`📊 查询学生数据，页码:${page}, 限制:${limit}, 关键词:${keyword}`)
      
      let filteredStudents = this.mockData.students
      
      if (keyword) {
        filteredStudents = this.mockData.students.filter(student => 
          student.name.includes(keyword) || student.class.includes(keyword)
        )
      }
      
      const skip = (page - 1) * limit
      const paginatedStudents = filteredStudents.slice(skip, skip + limit)
      
      const students = paginatedStudents.map(student => ({
        id: student._id,
        name: student.name,
        class: student.class,
        teacher: student.teacher,
        commentsCount: student.commentsCount,
        lastUpdate: student.updateTime,
        status: student.status
      }))
      
      console.log('✅ 学生数据查询成功:', students.length)
      
      return {
        list: students,
        total: filteredStudents.length,
        page,
        limit
      }
    } catch (error) {
      console.error('❌ 查询学生数据失败:', error)
      return {
        list: [],
        total: 0,
        page: 1,
        limit: 20
      }
    }
  }

  /**
   * 获取评语记录
   */
  async getComments(params = {}) {
    try {
      const { page = 1, limit = 20, teacherId = '', studentId = '' } = params
      
      console.log(`📊 查询评语记录，页码:${page}, 限制:${limit}`)
      
      let filteredComments = this.mockData.comments
      
      if (teacherId) {
        filteredComments = filteredComments.filter(comment => comment.openid === teacherId)
      }
      if (studentId) {
        filteredComments = filteredComments.filter(comment => comment._id.includes(studentId))
      }
      
      const skip = (page - 1) * limit
      const paginatedComments = filteredComments.slice(skip, skip + limit)
      
      const comments = paginatedComments.map(comment => ({
        id: comment._id,
        studentId: comment._id, // 简化处理
        studentName: comment.studentName,
        teacherId: comment.openid,
        teacherName: comment.teacherName,
        content: comment.content,
        aiModel: comment.aiModel,
        tokensUsed: comment.tokensUsed,
        createTime: comment.createTime,
        subject: comment.subject
      }))
      
      console.log('✅ 评语记录查询成功:', comments.length)
      
      return {
        list: comments,
        total: filteredComments.length,
        page,
        limit
      }
    } catch (error) {
      console.error('❌ 查询评语记录失败:', error)
      return {
        list: [],
        total: 0,
        page: 1,
        limit: 20
      }
    }
  }

  /**
   * 获取仪表板统计数据
   */
  async getDashboardStats() {
    try {
      console.log('📊 获取仪表板统计数据...')
      
      const [totalUsers, todayComments, aiCalls] = await Promise.all([
        this.getActiveTeachers(),
        this.getTodayComments(),
        this.getTotalAICalls()
      ])
      
      const stats = {
        totalUsers,
        todayComments,
        aiCalls,
        satisfaction: 4.8,
        lastUpdated: new Date().toISOString()
      }
      
      console.log('✅ 仪表板统计数据获取成功:', stats)
      return stats
    } catch (error) {
      console.error('❌ 获取仪表板统计数据失败:', error)
      return {
        totalUsers: 0,
        todayComments: 0,
        aiCalls: 0,
        satisfaction: 0,
        lastUpdated: new Date().toISOString()
      }
    }
  }

  /**
   * 测试数据库连接
   */
  async testConnection() {
    try {
      console.log('🔍 测试数据库连接...')
      
      const sampleUser = this.mockData.users.length > 0 ? this.mockData.users[0] : null
      
      console.log('✅ 数据库连接测试成功（混合数据模式）')
      return {
        success: true,
        message: '混合数据服务连接正常（使用真实数据结构的模拟数据）',
        collections: ['users', 'students', 'comments'],
        sampleData: sampleUser
      }
    } catch (error) {
      console.error('❌ 数据库连接测试失败:', error)
      return {
        success: false,
        message: error.message,
        collections: [],
        sampleData: null
      }
    }
  }
}

// 导出单例实例
const hybridDataService = new HybridDataService()
module.exports = hybridDataService
